{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Paper,Typography,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TablePagination,IconButton,Button,TextField,MenuItem,Select,FormControl,InputLabel,Chip,Avatar,Dialog,DialogTitle,DialogContent,DialogActions,Alert,Tooltip,Card,CardContent,Grid,Container}from'@mui/material';import{Restore as RestoreIcon,DeleteForever as DeleteForeverIcon,Visibility as ViewIcon,Search as SearchIcon,Refresh as RefreshIcon}from'@mui/icons-material';import{useTranslation}from'react-i18next';import axios from'../../utils/axios';import{format}from'date-fns';import{ar}from'date-fns/locale';import Layout from'../../components/Layout';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DeletedUsers=()=>{const{t,i18n}=useTranslation();const[users,setUsers]=useState([]);const[loading,setLoading]=useState(true);const[page,setPage]=useState(0);const[rowsPerPage,setRowsPerPage]=useState(10);const[total,setTotal]=useState(0);const[search,setSearch]=useState('');const[roleFilter,setRoleFilter]=useState('');const[selectedUser,setSelectedUser]=useState(null);const[viewDialog,setViewDialog]=useState(false);const[restoreDialog,setRestoreDialog]=useState(false);const[deleteDialog,setDeleteDialog]=useState(false);const[stats,setStats]=useState({});const[actionLoading,setActionLoading]=useState(false);const fetchUsers=async()=>{try{setLoading(true);const params={page:page+1,limit:rowsPerPage,search,role:roleFilter};const response=await axios.get('/admin/deleted-users',{params});setUsers(response.data.users);setTotal(response.data.total);}catch(error){console.error('Error fetching deleted users:',error);}finally{setLoading(false);}};const fetchStats=async()=>{try{const response=await axios.get('/admin/deleted-users/stats/overview');setStats(response.data.stats);}catch(error){console.error('Error fetching stats:',error);}};useEffect(()=>{fetchUsers();},[page,rowsPerPage,search,roleFilter]);useEffect(()=>{fetchStats();},[]);const handleRestore=async userId=>{try{setActionLoading(true);await axios.post(`/admin/deleted-users/${userId}/restore`);setRestoreDialog(false);setSelectedUser(null);fetchUsers();fetchStats();}catch(error){console.error('Error restoring user:',error);}finally{setActionLoading(false);}};const handlePermanentDelete=async userId=>{try{setActionLoading(true);await axios.delete(`/admin/deleted-users/${userId}/permanent`);setDeleteDialog(false);setSelectedUser(null);fetchUsers();fetchStats();}catch(error){console.error('Error permanently deleting user:',error);}finally{setActionLoading(false);}};const getRoleLabel=role=>{switch(role){case'student':return'طالب';case'platform_teacher':return'معلم';case'new_teacher':return'معلم جديد';default:return role;}};const getRoleColor=role=>{switch(role){case'student':return'primary';case'platform_teacher':return'success';case'new_teacher':return'warning';default:return'default';}};return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(Container,{maxWidth:\"lg\",sx:{py:4},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,children:\"\\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u0648\\u0646 \\u0627\\u0644\\u0645\\u062D\\u0630\\u0648\\u0641\\u0648\\u0646\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,sx:{mb:3},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{color:\"textSecondary\",gutterBottom:true,children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u062D\\u0630\\u0648\\u0641\\u064A\\u0646\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",children:stats.total_deleted||0})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{color:\"textSecondary\",gutterBottom:true,children:\"\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0645\\u062D\\u0630\\u0648\\u0641\\u064A\\u0646\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"primary\",children:stats.deleted_students||0})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{color:\"textSecondary\",gutterBottom:true,children:\"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0645\\u064A\\u0646 \\u0627\\u0644\\u0645\\u062D\\u0630\\u0648\\u0641\\u064A\\u0646\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"success.main\",children:stats.deleted_teachers||0})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{color:\"textSecondary\",gutterBottom:true,children:\"\\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646 \\u0627\\u0644\\u0646\\u0634\\u0637\\u064A\\u0646\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"info.main\",children:stats.total_active||0})]})})})]}),/*#__PURE__*/_jsx(Paper,{sx:{p:2,mb:2},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2,alignItems:'center',flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(TextField,{label:\"\\u0627\\u0644\\u0628\\u062D\\u062B\",variant:\"outlined\",size:\"small\",value:search,onChange:e=>setSearch(e.target.value),InputProps:{startAdornment:/*#__PURE__*/_jsx(SearchIcon,{sx:{mr:1,color:'text.secondary'}})},sx:{minWidth:200}}),/*#__PURE__*/_jsxs(FormControl,{size:\"small\",sx:{minWidth:120},children:[/*#__PURE__*/_jsx(InputLabel,{children:\"\\u0627\\u0644\\u0646\\u0648\\u0639\"}),/*#__PURE__*/_jsxs(Select,{value:roleFilter,label:\"\\u0627\\u0644\\u0646\\u0648\\u0639\",onChange:e=>setRoleFilter(e.target.value),children:[/*#__PURE__*/_jsx(MenuItem,{value:\"\",children:\"\\u0627\\u0644\\u0643\\u0644\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"student\",children:\"\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"platform_teacher\",children:\"\\u0645\\u0639\\u0644\\u0645\\u064A\\u0646\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"new_teacher\",children:\"\\u0645\\u0639\\u0644\\u0645\\u064A\\u0646 \\u062C\\u062F\\u062F\"})]})]}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(RefreshIcon,{}),onClick:()=>{fetchUsers();fetchStats();},children:\"\\u062A\\u062D\\u062F\\u064A\\u062B\"})]})}),/*#__PURE__*/_jsxs(TableContainer,{component:Paper,children:[/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"\\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u0627\\u0644\\u0646\\u0648\\u0639\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u062D\\u0630\\u0641\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u0633\\u0628\\u0628 \\u0627\\u0644\\u062D\\u0630\\u0641\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u062D\\u064F\\u0630\\u0641 \\u0628\\u0648\\u0627\\u0633\\u0637\\u0629\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:users.map(user=>{var _user$full_name;return/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Avatar,{src:user.profile_picture_url,children:(_user$full_name=user.full_name)===null||_user$full_name===void 0?void 0:_user$full_name.charAt(0)}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",children:user.full_name}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:user.email})]})]})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:getRoleLabel(user.role),color:getRoleColor(user.role),size:\"small\"})}),/*#__PURE__*/_jsx(TableCell,{children:format(new Date(user.deleted_at),'dd/MM/yyyy HH:mm',{locale:i18n.language==='ar'?ar:undefined})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Typography,{variant:\"caption\",children:user.deletion_reason||'غير محدد'})}),/*#__PURE__*/_jsx(TableCell,{children:user.deleted_by_name||'حذف ذاتي'}),/*#__PURE__*/_jsxs(TableCell,{children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>{setSelectedUser(user);setViewDialog(true);},children:/*#__PURE__*/_jsx(ViewIcon,{})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u0627\\u0633\\u062A\\u0631\\u062F\\u0627\\u062F \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",color:\"success\",onClick:()=>{setSelectedUser(user);setRestoreDialog(true);},children:/*#__PURE__*/_jsx(RestoreIcon,{})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u062D\\u0630\\u0641 \\u0646\\u0647\\u0627\\u0626\\u064A\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",color:\"error\",onClick:()=>{setSelectedUser(user);setDeleteDialog(true);},children:/*#__PURE__*/_jsx(DeleteForeverIcon,{})})})]})]},user.id);})})]}),/*#__PURE__*/_jsx(TablePagination,{component:\"div\",count:total,page:page,onPageChange:(e,newPage)=>setPage(newPage),rowsPerPage:rowsPerPage,onRowsPerPageChange:e=>{setRowsPerPage(parseInt(e.target.value,10));setPage(0);},labelRowsPerPage:\"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0635\\u0641\\u0648\\u0641:\",labelDisplayedRows:_ref=>{let{from,to,count}=_ref;return`${from}-${to} من ${count!==-1?count:`أكثر من ${to}`}`;}})]}),/*#__PURE__*/_jsxs(Dialog,{open:restoreDialog,onClose:()=>setRestoreDialog(false),children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"\\u0627\\u0633\\u062A\\u0631\\u062F\\u0627\\u062F \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsxs(Typography,{children:[\"\\u0647\\u0644 \\u0623\\u0646\\u062A \\u0645\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u0627\\u0633\\u062A\\u0631\\u062F\\u0627\\u062F \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645 \\\"\",selectedUser===null||selectedUser===void 0?void 0:selectedUser.full_name,\"\\\"\\u061F\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",sx:{mt:1,display:'block'},children:\"\\u0633\\u064A\\u062A\\u0645\\u0643\\u0646 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0645\\u0646 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0648\\u0627\\u0644\\u0648\\u0635\\u0648\\u0644 \\u0644\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0645\\u0631\\u0629 \\u0623\\u062E\\u0631\\u0649.\"})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setRestoreDialog(false),children:\"\\u0625\\u0644\\u063A\\u0627\\u0621\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>handleRestore(selectedUser===null||selectedUser===void 0?void 0:selectedUser.id),color:\"success\",variant:\"contained\",disabled:actionLoading,children:actionLoading?'جاري الاسترداد...':'استرداد'})]})]}),/*#__PURE__*/_jsxs(Dialog,{open:deleteDialog,onClose:()=>setDeleteDialog(false),children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"\\u062D\\u0630\\u0641 \\u0646\\u0647\\u0627\\u0626\\u064A\"}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:\"\\u062A\\u062D\\u0630\\u064A\\u0631: \\u0647\\u0630\\u0627 \\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621 \\u0644\\u0627 \\u064A\\u0645\\u0643\\u0646 \\u0627\\u0644\\u062A\\u0631\\u0627\\u062C\\u0639 \\u0639\\u0646\\u0647!\"}),/*#__PURE__*/_jsxs(Typography,{children:[\"\\u0647\\u0644 \\u0623\\u0646\\u062A \\u0645\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u0627\\u0644\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0646\\u0647\\u0627\\u0626\\u064A \\u0644\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645 \\\"\",selectedUser===null||selectedUser===void 0?void 0:selectedUser.full_name,\"\\\"\\u061F\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",sx:{mt:1,display:'block'},children:\"\\u0633\\u064A\\u062A\\u0645 \\u062D\\u0630\\u0641 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0631\\u062A\\u0628\\u0637\\u0629 \\u0628\\u0647\\u0630\\u0627 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0646\\u0647\\u0627\\u0626\\u064A\\u0627\\u064B.\"})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDeleteDialog(false),children:\"\\u0625\\u0644\\u063A\\u0627\\u0621\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>handlePermanentDelete(selectedUser===null||selectedUser===void 0?void 0:selectedUser.id),color:\"error\",variant:\"contained\",disabled:actionLoading,children:actionLoading?'جاري الحذف...':'حذف نهائي'})]})]})]})});};export default DeletedUsers;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TablePagination", "IconButton", "<PERSON><PERSON>", "TextField", "MenuItem", "Select", "FormControl", "InputLabel", "Chip", "Avatar", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "Container", "Rest<PERSON>", "RestoreIcon", "DeleteForever", "DeleteForeverIcon", "Visibility", "ViewIcon", "Search", "SearchIcon", "Refresh", "RefreshIcon", "useTranslation", "axios", "format", "ar", "Layout", "jsx", "_jsx", "jsxs", "_jsxs", "DeletedUsers", "t", "i18n", "users", "setUsers", "loading", "setLoading", "page", "setPage", "rowsPerPage", "setRowsPerPage", "total", "setTotal", "search", "setSearch", "<PERSON><PERSON><PERSON>er", "setRoleFilter", "selected<PERSON>ser", "setSelectedUser", "viewDialog", "setViewDialog", "restoreDialog", "setRestoreDialog", "deleteDialog", "setDeleteDialog", "stats", "setStats", "actionLoading", "setActionLoading", "fetchUsers", "params", "limit", "role", "response", "get", "data", "error", "console", "fetchStats", "handleRestore", "userId", "post", "handlePermanentDelete", "delete", "getRoleLabel", "getRoleColor", "children", "max<PERSON><PERSON><PERSON>", "sx", "py", "variant", "gutterBottom", "container", "spacing", "mb", "item", "xs", "sm", "md", "color", "total_deleted", "deleted_students", "deleted_teachers", "total_active", "p", "display", "gap", "alignItems", "flexWrap", "label", "size", "value", "onChange", "e", "target", "InputProps", "startAdornment", "mr", "min<PERSON><PERSON><PERSON>", "startIcon", "onClick", "component", "map", "user", "_user$full_name", "src", "profile_picture_url", "full_name", "char<PERSON>t", "email", "Date", "deleted_at", "locale", "language", "undefined", "deletion_reason", "deleted_by_name", "title", "id", "count", "onPageChange", "newPage", "onRowsPerPageChange", "parseInt", "labelRowsPerPage", "labelDisplayedRows", "_ref", "from", "to", "open", "onClose", "mt", "disabled", "severity"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/admin/DeletedUsers.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TablePagination,\n  IconButton,\n  Button,\n  TextField,\n  MenuItem,\n  Select,\n  FormControl,\n  InputLabel,\n  Chip,\n  Avatar,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Alert,\n  Tooltip,\n  Card,\n  CardContent,\n  Grid,\n  Container\n} from '@mui/material';\nimport {\n  Restore as RestoreIcon,\n  DeleteForever as DeleteForeverIcon,\n  Visibility as ViewIcon,\n  Search as SearchIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useTranslation } from 'react-i18next';\nimport axios from '../../utils/axios';\nimport { format } from 'date-fns';\nimport { ar } from 'date-fns/locale';\nimport Layout from '../../components/Layout';\n\nconst DeletedUsers = () => {\n  const { t, i18n } = useTranslation();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [total, setTotal] = useState(0);\n  const [search, setSearch] = useState('');\n  const [roleFilter, setRoleFilter] = useState('');\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [viewDialog, setViewDialog] = useState(false);\n  const [restoreDialog, setRestoreDialog] = useState(false);\n  const [deleteDialog, setDeleteDialog] = useState(false);\n  const [stats, setStats] = useState({});\n  const [actionLoading, setActionLoading] = useState(false);\n\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: page + 1,\n        limit: rowsPerPage,\n        search,\n        role: roleFilter\n      };\n\n      const response = await axios.get('/admin/deleted-users', { params });\n      setUsers(response.data.users);\n      setTotal(response.data.total);\n    } catch (error) {\n      console.error('Error fetching deleted users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchStats = async () => {\n    try {\n      const response = await axios.get('/admin/deleted-users/stats/overview');\n      setStats(response.data.stats);\n    } catch (error) {\n      console.error('Error fetching stats:', error);\n    }\n  };\n\n  useEffect(() => {\n    fetchUsers();\n  }, [page, rowsPerPage, search, roleFilter]);\n\n  useEffect(() => {\n    fetchStats();\n  }, []);\n\n  const handleRestore = async (userId) => {\n    try {\n      setActionLoading(true);\n      await axios.post(`/admin/deleted-users/${userId}/restore`);\n      setRestoreDialog(false);\n      setSelectedUser(null);\n      fetchUsers();\n      fetchStats();\n    } catch (error) {\n      console.error('Error restoring user:', error);\n    } finally {\n      setActionLoading(false);\n    }\n  };\n\n  const handlePermanentDelete = async (userId) => {\n    try {\n      setActionLoading(true);\n      await axios.delete(`/admin/deleted-users/${userId}/permanent`);\n      setDeleteDialog(false);\n      setSelectedUser(null);\n      fetchUsers();\n      fetchStats();\n    } catch (error) {\n      console.error('Error permanently deleting user:', error);\n    } finally {\n      setActionLoading(false);\n    }\n  };\n\n  const getRoleLabel = (role) => {\n    switch (role) {\n      case 'student': return 'طالب';\n      case 'platform_teacher': return 'معلم';\n      case 'new_teacher': return 'معلم جديد';\n      default: return role;\n    }\n  };\n\n  const getRoleColor = (role) => {\n    switch (role) {\n      case 'student': return 'primary';\n      case 'platform_teacher': return 'success';\n      case 'new_teacher': return 'warning';\n      default: return 'default';\n    }\n  };\n\n  return (\n    <Layout>\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        <Typography variant=\"h4\" gutterBottom>\n          المستخدمون المحذوفون\n        </Typography>\n\n      {/* Statistics Cards */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                إجمالي المحذوفين\n              </Typography>\n              <Typography variant=\"h4\">\n                {stats.total_deleted || 0}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                الطلاب المحذوفين\n              </Typography>\n              <Typography variant=\"h4\" color=\"primary\">\n                {stats.deleted_students || 0}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                المعلمين المحذوفين\n              </Typography>\n              <Typography variant=\"h4\" color=\"success.main\">\n                {stats.deleted_teachers || 0}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                المستخدمين النشطين\n              </Typography>\n              <Typography variant=\"h4\" color=\"info.main\">\n                {stats.total_active || 0}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Filters */}\n      <Paper sx={{ p: 2, mb: 2 }}>\n        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>\n          <TextField\n            label=\"البحث\"\n            variant=\"outlined\"\n            size=\"small\"\n            value={search}\n            onChange={(e) => setSearch(e.target.value)}\n            InputProps={{\n              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />\n            }}\n            sx={{ minWidth: 200 }}\n          />\n          \n          <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n            <InputLabel>النوع</InputLabel>\n            <Select\n              value={roleFilter}\n              label=\"النوع\"\n              onChange={(e) => setRoleFilter(e.target.value)}\n            >\n              <MenuItem value=\"\">الكل</MenuItem>\n              <MenuItem value=\"student\">طلاب</MenuItem>\n              <MenuItem value=\"platform_teacher\">معلمين</MenuItem>\n              <MenuItem value=\"new_teacher\">معلمين جدد</MenuItem>\n            </Select>\n          </FormControl>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<RefreshIcon />}\n            onClick={() => {\n              fetchUsers();\n              fetchStats();\n            }}\n          >\n            تحديث\n          </Button>\n        </Box>\n      </Paper>\n\n      {/* Users Table */}\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>المستخدم</TableCell>\n              <TableCell>النوع</TableCell>\n              <TableCell>تاريخ الحذف</TableCell>\n              <TableCell>سبب الحذف</TableCell>\n              <TableCell>حُذف بواسطة</TableCell>\n              <TableCell>الإجراءات</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {users.map((user) => (\n              <TableRow key={user.id}>\n                <TableCell>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Avatar src={user.profile_picture_url}>\n                      {user.full_name?.charAt(0)}\n                    </Avatar>\n                    <Box>\n                      <Typography variant=\"subtitle2\">\n                        {user.full_name}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {user.email}\n                      </Typography>\n                    </Box>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={getRoleLabel(user.role)}\n                    color={getRoleColor(user.role)}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>\n                  {format(new Date(user.deleted_at), 'dd/MM/yyyy HH:mm', {\n                    locale: i18n.language === 'ar' ? ar : undefined\n                  })}\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"caption\">\n                    {user.deletion_reason || 'غير محدد'}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  {user.deleted_by_name || 'حذف ذاتي'}\n                </TableCell>\n                <TableCell>\n                  <Tooltip title=\"عرض التفاصيل\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => {\n                        setSelectedUser(user);\n                        setViewDialog(true);\n                      }}\n                    >\n                      <ViewIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"استرداد المستخدم\">\n                    <IconButton\n                      size=\"small\"\n                      color=\"success\"\n                      onClick={() => {\n                        setSelectedUser(user);\n                        setRestoreDialog(true);\n                      }}\n                    >\n                      <RestoreIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"حذف نهائي\">\n                    <IconButton\n                      size=\"small\"\n                      color=\"error\"\n                      onClick={() => {\n                        setSelectedUser(user);\n                        setDeleteDialog(true);\n                      }}\n                    >\n                      <DeleteForeverIcon />\n                    </IconButton>\n                  </Tooltip>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n        <TablePagination\n          component=\"div\"\n          count={total}\n          page={page}\n          onPageChange={(e, newPage) => setPage(newPage)}\n          rowsPerPage={rowsPerPage}\n          onRowsPerPageChange={(e) => {\n            setRowsPerPage(parseInt(e.target.value, 10));\n            setPage(0);\n          }}\n          labelRowsPerPage=\"عدد الصفوف:\"\n          labelDisplayedRows={({ from, to, count }) => \n            `${from}-${to} من ${count !== -1 ? count : `أكثر من ${to}`}`\n          }\n        />\n      </TableContainer>\n\n      {/* Restore Dialog */}\n      <Dialog open={restoreDialog} onClose={() => setRestoreDialog(false)}>\n        <DialogTitle>استرداد المستخدم</DialogTitle>\n        <DialogContent>\n          <Typography>\n            هل أنت متأكد من استرداد المستخدم \"{selectedUser?.full_name}\"؟\n          </Typography>\n          <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1, display: 'block' }}>\n            سيتمكن المستخدم من تسجيل الدخول والوصول للنظام مرة أخرى.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setRestoreDialog(false)}>إلغاء</Button>\n          <Button\n            onClick={() => handleRestore(selectedUser?.id)}\n            color=\"success\"\n            variant=\"contained\"\n            disabled={actionLoading}\n          >\n            {actionLoading ? 'جاري الاسترداد...' : 'استرداد'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Permanent Delete Dialog */}\n      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>\n        <DialogTitle>حذف نهائي</DialogTitle>\n        <DialogContent>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            تحذير: هذا الإجراء لا يمكن التراجع عنه!\n          </Alert>\n          <Typography>\n            هل أنت متأكد من الحذف النهائي للمستخدم \"{selectedUser?.full_name}\"؟\n          </Typography>\n          <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1, display: 'block' }}>\n            سيتم حذف جميع البيانات المرتبطة بهذا المستخدم نهائياً.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialog(false)}>إلغاء</Button>\n          <Button\n            onClick={() => handlePermanentDelete(selectedUser?.id)}\n            color=\"error\"\n            variant=\"contained\"\n            disabled={actionLoading}\n          >\n            {actionLoading ? 'جاري الحذف...' : 'حذف نهائي'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n      </Container>\n    </Layout>\n  );\n};\n\nexport default DeletedUsers;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,KAAK,CACLC,UAAU,CACVC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,eAAe,CACfC,UAAU,CACVC,MAAM,CACNC,SAAS,CACTC,QAAQ,CACRC,MAAM,CACNC,WAAW,CACXC,UAAU,CACVC,IAAI,CACJC,MAAM,CACNC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,KAAK,CACLC,OAAO,CACPC,IAAI,CACJC,WAAW,CACXC,IAAI,CACJC,SAAS,KACJ,eAAe,CACtB,OACEC,OAAO,GAAI,CAAAC,WAAW,CACtBC,aAAa,GAAI,CAAAC,iBAAiB,CAClCC,UAAU,GAAI,CAAAC,QAAQ,CACtBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,OAAO,GAAI,CAAAC,WAAW,KACjB,qBAAqB,CAC5B,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,CAAAC,KAAK,KAAM,mBAAmB,CACrC,OAASC,MAAM,KAAQ,UAAU,CACjC,OAASC,EAAE,KAAQ,iBAAiB,CACpC,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7C,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGX,cAAc,CAAC,CAAC,CACpC,KAAM,CAACY,KAAK,CAAEC,QAAQ,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACuD,OAAO,CAAEC,UAAU,CAAC,CAAGxD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACyD,IAAI,CAAEC,OAAO,CAAC,CAAG1D,QAAQ,CAAC,CAAC,CAAC,CACnC,KAAM,CAAC2D,WAAW,CAAEC,cAAc,CAAC,CAAG5D,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC6D,KAAK,CAAEC,QAAQ,CAAC,CAAG9D,QAAQ,CAAC,CAAC,CAAC,CACrC,KAAM,CAAC+D,MAAM,CAAEC,SAAS,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACiE,UAAU,CAAEC,aAAa,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACmE,YAAY,CAAEC,eAAe,CAAC,CAAGpE,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACqE,UAAU,CAAEC,aAAa,CAAC,CAAGtE,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACuE,aAAa,CAAEC,gBAAgB,CAAC,CAAGxE,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACyE,YAAY,CAAEC,eAAe,CAAC,CAAG1E,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC2E,KAAK,CAAEC,QAAQ,CAAC,CAAG5E,QAAQ,CAAC,CAAC,CAAC,CAAC,CACtC,KAAM,CAAC6E,aAAa,CAAEC,gBAAgB,CAAC,CAAG9E,QAAQ,CAAC,KAAK,CAAC,CAEzD,KAAM,CAAA+E,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACFvB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAwB,MAAM,CAAG,CACbvB,IAAI,CAAEA,IAAI,CAAG,CAAC,CACdwB,KAAK,CAAEtB,WAAW,CAClBI,MAAM,CACNmB,IAAI,CAAEjB,UACR,CAAC,CAED,KAAM,CAAAkB,QAAQ,CAAG,KAAM,CAAAzC,KAAK,CAAC0C,GAAG,CAAC,sBAAsB,CAAE,CAAEJ,MAAO,CAAC,CAAC,CACpE1B,QAAQ,CAAC6B,QAAQ,CAACE,IAAI,CAAChC,KAAK,CAAC,CAC7BS,QAAQ,CAACqB,QAAQ,CAACE,IAAI,CAACxB,KAAK,CAAC,CAC/B,CAAE,MAAOyB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CAAC,OAAS,CACR9B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAgC,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAzC,KAAK,CAAC0C,GAAG,CAAC,qCAAqC,CAAC,CACvER,QAAQ,CAACO,QAAQ,CAACE,IAAI,CAACV,KAAK,CAAC,CAC/B,CAAE,MAAOW,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CACF,CAAC,CAEDrF,SAAS,CAAC,IAAM,CACd8E,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,CAACtB,IAAI,CAAEE,WAAW,CAAEI,MAAM,CAAEE,UAAU,CAAC,CAAC,CAE3ChE,SAAS,CAAC,IAAM,CACduF,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,aAAa,CAAG,KAAO,CAAAC,MAAM,EAAK,CACtC,GAAI,CACFZ,gBAAgB,CAAC,IAAI,CAAC,CACtB,KAAM,CAAApC,KAAK,CAACiD,IAAI,CAAC,wBAAwBD,MAAM,UAAU,CAAC,CAC1DlB,gBAAgB,CAAC,KAAK,CAAC,CACvBJ,eAAe,CAAC,IAAI,CAAC,CACrBW,UAAU,CAAC,CAAC,CACZS,UAAU,CAAC,CAAC,CACd,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CAAC,OAAS,CACRR,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAc,qBAAqB,CAAG,KAAO,CAAAF,MAAM,EAAK,CAC9C,GAAI,CACFZ,gBAAgB,CAAC,IAAI,CAAC,CACtB,KAAM,CAAApC,KAAK,CAACmD,MAAM,CAAC,wBAAwBH,MAAM,YAAY,CAAC,CAC9DhB,eAAe,CAAC,KAAK,CAAC,CACtBN,eAAe,CAAC,IAAI,CAAC,CACrBW,UAAU,CAAC,CAAC,CACZS,UAAU,CAAC,CAAC,CACd,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CAC1D,CAAC,OAAS,CACRR,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAgB,YAAY,CAAIZ,IAAI,EAAK,CAC7B,OAAQA,IAAI,EACV,IAAK,SAAS,CAAE,MAAO,MAAM,CAC7B,IAAK,kBAAkB,CAAE,MAAO,MAAM,CACtC,IAAK,aAAa,CAAE,MAAO,WAAW,CACtC,QAAS,MAAO,CAAAA,IAAI,CACtB,CACF,CAAC,CAED,KAAM,CAAAa,YAAY,CAAIb,IAAI,EAAK,CAC7B,OAAQA,IAAI,EACV,IAAK,SAAS,CAAE,MAAO,SAAS,CAChC,IAAK,kBAAkB,CAAE,MAAO,SAAS,CACzC,IAAK,aAAa,CAAE,MAAO,SAAS,CACpC,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,mBACEnC,IAAA,CAACF,MAAM,EAAAmD,QAAA,cACL/C,KAAA,CAACnB,SAAS,EAACmE,QAAQ,CAAC,IAAI,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACrCjD,IAAA,CAAC3C,UAAU,EAACgG,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAL,QAAA,CAAC,qHAEtC,CAAY,CAAC,cAGf/C,KAAA,CAACpB,IAAI,EAACyE,SAAS,MAACC,OAAO,CAAE,CAAE,CAACL,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,eACxCjD,IAAA,CAAClB,IAAI,EAAC4E,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cAC9BjD,IAAA,CAACpB,IAAI,EAAAqE,QAAA,cACH/C,KAAA,CAACrB,WAAW,EAAAoE,QAAA,eACVjD,IAAA,CAAC3C,UAAU,EAACyG,KAAK,CAAC,eAAe,CAACR,YAAY,MAAAL,QAAA,CAAC,6FAE/C,CAAY,CAAC,cACbjD,IAAA,CAAC3C,UAAU,EAACgG,OAAO,CAAC,IAAI,CAAAJ,QAAA,CACrBrB,KAAK,CAACmC,aAAa,EAAI,CAAC,CACf,CAAC,EACF,CAAC,CACV,CAAC,CACH,CAAC,cACP/D,IAAA,CAAClB,IAAI,EAAC4E,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cAC9BjD,IAAA,CAACpB,IAAI,EAAAqE,QAAA,cACH/C,KAAA,CAACrB,WAAW,EAAAoE,QAAA,eACVjD,IAAA,CAAC3C,UAAU,EAACyG,KAAK,CAAC,eAAe,CAACR,YAAY,MAAAL,QAAA,CAAC,6FAE/C,CAAY,CAAC,cACbjD,IAAA,CAAC3C,UAAU,EAACgG,OAAO,CAAC,IAAI,CAACS,KAAK,CAAC,SAAS,CAAAb,QAAA,CACrCrB,KAAK,CAACoC,gBAAgB,EAAI,CAAC,CAClB,CAAC,EACF,CAAC,CACV,CAAC,CACH,CAAC,cACPhE,IAAA,CAAClB,IAAI,EAAC4E,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cAC9BjD,IAAA,CAACpB,IAAI,EAAAqE,QAAA,cACH/C,KAAA,CAACrB,WAAW,EAAAoE,QAAA,eACVjD,IAAA,CAAC3C,UAAU,EAACyG,KAAK,CAAC,eAAe,CAACR,YAAY,MAAAL,QAAA,CAAC,yGAE/C,CAAY,CAAC,cACbjD,IAAA,CAAC3C,UAAU,EAACgG,OAAO,CAAC,IAAI,CAACS,KAAK,CAAC,cAAc,CAAAb,QAAA,CAC1CrB,KAAK,CAACqC,gBAAgB,EAAI,CAAC,CAClB,CAAC,EACF,CAAC,CACV,CAAC,CACH,CAAC,cACPjE,IAAA,CAAClB,IAAI,EAAC4E,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cAC9BjD,IAAA,CAACpB,IAAI,EAAAqE,QAAA,cACH/C,KAAA,CAACrB,WAAW,EAAAoE,QAAA,eACVjD,IAAA,CAAC3C,UAAU,EAACyG,KAAK,CAAC,eAAe,CAACR,YAAY,MAAAL,QAAA,CAAC,yGAE/C,CAAY,CAAC,cACbjD,IAAA,CAAC3C,UAAU,EAACgG,OAAO,CAAC,IAAI,CAACS,KAAK,CAAC,WAAW,CAAAb,QAAA,CACvCrB,KAAK,CAACsC,YAAY,EAAI,CAAC,CACd,CAAC,EACF,CAAC,CACV,CAAC,CACH,CAAC,EACH,CAAC,cAGPlE,IAAA,CAAC5C,KAAK,EAAC+F,EAAE,CAAE,CAAEgB,CAAC,CAAE,CAAC,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,cACzB/C,KAAA,CAAC/C,GAAG,EAACgG,EAAE,CAAE,CAAEiB,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAC,CAAEC,UAAU,CAAE,QAAQ,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAtB,QAAA,eAC3EjD,IAAA,CAACjC,SAAS,EACRyG,KAAK,CAAC,gCAAO,CACbnB,OAAO,CAAC,UAAU,CAClBoB,IAAI,CAAC,OAAO,CACZC,KAAK,CAAE1D,MAAO,CACd2D,QAAQ,CAAGC,CAAC,EAAK3D,SAAS,CAAC2D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC3CI,UAAU,CAAE,CACVC,cAAc,cAAE/E,IAAA,CAACT,UAAU,EAAC4D,EAAE,CAAE,CAAE6B,EAAE,CAAE,CAAC,CAAElB,KAAK,CAAE,gBAAiB,CAAE,CAAE,CACvE,CAAE,CACFX,EAAE,CAAE,CAAE8B,QAAQ,CAAE,GAAI,CAAE,CACvB,CAAC,cAEF/E,KAAA,CAAChC,WAAW,EAACuG,IAAI,CAAC,OAAO,CAACtB,EAAE,CAAE,CAAE8B,QAAQ,CAAE,GAAI,CAAE,CAAAhC,QAAA,eAC9CjD,IAAA,CAAC7B,UAAU,EAAA8E,QAAA,CAAC,gCAAK,CAAY,CAAC,cAC9B/C,KAAA,CAACjC,MAAM,EACLyG,KAAK,CAAExD,UAAW,CAClBsD,KAAK,CAAC,gCAAO,CACbG,QAAQ,CAAGC,CAAC,EAAKzD,aAAa,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAzB,QAAA,eAE/CjD,IAAA,CAAChC,QAAQ,EAAC0G,KAAK,CAAC,EAAE,CAAAzB,QAAA,CAAC,0BAAI,CAAU,CAAC,cAClCjD,IAAA,CAAChC,QAAQ,EAAC0G,KAAK,CAAC,SAAS,CAAAzB,QAAA,CAAC,0BAAI,CAAU,CAAC,cACzCjD,IAAA,CAAChC,QAAQ,EAAC0G,KAAK,CAAC,kBAAkB,CAAAzB,QAAA,CAAC,sCAAM,CAAU,CAAC,cACpDjD,IAAA,CAAChC,QAAQ,EAAC0G,KAAK,CAAC,aAAa,CAAAzB,QAAA,CAAC,yDAAU,CAAU,CAAC,EAC7C,CAAC,EACE,CAAC,cAEdjD,IAAA,CAAClC,MAAM,EACLuF,OAAO,CAAC,UAAU,CAClB6B,SAAS,cAAElF,IAAA,CAACP,WAAW,GAAE,CAAE,CAC3B0F,OAAO,CAAEA,CAAA,GAAM,CACbnD,UAAU,CAAC,CAAC,CACZS,UAAU,CAAC,CAAC,CACd,CAAE,CAAAQ,QAAA,CACH,gCAED,CAAQ,CAAC,EACN,CAAC,CACD,CAAC,cAGR/C,KAAA,CAACzC,cAAc,EAAC2H,SAAS,CAAEhI,KAAM,CAAA6F,QAAA,eAC/B/C,KAAA,CAAC5C,KAAK,EAAA2F,QAAA,eACJjD,IAAA,CAACtC,SAAS,EAAAuF,QAAA,cACR/C,KAAA,CAACvC,QAAQ,EAAAsF,QAAA,eACPjD,IAAA,CAACxC,SAAS,EAAAyF,QAAA,CAAC,kDAAQ,CAAW,CAAC,cAC/BjD,IAAA,CAACxC,SAAS,EAAAyF,QAAA,CAAC,gCAAK,CAAW,CAAC,cAC5BjD,IAAA,CAACxC,SAAS,EAAAyF,QAAA,CAAC,+DAAW,CAAW,CAAC,cAClCjD,IAAA,CAACxC,SAAS,EAAAyF,QAAA,CAAC,mDAAS,CAAW,CAAC,cAChCjD,IAAA,CAACxC,SAAS,EAAAyF,QAAA,CAAC,+DAAW,CAAW,CAAC,cAClCjD,IAAA,CAACxC,SAAS,EAAAyF,QAAA,CAAC,wDAAS,CAAW,CAAC,EACxB,CAAC,CACF,CAAC,cACZjD,IAAA,CAACzC,SAAS,EAAA0F,QAAA,CACP3C,KAAK,CAAC+E,GAAG,CAAEC,IAAI,OAAAC,eAAA,oBACdrF,KAAA,CAACvC,QAAQ,EAAAsF,QAAA,eACPjD,IAAA,CAACxC,SAAS,EAAAyF,QAAA,cACR/C,KAAA,CAAC/C,GAAG,EAACgG,EAAE,CAAE,CAAEiB,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAED,GAAG,CAAE,CAAE,CAAE,CAAApB,QAAA,eACzDjD,IAAA,CAAC3B,MAAM,EAACmH,GAAG,CAAEF,IAAI,CAACG,mBAAoB,CAAAxC,QAAA,EAAAsC,eAAA,CACnCD,IAAI,CAACI,SAAS,UAAAH,eAAA,iBAAdA,eAAA,CAAgBI,MAAM,CAAC,CAAC,CAAC,CACpB,CAAC,cACTzF,KAAA,CAAC/C,GAAG,EAAA8F,QAAA,eACFjD,IAAA,CAAC3C,UAAU,EAACgG,OAAO,CAAC,WAAW,CAAAJ,QAAA,CAC5BqC,IAAI,CAACI,SAAS,CACL,CAAC,cACb1F,IAAA,CAAC3C,UAAU,EAACgG,OAAO,CAAC,SAAS,CAACS,KAAK,CAAC,gBAAgB,CAAAb,QAAA,CACjDqC,IAAI,CAACM,KAAK,CACD,CAAC,EACV,CAAC,EACH,CAAC,CACG,CAAC,cACZ5F,IAAA,CAACxC,SAAS,EAAAyF,QAAA,cACRjD,IAAA,CAAC5B,IAAI,EACHoG,KAAK,CAAEzB,YAAY,CAACuC,IAAI,CAACnD,IAAI,CAAE,CAC/B2B,KAAK,CAAEd,YAAY,CAACsC,IAAI,CAACnD,IAAI,CAAE,CAC/BsC,IAAI,CAAC,OAAO,CACb,CAAC,CACO,CAAC,cACZzE,IAAA,CAACxC,SAAS,EAAAyF,QAAA,CACPrD,MAAM,CAAC,GAAI,CAAAiG,IAAI,CAACP,IAAI,CAACQ,UAAU,CAAC,CAAE,kBAAkB,CAAE,CACrDC,MAAM,CAAE1F,IAAI,CAAC2F,QAAQ,GAAK,IAAI,CAAGnG,EAAE,CAAGoG,SACxC,CAAC,CAAC,CACO,CAAC,cACZjG,IAAA,CAACxC,SAAS,EAAAyF,QAAA,cACRjD,IAAA,CAAC3C,UAAU,EAACgG,OAAO,CAAC,SAAS,CAAAJ,QAAA,CAC1BqC,IAAI,CAACY,eAAe,EAAI,UAAU,CACzB,CAAC,CACJ,CAAC,cACZlG,IAAA,CAACxC,SAAS,EAAAyF,QAAA,CACPqC,IAAI,CAACa,eAAe,EAAI,UAAU,CAC1B,CAAC,cACZjG,KAAA,CAAC1C,SAAS,EAAAyF,QAAA,eACRjD,IAAA,CAACrB,OAAO,EAACyH,KAAK,CAAC,qEAAc,CAAAnD,QAAA,cAC3BjD,IAAA,CAACnC,UAAU,EACT4G,IAAI,CAAC,OAAO,CACZU,OAAO,CAAEA,CAAA,GAAM,CACb9D,eAAe,CAACiE,IAAI,CAAC,CACrB/D,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CAAA0B,QAAA,cAEFjD,IAAA,CAACX,QAAQ,GAAE,CAAC,CACF,CAAC,CACN,CAAC,cACVW,IAAA,CAACrB,OAAO,EAACyH,KAAK,CAAC,6FAAkB,CAAAnD,QAAA,cAC/BjD,IAAA,CAACnC,UAAU,EACT4G,IAAI,CAAC,OAAO,CACZX,KAAK,CAAC,SAAS,CACfqB,OAAO,CAAEA,CAAA,GAAM,CACb9D,eAAe,CAACiE,IAAI,CAAC,CACrB7D,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CAAAwB,QAAA,cAEFjD,IAAA,CAACf,WAAW,GAAE,CAAC,CACL,CAAC,CACN,CAAC,cACVe,IAAA,CAACrB,OAAO,EAACyH,KAAK,CAAC,mDAAW,CAAAnD,QAAA,cACxBjD,IAAA,CAACnC,UAAU,EACT4G,IAAI,CAAC,OAAO,CACZX,KAAK,CAAC,OAAO,CACbqB,OAAO,CAAEA,CAAA,GAAM,CACb9D,eAAe,CAACiE,IAAI,CAAC,CACrB3D,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,CAAAsB,QAAA,cAEFjD,IAAA,CAACb,iBAAiB,GAAE,CAAC,CACX,CAAC,CACN,CAAC,EACD,CAAC,GAxECmG,IAAI,CAACe,EAyEV,CAAC,EACZ,CAAC,CACO,CAAC,EACP,CAAC,cACRrG,IAAA,CAACpC,eAAe,EACdwH,SAAS,CAAC,KAAK,CACfkB,KAAK,CAAExF,KAAM,CACbJ,IAAI,CAAEA,IAAK,CACX6F,YAAY,CAAEA,CAAC3B,CAAC,CAAE4B,OAAO,GAAK7F,OAAO,CAAC6F,OAAO,CAAE,CAC/C5F,WAAW,CAAEA,WAAY,CACzB6F,mBAAmB,CAAG7B,CAAC,EAAK,CAC1B/D,cAAc,CAAC6F,QAAQ,CAAC9B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,EAAE,CAAC,CAAC,CAC5C/D,OAAO,CAAC,CAAC,CAAC,CACZ,CAAE,CACFgG,gBAAgB,CAAC,0DAAa,CAC9BC,kBAAkB,CAAEC,IAAA,MAAC,CAAEC,IAAI,CAAEC,EAAE,CAAET,KAAM,CAAC,CAAAO,IAAA,OACtC,GAAGC,IAAI,IAAIC,EAAE,OAAOT,KAAK,GAAK,CAAC,CAAC,CAAGA,KAAK,CAAG,WAAWS,EAAE,EAAE,EAAE,EAC7D,CACF,CAAC,EACY,CAAC,cAGjB7G,KAAA,CAAC5B,MAAM,EAAC0I,IAAI,CAAExF,aAAc,CAACyF,OAAO,CAAEA,CAAA,GAAMxF,gBAAgB,CAAC,KAAK,CAAE,CAAAwB,QAAA,eAClEjD,IAAA,CAACzB,WAAW,EAAA0E,QAAA,CAAC,6FAAgB,CAAa,CAAC,cAC3C/C,KAAA,CAAC1B,aAAa,EAAAyE,QAAA,eACZ/C,KAAA,CAAC7C,UAAU,EAAA4F,QAAA,EAAC,4KACwB,CAAC7B,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEsE,SAAS,CAAC,UAC7D,EAAY,CAAC,cACb1F,IAAA,CAAC3C,UAAU,EAACgG,OAAO,CAAC,SAAS,CAACS,KAAK,CAAC,gBAAgB,CAACX,EAAE,CAAE,CAAE+D,EAAE,CAAE,CAAC,CAAE9C,OAAO,CAAE,OAAQ,CAAE,CAAAnB,QAAA,CAAC,qSAEtF,CAAY,CAAC,EACA,CAAC,cAChB/C,KAAA,CAACzB,aAAa,EAAAwE,QAAA,eACZjD,IAAA,CAAClC,MAAM,EAACqH,OAAO,CAAEA,CAAA,GAAM1D,gBAAgB,CAAC,KAAK,CAAE,CAAAwB,QAAA,CAAC,gCAAK,CAAQ,CAAC,cAC9DjD,IAAA,CAAClC,MAAM,EACLqH,OAAO,CAAEA,CAAA,GAAMzC,aAAa,CAACtB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEiF,EAAE,CAAE,CAC/CvC,KAAK,CAAC,SAAS,CACfT,OAAO,CAAC,WAAW,CACnB8D,QAAQ,CAAErF,aAAc,CAAAmB,QAAA,CAEvBnB,aAAa,CAAG,mBAAmB,CAAG,SAAS,CAC1C,CAAC,EACI,CAAC,EACV,CAAC,cAGT5B,KAAA,CAAC5B,MAAM,EAAC0I,IAAI,CAAEtF,YAAa,CAACuF,OAAO,CAAEA,CAAA,GAAMtF,eAAe,CAAC,KAAK,CAAE,CAAAsB,QAAA,eAChEjD,IAAA,CAACzB,WAAW,EAAA0E,QAAA,CAAC,mDAAS,CAAa,CAAC,cACpC/C,KAAA,CAAC1B,aAAa,EAAAyE,QAAA,eACZjD,IAAA,CAACtB,KAAK,EAAC0I,QAAQ,CAAC,OAAO,CAACjE,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,CAAC,oMAEvC,CAAO,CAAC,cACR/C,KAAA,CAAC7C,UAAU,EAAA4F,QAAA,EAAC,2MAC8B,CAAC7B,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEsE,SAAS,CAAC,UACnE,EAAY,CAAC,cACb1F,IAAA,CAAC3C,UAAU,EAACgG,OAAO,CAAC,SAAS,CAACS,KAAK,CAAC,gBAAgB,CAACX,EAAE,CAAE,CAAE+D,EAAE,CAAE,CAAC,CAAE9C,OAAO,CAAE,OAAQ,CAAE,CAAAnB,QAAA,CAAC,8RAEtF,CAAY,CAAC,EACA,CAAC,cAChB/C,KAAA,CAACzB,aAAa,EAAAwE,QAAA,eACZjD,IAAA,CAAClC,MAAM,EAACqH,OAAO,CAAEA,CAAA,GAAMxD,eAAe,CAAC,KAAK,CAAE,CAAAsB,QAAA,CAAC,gCAAK,CAAQ,CAAC,cAC7DjD,IAAA,CAAClC,MAAM,EACLqH,OAAO,CAAEA,CAAA,GAAMtC,qBAAqB,CAACzB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEiF,EAAE,CAAE,CACvDvC,KAAK,CAAC,OAAO,CACbT,OAAO,CAAC,WAAW,CACnB8D,QAAQ,CAAErF,aAAc,CAAAmB,QAAA,CAEvBnB,aAAa,CAAG,eAAe,CAAG,WAAW,CACxC,CAAC,EACI,CAAC,EACV,CAAC,EACE,CAAC,CACN,CAAC,CAEb,CAAC,CAED,cAAe,CAAA3B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}