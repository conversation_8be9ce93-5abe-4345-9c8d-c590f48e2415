{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Paper,Typography,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TablePagination,IconButton,Button,TextField,MenuItem,Select,FormControl,InputLabel,Chip,Avatar,Dialog,DialogTitle,DialogContent,DialogActions,Alert,Tooltip,Card,CardContent,Grid}from'@mui/material';import{Restore as RestoreIcon,DeleteForever as DeleteForeverIcon,Visibility as ViewIcon,Search as SearchIcon,Refresh as RefreshIcon}from'@mui/icons-material';import{useTranslation}from'react-i18next';import axios from'../../utils/axios';import{format}from'date-fns';import{ar}from'date-fns/locale';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DeletedUsers=()=>{const{t,i18n}=useTranslation();const[users,setUsers]=useState([]);const[loading,setLoading]=useState(true);const[page,setPage]=useState(0);const[rowsPerPage,setRowsPerPage]=useState(10);const[total,setTotal]=useState(0);const[search,setSearch]=useState('');const[roleFilter,setRoleFilter]=useState('');const[selectedUser,setSelectedUser]=useState(null);const[viewDialog,setViewDialog]=useState(false);const[restoreDialog,setRestoreDialog]=useState(false);const[deleteDialog,setDeleteDialog]=useState(false);const[stats,setStats]=useState({});const[actionLoading,setActionLoading]=useState(false);const fetchUsers=async()=>{try{setLoading(true);const params={page:page+1,limit:rowsPerPage,search,role:roleFilter};const response=await axios.get('/admin/deleted-users',{params});setUsers(response.data.users);setTotal(response.data.total);}catch(error){console.error('Error fetching deleted users:',error);}finally{setLoading(false);}};const fetchStats=async()=>{try{const response=await axios.get('/admin/deleted-users/stats/overview');setStats(response.data.stats);}catch(error){console.error('Error fetching stats:',error);}};useEffect(()=>{fetchUsers();},[page,rowsPerPage,search,roleFilter]);useEffect(()=>{fetchStats();},[]);const handleRestore=async userId=>{try{setActionLoading(true);await axios.post(`/admin/deleted-users/${userId}/restore`);setRestoreDialog(false);setSelectedUser(null);fetchUsers();fetchStats();}catch(error){console.error('Error restoring user:',error);}finally{setActionLoading(false);}};const handlePermanentDelete=async userId=>{try{setActionLoading(true);await axios.delete(`/admin/deleted-users/${userId}/permanent`);setDeleteDialog(false);setSelectedUser(null);fetchUsers();fetchStats();}catch(error){console.error('Error permanently deleting user:',error);}finally{setActionLoading(false);}};const getRoleLabel=role=>{switch(role){case'student':return'طالب';case'platform_teacher':return'معلم';case'new_teacher':return'معلم جديد';default:return role;}};const getRoleColor=role=>{switch(role){case'student':return'primary';case'platform_teacher':return'success';case'new_teacher':return'warning';default:return'default';}};return/*#__PURE__*/_jsxs(Box,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,children:\"\\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u0648\\u0646 \\u0627\\u0644\\u0645\\u062D\\u0630\\u0648\\u0641\\u0648\\u0646\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,sx:{mb:3},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{color:\"textSecondary\",gutterBottom:true,children:\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u062D\\u0630\\u0648\\u0641\\u064A\\u0646\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",children:stats.total_deleted||0})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{color:\"textSecondary\",gutterBottom:true,children:\"\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 \\u0627\\u0644\\u0645\\u062D\\u0630\\u0648\\u0641\\u064A\\u0646\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"primary\",children:stats.deleted_students||0})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{color:\"textSecondary\",gutterBottom:true,children:\"\\u0627\\u0644\\u0645\\u0639\\u0644\\u0645\\u064A\\u0646 \\u0627\\u0644\\u0645\\u062D\\u0630\\u0648\\u0641\\u064A\\u0646\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"success.main\",children:stats.deleted_teachers||0})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{color:\"textSecondary\",gutterBottom:true,children:\"\\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646 \\u0627\\u0644\\u0646\\u0634\\u0637\\u064A\\u0646\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"info.main\",children:stats.total_active||0})]})})})]}),/*#__PURE__*/_jsx(Paper,{sx:{p:2,mb:2},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2,alignItems:'center',flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(TextField,{label:\"\\u0627\\u0644\\u0628\\u062D\\u062B\",variant:\"outlined\",size:\"small\",value:search,onChange:e=>setSearch(e.target.value),InputProps:{startAdornment:/*#__PURE__*/_jsx(SearchIcon,{sx:{mr:1,color:'text.secondary'}})},sx:{minWidth:200}}),/*#__PURE__*/_jsxs(FormControl,{size:\"small\",sx:{minWidth:120},children:[/*#__PURE__*/_jsx(InputLabel,{children:\"\\u0627\\u0644\\u0646\\u0648\\u0639\"}),/*#__PURE__*/_jsxs(Select,{value:roleFilter,label:\"\\u0627\\u0644\\u0646\\u0648\\u0639\",onChange:e=>setRoleFilter(e.target.value),children:[/*#__PURE__*/_jsx(MenuItem,{value:\"\",children:\"\\u0627\\u0644\\u0643\\u0644\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"student\",children:\"\\u0637\\u0644\\u0627\\u0628\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"platform_teacher\",children:\"\\u0645\\u0639\\u0644\\u0645\\u064A\\u0646\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"new_teacher\",children:\"\\u0645\\u0639\\u0644\\u0645\\u064A\\u0646 \\u062C\\u062F\\u062F\"})]})]}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(RefreshIcon,{}),onClick:()=>{fetchUsers();fetchStats();},children:\"\\u062A\\u062D\\u062F\\u064A\\u062B\"})]})}),/*#__PURE__*/_jsxs(TableContainer,{component:Paper,children:[/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"\\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u0627\\u0644\\u0646\\u0648\\u0639\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u062D\\u0630\\u0641\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u0633\\u0628\\u0628 \\u0627\\u0644\\u062D\\u0630\\u0641\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u062D\\u064F\\u0630\\u0641 \\u0628\\u0648\\u0627\\u0633\\u0637\\u0629\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:users.map(user=>{var _user$full_name;return/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Avatar,{src:user.profile_picture_url,children:(_user$full_name=user.full_name)===null||_user$full_name===void 0?void 0:_user$full_name.charAt(0)}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",children:user.full_name}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:user.email})]})]})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:getRoleLabel(user.role),color:getRoleColor(user.role),size:\"small\"})}),/*#__PURE__*/_jsx(TableCell,{children:format(new Date(user.deleted_at),'dd/MM/yyyy HH:mm',{locale:i18n.language==='ar'?ar:undefined})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Typography,{variant:\"caption\",children:user.deletion_reason||'غير محدد'})}),/*#__PURE__*/_jsx(TableCell,{children:user.deleted_by_name||'حذف ذاتي'}),/*#__PURE__*/_jsxs(TableCell,{children:[/*#__PURE__*/_jsx(Tooltip,{title:\"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>{setSelectedUser(user);setViewDialog(true);},children:/*#__PURE__*/_jsx(ViewIcon,{})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u0627\\u0633\\u062A\\u0631\\u062F\\u0627\\u062F \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",color:\"success\",onClick:()=>{setSelectedUser(user);setRestoreDialog(true);},children:/*#__PURE__*/_jsx(RestoreIcon,{})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"\\u062D\\u0630\\u0641 \\u0646\\u0647\\u0627\\u0626\\u064A\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",color:\"error\",onClick:()=>{setSelectedUser(user);setDeleteDialog(true);},children:/*#__PURE__*/_jsx(DeleteForeverIcon,{})})})]})]},user.id);})})]}),/*#__PURE__*/_jsx(TablePagination,{component:\"div\",count:total,page:page,onPageChange:(e,newPage)=>setPage(newPage),rowsPerPage:rowsPerPage,onRowsPerPageChange:e=>{setRowsPerPage(parseInt(e.target.value,10));setPage(0);},labelRowsPerPage:\"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0635\\u0641\\u0648\\u0641:\",labelDisplayedRows:_ref=>{let{from,to,count}=_ref;return`${from}-${to} من ${count!==-1?count:`أكثر من ${to}`}`;}})]}),/*#__PURE__*/_jsxs(Dialog,{open:restoreDialog,onClose:()=>setRestoreDialog(false),children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"\\u0627\\u0633\\u062A\\u0631\\u062F\\u0627\\u062F \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsxs(Typography,{children:[\"\\u0647\\u0644 \\u0623\\u0646\\u062A \\u0645\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u0627\\u0633\\u062A\\u0631\\u062F\\u0627\\u062F \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645 \\\"\",selectedUser===null||selectedUser===void 0?void 0:selectedUser.full_name,\"\\\"\\u061F\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",sx:{mt:1,display:'block'},children:\"\\u0633\\u064A\\u062A\\u0645\\u0643\\u0646 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0645\\u0646 \\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0648\\u0627\\u0644\\u0648\\u0635\\u0648\\u0644 \\u0644\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0645\\u0631\\u0629 \\u0623\\u062E\\u0631\\u0649.\"})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setRestoreDialog(false),children:\"\\u0625\\u0644\\u063A\\u0627\\u0621\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>handleRestore(selectedUser===null||selectedUser===void 0?void 0:selectedUser.id),color:\"success\",variant:\"contained\",disabled:actionLoading,children:actionLoading?'جاري الاسترداد...':'استرداد'})]})]}),/*#__PURE__*/_jsxs(Dialog,{open:deleteDialog,onClose:()=>setDeleteDialog(false),children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"\\u062D\\u0630\\u0641 \\u0646\\u0647\\u0627\\u0626\\u064A\"}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:\"\\u062A\\u062D\\u0630\\u064A\\u0631: \\u0647\\u0630\\u0627 \\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621 \\u0644\\u0627 \\u064A\\u0645\\u0643\\u0646 \\u0627\\u0644\\u062A\\u0631\\u0627\\u062C\\u0639 \\u0639\\u0646\\u0647!\"}),/*#__PURE__*/_jsxs(Typography,{children:[\"\\u0647\\u0644 \\u0623\\u0646\\u062A \\u0645\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u0627\\u0644\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0646\\u0647\\u0627\\u0626\\u064A \\u0644\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645 \\\"\",selectedUser===null||selectedUser===void 0?void 0:selectedUser.full_name,\"\\\"\\u061F\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",sx:{mt:1,display:'block'},children:\"\\u0633\\u064A\\u062A\\u0645 \\u062D\\u0630\\u0641 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0631\\u062A\\u0628\\u0637\\u0629 \\u0628\\u0647\\u0630\\u0627 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0646\\u0647\\u0627\\u0626\\u064A\\u0627\\u064B.\"})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDeleteDialog(false),children:\"\\u0625\\u0644\\u063A\\u0627\\u0621\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>handlePermanentDelete(selectedUser===null||selectedUser===void 0?void 0:selectedUser.id),color:\"error\",variant:\"contained\",disabled:actionLoading,children:actionLoading?'جاري الحذف...':'حذف نهائي'})]})]})]});};export default DeletedUsers;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TablePagination", "IconButton", "<PERSON><PERSON>", "TextField", "MenuItem", "Select", "FormControl", "InputLabel", "Chip", "Avatar", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "Rest<PERSON>", "RestoreIcon", "DeleteForever", "DeleteForeverIcon", "Visibility", "ViewIcon", "Search", "SearchIcon", "Refresh", "RefreshIcon", "useTranslation", "axios", "format", "ar", "jsx", "_jsx", "jsxs", "_jsxs", "DeletedUsers", "t", "i18n", "users", "setUsers", "loading", "setLoading", "page", "setPage", "rowsPerPage", "setRowsPerPage", "total", "setTotal", "search", "setSearch", "<PERSON><PERSON><PERSON>er", "setRoleFilter", "selected<PERSON>ser", "setSelectedUser", "viewDialog", "setViewDialog", "restoreDialog", "setRestoreDialog", "deleteDialog", "setDeleteDialog", "stats", "setStats", "actionLoading", "setActionLoading", "fetchUsers", "params", "limit", "role", "response", "get", "data", "error", "console", "fetchStats", "handleRestore", "userId", "post", "handlePermanentDelete", "delete", "getRoleLabel", "getRoleColor", "sx", "p", "children", "variant", "gutterBottom", "container", "spacing", "mb", "item", "xs", "sm", "md", "color", "total_deleted", "deleted_students", "deleted_teachers", "total_active", "display", "gap", "alignItems", "flexWrap", "label", "size", "value", "onChange", "e", "target", "InputProps", "startAdornment", "mr", "min<PERSON><PERSON><PERSON>", "startIcon", "onClick", "component", "map", "user", "_user$full_name", "src", "profile_picture_url", "full_name", "char<PERSON>t", "email", "Date", "deleted_at", "locale", "language", "undefined", "deletion_reason", "deleted_by_name", "title", "id", "count", "onPageChange", "newPage", "onRowsPerPageChange", "parseInt", "labelRowsPerPage", "labelDisplayedRows", "_ref", "from", "to", "open", "onClose", "mt", "disabled", "severity"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/admin/DeletedUsers.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TablePagination,\n  IconButton,\n  Button,\n  TextField,\n  MenuItem,\n  Select,\n  FormControl,\n  InputLabel,\n  Chip,\n  Avatar,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Alert,\n  Tooltip,\n  Card,\n  CardContent,\n  Grid\n} from '@mui/material';\nimport {\n  Restore as RestoreIcon,\n  DeleteForever as DeleteForeverIcon,\n  Visibility as ViewIcon,\n  Search as SearchIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useTranslation } from 'react-i18next';\nimport axios from '../../utils/axios';\nimport { format } from 'date-fns';\nimport { ar } from 'date-fns/locale';\n\nconst DeletedUsers = () => {\n  const { t, i18n } = useTranslation();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [total, setTotal] = useState(0);\n  const [search, setSearch] = useState('');\n  const [roleFilter, setRoleFilter] = useState('');\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [viewDialog, setViewDialog] = useState(false);\n  const [restoreDialog, setRestoreDialog] = useState(false);\n  const [deleteDialog, setDeleteDialog] = useState(false);\n  const [stats, setStats] = useState({});\n  const [actionLoading, setActionLoading] = useState(false);\n\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: page + 1,\n        limit: rowsPerPage,\n        search,\n        role: roleFilter\n      };\n\n      const response = await axios.get('/admin/deleted-users', { params });\n      setUsers(response.data.users);\n      setTotal(response.data.total);\n    } catch (error) {\n      console.error('Error fetching deleted users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchStats = async () => {\n    try {\n      const response = await axios.get('/admin/deleted-users/stats/overview');\n      setStats(response.data.stats);\n    } catch (error) {\n      console.error('Error fetching stats:', error);\n    }\n  };\n\n  useEffect(() => {\n    fetchUsers();\n  }, [page, rowsPerPage, search, roleFilter]);\n\n  useEffect(() => {\n    fetchStats();\n  }, []);\n\n  const handleRestore = async (userId) => {\n    try {\n      setActionLoading(true);\n      await axios.post(`/admin/deleted-users/${userId}/restore`);\n      setRestoreDialog(false);\n      setSelectedUser(null);\n      fetchUsers();\n      fetchStats();\n    } catch (error) {\n      console.error('Error restoring user:', error);\n    } finally {\n      setActionLoading(false);\n    }\n  };\n\n  const handlePermanentDelete = async (userId) => {\n    try {\n      setActionLoading(true);\n      await axios.delete(`/admin/deleted-users/${userId}/permanent`);\n      setDeleteDialog(false);\n      setSelectedUser(null);\n      fetchUsers();\n      fetchStats();\n    } catch (error) {\n      console.error('Error permanently deleting user:', error);\n    } finally {\n      setActionLoading(false);\n    }\n  };\n\n  const getRoleLabel = (role) => {\n    switch (role) {\n      case 'student': return 'طالب';\n      case 'platform_teacher': return 'معلم';\n      case 'new_teacher': return 'معلم جديد';\n      default: return role;\n    }\n  };\n\n  const getRoleColor = (role) => {\n    switch (role) {\n      case 'student': return 'primary';\n      case 'platform_teacher': return 'success';\n      case 'new_teacher': return 'warning';\n      default: return 'default';\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        المستخدمون المحذوفون\n      </Typography>\n\n      {/* Statistics Cards */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                إجمالي المحذوفين\n              </Typography>\n              <Typography variant=\"h4\">\n                {stats.total_deleted || 0}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                الطلاب المحذوفين\n              </Typography>\n              <Typography variant=\"h4\" color=\"primary\">\n                {stats.deleted_students || 0}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                المعلمين المحذوفين\n              </Typography>\n              <Typography variant=\"h4\" color=\"success.main\">\n                {stats.deleted_teachers || 0}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                المستخدمين النشطين\n              </Typography>\n              <Typography variant=\"h4\" color=\"info.main\">\n                {stats.total_active || 0}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Filters */}\n      <Paper sx={{ p: 2, mb: 2 }}>\n        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>\n          <TextField\n            label=\"البحث\"\n            variant=\"outlined\"\n            size=\"small\"\n            value={search}\n            onChange={(e) => setSearch(e.target.value)}\n            InputProps={{\n              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />\n            }}\n            sx={{ minWidth: 200 }}\n          />\n          \n          <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n            <InputLabel>النوع</InputLabel>\n            <Select\n              value={roleFilter}\n              label=\"النوع\"\n              onChange={(e) => setRoleFilter(e.target.value)}\n            >\n              <MenuItem value=\"\">الكل</MenuItem>\n              <MenuItem value=\"student\">طلاب</MenuItem>\n              <MenuItem value=\"platform_teacher\">معلمين</MenuItem>\n              <MenuItem value=\"new_teacher\">معلمين جدد</MenuItem>\n            </Select>\n          </FormControl>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<RefreshIcon />}\n            onClick={() => {\n              fetchUsers();\n              fetchStats();\n            }}\n          >\n            تحديث\n          </Button>\n        </Box>\n      </Paper>\n\n      {/* Users Table */}\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>المستخدم</TableCell>\n              <TableCell>النوع</TableCell>\n              <TableCell>تاريخ الحذف</TableCell>\n              <TableCell>سبب الحذف</TableCell>\n              <TableCell>حُذف بواسطة</TableCell>\n              <TableCell>الإجراءات</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {users.map((user) => (\n              <TableRow key={user.id}>\n                <TableCell>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Avatar src={user.profile_picture_url}>\n                      {user.full_name?.charAt(0)}\n                    </Avatar>\n                    <Box>\n                      <Typography variant=\"subtitle2\">\n                        {user.full_name}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {user.email}\n                      </Typography>\n                    </Box>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={getRoleLabel(user.role)}\n                    color={getRoleColor(user.role)}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>\n                  {format(new Date(user.deleted_at), 'dd/MM/yyyy HH:mm', {\n                    locale: i18n.language === 'ar' ? ar : undefined\n                  })}\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"caption\">\n                    {user.deletion_reason || 'غير محدد'}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  {user.deleted_by_name || 'حذف ذاتي'}\n                </TableCell>\n                <TableCell>\n                  <Tooltip title=\"عرض التفاصيل\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => {\n                        setSelectedUser(user);\n                        setViewDialog(true);\n                      }}\n                    >\n                      <ViewIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"استرداد المستخدم\">\n                    <IconButton\n                      size=\"small\"\n                      color=\"success\"\n                      onClick={() => {\n                        setSelectedUser(user);\n                        setRestoreDialog(true);\n                      }}\n                    >\n                      <RestoreIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"حذف نهائي\">\n                    <IconButton\n                      size=\"small\"\n                      color=\"error\"\n                      onClick={() => {\n                        setSelectedUser(user);\n                        setDeleteDialog(true);\n                      }}\n                    >\n                      <DeleteForeverIcon />\n                    </IconButton>\n                  </Tooltip>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n        <TablePagination\n          component=\"div\"\n          count={total}\n          page={page}\n          onPageChange={(e, newPage) => setPage(newPage)}\n          rowsPerPage={rowsPerPage}\n          onRowsPerPageChange={(e) => {\n            setRowsPerPage(parseInt(e.target.value, 10));\n            setPage(0);\n          }}\n          labelRowsPerPage=\"عدد الصفوف:\"\n          labelDisplayedRows={({ from, to, count }) => \n            `${from}-${to} من ${count !== -1 ? count : `أكثر من ${to}`}`\n          }\n        />\n      </TableContainer>\n\n      {/* Restore Dialog */}\n      <Dialog open={restoreDialog} onClose={() => setRestoreDialog(false)}>\n        <DialogTitle>استرداد المستخدم</DialogTitle>\n        <DialogContent>\n          <Typography>\n            هل أنت متأكد من استرداد المستخدم \"{selectedUser?.full_name}\"؟\n          </Typography>\n          <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1, display: 'block' }}>\n            سيتمكن المستخدم من تسجيل الدخول والوصول للنظام مرة أخرى.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setRestoreDialog(false)}>إلغاء</Button>\n          <Button\n            onClick={() => handleRestore(selectedUser?.id)}\n            color=\"success\"\n            variant=\"contained\"\n            disabled={actionLoading}\n          >\n            {actionLoading ? 'جاري الاسترداد...' : 'استرداد'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Permanent Delete Dialog */}\n      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>\n        <DialogTitle>حذف نهائي</DialogTitle>\n        <DialogContent>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            تحذير: هذا الإجراء لا يمكن التراجع عنه!\n          </Alert>\n          <Typography>\n            هل أنت متأكد من الحذف النهائي للمستخدم \"{selectedUser?.full_name}\"؟\n          </Typography>\n          <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1, display: 'block' }}>\n            سيتم حذف جميع البيانات المرتبطة بهذا المستخدم نهائياً.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialog(false)}>إلغاء</Button>\n          <Button\n            onClick={() => handlePermanentDelete(selectedUser?.id)}\n            color=\"error\"\n            variant=\"contained\"\n            disabled={actionLoading}\n          >\n            {actionLoading ? 'جاري الحذف...' : 'حذف نهائي'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default DeletedUsers;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,KAAK,CACLC,UAAU,CACVC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,eAAe,CACfC,UAAU,CACVC,MAAM,CACNC,SAAS,CACTC,QAAQ,CACRC,MAAM,CACNC,WAAW,CACXC,UAAU,CACVC,IAAI,CACJC,MAAM,CACNC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,KAAK,CACLC,OAAO,CACPC,IAAI,CACJC,WAAW,CACXC,IAAI,KACC,eAAe,CACtB,OACEC,OAAO,GAAI,CAAAC,WAAW,CACtBC,aAAa,GAAI,CAAAC,iBAAiB,CAClCC,UAAU,GAAI,CAAAC,QAAQ,CACtBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,OAAO,GAAI,CAAAC,WAAW,KACjB,qBAAqB,CAC5B,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,CAAAC,KAAK,KAAM,mBAAmB,CACrC,OAASC,MAAM,KAAQ,UAAU,CACjC,OAASC,EAAE,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErC,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGV,cAAc,CAAC,CAAC,CACpC,KAAM,CAACW,KAAK,CAAEC,QAAQ,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACqD,OAAO,CAAEC,UAAU,CAAC,CAAGtD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACuD,IAAI,CAAEC,OAAO,CAAC,CAAGxD,QAAQ,CAAC,CAAC,CAAC,CACnC,KAAM,CAACyD,WAAW,CAAEC,cAAc,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC2D,KAAK,CAAEC,QAAQ,CAAC,CAAG5D,QAAQ,CAAC,CAAC,CAAC,CACrC,KAAM,CAAC6D,MAAM,CAAEC,SAAS,CAAC,CAAG9D,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAAC+D,UAAU,CAAEC,aAAa,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiE,YAAY,CAAEC,eAAe,CAAC,CAAGlE,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACmE,UAAU,CAAEC,aAAa,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACqE,aAAa,CAAEC,gBAAgB,CAAC,CAAGtE,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACuE,YAAY,CAAEC,eAAe,CAAC,CAAGxE,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACyE,KAAK,CAAEC,QAAQ,CAAC,CAAG1E,QAAQ,CAAC,CAAC,CAAC,CAAC,CACtC,KAAM,CAAC2E,aAAa,CAAEC,gBAAgB,CAAC,CAAG5E,QAAQ,CAAC,KAAK,CAAC,CAEzD,KAAM,CAAA6E,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACFvB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAwB,MAAM,CAAG,CACbvB,IAAI,CAAEA,IAAI,CAAG,CAAC,CACdwB,KAAK,CAAEtB,WAAW,CAClBI,MAAM,CACNmB,IAAI,CAAEjB,UACR,CAAC,CAED,KAAM,CAAAkB,QAAQ,CAAG,KAAM,CAAAxC,KAAK,CAACyC,GAAG,CAAC,sBAAsB,CAAE,CAAEJ,MAAO,CAAC,CAAC,CACpE1B,QAAQ,CAAC6B,QAAQ,CAACE,IAAI,CAAChC,KAAK,CAAC,CAC7BS,QAAQ,CAACqB,QAAQ,CAACE,IAAI,CAACxB,KAAK,CAAC,CAC/B,CAAE,MAAOyB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CAAC,OAAS,CACR9B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAgC,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAxC,KAAK,CAACyC,GAAG,CAAC,qCAAqC,CAAC,CACvER,QAAQ,CAACO,QAAQ,CAACE,IAAI,CAACV,KAAK,CAAC,CAC/B,CAAE,MAAOW,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CACF,CAAC,CAEDnF,SAAS,CAAC,IAAM,CACd4E,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,CAACtB,IAAI,CAAEE,WAAW,CAAEI,MAAM,CAAEE,UAAU,CAAC,CAAC,CAE3C9D,SAAS,CAAC,IAAM,CACdqF,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,aAAa,CAAG,KAAO,CAAAC,MAAM,EAAK,CACtC,GAAI,CACFZ,gBAAgB,CAAC,IAAI,CAAC,CACtB,KAAM,CAAAnC,KAAK,CAACgD,IAAI,CAAC,wBAAwBD,MAAM,UAAU,CAAC,CAC1DlB,gBAAgB,CAAC,KAAK,CAAC,CACvBJ,eAAe,CAAC,IAAI,CAAC,CACrBW,UAAU,CAAC,CAAC,CACZS,UAAU,CAAC,CAAC,CACd,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CAAC,OAAS,CACRR,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAc,qBAAqB,CAAG,KAAO,CAAAF,MAAM,EAAK,CAC9C,GAAI,CACFZ,gBAAgB,CAAC,IAAI,CAAC,CACtB,KAAM,CAAAnC,KAAK,CAACkD,MAAM,CAAC,wBAAwBH,MAAM,YAAY,CAAC,CAC9DhB,eAAe,CAAC,KAAK,CAAC,CACtBN,eAAe,CAAC,IAAI,CAAC,CACrBW,UAAU,CAAC,CAAC,CACZS,UAAU,CAAC,CAAC,CACd,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CAC1D,CAAC,OAAS,CACRR,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAgB,YAAY,CAAIZ,IAAI,EAAK,CAC7B,OAAQA,IAAI,EACV,IAAK,SAAS,CAAE,MAAO,MAAM,CAC7B,IAAK,kBAAkB,CAAE,MAAO,MAAM,CACtC,IAAK,aAAa,CAAE,MAAO,WAAW,CACtC,QAAS,MAAO,CAAAA,IAAI,CACtB,CACF,CAAC,CAED,KAAM,CAAAa,YAAY,CAAIb,IAAI,EAAK,CAC7B,OAAQA,IAAI,EACV,IAAK,SAAS,CAAE,MAAO,SAAS,CAChC,IAAK,kBAAkB,CAAE,MAAO,SAAS,CACzC,IAAK,aAAa,CAAE,MAAO,SAAS,CACpC,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,mBACEjC,KAAA,CAAC7C,GAAG,EAAC4F,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAC,QAAA,eAChBnD,IAAA,CAACzC,UAAU,EAAC6F,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,qHAEtC,CAAY,CAAC,cAGbjD,KAAA,CAAClB,IAAI,EAACsE,SAAS,MAACC,OAAO,CAAE,CAAE,CAACN,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACxCnD,IAAA,CAAChB,IAAI,EAACyE,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAT,QAAA,cAC9BnD,IAAA,CAAClB,IAAI,EAAAqE,QAAA,cACHjD,KAAA,CAACnB,WAAW,EAAAoE,QAAA,eACVnD,IAAA,CAACzC,UAAU,EAACsG,KAAK,CAAC,eAAe,CAACR,YAAY,MAAAF,QAAA,CAAC,6FAE/C,CAAY,CAAC,cACbnD,IAAA,CAACzC,UAAU,EAAC6F,OAAO,CAAC,IAAI,CAAAD,QAAA,CACrBvB,KAAK,CAACkC,aAAa,EAAI,CAAC,CACf,CAAC,EACF,CAAC,CACV,CAAC,CACH,CAAC,cACP9D,IAAA,CAAChB,IAAI,EAACyE,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAT,QAAA,cAC9BnD,IAAA,CAAClB,IAAI,EAAAqE,QAAA,cACHjD,KAAA,CAACnB,WAAW,EAAAoE,QAAA,eACVnD,IAAA,CAACzC,UAAU,EAACsG,KAAK,CAAC,eAAe,CAACR,YAAY,MAAAF,QAAA,CAAC,6FAE/C,CAAY,CAAC,cACbnD,IAAA,CAACzC,UAAU,EAAC6F,OAAO,CAAC,IAAI,CAACS,KAAK,CAAC,SAAS,CAAAV,QAAA,CACrCvB,KAAK,CAACmC,gBAAgB,EAAI,CAAC,CAClB,CAAC,EACF,CAAC,CACV,CAAC,CACH,CAAC,cACP/D,IAAA,CAAChB,IAAI,EAACyE,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAT,QAAA,cAC9BnD,IAAA,CAAClB,IAAI,EAAAqE,QAAA,cACHjD,KAAA,CAACnB,WAAW,EAAAoE,QAAA,eACVnD,IAAA,CAACzC,UAAU,EAACsG,KAAK,CAAC,eAAe,CAACR,YAAY,MAAAF,QAAA,CAAC,yGAE/C,CAAY,CAAC,cACbnD,IAAA,CAACzC,UAAU,EAAC6F,OAAO,CAAC,IAAI,CAACS,KAAK,CAAC,cAAc,CAAAV,QAAA,CAC1CvB,KAAK,CAACoC,gBAAgB,EAAI,CAAC,CAClB,CAAC,EACF,CAAC,CACV,CAAC,CACH,CAAC,cACPhE,IAAA,CAAChB,IAAI,EAACyE,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAT,QAAA,cAC9BnD,IAAA,CAAClB,IAAI,EAAAqE,QAAA,cACHjD,KAAA,CAACnB,WAAW,EAAAoE,QAAA,eACVnD,IAAA,CAACzC,UAAU,EAACsG,KAAK,CAAC,eAAe,CAACR,YAAY,MAAAF,QAAA,CAAC,yGAE/C,CAAY,CAAC,cACbnD,IAAA,CAACzC,UAAU,EAAC6F,OAAO,CAAC,IAAI,CAACS,KAAK,CAAC,WAAW,CAAAV,QAAA,CACvCvB,KAAK,CAACqC,YAAY,EAAI,CAAC,CACd,CAAC,EACF,CAAC,CACV,CAAC,CACH,CAAC,EACH,CAAC,cAGPjE,IAAA,CAAC1C,KAAK,EAAC2F,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,cACzBjD,KAAA,CAAC7C,GAAG,EAAC4F,EAAE,CAAE,CAAEiB,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAC,CAAEC,UAAU,CAAE,QAAQ,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAlB,QAAA,eAC3EnD,IAAA,CAAC/B,SAAS,EACRqG,KAAK,CAAC,gCAAO,CACblB,OAAO,CAAC,UAAU,CAClBmB,IAAI,CAAC,OAAO,CACZC,KAAK,CAAExD,MAAO,CACdyD,QAAQ,CAAGC,CAAC,EAAKzD,SAAS,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC3CI,UAAU,CAAE,CACVC,cAAc,cAAE7E,IAAA,CAACR,UAAU,EAACyD,EAAE,CAAE,CAAE6B,EAAE,CAAE,CAAC,CAAEjB,KAAK,CAAE,gBAAiB,CAAE,CAAE,CACvE,CAAE,CACFZ,EAAE,CAAE,CAAE8B,QAAQ,CAAE,GAAI,CAAE,CACvB,CAAC,cAEF7E,KAAA,CAAC9B,WAAW,EAACmG,IAAI,CAAC,OAAO,CAACtB,EAAE,CAAE,CAAE8B,QAAQ,CAAE,GAAI,CAAE,CAAA5B,QAAA,eAC9CnD,IAAA,CAAC3B,UAAU,EAAA8E,QAAA,CAAC,gCAAK,CAAY,CAAC,cAC9BjD,KAAA,CAAC/B,MAAM,EACLqG,KAAK,CAAEtD,UAAW,CAClBoD,KAAK,CAAC,gCAAO,CACbG,QAAQ,CAAGC,CAAC,EAAKvD,aAAa,CAACuD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAArB,QAAA,eAE/CnD,IAAA,CAAC9B,QAAQ,EAACsG,KAAK,CAAC,EAAE,CAAArB,QAAA,CAAC,0BAAI,CAAU,CAAC,cAClCnD,IAAA,CAAC9B,QAAQ,EAACsG,KAAK,CAAC,SAAS,CAAArB,QAAA,CAAC,0BAAI,CAAU,CAAC,cACzCnD,IAAA,CAAC9B,QAAQ,EAACsG,KAAK,CAAC,kBAAkB,CAAArB,QAAA,CAAC,sCAAM,CAAU,CAAC,cACpDnD,IAAA,CAAC9B,QAAQ,EAACsG,KAAK,CAAC,aAAa,CAAArB,QAAA,CAAC,yDAAU,CAAU,CAAC,EAC7C,CAAC,EACE,CAAC,cAEdnD,IAAA,CAAChC,MAAM,EACLoF,OAAO,CAAC,UAAU,CAClB4B,SAAS,cAAEhF,IAAA,CAACN,WAAW,GAAE,CAAE,CAC3BuF,OAAO,CAAEA,CAAA,GAAM,CACbjD,UAAU,CAAC,CAAC,CACZS,UAAU,CAAC,CAAC,CACd,CAAE,CAAAU,QAAA,CACH,gCAED,CAAQ,CAAC,EACN,CAAC,CACD,CAAC,cAGRjD,KAAA,CAACvC,cAAc,EAACuH,SAAS,CAAE5H,KAAM,CAAA6F,QAAA,eAC/BjD,KAAA,CAAC1C,KAAK,EAAA2F,QAAA,eACJnD,IAAA,CAACpC,SAAS,EAAAuF,QAAA,cACRjD,KAAA,CAACrC,QAAQ,EAAAsF,QAAA,eACPnD,IAAA,CAACtC,SAAS,EAAAyF,QAAA,CAAC,kDAAQ,CAAW,CAAC,cAC/BnD,IAAA,CAACtC,SAAS,EAAAyF,QAAA,CAAC,gCAAK,CAAW,CAAC,cAC5BnD,IAAA,CAACtC,SAAS,EAAAyF,QAAA,CAAC,+DAAW,CAAW,CAAC,cAClCnD,IAAA,CAACtC,SAAS,EAAAyF,QAAA,CAAC,mDAAS,CAAW,CAAC,cAChCnD,IAAA,CAACtC,SAAS,EAAAyF,QAAA,CAAC,+DAAW,CAAW,CAAC,cAClCnD,IAAA,CAACtC,SAAS,EAAAyF,QAAA,CAAC,wDAAS,CAAW,CAAC,EACxB,CAAC,CACF,CAAC,cACZnD,IAAA,CAACvC,SAAS,EAAA0F,QAAA,CACP7C,KAAK,CAAC6E,GAAG,CAAEC,IAAI,OAAAC,eAAA,oBACdnF,KAAA,CAACrC,QAAQ,EAAAsF,QAAA,eACPnD,IAAA,CAACtC,SAAS,EAAAyF,QAAA,cACRjD,KAAA,CAAC7C,GAAG,EAAC4F,EAAE,CAAE,CAAEiB,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAED,GAAG,CAAE,CAAE,CAAE,CAAAhB,QAAA,eACzDnD,IAAA,CAACzB,MAAM,EAAC+G,GAAG,CAAEF,IAAI,CAACG,mBAAoB,CAAApC,QAAA,EAAAkC,eAAA,CACnCD,IAAI,CAACI,SAAS,UAAAH,eAAA,iBAAdA,eAAA,CAAgBI,MAAM,CAAC,CAAC,CAAC,CACpB,CAAC,cACTvF,KAAA,CAAC7C,GAAG,EAAA8F,QAAA,eACFnD,IAAA,CAACzC,UAAU,EAAC6F,OAAO,CAAC,WAAW,CAAAD,QAAA,CAC5BiC,IAAI,CAACI,SAAS,CACL,CAAC,cACbxF,IAAA,CAACzC,UAAU,EAAC6F,OAAO,CAAC,SAAS,CAACS,KAAK,CAAC,gBAAgB,CAAAV,QAAA,CACjDiC,IAAI,CAACM,KAAK,CACD,CAAC,EACV,CAAC,EACH,CAAC,CACG,CAAC,cACZ1F,IAAA,CAACtC,SAAS,EAAAyF,QAAA,cACRnD,IAAA,CAAC1B,IAAI,EACHgG,KAAK,CAAEvB,YAAY,CAACqC,IAAI,CAACjD,IAAI,CAAE,CAC/B0B,KAAK,CAAEb,YAAY,CAACoC,IAAI,CAACjD,IAAI,CAAE,CAC/BoC,IAAI,CAAC,OAAO,CACb,CAAC,CACO,CAAC,cACZvE,IAAA,CAACtC,SAAS,EAAAyF,QAAA,CACPtD,MAAM,CAAC,GAAI,CAAA8F,IAAI,CAACP,IAAI,CAACQ,UAAU,CAAC,CAAE,kBAAkB,CAAE,CACrDC,MAAM,CAAExF,IAAI,CAACyF,QAAQ,GAAK,IAAI,CAAGhG,EAAE,CAAGiG,SACxC,CAAC,CAAC,CACO,CAAC,cACZ/F,IAAA,CAACtC,SAAS,EAAAyF,QAAA,cACRnD,IAAA,CAACzC,UAAU,EAAC6F,OAAO,CAAC,SAAS,CAAAD,QAAA,CAC1BiC,IAAI,CAACY,eAAe,EAAI,UAAU,CACzB,CAAC,CACJ,CAAC,cACZhG,IAAA,CAACtC,SAAS,EAAAyF,QAAA,CACPiC,IAAI,CAACa,eAAe,EAAI,UAAU,CAC1B,CAAC,cACZ/F,KAAA,CAACxC,SAAS,EAAAyF,QAAA,eACRnD,IAAA,CAACnB,OAAO,EAACqH,KAAK,CAAC,qEAAc,CAAA/C,QAAA,cAC3BnD,IAAA,CAACjC,UAAU,EACTwG,IAAI,CAAC,OAAO,CACZU,OAAO,CAAEA,CAAA,GAAM,CACb5D,eAAe,CAAC+D,IAAI,CAAC,CACrB7D,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CAAA4B,QAAA,cAEFnD,IAAA,CAACV,QAAQ,GAAE,CAAC,CACF,CAAC,CACN,CAAC,cACVU,IAAA,CAACnB,OAAO,EAACqH,KAAK,CAAC,6FAAkB,CAAA/C,QAAA,cAC/BnD,IAAA,CAACjC,UAAU,EACTwG,IAAI,CAAC,OAAO,CACZV,KAAK,CAAC,SAAS,CACfoB,OAAO,CAAEA,CAAA,GAAM,CACb5D,eAAe,CAAC+D,IAAI,CAAC,CACrB3D,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CAAA0B,QAAA,cAEFnD,IAAA,CAACd,WAAW,GAAE,CAAC,CACL,CAAC,CACN,CAAC,cACVc,IAAA,CAACnB,OAAO,EAACqH,KAAK,CAAC,mDAAW,CAAA/C,QAAA,cACxBnD,IAAA,CAACjC,UAAU,EACTwG,IAAI,CAAC,OAAO,CACZV,KAAK,CAAC,OAAO,CACboB,OAAO,CAAEA,CAAA,GAAM,CACb5D,eAAe,CAAC+D,IAAI,CAAC,CACrBzD,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,CAAAwB,QAAA,cAEFnD,IAAA,CAACZ,iBAAiB,GAAE,CAAC,CACX,CAAC,CACN,CAAC,EACD,CAAC,GAxECgG,IAAI,CAACe,EAyEV,CAAC,EACZ,CAAC,CACO,CAAC,EACP,CAAC,cACRnG,IAAA,CAAClC,eAAe,EACdoH,SAAS,CAAC,KAAK,CACfkB,KAAK,CAAEtF,KAAM,CACbJ,IAAI,CAAEA,IAAK,CACX2F,YAAY,CAAEA,CAAC3B,CAAC,CAAE4B,OAAO,GAAK3F,OAAO,CAAC2F,OAAO,CAAE,CAC/C1F,WAAW,CAAEA,WAAY,CACzB2F,mBAAmB,CAAG7B,CAAC,EAAK,CAC1B7D,cAAc,CAAC2F,QAAQ,CAAC9B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,EAAE,CAAC,CAAC,CAC5C7D,OAAO,CAAC,CAAC,CAAC,CACZ,CAAE,CACF8F,gBAAgB,CAAC,0DAAa,CAC9BC,kBAAkB,CAAEC,IAAA,MAAC,CAAEC,IAAI,CAAEC,EAAE,CAAET,KAAM,CAAC,CAAAO,IAAA,OACtC,GAAGC,IAAI,IAAIC,EAAE,OAAOT,KAAK,GAAK,CAAC,CAAC,CAAGA,KAAK,CAAG,WAAWS,EAAE,EAAE,EAAE,EAC7D,CACF,CAAC,EACY,CAAC,cAGjB3G,KAAA,CAAC1B,MAAM,EAACsI,IAAI,CAAEtF,aAAc,CAACuF,OAAO,CAAEA,CAAA,GAAMtF,gBAAgB,CAAC,KAAK,CAAE,CAAA0B,QAAA,eAClEnD,IAAA,CAACvB,WAAW,EAAA0E,QAAA,CAAC,6FAAgB,CAAa,CAAC,cAC3CjD,KAAA,CAACxB,aAAa,EAAAyE,QAAA,eACZjD,KAAA,CAAC3C,UAAU,EAAA4F,QAAA,EAAC,4KACwB,CAAC/B,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEoE,SAAS,CAAC,UAC7D,EAAY,CAAC,cACbxF,IAAA,CAACzC,UAAU,EAAC6F,OAAO,CAAC,SAAS,CAACS,KAAK,CAAC,gBAAgB,CAACZ,EAAE,CAAE,CAAE+D,EAAE,CAAE,CAAC,CAAE9C,OAAO,CAAE,OAAQ,CAAE,CAAAf,QAAA,CAAC,qSAEtF,CAAY,CAAC,EACA,CAAC,cAChBjD,KAAA,CAACvB,aAAa,EAAAwE,QAAA,eACZnD,IAAA,CAAChC,MAAM,EAACiH,OAAO,CAAEA,CAAA,GAAMxD,gBAAgB,CAAC,KAAK,CAAE,CAAA0B,QAAA,CAAC,gCAAK,CAAQ,CAAC,cAC9DnD,IAAA,CAAChC,MAAM,EACLiH,OAAO,CAAEA,CAAA,GAAMvC,aAAa,CAACtB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE+E,EAAE,CAAE,CAC/CtC,KAAK,CAAC,SAAS,CACfT,OAAO,CAAC,WAAW,CACnB6D,QAAQ,CAAEnF,aAAc,CAAAqB,QAAA,CAEvBrB,aAAa,CAAG,mBAAmB,CAAG,SAAS,CAC1C,CAAC,EACI,CAAC,EACV,CAAC,cAGT5B,KAAA,CAAC1B,MAAM,EAACsI,IAAI,CAAEpF,YAAa,CAACqF,OAAO,CAAEA,CAAA,GAAMpF,eAAe,CAAC,KAAK,CAAE,CAAAwB,QAAA,eAChEnD,IAAA,CAACvB,WAAW,EAAA0E,QAAA,CAAC,mDAAS,CAAa,CAAC,cACpCjD,KAAA,CAACxB,aAAa,EAAAyE,QAAA,eACZnD,IAAA,CAACpB,KAAK,EAACsI,QAAQ,CAAC,OAAO,CAACjE,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,CAAC,oMAEvC,CAAO,CAAC,cACRjD,KAAA,CAAC3C,UAAU,EAAA4F,QAAA,EAAC,2MAC8B,CAAC/B,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEoE,SAAS,CAAC,UACnE,EAAY,CAAC,cACbxF,IAAA,CAACzC,UAAU,EAAC6F,OAAO,CAAC,SAAS,CAACS,KAAK,CAAC,gBAAgB,CAACZ,EAAE,CAAE,CAAE+D,EAAE,CAAE,CAAC,CAAE9C,OAAO,CAAE,OAAQ,CAAE,CAAAf,QAAA,CAAC,8RAEtF,CAAY,CAAC,EACA,CAAC,cAChBjD,KAAA,CAACvB,aAAa,EAAAwE,QAAA,eACZnD,IAAA,CAAChC,MAAM,EAACiH,OAAO,CAAEA,CAAA,GAAMtD,eAAe,CAAC,KAAK,CAAE,CAAAwB,QAAA,CAAC,gCAAK,CAAQ,CAAC,cAC7DnD,IAAA,CAAChC,MAAM,EACLiH,OAAO,CAAEA,CAAA,GAAMpC,qBAAqB,CAACzB,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE+E,EAAE,CAAE,CACvDtC,KAAK,CAAC,OAAO,CACbT,OAAO,CAAC,WAAW,CACnB6D,QAAQ,CAAEnF,aAAc,CAAAqB,QAAA,CAEvBrB,aAAa,CAAG,eAAe,CAAG,WAAW,CACxC,CAAC,EACI,CAAC,EACV,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}