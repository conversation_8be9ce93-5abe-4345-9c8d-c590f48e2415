[{"D:\\xampp\\htdocs\\allemnionline\\client\\src\\index.js": "1", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\App.js": "2", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\config\\axios.js": "3", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\i18n.js": "4", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\reportWebVitals.js": "5", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\ResizeObserverFix.js": "6", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\resizeObserver.js": "7", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\AuthContext.js": "8", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Footer.js": "9", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\SocketContext.js": "10", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\UnreadMessagesContext.js": "11", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AuthenticatedFooter.js": "12", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\layout\\Header.js": "13", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\Home.js": "14", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PrivacyPolicy.js": "15", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\AboutUs.js": "16", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PlatformPolicy.js": "17", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\FindTeacher.js": "18", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TeacherDetails.js": "19", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\StudentRegister.js": "20", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\TeacherRegister.js": "21", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\Login.js": "22", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyResetCode.js": "23", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ResetPassword.js": "24", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ForgotPassword.js": "25", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\RegisterChoice.js": "26", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyEmail.js": "27", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Dashboard.js": "28", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Students.js": "29", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Profile.js": "30", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Teachers.js": "31", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\TeacherApplications.js": "32", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Languages.js": "33", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Categories.js": "34", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\ProfileUpdates.js": "35", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingSessions.js": "36", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Wallet.js": "37", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Messages.js": "38", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\AdminEarnings.js": "39", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Dashboard.js": "40", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\WithdrawalManagement.js": "41", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditVideoUpload.js": "42", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditApplication.js": "43", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Application.js": "44", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\TeacherAvailableHours.js": "45", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ViewAvailableHours.js": "46", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\VideoUpload.js": "47", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\AvailableHours.js": "48", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Meetings.js": "49", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Bookings.js": "50", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Chat.js": "51", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Profile.js": "52", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyLessons.js": "53", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Wallet.js": "54", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ContactUs.js": "55", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Withdrawal.js": "56", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyMessages.js": "57", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Reviews.js": "58", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Profile.js": "59", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Dashboard.js": "60", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\CompleteProfile.js": "61", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\FindTeacher.js": "62", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\TeacherProfile.js": "63", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ChatEmbed.js": "64", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Chat.js": "65", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ContactUs.js": "66", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyMessages.js": "67", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\JoinMeeting.js": "68", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyTeachers.js": "69", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Meetings.js": "70", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Wallet.js": "71", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\BookingPage.js": "72", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Bookings.js": "73", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\WriteReview.js": "74", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AvailableHoursTable.js": "75", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Layout.js": "76", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axios.js": "77", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\timezone.js": "78", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\WeeklyBookingsTable.js": "79", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\constants.js": "80", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\BookableHoursTable.js": "81", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\StripePayment.js": "82", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatWindow.js": "83", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatList.js": "84", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\auth\\GenderDialog.js": "85", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\CropImageDialog.js": "86", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationStatus.js": "87", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationForm.js": "88", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\Spinner.js": "89", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\VideoSDKMeeting.js": "90", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\student\\ProfileCompletionAlert.js": "91", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\api.js": "92", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingAppContext.js": "93", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingContainer.js": "94", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\JoiningScreen.js": "95", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\WaitingToJoinScreen.js": "96", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\LeaveScreen.js": "97", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\PresenterView.js": "98", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\SimpleBottomBar.js": "99", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ParticipantView.js": "100", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ConfirmBox.js": "101", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\helper.js": "102", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsMobile.js": "103", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\SidebarContainer.js": "104", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsTab.js": "105", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\MeetingDetailsScreen.js": "106", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useMediaStream.js": "107", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownCam.js": "108", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDown.js": "109", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownSpeaker.js": "110", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\NetworkStats.js": "111", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\WebcamOffIcon.js": "112", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffIcon.js": "113", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicPermissionDenied.jsx": "114", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\CameraPermissionDenied.jsx": "115", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOnIcon.js": "116", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\WebcamOnIcon.js": "117", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffSmallIcon.js": "118", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\SpeakerIcon.js": "119", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useWindowSize.js": "120", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ScreenShareIcon.js": "121", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\common.js": "122", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkIcon.js": "123", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOffIcon.js": "124", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\PipIcon.js": "125", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ChatPanel.js": "126", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\NotesPanel.js": "127", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ParticipantPanel.js": "128", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropMIC.jsx": "129", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMicOff.jsx": "130", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropCAM.jsx": "131", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestSpeaker.jsx": "132", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMic.jsx": "133", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\PauseButton.jsx": "134", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropSpeaker.jsx": "135", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\UploadIcon.jsx": "136", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshCheck.jsx": "137", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshIcon.jsx": "138", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\DownloadIcon.jsx": "139", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\WifiOff.jsx": "140", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\RaiseHand.js": "141", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOffIcon.js": "142", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOnIcon.js": "143", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOnIcon.js": "144", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOffIcon.js": "145", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingPaymentPolicy.js": "146", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\RefundPolicy.js": "147", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingCancellationPolicy.js": "148", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\ContactUs.js": "149", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TermsAndConditions.js": "150", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\termsConditions.js": "151", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\contactUs.js": "152", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\MeetingFeedbackDialog.js": "153", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingIssues.js": "154", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\RescheduleDialog.js": "155", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\UserStatusChecker.js": "156", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\ProtectedRoute.js": "157", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\userStatusHandler.js": "158", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axiosConfig.js": "159"}, {"size": 641, "mtime": 1742576862818, "results": "160", "hashOfConfig": "161"}, {"size": 24973, "mtime": 1753822989206, "results": "162", "hashOfConfig": "161"}, {"size": 1456, "mtime": 1746732811228, "results": "163", "hashOfConfig": "161"}, {"size": 217699, "mtime": 1753734517006, "results": "164", "hashOfConfig": "161"}, {"size": 362, "mtime": 1742419325522, "results": "165", "hashOfConfig": "161"}, {"size": 1029, "mtime": 1744808128594, "results": "166", "hashOfConfig": "161"}, {"size": 325, "mtime": 1743221258725, "results": "167", "hashOfConfig": "161"}, {"size": 9961, "mtime": 1753822989204, "results": "168", "hashOfConfig": "161"}, {"size": 7654, "mtime": 1751398254796, "results": "169", "hashOfConfig": "161"}, {"size": 3432, "mtime": 1751285065551, "results": "170", "hashOfConfig": "161"}, {"size": 3326, "mtime": 1751040721930, "results": "171", "hashOfConfig": "161"}, {"size": 943, "mtime": 1749309630963, "results": "172", "hashOfConfig": "161"}, {"size": 24114, "mtime": 1751284599336, "results": "173", "hashOfConfig": "161"}, {"size": 36606, "mtime": 1753386956890, "results": "174", "hashOfConfig": "161"}, {"size": 4768, "mtime": 1751278844207, "results": "175", "hashOfConfig": "161"}, {"size": 10446, "mtime": 1751293932223, "results": "176", "hashOfConfig": "161"}, {"size": 28837, "mtime": 1753731053731, "results": "177", "hashOfConfig": "161"}, {"size": 30616, "mtime": 1751485922771, "results": "178", "hashOfConfig": "161"}, {"size": 15736, "mtime": 1751486831358, "results": "179", "hashOfConfig": "161"}, {"size": 15894, "mtime": 1749479492637, "results": "180", "hashOfConfig": "161"}, {"size": 15959, "mtime": 1749479507046, "results": "181", "hashOfConfig": "161"}, {"size": 18148, "mtime": 1753822989205, "results": "182", "hashOfConfig": "161"}, {"size": 7902, "mtime": 1747334952040, "results": "183", "hashOfConfig": "161"}, {"size": 10135, "mtime": 1747335101462, "results": "184", "hashOfConfig": "161"}, {"size": 7229, "mtime": 1747334912778, "results": "185", "hashOfConfig": "161"}, {"size": 9247, "mtime": 1742785011813, "results": "186", "hashOfConfig": "161"}, {"size": 13648, "mtime": 1751303763967, "results": "187", "hashOfConfig": "161"}, {"size": 29493, "mtime": 1744817248860, "results": "188", "hashOfConfig": "161"}, {"size": 13701, "mtime": 1744022069977, "results": "189", "hashOfConfig": "161"}, {"size": 12085, "mtime": 1746733346280, "results": "190", "hashOfConfig": "161"}, {"size": 11112, "mtime": 1744799843410, "results": "191", "hashOfConfig": "161"}, {"size": 30331, "mtime": 1751480317262, "results": "192", "hashOfConfig": "161"}, {"size": 6641, "mtime": 1742964048040, "results": "193", "hashOfConfig": "161"}, {"size": 6618, "mtime": 1744022276817, "results": "194", "hashOfConfig": "161"}, {"size": 22625, "mtime": 1751041436226, "results": "195", "hashOfConfig": "161"}, {"size": 11838, "mtime": 1749581734964, "results": "196", "hashOfConfig": "161"}, {"size": 7554, "mtime": 1744093753745, "results": "197", "hashOfConfig": "161"}, {"size": 19961, "mtime": 1746026434563, "results": "198", "hashOfConfig": "161"}, {"size": 10379, "mtime": 1749855699542, "results": "199", "hashOfConfig": "161"}, {"size": 7520, "mtime": 1742769302670, "results": "200", "hashOfConfig": "161"}, {"size": 12485, "mtime": 1749828882192, "results": "201", "hashOfConfig": "161"}, {"size": 12535, "mtime": 1749506609302, "results": "202", "hashOfConfig": "161"}, {"size": 24173, "mtime": 1750508377674, "results": "203", "hashOfConfig": "161"}, {"size": 6673, "mtime": 1749331131513, "results": "204", "hashOfConfig": "161"}, {"size": 35597, "mtime": 1749498641458, "results": "205", "hashOfConfig": "161"}, {"size": 32081, "mtime": 1749321424781, "results": "206", "hashOfConfig": "161"}, {"size": 10975, "mtime": 1749337833332, "results": "207", "hashOfConfig": "161"}, {"size": 33527, "mtime": 1749322312984, "results": "208", "hashOfConfig": "161"}, {"size": 15465, "mtime": 1750848677426, "results": "209", "hashOfConfig": "161"}, {"size": 34185, "mtime": 1753480100105, "results": "210", "hashOfConfig": "161"}, {"size": 15303, "mtime": 1750870211486, "results": "211", "hashOfConfig": "161"}, {"size": 59692, "mtime": 1753723359743, "results": "212", "hashOfConfig": "161"}, {"size": 12480, "mtime": 1752698192448, "results": "213", "hashOfConfig": "161"}, {"size": 8011, "mtime": 1744093709651, "results": "214", "hashOfConfig": "161"}, {"size": 4974, "mtime": 1744028374118, "results": "215", "hashOfConfig": "161"}, {"size": 17010, "mtime": 1749916559907, "results": "216", "hashOfConfig": "161"}, {"size": 10864, "mtime": 1746026292144, "results": "217", "hashOfConfig": "161"}, {"size": 21665, "mtime": 1750041191553, "results": "218", "hashOfConfig": "161"}, {"size": 35014, "mtime": 1753632113764, "results": "219", "hashOfConfig": "161"}, {"size": 4214, "mtime": 1742913454633, "results": "220", "hashOfConfig": "161"}, {"size": 8524, "mtime": 1749491950143, "results": "221", "hashOfConfig": "161"}, {"size": 34718, "mtime": 1751485938458, "results": "222", "hashOfConfig": "161"}, {"size": 35898, "mtime": 1753286098780, "results": "223", "hashOfConfig": "161"}, {"size": 5814, "mtime": 1744024131309, "results": "224", "hashOfConfig": "161"}, {"size": 15814, "mtime": 1749589859006, "results": "225", "hashOfConfig": "161"}, {"size": 5024, "mtime": 1746026315740, "results": "226", "hashOfConfig": "161"}, {"size": 11083, "mtime": 1746026269030, "results": "227", "hashOfConfig": "161"}, {"size": 2721, "mtime": 1750156638150, "results": "228", "hashOfConfig": "161"}, {"size": 7189, "mtime": 1750568564445, "results": "229", "hashOfConfig": "161"}, {"size": 13724, "mtime": 1751567205888, "results": "230", "hashOfConfig": "161"}, {"size": 14721, "mtime": 1749819465295, "results": "231", "hashOfConfig": "161"}, {"size": 40555, "mtime": 1753286098776, "results": "232", "hashOfConfig": "161"}, {"size": 40659, "mtime": 1753453083171, "results": "233", "hashOfConfig": "161"}, {"size": 57051, "mtime": 1746744695128, "results": "234", "hashOfConfig": "161"}, {"size": 14257, "mtime": 1749585929387, "results": "235", "hashOfConfig": "161"}, {"size": 24555, "mtime": 1752956536040, "results": "236", "hashOfConfig": "161"}, {"size": 3262, "mtime": 1753034610621, "results": "237", "hashOfConfig": "161"}, {"size": 7726, "mtime": 1753129936379, "results": "238", "hashOfConfig": "161"}, {"size": 56521, "mtime": 1753443150818, "results": "239", "hashOfConfig": "161"}, {"size": 5549, "mtime": 1749490160444, "results": "240", "hashOfConfig": "161"}, {"size": 25593, "mtime": 1753442449956, "results": "241", "hashOfConfig": "161"}, {"size": 3697, "mtime": 1749774472728, "results": "242", "hashOfConfig": "161"}, {"size": 13801, "mtime": 1743218310347, "results": "243", "hashOfConfig": "161"}, {"size": 7971, "mtime": 1746023858919, "results": "244", "hashOfConfig": "161"}, {"size": 2877, "mtime": 1747400985303, "results": "245", "hashOfConfig": "161"}, {"size": 2530, "mtime": 1750507091156, "results": "246", "hashOfConfig": "161"}, {"size": 1488, "mtime": 1742858201413, "results": "247", "hashOfConfig": "161"}, {"size": 42233, "mtime": 1753723325313, "results": "248", "hashOfConfig": "161"}, {"size": 153, "mtime": 1742445554097, "results": "249", "hashOfConfig": "161"}, {"size": 7365, "mtime": 1750226849238, "results": "250", "hashOfConfig": "161"}, {"size": 3181, "mtime": 1744798236742, "results": "251", "hashOfConfig": "161"}, {"size": 2614, "mtime": 1750185563183, "results": "252", "hashOfConfig": "161"}, {"size": 3068, "mtime": 1751036509706, "results": "253", "hashOfConfig": "161"}, {"size": 11091, "mtime": 1752283251033, "results": "254", "hashOfConfig": "161"}, {"size": 20466, "mtime": 1750170525751, "results": "255", "hashOfConfig": "161"}, {"size": 2219, "mtime": 1750163211180, "results": "256", "hashOfConfig": "161"}, {"size": 836, "mtime": 1750184890252, "results": "257", "hashOfConfig": "161"}, {"size": 4725, "mtime": 1750162079006, "results": "258", "hashOfConfig": "161"}, {"size": 11306, "mtime": 1750569740310, "results": "259", "hashOfConfig": "161"}, {"size": 18054, "mtime": 1750163343664, "results": "260", "hashOfConfig": "161"}, {"size": 2950, "mtime": 1750162078997, "results": "261", "hashOfConfig": "161"}, {"size": 938, "mtime": 1750162079095, "results": "262", "hashOfConfig": "161"}, {"size": 211, "mtime": 1750162079017, "results": "263", "hashOfConfig": "161"}, {"size": 6398, "mtime": 1753615402623, "results": "264", "hashOfConfig": "161"}, {"size": 225, "mtime": 1750162079019, "results": "265", "hashOfConfig": "161"}, {"size": 4612, "mtime": 1750486746007, "results": "266", "hashOfConfig": "161"}, {"size": 831, "mtime": 1750162079019, "results": "267", "hashOfConfig": "161"}, {"size": 4793, "mtime": 1750163289799, "results": "268", "hashOfConfig": "161"}, {"size": 12075, "mtime": 1750163272446, "results": "269", "hashOfConfig": "161"}, {"size": 6897, "mtime": 1750163306463, "results": "270", "hashOfConfig": "161"}, {"size": 3348, "mtime": 1750162079002, "results": "271", "hashOfConfig": "161"}, {"size": 1056, "mtime": 1750162079048, "results": "272", "hashOfConfig": "161"}, {"size": 1842, "mtime": 1750162079037, "results": "273", "hashOfConfig": "161"}, {"size": 3656, "mtime": 1750162079039, "results": "274", "hashOfConfig": "161"}, {"size": 4224, "mtime": 1750162079030, "results": "275", "hashOfConfig": "161"}, {"size": 1616, "mtime": 1750162079025, "results": "276", "hashOfConfig": "161"}, {"size": 937, "mtime": 1750162079029, "results": "277", "hashOfConfig": "161"}, {"size": 1738, "mtime": 1750162079038, "results": "278", "hashOfConfig": "161"}, {"size": 662, "mtime": 1750162079047, "results": "279", "hashOfConfig": "161"}, {"size": 550, "mtime": 1750162079020, "results": "280", "hashOfConfig": "161"}, {"size": 519, "mtime": 1750162079047, "results": "281", "hashOfConfig": "161"}, {"size": 1921, "mtime": 1750265054387, "results": "282", "hashOfConfig": "161"}, {"size": 577, "mtime": 1750162079039, "results": "283", "hashOfConfig": "161"}, {"size": 1773, "mtime": 1750162079025, "results": "284", "hashOfConfig": "161"}, {"size": 503, "mtime": 1750523557957, "results": "285", "hashOfConfig": "161"}, {"size": 6750, "mtime": 1750251645533, "results": "286", "hashOfConfig": "161"}, {"size": 4294, "mtime": 1753615413502, "results": "287", "hashOfConfig": "161"}, {"size": 3765, "mtime": 1750163361559, "results": "288", "hashOfConfig": "161"}, {"size": 615, "mtime": 1750162079034, "results": "289", "hashOfConfig": "161"}, {"size": 853, "mtime": 1750162079036, "results": "290", "hashOfConfig": "161"}, {"size": 857, "mtime": 1750162079033, "results": "291", "hashOfConfig": "161"}, {"size": 564, "mtime": 1750162079037, "results": "292", "hashOfConfig": "161"}, {"size": 594, "mtime": 1750162079035, "results": "293", "hashOfConfig": "161"}, {"size": 750, "mtime": 1750162079035, "results": "294", "hashOfConfig": "161"}, {"size": 589, "mtime": 1750162079034, "results": "295", "hashOfConfig": "161"}, {"size": 492, "mtime": 1750162079042, "results": "296", "hashOfConfig": "161"}, {"size": 810, "mtime": 1750162079041, "results": "297", "hashOfConfig": "161"}, {"size": 490, "mtime": 1750162079042, "results": "298", "hashOfConfig": "161"}, {"size": 492, "mtime": 1750162079041, "results": "299", "hashOfConfig": "161"}, {"size": 840, "mtime": 1750162079043, "results": "300", "hashOfConfig": "161"}, {"size": 1776, "mtime": 1750162079045, "results": "301", "hashOfConfig": "161"}, {"size": 896, "mtime": 1750162079044, "results": "302", "hashOfConfig": "161"}, {"size": 548, "mtime": 1750162079044, "results": "303", "hashOfConfig": "161"}, {"size": 825, "mtime": 1750162079046, "results": "304", "hashOfConfig": "161"}, {"size": 1014, "mtime": 1750162079045, "results": "305", "hashOfConfig": "161"}, {"size": 5638, "mtime": 1753617596678, "results": "306", "hashOfConfig": "161"}, {"size": 5701, "mtime": 1753621506072, "results": "307", "hashOfConfig": "161"}, {"size": 6823, "mtime": 1751280498212, "results": "308", "hashOfConfig": "161"}, {"size": 4794, "mtime": 1751272383355, "results": "309", "hashOfConfig": "161"}, {"size": 3832, "mtime": 1751274488484, "results": "310", "hashOfConfig": "161"}, {"size": 6866, "mtime": 1751269538235, "results": "311", "hashOfConfig": "161"}, {"size": 2091, "mtime": 1751304469748, "results": "312", "hashOfConfig": "161"}, {"size": 4523, "mtime": 1752327988910, "results": "313", "hashOfConfig": "161"}, {"size": 21251, "mtime": 1752953246619, "results": "314", "hashOfConfig": "161"}, {"size": 41014, "mtime": 1753477116038, "results": "315", "hashOfConfig": "161"}, {"size": 3682, "mtime": 1753740489711, "results": "316", "hashOfConfig": "161"}, {"size": 4711, "mtime": 1753822881474, "results": "317", "hashOfConfig": "161"}, {"size": 4729, "mtime": 1753822989202, "results": "318", "hashOfConfig": "161"}, {"size": 2037, "mtime": 1753743828980, "results": "319", "hashOfConfig": "161"}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kyl3u4", {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 41, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\xampp\\htdocs\\allemnionline\\client\\src\\index.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\App.js", ["797", "798", "799"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\config\\axios.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\i18n.js", ["800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\reportWebVitals.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\ResizeObserverFix.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\resizeObserver.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\AuthContext.js", ["841"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Footer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\SocketContext.js", ["842"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\UnreadMessagesContext.js", ["843"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AuthenticatedFooter.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\layout\\Header.js", ["844", "845", "846"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\Home.js", ["847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PrivacyPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\AboutUs.js", ["861", "862", "863", "864", "865", "866"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PlatformPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\FindTeacher.js", ["867", "868", "869", "870", "871", "872", "873"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TeacherDetails.js", ["874", "875", "876", "877"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\StudentRegister.js", ["878"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\TeacherRegister.js", ["879"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\Login.js", ["880", "881", "882"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyResetCode.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ResetPassword.js", ["883"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ForgotPassword.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\RegisterChoice.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyEmail.js", ["884", "885"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Dashboard.js", ["886", "887", "888"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Students.js", ["889", "890"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Profile.js", ["891"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Teachers.js", ["892", "893", "894", "895", "896"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\TeacherApplications.js", ["897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Languages.js", ["909"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Categories.js", ["910"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\ProfileUpdates.js", ["911", "912"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingSessions.js", ["913", "914"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Wallet.js", ["915"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Messages.js", ["916", "917"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\AdminEarnings.js", ["918"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Dashboard.js", ["919"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\WithdrawalManagement.js", ["920"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditVideoUpload.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditApplication.js", ["921", "922", "923", "924", "925"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Application.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\TeacherAvailableHours.js", ["926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ViewAvailableHours.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\VideoUpload.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\AvailableHours.js", ["944", "945", "946", "947", "948", "949", "950", "951"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Meetings.js", ["952", "953", "954", "955", "956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Bookings.js", ["969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Chat.js", ["984", "985", "986", "987"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Profile.js", ["988", "989", "990", "991", "992", "993", "994", "995"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyLessons.js", ["996"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Wallet.js", ["997", "998"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ContactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Withdrawal.js", ["999"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyMessages.js", ["1000"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Reviews.js", ["1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Profile.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Dashboard.js", ["1010"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\CompleteProfile.js", ["1011"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\FindTeacher.js", ["1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\TeacherProfile.js", ["1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ChatEmbed.js", ["1041", "1042"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Chat.js", ["1043", "1044", "1045"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ContactUs.js", ["1046"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyMessages.js", ["1047"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\JoinMeeting.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyTeachers.js", ["1048", "1049", "1050"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Meetings.js", ["1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Wallet.js", ["1060"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\BookingPage.js", ["1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Bookings.js", ["1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\WriteReview.js", ["1090", "1091", "1092", "1093"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AvailableHoursTable.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Layout.js", ["1094", "1095", "1096", "1097", "1098"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axios.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\timezone.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\WeeklyBookingsTable.js", ["1099", "1100", "1101", "1102"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\constants.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\BookableHoursTable.js", ["1103", "1104", "1105", "1106", "1107"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\StripePayment.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatWindow.js", ["1108", "1109", "1110"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatList.js", ["1111", "1112"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\auth\\GenderDialog.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\CropImageDialog.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationStatus.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationForm.js", ["1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\Spinner.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\VideoSDKMeeting.js", ["1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\student\\ProfileCompletionAlert.js", ["1132"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\api.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingAppContext.js", ["1133"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingContainer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\JoiningScreen.js", ["1134", "1135", "1136", "1137", "1138"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\WaitingToJoinScreen.js", ["1139"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\LeaveScreen.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\PresenterView.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\SimpleBottomBar.js", ["1140"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ParticipantView.js", ["1141"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ConfirmBox.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\helper.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsMobile.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\SidebarContainer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsTab.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\MeetingDetailsScreen.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useMediaStream.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownCam.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDown.js", ["1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownSpeaker.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\NetworkStats.js", ["1150"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\WebcamOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicPermissionDenied.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\CameraPermissionDenied.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\WebcamOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffSmallIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\SpeakerIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useWindowSize.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ScreenShareIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\common.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\PipIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ChatPanel.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\NotesPanel.js", ["1151"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ParticipantPanel.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropMIC.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMicOff.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropCAM.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestSpeaker.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMic.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\PauseButton.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropSpeaker.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\UploadIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshCheck.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\DownloadIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\WifiOff.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\RaiseHand.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingPaymentPolicy.js", ["1152", "1153", "1154", "1155", "1156", "1157"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\RefundPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingCancellationPolicy.js", ["1158", "1159", "1160", "1161", "1162", "1163"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\ContactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TermsAndConditions.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\termsConditions.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\contactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\MeetingFeedbackDialog.js", ["1164"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingIssues.js", ["1165"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\RescheduleDialog.js", ["1166", "1167", "1168", "1169"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\UserStatusChecker.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\ProtectedRoute.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\userStatusHandler.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axiosConfig.js", [], [], {"ruleId": "1170", "severity": 1, "message": "1171", "line": 98, "column": 11, "nodeType": "1172", "messageId": "1173", "endLine": 98, "endColumn": 22}, {"ruleId": "1170", "severity": 1, "message": "1174", "line": 98, "column": 24, "nodeType": "1172", "messageId": "1173", "endLine": 98, "endColumn": 39}, {"ruleId": "1170", "severity": 1, "message": "1175", "line": 99, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 99, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1176", "line": 3, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 3, "endColumn": 23}, {"ruleId": "1170", "severity": 1, "message": "1177", "line": 4, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 4, "endColumn": 23}, {"ruleId": "1178", "severity": 1, "message": "1179", "line": 63, "column": 24, "nodeType": "1180", "messageId": "1181", "endLine": 63, "endColumn": 66}, {"ruleId": "1182", "severity": 1, "message": "1183", "line": 299, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 299, "endColumn": 16}, {"ruleId": "1182", "severity": 1, "message": "1186", "line": 329, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 329, "endColumn": 17}, {"ruleId": "1182", "severity": 1, "message": "1187", "line": 587, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 587, "endColumn": 24}, {"ruleId": "1182", "severity": 1, "message": "1188", "line": 595, "column": 7, "nodeType": "1184", "messageId": "1185", "endLine": 595, "endColumn": 14}, {"ruleId": "1178", "severity": 1, "message": "1179", "line": 627, "column": 30, "nodeType": "1180", "messageId": "1181", "endLine": 627, "endColumn": 107}, {"ruleId": "1182", "severity": 1, "message": "1189", "line": 646, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 646, "endColumn": 23}, {"ruleId": "1182", "severity": 1, "message": "1190", "line": 715, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 715, "endColumn": 23}, {"ruleId": "1182", "severity": 1, "message": "1191", "line": 827, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 827, "endColumn": 20}, {"ruleId": "1182", "severity": 1, "message": "1192", "line": 828, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 828, "endColumn": 27}, {"ruleId": "1182", "severity": 1, "message": "1193", "line": 915, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 915, "endColumn": 22}, {"ruleId": "1182", "severity": 1, "message": "1194", "line": 1066, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 1066, "endColumn": 23}, {"ruleId": "1182", "severity": 1, "message": "1195", "line": 1067, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 1067, "endColumn": 26}, {"ruleId": "1182", "severity": 1, "message": "1196", "line": 1069, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 1069, "endColumn": 23}, {"ruleId": "1182", "severity": 1, "message": "1197", "line": 1128, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 1128, "endColumn": 22}, {"ruleId": "1182", "severity": 1, "message": "1198", "line": 1135, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 1135, "endColumn": 23}, {"ruleId": "1182", "severity": 1, "message": "1199", "line": 1136, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 1136, "endColumn": 20}, {"ruleId": "1182", "severity": 1, "message": "1200", "line": 1261, "column": 7, "nodeType": "1184", "messageId": "1185", "endLine": 1261, "endColumn": 12}, {"ruleId": "1182", "severity": 1, "message": "1201", "line": 1913, "column": 7, "nodeType": "1184", "messageId": "1185", "endLine": 1913, "endColumn": 12}, {"ruleId": "1178", "severity": 1, "message": "1179", "line": 1995, "column": 24, "nodeType": "1180", "messageId": "1181", "endLine": 1995, "endColumn": 58}, {"ruleId": "1182", "severity": 1, "message": "1202", "line": 2013, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 2013, "endColumn": 24}, {"ruleId": "1182", "severity": 1, "message": "1189", "line": 2103, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 2103, "endColumn": 23}, {"ruleId": "1182", "severity": 1, "message": "1183", "line": 2240, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 2240, "endColumn": 16}, {"ruleId": "1182", "severity": 1, "message": "1187", "line": 2526, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 2526, "endColumn": 24}, {"ruleId": "1182", "severity": 1, "message": "1188", "line": 2534, "column": 7, "nodeType": "1184", "messageId": "1185", "endLine": 2534, "endColumn": 14}, {"ruleId": "1182", "severity": 1, "message": "1189", "line": 2586, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 2586, "endColumn": 23}, {"ruleId": "1182", "severity": 1, "message": "1190", "line": 2655, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 2655, "endColumn": 23}, {"ruleId": "1182", "severity": 1, "message": "1191", "line": 2771, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 2771, "endColumn": 20}, {"ruleId": "1182", "severity": 1, "message": "1192", "line": 2772, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 2772, "endColumn": 27}, {"ruleId": "1182", "severity": 1, "message": "1193", "line": 2855, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 2855, "endColumn": 22}, {"ruleId": "1182", "severity": 1, "message": "1194", "line": 3006, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 3006, "endColumn": 23}, {"ruleId": "1182", "severity": 1, "message": "1195", "line": 3007, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 3007, "endColumn": 26}, {"ruleId": "1182", "severity": 1, "message": "1196", "line": 3009, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 3009, "endColumn": 23}, {"ruleId": "1182", "severity": 1, "message": "1197", "line": 3077, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 3077, "endColumn": 22}, {"ruleId": "1182", "severity": 1, "message": "1198", "line": 3095, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 3095, "endColumn": 23}, {"ruleId": "1182", "severity": 1, "message": "1199", "line": 3096, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 3096, "endColumn": 20}, {"ruleId": "1182", "severity": 1, "message": "1200", "line": 3204, "column": 7, "nodeType": "1184", "messageId": "1185", "endLine": 3204, "endColumn": 12}, {"ruleId": "1182", "severity": 1, "message": "1201", "line": 3677, "column": 7, "nodeType": "1184", "messageId": "1185", "endLine": 3677, "endColumn": 12}, {"ruleId": "1182", "severity": 1, "message": "1203", "line": 4162, "column": 9, "nodeType": "1184", "messageId": "1185", "endLine": 4162, "endColumn": 17}, {"ruleId": "1204", "severity": 1, "message": "1205", "line": 101, "column": 6, "nodeType": "1206", "endLine": 101, "endColumn": 8, "suggestions": "1207"}, {"ruleId": "1204", "severity": 1, "message": "1208", "line": 97, "column": 6, "nodeType": "1206", "endLine": 97, "endColumn": 43, "suggestions": "1209"}, {"ruleId": "1204", "severity": 1, "message": "1210", "line": 101, "column": 6, "nodeType": "1206", "endLine": 101, "endColumn": 40, "suggestions": "1211"}, {"ruleId": "1182", "severity": 1, "message": "1212", "line": 431, "column": 19, "nodeType": "1184", "messageId": "1185", "endLine": 431, "endColumn": 22}, {"ruleId": "1182", "severity": 1, "message": "1212", "line": 453, "column": 19, "nodeType": "1184", "messageId": "1185", "endLine": 453, "endColumn": 22}, {"ruleId": "1182", "severity": 1, "message": "1212", "line": 519, "column": 21, "nodeType": "1184", "messageId": "1185", "endLine": 519, "endColumn": 24}, {"ruleId": "1170", "severity": 1, "message": "1213", "line": 13, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 13, "endColumn": 12}, {"ruleId": "1170", "severity": 1, "message": "1214", "line": 14, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 14, "endColumn": 13}, {"ruleId": "1170", "severity": 1, "message": "1215", "line": 19, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 19, "endColumn": 8}, {"ruleId": "1170", "severity": 1, "message": "1216", "line": 23, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 23, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1217", "line": 28, "column": 15, "nodeType": "1172", "messageId": "1173", "endLine": 28, "endColumn": 27}, {"ruleId": "1170", "severity": 1, "message": "1218", "line": 33, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 33, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1219", "line": 34, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 34, "endColumn": 11}, {"ruleId": "1170", "severity": 1, "message": "1220", "line": 35, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 35, "endColumn": 15}, {"ruleId": "1170", "severity": 1, "message": "1221", "line": 38, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 38, "endColumn": 11}, {"ruleId": "1170", "severity": 1, "message": "1222", "line": 40, "column": 16, "nodeType": "1172", "messageId": "1173", "endLine": 40, "endColumn": 29}, {"ruleId": "1170", "severity": 1, "message": "1223", "line": 42, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 42, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1224", "line": 327, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 327, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1225", "line": 368, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 368, "endColumn": 29}, {"ruleId": "1226", "severity": 1, "message": "1227", "line": 371, "column": 5, "nodeType": "1172", "messageId": "1228", "endLine": 371, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1229", "line": 6, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 6, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1230", "line": 7, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 7, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1217", "line": 17, "column": 15, "nodeType": "1172", "messageId": "1173", "endLine": 17, "endColumn": 27}, {"ruleId": "1170", "severity": 1, "message": "1231", "line": 18, "column": 13, "nodeType": "1172", "messageId": "1173", "endLine": 18, "endColumn": 23}, {"ruleId": "1170", "severity": 1, "message": "1232", "line": 20, "column": 13, "nodeType": "1172", "messageId": "1173", "endLine": 20, "endColumn": 23}, {"ruleId": "1170", "severity": 1, "message": "1233", "line": 21, "column": 15, "nodeType": "1172", "messageId": "1173", "endLine": 21, "endColumn": 27}, {"ruleId": "1170", "severity": 1, "message": "1234", "line": 9, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 9, "endColumn": 12}, {"ruleId": "1170", "severity": 1, "message": "1235", "line": 26, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 26, "endColumn": 9}, {"ruleId": "1170", "severity": 1, "message": "1236", "line": 31, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 31, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1237", "line": 34, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 34, "endColumn": 19}, {"ruleId": "1170", "severity": 1, "message": "1238", "line": 40, "column": 11, "nodeType": "1172", "messageId": "1173", "endLine": 40, "endColumn": 19}, {"ruleId": "1204", "severity": 1, "message": "1239", "line": 310, "column": 6, "nodeType": "1206", "endLine": 310, "endColumn": 54, "suggestions": "1240"}, {"ruleId": "1170", "severity": 1, "message": "1241", "line": 367, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 367, "endColumn": 26}, {"ruleId": "1170", "severity": 1, "message": "1242", "line": 40, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 40, "endColumn": 14}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 93, "column": 66, "nodeType": "1180", "messageId": "1245", "endLine": 93, "endColumn": 67, "suggestions": "1246"}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 93, "column": 75, "nodeType": "1180", "messageId": "1245", "endLine": 93, "endColumn": 76, "suggestions": "1247"}, {"ruleId": "1243", "severity": 1, "message": "1248", "line": 93, "column": 77, "nodeType": "1180", "messageId": "1245", "endLine": 93, "endColumn": 78, "suggestions": "1249"}, {"ruleId": "1170", "severity": 1, "message": "1250", "line": 37, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 37, "endColumn": 15}, {"ruleId": "1170", "severity": 1, "message": "1250", "line": 37, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 37, "endColumn": 15}, {"ruleId": "1170", "severity": 1, "message": "1251", "line": 1, "column": 27, "nodeType": "1172", "messageId": "1173", "endLine": 1, "endColumn": 33}, {"ruleId": "1170", "severity": 1, "message": "1252", "line": 30, "column": 13, "nodeType": "1172", "messageId": "1173", "endLine": 30, "endColumn": 23}, {"ruleId": "1204", "severity": 1, "message": "1253", "line": 110, "column": 6, "nodeType": "1206", "endLine": 110, "endColumn": 37, "suggestions": "1254"}, {"ruleId": "1170", "severity": 1, "message": "1255", "line": 2, "column": 23, "nodeType": "1172", "messageId": "1173", "endLine": 2, "endColumn": 27}, {"ruleId": "1170", "severity": 1, "message": "1256", "line": 23, "column": 15, "nodeType": "1172", "messageId": "1173", "endLine": 23, "endColumn": 27}, {"ruleId": "1170", "severity": 1, "message": "1250", "line": 40, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 40, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1257", "line": 32, "column": 15, "nodeType": "1172", "messageId": "1173", "endLine": 32, "endColumn": 27}, {"ruleId": "1170", "severity": 1, "message": "1258", "line": 36, "column": 18, "nodeType": "1172", "messageId": "1173", "endLine": 36, "endColumn": 33}, {"ruleId": "1170", "severity": 1, "message": "1259", "line": 37, "column": 20, "nodeType": "1172", "messageId": "1173", "endLine": 37, "endColumn": 37}, {"ruleId": "1170", "severity": 1, "message": "1260", "line": 27, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 27, "endColumn": 8}, {"ruleId": "1204", "severity": 1, "message": "1261", "line": 82, "column": 6, "nodeType": "1206", "endLine": 82, "endColumn": 41, "suggestions": "1262"}, {"ruleId": "1170", "severity": 1, "message": "1263", "line": 15, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 15, "endColumn": 19}, {"ruleId": "1170", "severity": 1, "message": "1264", "line": 26, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 26, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1265", "line": 27, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 27, "endColumn": 13}, {"ruleId": "1170", "severity": 1, "message": "1266", "line": 28, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 28, "endColumn": 9}, {"ruleId": "1170", "severity": 1, "message": "1267", "line": 29, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 29, "endColumn": 11}, {"ruleId": "1204", "severity": 1, "message": "1268", "line": 84, "column": 6, "nodeType": "1206", "endLine": 84, "endColumn": 41, "suggestions": "1269"}, {"ruleId": "1170", "severity": 1, "message": "1229", "line": 21, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 21, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1230", "line": 22, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 22, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1236", "line": 32, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 32, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1270", "line": 39, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 39, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1271", "line": 58, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 58, "endColumn": 16}, {"ruleId": "1170", "severity": 1, "message": "1272", "line": 70, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 70, "endColumn": 25}, {"ruleId": "1170", "severity": 1, "message": "1273", "line": 190, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 190, "endColumn": 30}, {"ruleId": "1170", "severity": 1, "message": "1274", "line": 194, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 194, "endColumn": 31}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 203, "column": 66, "nodeType": "1180", "messageId": "1245", "endLine": 203, "endColumn": 67, "suggestions": "1275"}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 203, "column": 75, "nodeType": "1180", "messageId": "1245", "endLine": 203, "endColumn": 76, "suggestions": "1276"}, {"ruleId": "1243", "severity": 1, "message": "1248", "line": 203, "column": 77, "nodeType": "1180", "messageId": "1245", "endLine": 203, "endColumn": 78, "suggestions": "1277"}, {"ruleId": "1170", "severity": 1, "message": "1278", "line": 253, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 253, "endColumn": 22}, {"ruleId": "1204", "severity": 1, "message": "1279", "line": 43, "column": 6, "nodeType": "1206", "endLine": 43, "endColumn": 8, "suggestions": "1280"}, {"ruleId": "1204", "severity": 1, "message": "1281", "line": 43, "column": 6, "nodeType": "1206", "endLine": 43, "endColumn": 8, "suggestions": "1282"}, {"ruleId": "1170", "severity": 1, "message": "1283", "line": 39, "column": 11, "nodeType": "1172", "messageId": "1173", "endLine": 39, "endColumn": 12}, {"ruleId": "1204", "severity": 1, "message": "1284", "line": 88, "column": 6, "nodeType": "1206", "endLine": 88, "endColumn": 39, "suggestions": "1285"}, {"ruleId": "1170", "severity": 1, "message": "1286", "line": 40, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 40, "endColumn": 24}, {"ruleId": "1204", "severity": 1, "message": "1287", "line": 107, "column": 6, "nodeType": "1206", "endLine": 107, "endColumn": 15, "suggestions": "1288"}, {"ruleId": "1204", "severity": 1, "message": "1289", "line": 45, "column": 6, "nodeType": "1206", "endLine": 45, "endColumn": 45, "suggestions": "1290"}, {"ruleId": "1170", "severity": 1, "message": "1291", "line": 33, "column": 12, "nodeType": "1172", "messageId": "1173", "endLine": 33, "endColumn": 21}, {"ruleId": "1204", "severity": 1, "message": "1292", "line": 103, "column": 6, "nodeType": "1206", "endLine": 103, "endColumn": 42, "suggestions": "1293"}, {"ruleId": "1204", "severity": 1, "message": "1294", "line": 50, "column": 6, "nodeType": "1206", "endLine": 50, "endColumn": 25, "suggestions": "1295"}, {"ruleId": "1170", "severity": 1, "message": "1296", "line": 54, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 54, "endColumn": 17}, {"ruleId": "1204", "severity": 1, "message": "1297", "line": 60, "column": 6, "nodeType": "1206", "endLine": 60, "endColumn": 39, "suggestions": "1298"}, {"ruleId": "1170", "severity": 1, "message": "1299", "line": 31, "column": 24, "nodeType": "1172", "messageId": "1173", "endLine": 31, "endColumn": 34}, {"ruleId": "1204", "severity": 1, "message": "1300", "line": 110, "column": 6, "nodeType": "1206", "endLine": 110, "endColumn": 22, "suggestions": "1301"}, {"ruleId": "1204", "severity": 1, "message": "1300", "line": 133, "column": 6, "nodeType": "1206", "endLine": 133, "endColumn": 19, "suggestions": "1302"}, {"ruleId": "1204", "severity": 1, "message": "1300", "line": 145, "column": 6, "nodeType": "1206", "endLine": 145, "endColumn": 19, "suggestions": "1303"}, {"ruleId": "1204", "severity": 1, "message": "1300", "line": 162, "column": 6, "nodeType": "1206", "endLine": 162, "endColumn": 19, "suggestions": "1304"}, {"ruleId": "1170", "severity": 1, "message": "1305", "line": 10, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 10, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1306", "line": 13, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 13, "endColumn": 8}, {"ruleId": "1170", "severity": 1, "message": "1307", "line": 14, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 14, "endColumn": 12}, {"ruleId": "1170", "severity": 1, "message": "1308", "line": 15, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 15, "endColumn": 12}, {"ruleId": "1170", "severity": 1, "message": "1309", "line": 16, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 16, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1310", "line": 17, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 17, "endColumn": 12}, {"ruleId": "1170", "severity": 1, "message": "1311", "line": 18, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 18, "endColumn": 11}, {"ruleId": "1170", "severity": 1, "message": "1214", "line": 20, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 20, "endColumn": 13}, {"ruleId": "1170", "severity": 1, "message": "1236", "line": 22, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 22, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1312", "line": 31, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 31, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1313", "line": 52, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 52, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1314", "line": 52, "column": 23, "nodeType": "1172", "messageId": "1173", "endLine": 52, "endColumn": 37}, {"ruleId": "1170", "severity": 1, "message": "1315", "line": 53, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 53, "endColumn": 22}, {"ruleId": "1170", "severity": 1, "message": "1316", "line": 53, "column": 24, "nodeType": "1172", "messageId": "1173", "endLine": 53, "endColumn": 39}, {"ruleId": "1170", "severity": 1, "message": "1317", "line": 227, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 227, "endColumn": 24}, {"ruleId": "1170", "severity": 1, "message": "1318", "line": 236, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 236, "endColumn": 23}, {"ruleId": "1170", "severity": 1, "message": "1319", "line": 245, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 245, "endColumn": 29}, {"ruleId": "1170", "severity": 1, "message": "1320", "line": 258, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 258, "endColumn": 28}, {"ruleId": "1170", "severity": 1, "message": "1321", "line": 2, "column": 23, "nodeType": "1172", "messageId": "1173", "endLine": 2, "endColumn": 34}, {"ruleId": "1170", "severity": 1, "message": "1322", "line": 4, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 4, "endColumn": 13}, {"ruleId": "1170", "severity": 1, "message": "1214", "line": 13, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 13, "endColumn": 13}, {"ruleId": "1170", "severity": 1, "message": "1236", "line": 15, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 15, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1313", "line": 45, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 45, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1314", "line": 45, "column": 23, "nodeType": "1172", "messageId": "1173", "endLine": 45, "endColumn": 37}, {"ruleId": "1170", "severity": 1, "message": "1315", "line": 46, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 46, "endColumn": 22}, {"ruleId": "1170", "severity": 1, "message": "1316", "line": 46, "column": 24, "nodeType": "1172", "messageId": "1173", "endLine": 46, "endColumn": 39}, {"ruleId": "1170", "severity": 1, "message": "1323", "line": 5, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 5, "endColumn": 9}, {"ruleId": "1170", "severity": 1, "message": "1234", "line": 7, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 7, "endColumn": 12}, {"ruleId": "1170", "severity": 1, "message": "1229", "line": 9, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 9, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1230", "line": 10, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 10, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1305", "line": 11, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 11, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1264", "line": 16, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 16, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1265", "line": 17, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 17, "endColumn": 13}, {"ruleId": "1170", "severity": 1, "message": "1266", "line": 18, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 18, "endColumn": 9}, {"ruleId": "1170", "severity": 1, "message": "1267", "line": 19, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 19, "endColumn": 11}, {"ruleId": "1170", "severity": 1, "message": "1324", "line": 33, "column": 18, "nodeType": "1172", "messageId": "1173", "endLine": 33, "endColumn": 33}, {"ruleId": "1170", "severity": 1, "message": "1325", "line": 46, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 46, "endColumn": 33}, {"ruleId": "1170", "severity": 1, "message": "1326", "line": 46, "column": 35, "nodeType": "1172", "messageId": "1173", "endLine": 46, "endColumn": 59}, {"ruleId": "1170", "severity": 1, "message": "1171", "line": 51, "column": 11, "nodeType": "1172", "messageId": "1173", "endLine": 51, "endColumn": 22}, {"ruleId": "1170", "severity": 1, "message": "1327", "line": 58, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 58, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1328", "line": 110, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 110, "endColumn": 23}, {"ruleId": "1170", "severity": 1, "message": "1329", "line": 154, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 154, "endColumn": 23}, {"ruleId": "1170", "severity": 1, "message": "1330", "line": 215, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 215, "endColumn": 26}, {"ruleId": "1170", "severity": 1, "message": "1264", "line": 21, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 21, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1265", "line": 22, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 22, "endColumn": 13}, {"ruleId": "1170", "severity": 1, "message": "1266", "line": 23, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 23, "endColumn": 9}, {"ruleId": "1170", "severity": 1, "message": "1267", "line": 24, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 24, "endColumn": 11}, {"ruleId": "1170", "severity": 1, "message": "1325", "line": 46, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 46, "endColumn": 33}, {"ruleId": "1170", "severity": 1, "message": "1326", "line": 46, "column": 64, "nodeType": "1172", "messageId": "1173", "endLine": 46, "endColumn": 88}, {"ruleId": "1170", "severity": 1, "message": "1327", "line": 67, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 67, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1331", "line": 72, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 72, "endColumn": 30}, {"ruleId": "1170", "severity": 1, "message": "1313", "line": 73, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 73, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1332", "line": 74, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 74, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1333", "line": 75, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 75, "endColumn": 22}, {"ruleId": "1170", "severity": 1, "message": "1334", "line": 75, "column": 24, "nodeType": "1172", "messageId": "1173", "endLine": 75, "endColumn": 39}, {"ruleId": "1204", "severity": 1, "message": "1335", "line": 171, "column": 6, "nodeType": "1206", "endLine": 171, "endColumn": 16, "suggestions": "1336"}, {"ruleId": "1204", "severity": 1, "message": "1337", "line": 178, "column": 6, "nodeType": "1206", "endLine": 178, "endColumn": 31, "suggestions": "1338"}, {"ruleId": "1170", "severity": 1, "message": "1339", "line": 478, "column": 13, "nodeType": "1172", "messageId": "1173", "endLine": 478, "endColumn": 21}, {"ruleId": "1204", "severity": 1, "message": "1239", "line": 180, "column": 6, "nodeType": "1206", "endLine": 180, "endColumn": 28, "suggestions": "1340"}, {"ruleId": "1204", "severity": 1, "message": "1341", "line": 217, "column": 6, "nodeType": "1206", "endLine": 217, "endColumn": 28, "suggestions": "1342"}, {"ruleId": "1204", "severity": 1, "message": "1343", "line": 257, "column": 6, "nodeType": "1206", "endLine": 257, "endColumn": 14, "suggestions": "1344"}, {"ruleId": "1204", "severity": 1, "message": "1345", "line": 324, "column": 6, "nodeType": "1206", "endLine": 324, "endColumn": 53, "suggestions": "1346"}, {"ruleId": "1170", "severity": 1, "message": "1236", "line": 14, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 14, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1217", "line": 30, "column": 15, "nodeType": "1172", "messageId": "1173", "endLine": 30, "endColumn": 27}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 71, "column": 64, "nodeType": "1180", "messageId": "1245", "endLine": 71, "endColumn": 65, "suggestions": "1347"}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 71, "column": 73, "nodeType": "1180", "messageId": "1245", "endLine": 71, "endColumn": 74, "suggestions": "1348"}, {"ruleId": "1243", "severity": 1, "message": "1248", "line": 71, "column": 75, "nodeType": "1180", "messageId": "1245", "endLine": 71, "endColumn": 76, "suggestions": "1349"}, {"ruleId": "1170", "severity": 1, "message": "1299", "line": 92, "column": 17, "nodeType": "1172", "messageId": "1173", "endLine": 92, "endColumn": 27}, {"ruleId": "1170", "severity": 1, "message": "1250", "line": 93, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 93, "endColumn": 15}, {"ruleId": "1170", "severity": 1, "message": "1350", "line": 93, "column": 17, "nodeType": "1172", "messageId": "1173", "endLine": 93, "endColumn": 25}, {"ruleId": "1170", "severity": 1, "message": "1351", "line": 25, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 25, "endColumn": 12}, {"ruleId": "1170", "severity": 1, "message": "1258", "line": 22, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 22, "endColumn": 23}, {"ruleId": "1204", "severity": 1, "message": "1289", "line": 46, "column": 6, "nodeType": "1206", "endLine": 46, "endColumn": 45, "suggestions": "1352"}, {"ruleId": "1204", "severity": 1, "message": "1353", "line": 65, "column": 6, "nodeType": "1206", "endLine": 65, "endColumn": 45, "suggestions": "1354"}, {"ruleId": "1204", "severity": 1, "message": "1292", "line": 83, "column": 6, "nodeType": "1206", "endLine": 83, "endColumn": 32, "suggestions": "1355"}, {"ruleId": "1170", "severity": 1, "message": "1356", "line": 17, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 17, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1357", "line": 29, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 29, "endColumn": 11}, {"ruleId": "1170", "severity": 1, "message": "1238", "line": 32, "column": 11, "nodeType": "1172", "messageId": "1173", "endLine": 32, "endColumn": 19}, {"ruleId": "1170", "severity": 1, "message": "1358", "line": 33, "column": 17, "nodeType": "1172", "messageId": "1173", "endLine": 33, "endColumn": 31}, {"ruleId": "1170", "severity": 1, "message": "1359", "line": 40, "column": 17, "nodeType": "1172", "messageId": "1173", "endLine": 40, "endColumn": 31}, {"ruleId": "1170", "severity": 1, "message": "1360", "line": 41, "column": 17, "nodeType": "1172", "messageId": "1173", "endLine": 41, "endColumn": 31}, {"ruleId": "1170", "severity": 1, "message": "1250", "line": 49, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 49, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1361", "line": 68, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 68, "endColumn": 25}, {"ruleId": "1170", "severity": 1, "message": "1362", "line": 289, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 289, "endColumn": 29}, {"ruleId": "1170", "severity": 1, "message": "1236", "line": 4, "column": 29, "nodeType": "1172", "messageId": "1173", "endLine": 4, "endColumn": 36}, {"ruleId": "1170", "severity": 1, "message": "1171", "line": 28, "column": 11, "nodeType": "1172", "messageId": "1173", "endLine": 28, "endColumn": 22}, {"ruleId": "1170", "severity": 1, "message": "1234", "line": 14, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 14, "endColumn": 12}, {"ruleId": "1170", "severity": 1, "message": "1235", "line": 31, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 31, "endColumn": 9}, {"ruleId": "1170", "severity": 1, "message": "1236", "line": 36, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 36, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1237", "line": 39, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 39, "endColumn": 19}, {"ruleId": "1170", "severity": 1, "message": "1363", "line": 42, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 42, "endColumn": 9}, {"ruleId": "1170", "severity": 1, "message": "1364", "line": 43, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 43, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1365", "line": 44, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 44, "endColumn": 16}, {"ruleId": "1170", "severity": 1, "message": "1366", "line": 45, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 45, "endColumn": 16}, {"ruleId": "1170", "severity": 1, "message": "1238", "line": 51, "column": 11, "nodeType": "1172", "messageId": "1173", "endLine": 51, "endColumn": 19}, {"ruleId": "1170", "severity": 1, "message": "1296", "line": 174, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 174, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1367", "line": 203, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 203, "endColumn": 19}, {"ruleId": "1204", "severity": 1, "message": "1268", "line": 298, "column": 6, "nodeType": "1206", "endLine": 298, "endColumn": 48, "suggestions": "1368"}, {"ruleId": "1170", "severity": 1, "message": "1241", "line": 337, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 337, "endColumn": 26}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 422, "column": 68, "nodeType": "1180", "messageId": "1245", "endLine": 422, "endColumn": 69, "suggestions": "1369"}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 422, "column": 77, "nodeType": "1180", "messageId": "1245", "endLine": 422, "endColumn": 78, "suggestions": "1370"}, {"ruleId": "1243", "severity": 1, "message": "1248", "line": 422, "column": 79, "nodeType": "1180", "messageId": "1245", "endLine": 422, "endColumn": 80, "suggestions": "1371"}, {"ruleId": "1170", "severity": 1, "message": "1229", "line": 15, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 15, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1230", "line": 16, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 16, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1366", "line": 30, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 30, "endColumn": 16}, {"ruleId": "1170", "severity": 1, "message": "1372", "line": 31, "column": 17, "nodeType": "1172", "messageId": "1173", "endLine": 31, "endColumn": 30}, {"ruleId": "1170", "severity": 1, "message": "1224", "line": 63, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 63, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1373", "line": 71, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 71, "endColumn": 24}, {"ruleId": "1170", "severity": 1, "message": "1374", "line": 72, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 72, "endColumn": 22}, {"ruleId": "1375", "severity": 1, "message": "1376", "line": 200, "column": 7, "nodeType": "1172", "messageId": "1377", "endLine": 200, "endColumn": 13}, {"ruleId": "1204", "severity": 1, "message": "1378", "line": 315, "column": 6, "nodeType": "1206", "endLine": 315, "endColumn": 39, "suggestions": "1379"}, {"ruleId": "1170", "severity": 1, "message": "1380", "line": 397, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 397, "endColumn": 23}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 487, "column": 68, "nodeType": "1180", "messageId": "1245", "endLine": 487, "endColumn": 69, "suggestions": "1381"}, {"ruleId": "1243", "severity": 1, "message": "1244", "line": 487, "column": 77, "nodeType": "1180", "messageId": "1245", "endLine": 487, "endColumn": 78, "suggestions": "1382"}, {"ruleId": "1243", "severity": 1, "message": "1248", "line": 487, "column": 79, "nodeType": "1180", "messageId": "1245", "endLine": 487, "endColumn": 80, "suggestions": "1383"}, {"ruleId": "1170", "severity": 1, "message": "1384", "line": 7, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 7, "endColumn": 13}, {"ruleId": "1170", "severity": 1, "message": "1385", "line": 22, "column": 19, "nodeType": "1172", "messageId": "1173", "endLine": 22, "endColumn": 30}, {"ruleId": "1204", "severity": 1, "message": "1239", "line": 196, "column": 6, "nodeType": "1206", "endLine": 196, "endColumn": 28, "suggestions": "1386"}, {"ruleId": "1204", "severity": 1, "message": "1341", "line": 233, "column": 6, "nodeType": "1206", "endLine": 233, "endColumn": 28, "suggestions": "1387"}, {"ruleId": "1204", "severity": 1, "message": "1343", "line": 273, "column": 6, "nodeType": "1206", "endLine": 273, "endColumn": 14, "suggestions": "1388"}, {"ruleId": "1170", "severity": 1, "message": "1389", "line": 24, "column": 24, "nodeType": "1172", "messageId": "1173", "endLine": 24, "endColumn": 29}, {"ruleId": "1204", "severity": 1, "message": "1292", "line": 84, "column": 6, "nodeType": "1206", "endLine": 84, "endColumn": 32, "suggestions": "1390"}, {"ruleId": "1170", "severity": 1, "message": "1305", "line": 5, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 5, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1229", "line": 6, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 6, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1255", "line": 20, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 20, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1391", "line": 27, "column": 18, "nodeType": "1172", "messageId": "1173", "endLine": 27, "endColumn": 27}, {"ruleId": "1170", "severity": 1, "message": "1392", "line": 29, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 29, "endColumn": 15}, {"ruleId": "1170", "severity": 1, "message": "1327", "line": 44, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 44, "endColumn": 21}, {"ruleId": "1204", "severity": 1, "message": "1393", "line": 66, "column": 6, "nodeType": "1206", "endLine": 66, "endColumn": 19, "suggestions": "1394"}, {"ruleId": "1170", "severity": 1, "message": "1395", "line": 127, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 127, "endColumn": 40}, {"ruleId": "1170", "severity": 1, "message": "1396", "line": 135, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 135, "endColumn": 43}, {"ruleId": "1170", "severity": 1, "message": "1397", "line": 147, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 147, "endColumn": 22}, {"ruleId": "1170", "severity": 1, "message": "1398", "line": 156, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 156, "endColumn": 40}, {"ruleId": "1170", "severity": 1, "message": "1399", "line": 184, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 184, "endColumn": 23}, {"ruleId": "1204", "severity": 1, "message": "1289", "line": 74, "column": 6, "nodeType": "1206", "endLine": 74, "endColumn": 45, "suggestions": "1400"}, {"ruleId": "1170", "severity": 1, "message": "1356", "line": 14, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 14, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1401", "line": 17, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 17, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1236", "line": 18, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 18, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1402", "line": 19, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 19, "endColumn": 9}, {"ruleId": "1170", "severity": 1, "message": "1403", "line": 49, "column": 40, "nodeType": "1172", "messageId": "1173", "endLine": 49, "endColumn": 49}, {"ruleId": "1170", "severity": 1, "message": "1404", "line": 51, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 51, "endColumn": 32}, {"ruleId": "1170", "severity": 1, "message": "1326", "line": 51, "column": 34, "nodeType": "1172", "messageId": "1173", "endLine": 51, "endColumn": 58}, {"ruleId": "1170", "severity": 1, "message": "1325", "line": 51, "column": 89, "nodeType": "1172", "messageId": "1173", "endLine": 51, "endColumn": 112}, {"ruleId": "1170", "severity": 1, "message": "1296", "line": 59, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 59, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1224", "line": 62, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 62, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1398", "line": 278, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 278, "endColumn": 40}, {"ruleId": "1170", "severity": 1, "message": "1405", "line": 409, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 409, "endColumn": 28}, {"ruleId": "1170", "severity": 1, "message": "1406", "line": 428, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 428, "endColumn": 35}, {"ruleId": "1170", "severity": 1, "message": "1407", "line": 451, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 451, "endColumn": 36}, {"ruleId": "1170", "severity": 1, "message": "1408", "line": 712, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 712, "endColumn": 29}, {"ruleId": "1170", "severity": 1, "message": "1264", "line": 25, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 25, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1265", "line": 26, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 26, "endColumn": 13}, {"ruleId": "1170", "severity": 1, "message": "1266", "line": 27, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 27, "endColumn": 9}, {"ruleId": "1170", "severity": 1, "message": "1267", "line": 28, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 28, "endColumn": 11}, {"ruleId": "1170", "severity": 1, "message": "1409", "line": 33, "column": 13, "nodeType": "1172", "messageId": "1173", "endLine": 33, "endColumn": 23}, {"ruleId": "1170", "severity": 1, "message": "1331", "line": 88, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 88, "endColumn": 30}, {"ruleId": "1170", "severity": 1, "message": "1313", "line": 89, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 89, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1332", "line": 90, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 90, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1333", "line": 91, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 91, "endColumn": 22}, {"ruleId": "1170", "severity": 1, "message": "1334", "line": 91, "column": 24, "nodeType": "1172", "messageId": "1173", "endLine": 91, "endColumn": 39}, {"ruleId": "1170", "severity": 1, "message": "1339", "line": 285, "column": 13, "nodeType": "1172", "messageId": "1173", "endLine": 285, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1410", "line": 622, "column": 12, "nodeType": "1172", "messageId": "1173", "endLine": 622, "endColumn": 20}, {"ruleId": "1170", "severity": 1, "message": "1339", "line": 643, "column": 13, "nodeType": "1172", "messageId": "1173", "endLine": 643, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1411", "line": 666, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 666, "endColumn": 23}, {"ruleId": "1170", "severity": 1, "message": "1238", "line": 27, "column": 11, "nodeType": "1172", "messageId": "1173", "endLine": 27, "endColumn": 19}, {"ruleId": "1170", "severity": 1, "message": "1296", "line": 36, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 36, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1250", "line": 37, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 37, "endColumn": 14}, {"ruleId": "1204", "severity": 1, "message": "1412", "line": 467, "column": 6, "nodeType": "1206", "endLine": 467, "endColumn": 32, "suggestions": "1413"}, {"ruleId": "1170", "severity": 1, "message": "1414", "line": 18, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 18, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1270", "line": 22, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 22, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1255", "line": 34, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 34, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1415", "line": 49, "column": 20, "nodeType": "1172", "messageId": "1173", "endLine": 49, "endColumn": 37}, {"ruleId": "1204", "severity": 1, "message": "1416", "line": 110, "column": 6, "nodeType": "1206", "endLine": 110, "endColumn": 38, "suggestions": "1417"}, {"ruleId": "1170", "severity": 1, "message": "1356", "line": 8, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 8, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1214", "line": 17, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 17, "endColumn": 13}, {"ruleId": "1170", "severity": 1, "message": "1418", "line": 21, "column": 17, "nodeType": "1172", "messageId": "1173", "endLine": 21, "endColumn": 31}, {"ruleId": "1170", "severity": 1, "message": "1419", "line": 28, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 28, "endColumn": 14}, {"ruleId": "1170", "severity": 1, "message": "1420", "line": 18, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 18, "endColumn": 23}, {"ruleId": "1170", "severity": 1, "message": "1421", "line": 148, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 148, "endColumn": 25}, {"ruleId": "1170", "severity": 1, "message": "1422", "line": 158, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 158, "endColumn": 24}, {"ruleId": "1170", "severity": 1, "message": "1423", "line": 177, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 177, "endColumn": 29}, {"ruleId": "1170", "severity": 1, "message": "1424", "line": 306, "column": 17, "nodeType": "1172", "messageId": "1173", "endLine": 306, "endColumn": 30}, {"ruleId": "1170", "severity": 1, "message": "1236", "line": 11, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 11, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1425", "line": 26, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 26, "endColumn": 29}, {"ruleId": "1170", "severity": 1, "message": "1426", "line": 27, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 27, "endColumn": 12}, {"ruleId": "1170", "severity": 1, "message": "1425", "line": 21, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 21, "endColumn": 29}, {"ruleId": "1170", "severity": 1, "message": "1426", "line": 22, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 22, "endColumn": 12}, {"ruleId": "1170", "severity": 1, "message": "1427", "line": 18, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 18, "endColumn": 12}, {"ruleId": "1170", "severity": 1, "message": "1428", "line": 19, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 19, "endColumn": 19}, {"ruleId": "1170", "severity": 1, "message": "1429", "line": 20, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 20, "endColumn": 19}, {"ruleId": "1170", "severity": 1, "message": "1237", "line": 21, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 21, "endColumn": 19}, {"ruleId": "1170", "severity": 1, "message": "1430", "line": 22, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 22, "endColumn": 11}, {"ruleId": "1170", "severity": 1, "message": "1431", "line": 24, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 24, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1236", "line": 34, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 34, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1359", "line": 43, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 43, "endColumn": 22}, {"ruleId": "1170", "severity": 1, "message": "1432", "line": 44, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 44, "endColumn": 21}, {"ruleId": "1170", "severity": 1, "message": "1433", "line": 45, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 45, "endColumn": 16}, {"ruleId": "1170", "severity": 1, "message": "1434", "line": 127, "column": 9, "nodeType": "1172", "messageId": "1173", "endLine": 127, "endColumn": 18}, {"ruleId": "1170", "severity": 1, "message": "1251", "line": 1, "column": 38, "nodeType": "1172", "messageId": "1173", "endLine": 1, "endColumn": 44}, {"ruleId": "1170", "severity": 1, "message": "1435", "line": 1, "column": 46, "nodeType": "1172", "messageId": "1173", "endLine": 1, "endColumn": 53}, {"ruleId": "1170", "severity": 1, "message": "1436", "line": 4, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 4, "endColumn": 18}, {"ruleId": "1170", "severity": 1, "message": "1437", "line": 5, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 5, "endColumn": 13}, {"ruleId": "1170", "severity": 1, "message": "1438", "line": 6, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 6, "endColumn": 17}, {"ruleId": "1170", "severity": 1, "message": "1439", "line": 9, "column": 46, "nodeType": "1172", "messageId": "1173", "endLine": 9, "endColumn": 71}, {"ruleId": "1170", "severity": 1, "message": "1440", "line": 9, "column": 73, "nodeType": "1172", "messageId": "1173", "endLine": 9, "endColumn": 88}, {"ruleId": "1170", "severity": 1, "message": "1441", "line": 14, "column": 8, "nodeType": "1172", "messageId": "1173", "endLine": 14, "endColumn": 27}, {"ruleId": "1204", "severity": 1, "message": "1442", "line": 49, "column": 6, "nodeType": "1206", "endLine": 49, "endColumn": 22, "suggestions": "1443"}, {"ruleId": "1204", "severity": 1, "message": "1444", "line": 40, "column": 8, "nodeType": "1206", "endLine": 40, "endColumn": 33, "suggestions": "1445"}, {"ruleId": "1204", "severity": 1, "message": "1446", "line": 128, "column": 6, "nodeType": "1206", "endLine": 128, "endColumn": 33, "suggestions": "1447"}, {"ruleId": "1204", "severity": 1, "message": "1448", "line": 132, "column": 6, "nodeType": "1206", "endLine": 132, "endColumn": 37, "suggestions": "1449"}, {"ruleId": "1204", "severity": 1, "message": "1450", "line": 137, "column": 6, "nodeType": "1206", "endLine": 137, "endColumn": 8, "suggestions": "1451"}, {"ruleId": "1204", "severity": 1, "message": "1452", "line": 300, "column": 6, "nodeType": "1206", "endLine": 300, "endColumn": 8, "suggestions": "1453"}, {"ruleId": "1204", "severity": 1, "message": "1448", "line": 403, "column": 6, "nodeType": "1206", "endLine": 403, "endColumn": 8, "suggestions": "1454"}, {"ruleId": "1204", "severity": 1, "message": "1455", "line": 28, "column": 6, "nodeType": "1206", "endLine": 28, "endColumn": 8, "suggestions": "1456"}, {"ruleId": "1204", "severity": 1, "message": "1457", "line": 230, "column": 6, "nodeType": "1206", "endLine": 230, "endColumn": 34, "suggestions": "1458"}, {"ruleId": "1204", "severity": 1, "message": "1459", "line": 220, "column": 6, "nodeType": "1206", "endLine": 220, "endColumn": 50, "suggestions": "1460"}, {"ruleId": "1204", "severity": 1, "message": "1461", "line": 59, "column": 6, "nodeType": "1206", "endLine": 59, "endColumn": 23, "suggestions": "1462"}, {"ruleId": "1463", "severity": 1, "message": "1464", "line": 148, "column": 37, "nodeType": "1465", "messageId": "1185", "endLine": 148, "endColumn": 39}, {"ruleId": "1463", "severity": 1, "message": "1466", "line": 173, "column": 82, "nodeType": "1465", "messageId": "1185", "endLine": 173, "endColumn": 84}, {"ruleId": "1463", "severity": 1, "message": "1466", "line": 228, "column": 104, "nodeType": "1465", "messageId": "1185", "endLine": 228, "endColumn": 106}, {"ruleId": "1463", "severity": 1, "message": "1466", "line": 257, "column": 44, "nodeType": "1465", "messageId": "1185", "endLine": 257, "endColumn": 46}, {"ruleId": "1463", "severity": 1, "message": "1466", "line": 261, "column": 44, "nodeType": "1465", "messageId": "1185", "endLine": 261, "endColumn": 46}, {"ruleId": "1463", "severity": 1, "message": "1466", "line": 265, "column": 44, "nodeType": "1465", "messageId": "1185", "endLine": 265, "endColumn": 46}, {"ruleId": "1463", "severity": 1, "message": "1466", "line": 271, "column": 44, "nodeType": "1465", "messageId": "1185", "endLine": 271, "endColumn": 46}, {"ruleId": "1467", "severity": 1, "message": "1468", "line": 9, "column": 23, "nodeType": "1469", "messageId": "1185", "endLine": 9, "endColumn": 26}, {"ruleId": "1204", "severity": 1, "message": "1470", "line": 32, "column": 6, "nodeType": "1206", "endLine": 32, "endColumn": 28, "suggestions": "1471"}, {"ruleId": "1170", "severity": 1, "message": "1356", "line": 17, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 17, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1472", "line": 23, "column": 15, "nodeType": "1172", "messageId": "1173", "endLine": 23, "endColumn": 27}, {"ruleId": "1170", "severity": 1, "message": "1473", "line": 25, "column": 14, "nodeType": "1172", "messageId": "1173", "endLine": 25, "endColumn": 25}, {"ruleId": "1170", "severity": 1, "message": "1474", "line": 28, "column": 14, "nodeType": "1172", "messageId": "1173", "endLine": 28, "endColumn": 25}, {"ruleId": "1170", "severity": 1, "message": "1418", "line": 30, "column": 17, "nodeType": "1172", "messageId": "1173", "endLine": 30, "endColumn": 31}, {"ruleId": "1170", "severity": 1, "message": "1475", "line": 31, "column": 17, "nodeType": "1172", "messageId": "1173", "endLine": 31, "endColumn": 31}, {"ruleId": "1170", "severity": 1, "message": "1356", "line": 15, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 15, "endColumn": 7}, {"ruleId": "1170", "severity": 1, "message": "1260", "line": 20, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 20, "endColumn": 8}, {"ruleId": "1170", "severity": 1, "message": "1472", "line": 23, "column": 15, "nodeType": "1172", "messageId": "1173", "endLine": 23, "endColumn": 27}, {"ruleId": "1170", "severity": 1, "message": "1474", "line": 26, "column": 14, "nodeType": "1172", "messageId": "1173", "endLine": 26, "endColumn": 25}, {"ruleId": "1170", "severity": 1, "message": "1409", "line": 28, "column": 13, "nodeType": "1172", "messageId": "1173", "endLine": 28, "endColumn": 23}, {"ruleId": "1170", "severity": 1, "message": "1476", "line": 29, "column": 13, "nodeType": "1172", "messageId": "1173", "endLine": 29, "endColumn": 23}, {"ruleId": "1170", "severity": 1, "message": "1325", "line": 19, "column": 10, "nodeType": "1172", "messageId": "1173", "endLine": 19, "endColumn": 33}, {"ruleId": "1170", "severity": 1, "message": "1472", "line": 33, "column": 15, "nodeType": "1172", "messageId": "1173", "endLine": 33, "endColumn": 27}, {"ruleId": "1170", "severity": 1, "message": "1236", "line": 20, "column": 3, "nodeType": "1172", "messageId": "1173", "endLine": 20, "endColumn": 10}, {"ruleId": "1170", "severity": 1, "message": "1477", "line": 104, "column": 11, "nodeType": "1172", "messageId": "1173", "endLine": 104, "endColumn": 25}, {"ruleId": "1204", "severity": 1, "message": "1478", "line": 225, "column": 7, "nodeType": "1479", "endLine": 225, "endColumn": 48}, {"ruleId": "1170", "severity": 1, "message": "1480", "line": 836, "column": 33, "nodeType": "1172", "messageId": "1173", "endLine": 836, "endColumn": 42}, "no-unused-vars", "'currentUser' is assigned a value but never used.", "Identifier", "unusedVar", "'isAuthenticated' is assigned a value but never used.", "'location' is assigned a value but never used.", "'meetingIssuesEn' is defined but never used.", "'meetingIssuesAr' is defined but never used.", "no-template-curly-in-string", "Unexpected template string expression.", "Literal", "unexpectedTemplateExpression", "no-dupe-keys", "Duplicate key 'details'.", "ObjectExpression", "unexpected", "Duplicate key 'editInfo'.", "Duplicate key 'noTeachersFound'.", "Duplicate key 'booking'.", "Duplicate key 'selectDuration'.", "Duplicate key 'currentBooking'.", "Duplicate key 'invalidCode'.", "Duplicate key 'verificationFailed'.", "Duplicate key 'updateProfile'.", "Duplicate key 'nativeLanguage'.", "Duplicate key 'teachingLanguages'.", "Duplicate key 'qualifications'.", "Duplicate key 'formHasErrors'.", "Duplicate key 'allowedFormats'.", "Duplicate key 'maxFileSize'.", "Duplicate key 'admin'.", "Duplicate key 'about'.", "Duplicate key 'errorCancelling'.", "Duplicate key 'earnings'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleLogout'. Either include it or remove the dependency array.", "ArrayExpression", ["1481"], "React Hook useEffect has a missing dependency: 'socket'. Either include it or remove the dependency array.", ["1482"], "React Hook useEffect has a missing dependency: 'fetchUnreadCount'. Either include it or remove the dependency array.", ["1483"], "Duplicate key 'gap'.", "'CardMedia' is defined but never used.", "'IconButton' is defined but never used.", "'Slide' is defined but never used.", "'Zoom' is defined but never used.", "'LanguageIcon' is defined but never used.", "'Star' is defined but never used.", "'Timeline' is defined but never used.", "'LocalLibrary' is defined but never used.", "'Language' is defined but never used.", "'TranslateIcon' is defined but never used.", "'useAuth' is defined but never used.", "'isMobile' is assigned a value but never used.", "'handleLanguageChange' is assigned a value but never used.", "no-const-assign", "'isRtl' is constant.", "const", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'PeopleIcon' is defined but never used.", "'PublicIcon' is defined but never used.", "'SecurityIcon' is defined but never used.", "'TextField' is defined but never used.", "'Drawer' is defined but never used.", "'Divider' is defined but never used.", "'FormControlLabel' is defined but never used.", "'StarIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", ["1484"], "'getPriceRangeText' is assigned a value but never used.", "'theme' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\&.", "unnecessaryEscape", ["1485", "1486"], ["1487", "1488"], "Unnecessary escape character: \\?.", ["1489", "1490"], "'isRtl' is assigned a value but never used.", "'useRef' is defined but never used.", "'GoogleIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'location'. Either include it or remove the dependency array.", ["1491"], "'Link' is defined but never used.", "'VerifiedIcon' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'ArrowUpwardIcon' is defined but never used.", "'ArrowDownwardIcon' is defined but never used.", "'Stack' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["1492"], "'CircularProgress' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTeachers'. Either include it or remove the dependency array.", ["1493"], "'Tooltip' is defined but never used.", "'debounce' is defined but never used.", "'openVideoDialog' is assigned a value but never used.", "'handleOpenVideoDialog' is assigned a value but never used.", "'handleCloseVideoDialog' is assigned a value but never used.", ["1494", "1495"], ["1496", "1497"], ["1498", "1499"], "'safeParseJSON' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLanguages'. Either include it or remove the dependency array.", ["1500"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", ["1501"], "'t' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUpdates'. Either include it or remove the dependency array.", ["1502"], "'recentSessions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSessions'. Either include it or remove the dependency array.", ["1503"], "React Hook useEffect has missing dependencies: 'fetchBalance' and 'fetchTransactions'. Either include them or remove the dependency array.", ["1504"], "'CheckIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", ["1505"], "React Hook useEffect has a missing dependency: 'fetchEarnings'. Either include it or remove the dependency array.", ["1506"], "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchWithdrawals'. Either include it or remove the dependency array.", ["1507"], "'updateUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchProfileData'. Either include it or remove the dependency array.", ["1508"], ["1509"], ["1510"], ["1511"], "'Grid' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'EventBusyIcon' is defined but never used.", "'selectedDay' is assigned a value but never used.", "'setSelectedDay' is assigned a value but never used.", "'selectedHour' is assigned a value but never used.", "'setSelectedHour' is assigned a value but never used.", "'selectAllForDay' is assigned a value but never used.", "'clearAllForDay' is assigned a value but never used.", "'selectTimeForAllDays' is assigned a value but never used.", "'clearTimeForAllDays' is assigned a value but never used.", "'useLocation' is defined but never used.", "'axios' is defined but never used.", "'Button' is defined but never used.", "'ContentCopyIcon' is defined but never used.", "'convertFromDatabaseTime' is defined but never used.", "'getCurrentTimeInTimezone' is defined but never used.", "'currentTime' is assigned a value but never used.", "'calculatePrice' is assigned a value but never used.", "'handleCopyLink' is assigned a value but never used.", "'getMeetingActions' is assigned a value but never used.", "'availableTimesForDay' is assigned a value but never used.", "'loadingDays' is assigned a value but never used.", "'loadingTimes' is assigned a value but never used.", "'setLoadingTimes' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAvailableHours' and 'fetchWeeklyBreaks'. Either include them or remove the dependency array.", ["1512"], "React Hook useEffect has a missing dependency: 'fetchWeeklyBreaks'. Either include it or remove the dependency array.", ["1513"], "'response' is assigned a value but never used.", ["1514"], "React Hook useEffect has a missing dependency: 'decreaseUnreadCount'. Either include it or remove the dependency array.", ["1515"], "React Hook useCallback has a missing dependency: 'decreaseUnreadCount'. Either include it or remove the dependency array.", ["1516"], "React Hook useCallback has an unnecessary dependency: 'messages'. Either exclude it or remove the dependency array.", ["1517"], ["1518", "1519"], ["1520", "1521"], ["1522", "1523"], "'setIsRtl' is assigned a value but never used.", "'i18n' is defined but never used.", ["1524"], "React Hook useEffect has missing dependencies: 'fetchBalance', 'fetchSettings', and 'fetchWithdrawals'. Either include them or remove the dependency array.", ["1525"], ["1526"], "'Chip' is defined but never used.", "'Collapse' is defined but never used.", "'StarBorderIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'ExpandLessIcon' is defined but never used.", "'expandedReplies' is assigned a value but never used.", "'toggleReplyExpansion' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'fullScreen' is assigned a value but never used.", ["1527"], ["1528", "1529"], ["1530", "1531"], ["1532", "1533"], "'MuiIconButton' is defined but never used.", "'availableSlots' is assigned a value but never used.", "'loadingSlots' is assigned a value but never used.", "no-use-before-define", "'socket' was used before it was defined.", "usedBeforeDefined", "React Hook useCallback has an unnecessary dependency: 'id'. Either exclude it or remove the dependency array.", ["1534"], "'handleBookSlot' is assigned a value but never used.", ["1535", "1536"], ["1537", "1538"], ["1539", "1540"], "'Typography' is defined but never used.", "'isConnected' is assigned a value but never used.", ["1541"], ["1542"], ["1543"], "'token' is assigned a value but never used.", ["1544"], "'MoneyIcon' is defined but never used.", "'toast' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMeetings'. Either include it or remove the dependency array.", ["1545"], "'getMeetingDateInStudentTimezone' is assigned a value but never used.", "'formatMeetingDateInStudentTimezone' is assigned a value but never used.", "'dateFnsFormat' is assigned a value but never used.", "'getCurrentTimeInStudentTimezone' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", ["1546"], "'CardActions' is defined but never used.", "'Avatar' is defined but never used.", "'isSameDay' is defined but never used.", "'convertBookingDateTime' is defined but never used.", "'isFullHourAvailable' is assigned a value but never used.", "'checkCrossHourAvailability' is assigned a value but never used.", "'checkSecondHalfAvailability' is assigned a value but never used.", "'renderBookingSuccess' is assigned a value but never used.", "'PersonIcon' is defined but never used.", "'datePart' is assigned a value but never used.", "'renderBookings' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'teachers'. Either include it or remove the dependency array.", ["1547"], "'ListItemButton' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPending'. Either include it or remove the dependency array.", ["1548"], "'AccessTimeIcon' is defined but never used.", "'moment' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'isPartOfFullHour' is assigned a value but never used.", "'isFullHourStart' is assigned a value but never used.", "'isFullHourSecondSlot' is assigned a value but never used.", "'formattedDate' is defined but never used.", "'formatDistanceToNow' is defined but never used.", "'ar' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'Checkbox' is defined but never used.", "'FormHelperText' is defined but never used.", "'VideoFileIcon' is defined but never used.", "'LinkIcon' is defined but never used.", "'timeSlots' is assigned a value but never used.", "'useMemo' is defined but never used.", "'MeetingConsumer' is defined but never used.", "'useMeeting' is defined but never used.", "'useParticipant' is defined but never used.", "'createMeetingWithCustomId' is defined but never used.", "'validateMeeting' is defined but never used.", "'WaitingToJoinScreen' is defined but never used.", "React Hook useEffect has a missing dependency: 'isExemptPage'. Either include it or remove the dependency array.", ["1549"], "React Hook useEffect has an unnecessary dependency: 'raisedHandsParticipants'. Either exclude it or remove the dependency array. Outer scope values like 'raisedHandsParticipants' aren't valid dependencies because mutating them doesn't re-render the component.", ["1550"], "React Hook useEffect has a missing dependency: 'getCameraDevices'. Either include it or remove the dependency array.", ["1551"], "React Hook useEffect has a missing dependency: 'getAudioDevices'. Either include it or remove the dependency array.", ["1552"], "React Hook useEffect has a missing dependency: 'checkMediaPermission'. Either include it or remove the dependency array.", ["1553"], "React Hook useEffect has a missing dependency: 'onDeviceChanged'. Either include it or remove the dependency array.", ["1554"], ["1555"], "React Hook useEffect has a missing dependency: 'waitingMessages'. Either include it or remove the dependency array.", ["1556"], "React Hook useEffect has missing dependencies: 'leave', 'onClose', and 'setIsMeetingLeft'. Either include them or remove the dependency array. If 'setIsMeetingLeft' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1557"], "React Hook useEffect has a missing dependency: 'updateStats'. Either include it or remove the dependency array.", ["1558"], "React Hook useEffect has a missing dependency: 'setDidDeviceChange'. Either include it or remove the dependency array. If 'setDidDeviceChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1559"], "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "Expected '===' and instead saw '=='.", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "React Hook useEffect has a missing dependency: 'meetingId'. Either include it or remove the dependency array.", ["1560"], "'ScheduleIcon' is defined but never used.", "'PaymentIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'CreditCardIcon' is defined but never used.", "'SchoolIcon' is defined but never used.", "'oneHourFromNow' is assigned a value but never used.", "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "'isEvening' is assigned a value but never used.", {"desc": "1561", "fix": "1562"}, {"desc": "1563", "fix": "1564"}, {"desc": "1565", "fix": "1566"}, {"desc": "1567", "fix": "1568"}, {"messageId": "1569", "fix": "1570", "desc": "1571"}, {"messageId": "1572", "fix": "1573", "desc": "1574"}, {"messageId": "1569", "fix": "1575", "desc": "1571"}, {"messageId": "1572", "fix": "1576", "desc": "1574"}, {"messageId": "1569", "fix": "1577", "desc": "1571"}, {"messageId": "1572", "fix": "1578", "desc": "1574"}, {"desc": "1579", "fix": "1580"}, {"desc": "1581", "fix": "1582"}, {"desc": "1583", "fix": "1584"}, {"messageId": "1569", "fix": "1585", "desc": "1571"}, {"messageId": "1572", "fix": "1586", "desc": "1574"}, {"messageId": "1569", "fix": "1587", "desc": "1571"}, {"messageId": "1572", "fix": "1588", "desc": "1574"}, {"messageId": "1569", "fix": "1589", "desc": "1571"}, {"messageId": "1572", "fix": "1590", "desc": "1574"}, {"desc": "1591", "fix": "1592"}, {"desc": "1593", "fix": "1594"}, {"desc": "1595", "fix": "1596"}, {"desc": "1597", "fix": "1598"}, {"desc": "1599", "fix": "1600"}, {"desc": "1601", "fix": "1602"}, {"desc": "1603", "fix": "1604"}, {"desc": "1605", "fix": "1606"}, {"desc": "1607", "fix": "1608"}, {"desc": "1609", "fix": "1610"}, {"desc": "1609", "fix": "1611"}, {"desc": "1609", "fix": "1612"}, {"desc": "1613", "fix": "1614"}, {"desc": "1615", "fix": "1616"}, {"desc": "1617", "fix": "1618"}, {"desc": "1619", "fix": "1620"}, {"desc": "1621", "fix": "1622"}, {"desc": "1623", "fix": "1624"}, {"messageId": "1569", "fix": "1625", "desc": "1571"}, {"messageId": "1572", "fix": "1626", "desc": "1574"}, {"messageId": "1569", "fix": "1627", "desc": "1571"}, {"messageId": "1572", "fix": "1628", "desc": "1574"}, {"messageId": "1569", "fix": "1629", "desc": "1571"}, {"messageId": "1572", "fix": "1630", "desc": "1574"}, {"desc": "1599", "fix": "1631"}, {"desc": "1632", "fix": "1633"}, {"desc": "1634", "fix": "1635"}, {"desc": "1636", "fix": "1637"}, {"messageId": "1569", "fix": "1638", "desc": "1571"}, {"messageId": "1572", "fix": "1639", "desc": "1574"}, {"messageId": "1569", "fix": "1640", "desc": "1571"}, {"messageId": "1572", "fix": "1641", "desc": "1574"}, {"messageId": "1569", "fix": "1642", "desc": "1571"}, {"messageId": "1572", "fix": "1643", "desc": "1574"}, {"desc": "1644", "fix": "1645"}, {"messageId": "1569", "fix": "1646", "desc": "1571"}, {"messageId": "1572", "fix": "1647", "desc": "1574"}, {"messageId": "1569", "fix": "1648", "desc": "1571"}, {"messageId": "1572", "fix": "1649", "desc": "1574"}, {"messageId": "1569", "fix": "1650", "desc": "1571"}, {"messageId": "1572", "fix": "1651", "desc": "1574"}, {"desc": "1617", "fix": "1652"}, {"desc": "1619", "fix": "1653"}, {"desc": "1621", "fix": "1654"}, {"desc": "1634", "fix": "1655"}, {"desc": "1656", "fix": "1657"}, {"desc": "1599", "fix": "1658"}, {"desc": "1659", "fix": "1660"}, {"desc": "1661", "fix": "1662"}, {"desc": "1663", "fix": "1664"}, {"desc": "1665", "fix": "1666"}, {"desc": "1667", "fix": "1668"}, {"desc": "1669", "fix": "1670"}, {"desc": "1671", "fix": "1672"}, {"desc": "1673", "fix": "1674"}, {"desc": "1675", "fix": "1676"}, {"desc": "1677", "fix": "1678"}, {"desc": "1679", "fix": "1680"}, {"desc": "1681", "fix": "1682"}, {"desc": "1683", "fix": "1684"}, {"desc": "1685", "fix": "1686"}, "Update the dependencies array to be: [handleLogout]", {"range": "1687", "text": "1688"}, "Update the dependencies array to be: [isAuthenticated, token, currentUser, socket]", {"range": "1689", "text": "1690"}, "Update the dependencies array to be: [socket, isConnected, currentUser, fetchUnreadCount]", {"range": "1691", "text": "1692"}, "Update the dependencies array to be: [appliedFilters, page, searchFilters.priceRange, t]", {"range": "1693", "text": "1694"}, "removeEscape", {"range": "1695", "text": "1696"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1697", "text": "1698"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1699", "text": "1696"}, {"range": "1700", "text": "1698"}, {"range": "1701", "text": "1696"}, {"range": "1702", "text": "1698"}, "Update the dependencies array to be: [location.state, i18n.language, location]", {"range": "1703", "text": "1704"}, "Update the dependencies array to be: [fetchStudents, page, rowsPerPage, searchQuery, t]", {"range": "1705", "text": "1706"}, "Update the dependencies array to be: [fetchTeachers, page, rowsPerPage, searchQuery, t]", {"range": "1707", "text": "1708"}, {"range": "1709", "text": "1696"}, {"range": "1710", "text": "1698"}, {"range": "1711", "text": "1696"}, {"range": "1712", "text": "1698"}, {"range": "1713", "text": "1696"}, {"range": "1714", "text": "1698"}, "Update the dependencies array to be: [fetchLanguages]", {"range": "1715", "text": "1716"}, "Update the dependencies array to be: [fetchCategories]", {"range": "1717", "text": "1718"}, "Update the dependencies array to be: [fetchUpdates, page, rowsPerPage, statusFilter]", {"range": "1719", "text": "1720"}, "Update the dependencies array to be: [fetchSessions, filters]", {"range": "1721", "text": "1722"}, "Update the dependencies array to be: [currentUser, token, page, rowsPerPage, fetchBalance, fetchTransactions]", {"range": "1723", "text": "1724"}, "Update the dependencies array to be: [token, tabValue, page, rowsPerPage, fetchMessages]", {"range": "1725", "text": "1726"}, "Update the dependencies array to be: [fetchEarnings, page, rowsPerPage]", {"range": "1727", "text": "1728"}, "Update the dependencies array to be: [fetchWithdrawals, page, rowsPerPage, statusFilter]", {"range": "1729", "text": "1730"}, "Update the dependencies array to be: [t, currentUser, fetchProfileData]", {"range": "1731", "text": "1732"}, "Update the dependencies array to be: [currentUser, fetchProfileData]", {"range": "1733", "text": "1734"}, {"range": "1735", "text": "1734"}, {"range": "1736", "text": "1734"}, "Update the dependencies array to be: [token, t, fetchAvailableHours, fetchWeeklyBreaks]", {"range": "1737", "text": "1738"}, "Update the dependencies array to be: [currentWeekStart, fetchWeeklyBreaks, token]", {"range": "1739", "text": "1740"}, "Update the dependencies array to be: [socket, selectedChat, t]", {"range": "1741", "text": "1742"}, "Update the dependencies array to be: [socket, selectedChat, decreaseUnreadCount]", {"range": "1743", "text": "1744"}, "Update the dependencies array to be: [decreaseUnreadCount, socket]", {"range": "1745", "text": "1746"}, "Update the dependencies array to be: [socket, isConnected, currentUser, t]", {"range": "1747", "text": "1748"}, {"range": "1749", "text": "1696"}, {"range": "1750", "text": "1698"}, {"range": "1751", "text": "1696"}, {"range": "1752", "text": "1698"}, {"range": "1753", "text": "1696"}, {"range": "1754", "text": "1698"}, {"range": "1755", "text": "1724"}, "Update the dependencies array to be: [currentUser, token, page, rowsPerPage, fetchBalance, fetchWithdrawals, fetchSettings]", {"range": "1756", "text": "1757"}, "Update the dependencies array to be: [token, page, rowsPerPage, fetchMessages]", {"range": "1758", "text": "1759"}, "Update the dependencies array to be: [page, currentUser, token, appliedFilters, fetchTeachers]", {"range": "1760", "text": "1761"}, {"range": "1762", "text": "1696"}, {"range": "1763", "text": "1698"}, {"range": "1764", "text": "1696"}, {"range": "1765", "text": "1698"}, {"range": "1766", "text": "1696"}, {"range": "1767", "text": "1698"}, "Update the dependencies array to be: [socket, chatId, currentUser]", {"range": "1768", "text": "1769"}, {"range": "1770", "text": "1696"}, {"range": "1771", "text": "1698"}, {"range": "1772", "text": "1696"}, {"range": "1773", "text": "1698"}, {"range": "1774", "text": "1696"}, {"range": "1775", "text": "1698"}, {"range": "1776", "text": "1742"}, {"range": "1777", "text": "1744"}, {"range": "1778", "text": "1746"}, {"range": "1779", "text": "1759"}, "Update the dependencies array to be: [currentUser, fetchMeetings]", {"range": "1780", "text": "1781"}, {"range": "1782", "text": "1724"}, "Update the dependencies array to be: [success, editingReviewId, teachers]", {"range": "1783", "text": "1784"}, "Update the dependencies array to be: [currentUser, fetchPending, location.pathname]", {"range": "1785", "text": "1786"}, "Update the dependencies array to be: [t, exemptPages, isExemptPage]", {"range": "1787", "text": "1788"}, "Update the dependencies array to be: []", {"range": "1789", "text": "1790"}, "Update the dependencies array to be: [getCameraDevices, isCameraPermissionAllowed]", {"range": "1791", "text": "1792"}, "Update the dependencies array to be: [getAudioDevices, isMicrophonePermissionAllowed]", {"range": "1793", "text": "1794"}, "Update the dependencies array to be: [checkMediaPermission]", {"range": "1795", "text": "1796"}, "Update the dependencies array to be: [onDeviceChanged]", {"range": "1797", "text": "1798"}, "Update the dependencies array to be: [getAudioDevices]", {"range": "1799", "text": "1800"}, "Update the dependencies array to be: [waitingMessages]", {"range": "1801", "text": "1802"}, "Update the dependencies array to be: [leave, meetingData, onClose, participantTz, setIsMeetingLeft]", {"range": "1803", "text": "1804"}, "Update the dependencies array to be: [webcamStream, micStream, screenShareStream, updateStats]", {"range": "1805", "text": "1806"}, "Update the dependencies array to be: [didDeviceChange, setDidDeviceChange]", {"range": "1807", "text": "1808"}, "Update the dependencies array to be: [teacherId, studentId, meetingId]", {"range": "1809", "text": "1810"}, [3716, 3718], "[handleLogout]", [3199, 3236], "[isAuthenticated, token, currentUser, socket]", [3029, 3063], "[socket, isConnected, currentUser, fetchUnreadCount]", [9275, 9323], "[appliedFilters, page, searchFilters.priceRange, t]", [2348, 2349], "", [2348, 2348], "\\", [2357, 2358], [2357, 2357], [2359, 2360], [2359, 2359], [3432, 3463], "[location.state, i18n.language, location]", [2116, 2151], "[fetchStudents, page, rowsPerPage, searchQuery, t]", [2095, 2130], "[fetchTeachers, page, rowsPerPage, searchQuery, t]", [5698, 5699], [5698, 5698], [5707, 5708], [5707, 5707], [5709, 5710], [5709, 5709], [1057, 1059], "[fetchLanguages]", [1069, 1071], "[fetchCategories]", [2347, 2380], "[fetchUpdates, page, rowsPerPage, statusFilter]", [2831, 2840], "[fetchSessions, filters]", [1299, 1338], "[currentUser, token, page, rowsPerPage, fetchBalance, fetchTransactions]", [2708, 2744], "[token, tabValue, page, rowsPerPage, fetchMessages]", [1259, 1278], "[fetchEarnings, page, rowsPerPage]", [1628, 1661], "[fetchWithdrawals, page, rowsPerPage, statusFilter]", [3445, 3461], "[t, currentUser, fetchProfileData]", [4176, 4189], "[currentUser, fetchProfileData]", [4567, 4580], [5070, 5083], [5750, 5760], "[token, t, fetchAvailableHours, fetchWeeklyBreaks]", [5882, 5907], "[currentWeekStart, fetchWeeklyBreaks, token]", [6755, 6777], "[socket, selectedChat, t]", [8233, 8255], "[socket, selectedChat, decreaseUnreadCount]", [9760, 9768], "[decreaseUnreadCount, socket]", [11815, 11862], "[socket, isConnected, currentUser, t]", [1819, 1820], [1819, 1819], [1828, 1829], [1828, 1828], [1830, 1831], [1830, 1830], [1366, 1405], [1858, 1897], "[currentUser, token, page, rowsPerPage, fetchBalance, fetchWithdrawals, fetchSettings]", [2027, 2053], "[token, page, rowsPerPage, fetchMessages]", [8622, 8664], "[page, currentUser, token, appliedFilters, fetchTeachers]", [12227, 12228], [12227, 12227], [12236, 12237], [12236, 12236], [12238, 12239], [12238, 12238], [9071, 9104], "[socket, chatId, currentUser]", [14109, 14110], [14109, 14109], [14118, 14119], [14118, 14118], [14120, 14121], [14120, 14120], [7027, 7049], [8505, 8527], [10032, 10040], [2113, 2139], [1892, 1905], "[current<PERSON><PERSON>, fetchMeetings]", [2364, 2403], [20708, 20734], "[success, editing<PERSON><PERSON><PERSON>wId, teachers]", [3072, 3104], "[currentUser, fetchPending, location.pathname]", [1680, 1696], "[t, exemptPages, isExemptPage]", [1678, 1703], "[]", [4052, 4079], "[getCameraDevices, isCameraPermissionAllowed]", [4135, 4166], "[getAudioDevices, isMicrophonePermissionAllowed]", [4250, 4252], "[checkMediaPermission]", [9249, 9251], "[onDevice<PERSON>hanged]", [12542, 12544], "[getAudioDevices]", [845, 847], "[waitingMessages]", [6972, 7000], "[leave, meeting<PERSON><PERSON>, onClose, participantTz, setIsMeetingLeft]", [6514, 6558], "[webcamStream, micStream, screenShareStream, updateStats]", [1859, 1876], "[didD<PERSON><PERSON><PERSON><PERSON><PERSON>, setDidDeviceChange]", [1198, 1220], "[teacherId, studentId, meetingId]"]