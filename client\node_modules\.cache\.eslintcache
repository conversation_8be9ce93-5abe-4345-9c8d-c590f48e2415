[{"D:\\xampp\\htdocs\\allemnionline\\client\\src\\index.js": "1", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\App.js": "2", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\config\\axios.js": "3", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\i18n.js": "4", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\reportWebVitals.js": "5", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\ResizeObserverFix.js": "6", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\resizeObserver.js": "7", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\AuthContext.js": "8", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Footer.js": "9", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\SocketContext.js": "10", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\UnreadMessagesContext.js": "11", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AuthenticatedFooter.js": "12", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\layout\\Header.js": "13", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\Home.js": "14", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PrivacyPolicy.js": "15", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\AboutUs.js": "16", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PlatformPolicy.js": "17", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\FindTeacher.js": "18", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TeacherDetails.js": "19", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\StudentRegister.js": "20", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\TeacherRegister.js": "21", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\Login.js": "22", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyResetCode.js": "23", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ResetPassword.js": "24", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ForgotPassword.js": "25", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\RegisterChoice.js": "26", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyEmail.js": "27", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Dashboard.js": "28", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Students.js": "29", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Profile.js": "30", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Teachers.js": "31", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\TeacherApplications.js": "32", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Languages.js": "33", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Categories.js": "34", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\ProfileUpdates.js": "35", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingSessions.js": "36", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Wallet.js": "37", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Messages.js": "38", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\AdminEarnings.js": "39", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Dashboard.js": "40", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\WithdrawalManagement.js": "41", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditVideoUpload.js": "42", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditApplication.js": "43", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Application.js": "44", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\TeacherAvailableHours.js": "45", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ViewAvailableHours.js": "46", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\VideoUpload.js": "47", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\AvailableHours.js": "48", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Meetings.js": "49", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Bookings.js": "50", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Chat.js": "51", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Profile.js": "52", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyLessons.js": "53", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Wallet.js": "54", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ContactUs.js": "55", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Withdrawal.js": "56", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyMessages.js": "57", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Reviews.js": "58", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Profile.js": "59", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Dashboard.js": "60", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\CompleteProfile.js": "61", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\FindTeacher.js": "62", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\TeacherProfile.js": "63", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ChatEmbed.js": "64", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Chat.js": "65", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ContactUs.js": "66", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyMessages.js": "67", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\JoinMeeting.js": "68", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyTeachers.js": "69", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Meetings.js": "70", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Wallet.js": "71", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\BookingPage.js": "72", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Bookings.js": "73", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\WriteReview.js": "74", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AvailableHoursTable.js": "75", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Layout.js": "76", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axios.js": "77", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\timezone.js": "78", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\WeeklyBookingsTable.js": "79", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\constants.js": "80", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\BookableHoursTable.js": "81", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\StripePayment.js": "82", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatWindow.js": "83", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatList.js": "84", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\auth\\GenderDialog.js": "85", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\CropImageDialog.js": "86", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationStatus.js": "87", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationForm.js": "88", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\Spinner.js": "89", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\VideoSDKMeeting.js": "90", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\student\\ProfileCompletionAlert.js": "91", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\api.js": "92", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingAppContext.js": "93", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingContainer.js": "94", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\JoiningScreen.js": "95", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\WaitingToJoinScreen.js": "96", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\LeaveScreen.js": "97", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\PresenterView.js": "98", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\SimpleBottomBar.js": "99", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ParticipantView.js": "100", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ConfirmBox.js": "101", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\helper.js": "102", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsMobile.js": "103", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\SidebarContainer.js": "104", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsTab.js": "105", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\MeetingDetailsScreen.js": "106", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useMediaStream.js": "107", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownCam.js": "108", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDown.js": "109", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownSpeaker.js": "110", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\NetworkStats.js": "111", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\WebcamOffIcon.js": "112", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffIcon.js": "113", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicPermissionDenied.jsx": "114", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\CameraPermissionDenied.jsx": "115", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOnIcon.js": "116", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\WebcamOnIcon.js": "117", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffSmallIcon.js": "118", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\SpeakerIcon.js": "119", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useWindowSize.js": "120", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ScreenShareIcon.js": "121", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\common.js": "122", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkIcon.js": "123", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOffIcon.js": "124", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\PipIcon.js": "125", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ChatPanel.js": "126", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\NotesPanel.js": "127", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ParticipantPanel.js": "128", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropMIC.jsx": "129", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMicOff.jsx": "130", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropCAM.jsx": "131", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestSpeaker.jsx": "132", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMic.jsx": "133", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\PauseButton.jsx": "134", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropSpeaker.jsx": "135", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\UploadIcon.jsx": "136", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshCheck.jsx": "137", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshIcon.jsx": "138", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\DownloadIcon.jsx": "139", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\WifiOff.jsx": "140", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\RaiseHand.js": "141", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOffIcon.js": "142", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOnIcon.js": "143", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOnIcon.js": "144", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOffIcon.js": "145", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingPaymentPolicy.js": "146", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\RefundPolicy.js": "147", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingCancellationPolicy.js": "148", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\ContactUs.js": "149", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TermsAndConditions.js": "150", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\termsConditions.js": "151", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\contactUs.js": "152", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\MeetingFeedbackDialog.js": "153", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingIssues.js": "154", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\RescheduleDialog.js": "155", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\UserStatusChecker.js": "156", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\ProtectedRoute.js": "157", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\userStatusHandler.js": "158", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axiosConfig.js": "159", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\DeletedUsers.js": "160"}, {"size": 641, "mtime": 1742576862818, "results": "161", "hashOfConfig": "162"}, {"size": 25257, "mtime": 1753826058158, "results": "163", "hashOfConfig": "162"}, {"size": 1456, "mtime": 1746732811228, "results": "164", "hashOfConfig": "162"}, {"size": 223544, "mtime": 1753833835209, "results": "165", "hashOfConfig": "162"}, {"size": 362, "mtime": 1742419325522, "results": "166", "hashOfConfig": "162"}, {"size": 1029, "mtime": 1744808128594, "results": "167", "hashOfConfig": "162"}, {"size": 325, "mtime": 1743221258725, "results": "168", "hashOfConfig": "162"}, {"size": 10671, "mtime": 1753832023101, "results": "169", "hashOfConfig": "162"}, {"size": 7654, "mtime": 1751398254796, "results": "170", "hashOfConfig": "162"}, {"size": 3432, "mtime": 1751285065551, "results": "171", "hashOfConfig": "162"}, {"size": 3326, "mtime": 1751040721930, "results": "172", "hashOfConfig": "162"}, {"size": 943, "mtime": 1749309630963, "results": "173", "hashOfConfig": "162"}, {"size": 24114, "mtime": 1751284599336, "results": "174", "hashOfConfig": "162"}, {"size": 36606, "mtime": 1753386956890, "results": "175", "hashOfConfig": "162"}, {"size": 4768, "mtime": 1751278844207, "results": "176", "hashOfConfig": "162"}, {"size": 10446, "mtime": 1751293932223, "results": "177", "hashOfConfig": "162"}, {"size": 28837, "mtime": 1753731053731, "results": "178", "hashOfConfig": "162"}, {"size": 30616, "mtime": 1751485922771, "results": "179", "hashOfConfig": "162"}, {"size": 15736, "mtime": 1751486831358, "results": "180", "hashOfConfig": "162"}, {"size": 15894, "mtime": 1749479492637, "results": "181", "hashOfConfig": "162"}, {"size": 15959, "mtime": 1749479507046, "results": "182", "hashOfConfig": "162"}, {"size": 18542, "mtime": 1753825742995, "results": "183", "hashOfConfig": "162"}, {"size": 7902, "mtime": 1747334952040, "results": "184", "hashOfConfig": "162"}, {"size": 10135, "mtime": 1747335101462, "results": "185", "hashOfConfig": "162"}, {"size": 7229, "mtime": 1747334912778, "results": "186", "hashOfConfig": "162"}, {"size": 9247, "mtime": 1742785011813, "results": "187", "hashOfConfig": "162"}, {"size": 13648, "mtime": 1751303763967, "results": "188", "hashOfConfig": "162"}, {"size": 29493, "mtime": 1744817248860, "results": "189", "hashOfConfig": "162"}, {"size": 13701, "mtime": 1744022069977, "results": "190", "hashOfConfig": "162"}, {"size": 12085, "mtime": 1746733346280, "results": "191", "hashOfConfig": "162"}, {"size": 11112, "mtime": 1744799843410, "results": "192", "hashOfConfig": "162"}, {"size": 30331, "mtime": 1751480317262, "results": "193", "hashOfConfig": "162"}, {"size": 6641, "mtime": 1742964048040, "results": "194", "hashOfConfig": "162"}, {"size": 6618, "mtime": 1744022276817, "results": "195", "hashOfConfig": "162"}, {"size": 22625, "mtime": 1751041436226, "results": "196", "hashOfConfig": "162"}, {"size": 11838, "mtime": 1749581734964, "results": "197", "hashOfConfig": "162"}, {"size": 7554, "mtime": 1744093753745, "results": "198", "hashOfConfig": "162"}, {"size": 19961, "mtime": 1746026434563, "results": "199", "hashOfConfig": "162"}, {"size": 10379, "mtime": 1749855699542, "results": "200", "hashOfConfig": "162"}, {"size": 7520, "mtime": 1742769302670, "results": "201", "hashOfConfig": "162"}, {"size": 12485, "mtime": 1749828882192, "results": "202", "hashOfConfig": "162"}, {"size": 12535, "mtime": 1749506609302, "results": "203", "hashOfConfig": "162"}, {"size": 24173, "mtime": 1750508377674, "results": "204", "hashOfConfig": "162"}, {"size": 6673, "mtime": 1749331131513, "results": "205", "hashOfConfig": "162"}, {"size": 35597, "mtime": 1749498641458, "results": "206", "hashOfConfig": "162"}, {"size": 32081, "mtime": 1749321424781, "results": "207", "hashOfConfig": "162"}, {"size": 10975, "mtime": 1749337833332, "results": "208", "hashOfConfig": "162"}, {"size": 33527, "mtime": 1749322312984, "results": "209", "hashOfConfig": "162"}, {"size": 15465, "mtime": 1750848677426, "results": "210", "hashOfConfig": "162"}, {"size": 34185, "mtime": 1753480100105, "results": "211", "hashOfConfig": "162"}, {"size": 15303, "mtime": 1750870211486, "results": "212", "hashOfConfig": "162"}, {"size": 59692, "mtime": 1753723359743, "results": "213", "hashOfConfig": "162"}, {"size": 12480, "mtime": 1752698192448, "results": "214", "hashOfConfig": "162"}, {"size": 8011, "mtime": 1744093709651, "results": "215", "hashOfConfig": "162"}, {"size": 4974, "mtime": 1744028374118, "results": "216", "hashOfConfig": "162"}, {"size": 17010, "mtime": 1749916559907, "results": "217", "hashOfConfig": "162"}, {"size": 10864, "mtime": 1746026292144, "results": "218", "hashOfConfig": "162"}, {"size": 21665, "mtime": 1750041191553, "results": "219", "hashOfConfig": "162"}, {"size": 35014, "mtime": 1753632113764, "results": "220", "hashOfConfig": "162"}, {"size": 4214, "mtime": 1742913454633, "results": "221", "hashOfConfig": "162"}, {"size": 8524, "mtime": 1749491950143, "results": "222", "hashOfConfig": "162"}, {"size": 34718, "mtime": 1751485938458, "results": "223", "hashOfConfig": "162"}, {"size": 35898, "mtime": 1753286098780, "results": "224", "hashOfConfig": "162"}, {"size": 5814, "mtime": 1744024131309, "results": "225", "hashOfConfig": "162"}, {"size": 15814, "mtime": 1749589859006, "results": "226", "hashOfConfig": "162"}, {"size": 5024, "mtime": 1746026315740, "results": "227", "hashOfConfig": "162"}, {"size": 11083, "mtime": 1746026269030, "results": "228", "hashOfConfig": "162"}, {"size": 2721, "mtime": 1750156638150, "results": "229", "hashOfConfig": "162"}, {"size": 7189, "mtime": 1750568564445, "results": "230", "hashOfConfig": "162"}, {"size": 13724, "mtime": 1751567205888, "results": "231", "hashOfConfig": "162"}, {"size": 14721, "mtime": 1749819465295, "results": "232", "hashOfConfig": "162"}, {"size": 40555, "mtime": 1753286098776, "results": "233", "hashOfConfig": "162"}, {"size": 40659, "mtime": 1753453083171, "results": "234", "hashOfConfig": "162"}, {"size": 57051, "mtime": 1746744695128, "results": "235", "hashOfConfig": "162"}, {"size": 14257, "mtime": 1749585929387, "results": "236", "hashOfConfig": "162"}, {"size": 24713, "mtime": 1753825953931, "results": "237", "hashOfConfig": "162"}, {"size": 3262, "mtime": 1753034610621, "results": "238", "hashOfConfig": "162"}, {"size": 7726, "mtime": 1753129936379, "results": "239", "hashOfConfig": "162"}, {"size": 56521, "mtime": 1753443150818, "results": "240", "hashOfConfig": "162"}, {"size": 5549, "mtime": 1749490160444, "results": "241", "hashOfConfig": "162"}, {"size": 25593, "mtime": 1753442449956, "results": "242", "hashOfConfig": "162"}, {"size": 3697, "mtime": 1749774472728, "results": "243", "hashOfConfig": "162"}, {"size": 13801, "mtime": 1743218310347, "results": "244", "hashOfConfig": "162"}, {"size": 7971, "mtime": 1746023858919, "results": "245", "hashOfConfig": "162"}, {"size": 2877, "mtime": 1747400985303, "results": "246", "hashOfConfig": "162"}, {"size": 2530, "mtime": 1750507091156, "results": "247", "hashOfConfig": "162"}, {"size": 1488, "mtime": 1742858201413, "results": "248", "hashOfConfig": "162"}, {"size": 42233, "mtime": 1753723325313, "results": "249", "hashOfConfig": "162"}, {"size": 153, "mtime": 1742445554097, "results": "250", "hashOfConfig": "162"}, {"size": 7365, "mtime": 1750226849238, "results": "251", "hashOfConfig": "162"}, {"size": 3181, "mtime": 1744798236742, "results": "252", "hashOfConfig": "162"}, {"size": 2614, "mtime": 1750185563183, "results": "253", "hashOfConfig": "162"}, {"size": 3068, "mtime": 1751036509706, "results": "254", "hashOfConfig": "162"}, {"size": 11091, "mtime": 1752283251033, "results": "255", "hashOfConfig": "162"}, {"size": 20466, "mtime": 1750170525751, "results": "256", "hashOfConfig": "162"}, {"size": 2219, "mtime": 1750163211180, "results": "257", "hashOfConfig": "162"}, {"size": 836, "mtime": 1750184890252, "results": "258", "hashOfConfig": "162"}, {"size": 4725, "mtime": 1750162079006, "results": "259", "hashOfConfig": "162"}, {"size": 11306, "mtime": 1750569740310, "results": "260", "hashOfConfig": "162"}, {"size": 18054, "mtime": 1750163343664, "results": "261", "hashOfConfig": "162"}, {"size": 2950, "mtime": 1750162078997, "results": "262", "hashOfConfig": "162"}, {"size": 938, "mtime": 1750162079095, "results": "263", "hashOfConfig": "162"}, {"size": 211, "mtime": 1750162079017, "results": "264", "hashOfConfig": "162"}, {"size": 6398, "mtime": 1753615402623, "results": "265", "hashOfConfig": "162"}, {"size": 225, "mtime": 1750162079019, "results": "266", "hashOfConfig": "162"}, {"size": 4612, "mtime": 1750486746007, "results": "267", "hashOfConfig": "162"}, {"size": 831, "mtime": 1750162079019, "results": "268", "hashOfConfig": "162"}, {"size": 4793, "mtime": 1750163289799, "results": "269", "hashOfConfig": "162"}, {"size": 12075, "mtime": 1750163272446, "results": "270", "hashOfConfig": "162"}, {"size": 6897, "mtime": 1750163306463, "results": "271", "hashOfConfig": "162"}, {"size": 3348, "mtime": 1750162079002, "results": "272", "hashOfConfig": "162"}, {"size": 1056, "mtime": 1750162079048, "results": "273", "hashOfConfig": "162"}, {"size": 1842, "mtime": 1750162079037, "results": "274", "hashOfConfig": "162"}, {"size": 3656, "mtime": 1750162079039, "results": "275", "hashOfConfig": "162"}, {"size": 4224, "mtime": 1750162079030, "results": "276", "hashOfConfig": "162"}, {"size": 1616, "mtime": 1750162079025, "results": "277", "hashOfConfig": "162"}, {"size": 937, "mtime": 1750162079029, "results": "278", "hashOfConfig": "162"}, {"size": 1738, "mtime": 1750162079038, "results": "279", "hashOfConfig": "162"}, {"size": 662, "mtime": 1750162079047, "results": "280", "hashOfConfig": "162"}, {"size": 550, "mtime": 1750162079020, "results": "281", "hashOfConfig": "162"}, {"size": 519, "mtime": 1750162079047, "results": "282", "hashOfConfig": "162"}, {"size": 1921, "mtime": 1750265054387, "results": "283", "hashOfConfig": "162"}, {"size": 577, "mtime": 1750162079039, "results": "284", "hashOfConfig": "162"}, {"size": 1773, "mtime": 1750162079025, "results": "285", "hashOfConfig": "162"}, {"size": 503, "mtime": 1750523557957, "results": "286", "hashOfConfig": "162"}, {"size": 6750, "mtime": 1750251645533, "results": "287", "hashOfConfig": "162"}, {"size": 4294, "mtime": 1753615413502, "results": "288", "hashOfConfig": "162"}, {"size": 3765, "mtime": 1750163361559, "results": "289", "hashOfConfig": "162"}, {"size": 615, "mtime": 1750162079034, "results": "290", "hashOfConfig": "162"}, {"size": 853, "mtime": 1750162079036, "results": "291", "hashOfConfig": "162"}, {"size": 857, "mtime": 1750162079033, "results": "292", "hashOfConfig": "162"}, {"size": 564, "mtime": 1750162079037, "results": "293", "hashOfConfig": "162"}, {"size": 594, "mtime": 1750162079035, "results": "294", "hashOfConfig": "162"}, {"size": 750, "mtime": 1750162079035, "results": "295", "hashOfConfig": "162"}, {"size": 589, "mtime": 1750162079034, "results": "296", "hashOfConfig": "162"}, {"size": 492, "mtime": 1750162079042, "results": "297", "hashOfConfig": "162"}, {"size": 810, "mtime": 1750162079041, "results": "298", "hashOfConfig": "162"}, {"size": 490, "mtime": 1750162079042, "results": "299", "hashOfConfig": "162"}, {"size": 492, "mtime": 1750162079041, "results": "300", "hashOfConfig": "162"}, {"size": 840, "mtime": 1750162079043, "results": "301", "hashOfConfig": "162"}, {"size": 1776, "mtime": 1750162079045, "results": "302", "hashOfConfig": "162"}, {"size": 896, "mtime": 1750162079044, "results": "303", "hashOfConfig": "162"}, {"size": 548, "mtime": 1750162079044, "results": "304", "hashOfConfig": "162"}, {"size": 825, "mtime": 1750162079046, "results": "305", "hashOfConfig": "162"}, {"size": 1014, "mtime": 1750162079045, "results": "306", "hashOfConfig": "162"}, {"size": 5638, "mtime": 1753617596678, "results": "307", "hashOfConfig": "162"}, {"size": 5701, "mtime": 1753621506072, "results": "308", "hashOfConfig": "162"}, {"size": 6823, "mtime": 1751280498212, "results": "309", "hashOfConfig": "162"}, {"size": 4794, "mtime": 1751272383355, "results": "310", "hashOfConfig": "162"}, {"size": 3832, "mtime": 1751274488484, "results": "311", "hashOfConfig": "162"}, {"size": 6866, "mtime": 1751269538235, "results": "312", "hashOfConfig": "162"}, {"size": 2091, "mtime": 1751304469748, "results": "313", "hashOfConfig": "162"}, {"size": 4523, "mtime": 1752327988910, "results": "314", "hashOfConfig": "162"}, {"size": 21251, "mtime": 1752953246619, "results": "315", "hashOfConfig": "162"}, {"size": 41014, "mtime": 1753477116038, "results": "316", "hashOfConfig": "162"}, {"size": 3682, "mtime": 1753740489711, "results": "317", "hashOfConfig": "162"}, {"size": 4711, "mtime": 1753822881474, "results": "318", "hashOfConfig": "162"}, {"size": 4729, "mtime": 1753822989202, "results": "319", "hashOfConfig": "162"}, {"size": 2037, "mtime": 1753743828980, "results": "320", "hashOfConfig": "162"}, {"size": 21169, "mtime": 1753833789554, "results": "321", "hashOfConfig": "162"}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kyl3u4", {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 43, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\xampp\\htdocs\\allemnionline\\client\\src\\index.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\App.js", ["802", "803", "804"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\config\\axios.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\i18n.js", ["805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\reportWebVitals.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\ResizeObserverFix.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\resizeObserver.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\AuthContext.js", ["848"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Footer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\SocketContext.js", ["849"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\UnreadMessagesContext.js", ["850"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AuthenticatedFooter.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\layout\\Header.js", ["851", "852", "853"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\Home.js", ["854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PrivacyPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\AboutUs.js", ["868", "869", "870", "871", "872", "873"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PlatformPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\FindTeacher.js", ["874", "875", "876", "877", "878", "879", "880"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TeacherDetails.js", ["881", "882", "883", "884"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\StudentRegister.js", ["885"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\TeacherRegister.js", ["886"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\Login.js", ["887", "888", "889"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyResetCode.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ResetPassword.js", ["890"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ForgotPassword.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\RegisterChoice.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyEmail.js", ["891", "892"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Dashboard.js", ["893", "894", "895"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Students.js", ["896", "897"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Profile.js", ["898"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Teachers.js", ["899", "900", "901", "902", "903"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\TeacherApplications.js", ["904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Languages.js", ["916"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Categories.js", ["917"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\ProfileUpdates.js", ["918", "919"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingSessions.js", ["920", "921"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Wallet.js", ["922"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Messages.js", ["923", "924"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\AdminEarnings.js", ["925"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Dashboard.js", ["926"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\WithdrawalManagement.js", ["927"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditVideoUpload.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditApplication.js", ["928", "929", "930", "931", "932"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Application.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\TeacherAvailableHours.js", ["933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ViewAvailableHours.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\VideoUpload.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\AvailableHours.js", ["951", "952", "953", "954", "955", "956", "957", "958"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Meetings.js", ["959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Bookings.js", ["976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Chat.js", ["991", "992", "993", "994"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Profile.js", ["995", "996", "997", "998", "999", "1000", "1001", "1002"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyLessons.js", ["1003"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Wallet.js", ["1004", "1005"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ContactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Withdrawal.js", ["1006"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyMessages.js", ["1007"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Reviews.js", ["1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Profile.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Dashboard.js", ["1017"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\CompleteProfile.js", ["1018"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\FindTeacher.js", ["1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\TeacherProfile.js", ["1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ChatEmbed.js", ["1048", "1049"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Chat.js", ["1050", "1051", "1052"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ContactUs.js", ["1053"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyMessages.js", ["1054"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\JoinMeeting.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyTeachers.js", ["1055", "1056", "1057"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Meetings.js", ["1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Wallet.js", ["1067"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\BookingPage.js", ["1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Bookings.js", ["1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\WriteReview.js", ["1097", "1098", "1099", "1100"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AvailableHoursTable.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Layout.js", ["1101", "1102", "1103", "1104", "1105"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axios.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\timezone.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\WeeklyBookingsTable.js", ["1106", "1107", "1108", "1109"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\constants.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\BookableHoursTable.js", ["1110", "1111", "1112", "1113", "1114"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\StripePayment.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatWindow.js", ["1115", "1116", "1117"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatList.js", ["1118", "1119"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\auth\\GenderDialog.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\CropImageDialog.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationStatus.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationForm.js", ["1120", "1121", "1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\Spinner.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\VideoSDKMeeting.js", ["1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\student\\ProfileCompletionAlert.js", ["1139"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\api.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingAppContext.js", ["1140"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingContainer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\JoiningScreen.js", ["1141", "1142", "1143", "1144", "1145"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\WaitingToJoinScreen.js", ["1146"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\LeaveScreen.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\PresenterView.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\SimpleBottomBar.js", ["1147"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ParticipantView.js", ["1148"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ConfirmBox.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\helper.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsMobile.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\SidebarContainer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsTab.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\MeetingDetailsScreen.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useMediaStream.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownCam.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDown.js", ["1149", "1150", "1151", "1152", "1153", "1154", "1155", "1156"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownSpeaker.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\NetworkStats.js", ["1157"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\WebcamOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicPermissionDenied.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\CameraPermissionDenied.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\WebcamOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffSmallIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\SpeakerIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useWindowSize.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ScreenShareIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\common.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\PipIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ChatPanel.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\NotesPanel.js", ["1158"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ParticipantPanel.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropMIC.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMicOff.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropCAM.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestSpeaker.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMic.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\PauseButton.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropSpeaker.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\UploadIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshCheck.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\DownloadIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\WifiOff.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\RaiseHand.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingPaymentPolicy.js", ["1159", "1160", "1161", "1162", "1163", "1164"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\RefundPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingCancellationPolicy.js", ["1165", "1166", "1167", "1168", "1169", "1170"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\ContactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TermsAndConditions.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\termsConditions.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\contactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\MeetingFeedbackDialog.js", ["1171"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingIssues.js", ["1172"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\RescheduleDialog.js", ["1173", "1174", "1175", "1176"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\UserStatusChecker.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\ProtectedRoute.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\userStatusHandler.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axiosConfig.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\DeletedUsers.js", ["1177", "1178"], [], {"ruleId": "1179", "severity": 1, "message": "1180", "line": 99, "column": 11, "nodeType": "1181", "messageId": "1182", "endLine": 99, "endColumn": 22}, {"ruleId": "1179", "severity": 1, "message": "1183", "line": 99, "column": 24, "nodeType": "1181", "messageId": "1182", "endLine": 99, "endColumn": 39}, {"ruleId": "1179", "severity": 1, "message": "1184", "line": 100, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 100, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1185", "line": 3, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 3, "endColumn": 23}, {"ruleId": "1179", "severity": 1, "message": "1186", "line": 4, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 4, "endColumn": 23}, {"ruleId": "1187", "severity": 1, "message": "1188", "line": 63, "column": 24, "nodeType": "1189", "messageId": "1190", "endLine": 63, "endColumn": 66}, {"ruleId": "1191", "severity": 1, "message": "1192", "line": 299, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 299, "endColumn": 16}, {"ruleId": "1191", "severity": 1, "message": "1195", "line": 329, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 329, "endColumn": 17}, {"ruleId": "1191", "severity": 1, "message": "1196", "line": 556, "column": 7, "nodeType": "1193", "messageId": "1194", "endLine": 556, "endColumn": 13}, {"ruleId": "1191", "severity": 1, "message": "1197", "line": 653, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 653, "endColumn": 24}, {"ruleId": "1191", "severity": 1, "message": "1198", "line": 661, "column": 7, "nodeType": "1193", "messageId": "1194", "endLine": 661, "endColumn": 14}, {"ruleId": "1187", "severity": 1, "message": "1188", "line": 693, "column": 30, "nodeType": "1189", "messageId": "1190", "endLine": 693, "endColumn": 107}, {"ruleId": "1191", "severity": 1, "message": "1199", "line": 712, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 712, "endColumn": 23}, {"ruleId": "1191", "severity": 1, "message": "1200", "line": 781, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 781, "endColumn": 23}, {"ruleId": "1191", "severity": 1, "message": "1201", "line": 894, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 894, "endColumn": 20}, {"ruleId": "1191", "severity": 1, "message": "1202", "line": 895, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 895, "endColumn": 27}, {"ruleId": "1191", "severity": 1, "message": "1203", "line": 982, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 982, "endColumn": 22}, {"ruleId": "1191", "severity": 1, "message": "1204", "line": 1133, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 1133, "endColumn": 23}, {"ruleId": "1191", "severity": 1, "message": "1205", "line": 1134, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 1134, "endColumn": 26}, {"ruleId": "1191", "severity": 1, "message": "1206", "line": 1136, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 1136, "endColumn": 23}, {"ruleId": "1191", "severity": 1, "message": "1207", "line": 1195, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 1195, "endColumn": 22}, {"ruleId": "1191", "severity": 1, "message": "1208", "line": 1202, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 1202, "endColumn": 23}, {"ruleId": "1191", "severity": 1, "message": "1209", "line": 1203, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 1203, "endColumn": 20}, {"ruleId": "1191", "severity": 1, "message": "1210", "line": 1328, "column": 7, "nodeType": "1193", "messageId": "1194", "endLine": 1328, "endColumn": 12}, {"ruleId": "1191", "severity": 1, "message": "1211", "line": 1980, "column": 7, "nodeType": "1193", "messageId": "1194", "endLine": 1980, "endColumn": 12}, {"ruleId": "1187", "severity": 1, "message": "1188", "line": 2062, "column": 24, "nodeType": "1189", "messageId": "1190", "endLine": 2062, "endColumn": 58}, {"ruleId": "1191", "severity": 1, "message": "1212", "line": 2080, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 2080, "endColumn": 24}, {"ruleId": "1191", "severity": 1, "message": "1199", "line": 2170, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 2170, "endColumn": 23}, {"ruleId": "1191", "severity": 1, "message": "1192", "line": 2307, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 2307, "endColumn": 16}, {"ruleId": "1191", "severity": 1, "message": "1196", "line": 2562, "column": 7, "nodeType": "1193", "messageId": "1194", "endLine": 2562, "endColumn": 13}, {"ruleId": "1191", "severity": 1, "message": "1197", "line": 2659, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 2659, "endColumn": 24}, {"ruleId": "1191", "severity": 1, "message": "1198", "line": 2667, "column": 7, "nodeType": "1193", "messageId": "1194", "endLine": 2667, "endColumn": 14}, {"ruleId": "1191", "severity": 1, "message": "1199", "line": 2719, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 2719, "endColumn": 23}, {"ruleId": "1191", "severity": 1, "message": "1200", "line": 2788, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 2788, "endColumn": 23}, {"ruleId": "1191", "severity": 1, "message": "1201", "line": 2905, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 2905, "endColumn": 20}, {"ruleId": "1191", "severity": 1, "message": "1202", "line": 2906, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 2906, "endColumn": 27}, {"ruleId": "1191", "severity": 1, "message": "1203", "line": 2989, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 2989, "endColumn": 22}, {"ruleId": "1191", "severity": 1, "message": "1204", "line": 3140, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 3140, "endColumn": 23}, {"ruleId": "1191", "severity": 1, "message": "1205", "line": 3141, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 3141, "endColumn": 26}, {"ruleId": "1191", "severity": 1, "message": "1206", "line": 3143, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 3143, "endColumn": 23}, {"ruleId": "1191", "severity": 1, "message": "1207", "line": 3211, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 3211, "endColumn": 22}, {"ruleId": "1191", "severity": 1, "message": "1208", "line": 3229, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 3229, "endColumn": 23}, {"ruleId": "1191", "severity": 1, "message": "1209", "line": 3230, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 3230, "endColumn": 20}, {"ruleId": "1191", "severity": 1, "message": "1210", "line": 3338, "column": 7, "nodeType": "1193", "messageId": "1194", "endLine": 3338, "endColumn": 12}, {"ruleId": "1191", "severity": 1, "message": "1211", "line": 3811, "column": 7, "nodeType": "1193", "messageId": "1194", "endLine": 3811, "endColumn": 12}, {"ruleId": "1191", "severity": 1, "message": "1213", "line": 4296, "column": 9, "nodeType": "1193", "messageId": "1194", "endLine": 4296, "endColumn": 17}, {"ruleId": "1214", "severity": 1, "message": "1215", "line": 103, "column": 6, "nodeType": "1216", "endLine": 103, "endColumn": 8, "suggestions": "1217"}, {"ruleId": "1214", "severity": 1, "message": "1218", "line": 97, "column": 6, "nodeType": "1216", "endLine": 97, "endColumn": 43, "suggestions": "1219"}, {"ruleId": "1214", "severity": 1, "message": "1220", "line": 101, "column": 6, "nodeType": "1216", "endLine": 101, "endColumn": 40, "suggestions": "1221"}, {"ruleId": "1191", "severity": 1, "message": "1222", "line": 431, "column": 19, "nodeType": "1193", "messageId": "1194", "endLine": 431, "endColumn": 22}, {"ruleId": "1191", "severity": 1, "message": "1222", "line": 453, "column": 19, "nodeType": "1193", "messageId": "1194", "endLine": 453, "endColumn": 22}, {"ruleId": "1191", "severity": 1, "message": "1222", "line": 519, "column": 21, "nodeType": "1193", "messageId": "1194", "endLine": 519, "endColumn": 24}, {"ruleId": "1179", "severity": 1, "message": "1223", "line": 13, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 13, "endColumn": 12}, {"ruleId": "1179", "severity": 1, "message": "1224", "line": 14, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 14, "endColumn": 13}, {"ruleId": "1179", "severity": 1, "message": "1225", "line": 19, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 19, "endColumn": 8}, {"ruleId": "1179", "severity": 1, "message": "1226", "line": 23, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 23, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1227", "line": 28, "column": 15, "nodeType": "1181", "messageId": "1182", "endLine": 28, "endColumn": 27}, {"ruleId": "1179", "severity": 1, "message": "1228", "line": 33, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 33, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1229", "line": 34, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 34, "endColumn": 11}, {"ruleId": "1179", "severity": 1, "message": "1230", "line": 35, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 35, "endColumn": 15}, {"ruleId": "1179", "severity": 1, "message": "1231", "line": 38, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 38, "endColumn": 11}, {"ruleId": "1179", "severity": 1, "message": "1232", "line": 40, "column": 16, "nodeType": "1181", "messageId": "1182", "endLine": 40, "endColumn": 29}, {"ruleId": "1179", "severity": 1, "message": "1233", "line": 42, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 42, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1234", "line": 327, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 327, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1235", "line": 368, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 368, "endColumn": 29}, {"ruleId": "1236", "severity": 1, "message": "1237", "line": 371, "column": 5, "nodeType": "1181", "messageId": "1238", "endLine": 371, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1239", "line": 6, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 6, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1240", "line": 7, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 7, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1227", "line": 17, "column": 15, "nodeType": "1181", "messageId": "1182", "endLine": 17, "endColumn": 27}, {"ruleId": "1179", "severity": 1, "message": "1241", "line": 18, "column": 13, "nodeType": "1181", "messageId": "1182", "endLine": 18, "endColumn": 23}, {"ruleId": "1179", "severity": 1, "message": "1242", "line": 20, "column": 13, "nodeType": "1181", "messageId": "1182", "endLine": 20, "endColumn": 23}, {"ruleId": "1179", "severity": 1, "message": "1243", "line": 21, "column": 15, "nodeType": "1181", "messageId": "1182", "endLine": 21, "endColumn": 27}, {"ruleId": "1179", "severity": 1, "message": "1244", "line": 9, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 9, "endColumn": 12}, {"ruleId": "1179", "severity": 1, "message": "1245", "line": 26, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 26, "endColumn": 9}, {"ruleId": "1179", "severity": 1, "message": "1246", "line": 31, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 31, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1247", "line": 34, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 34, "endColumn": 19}, {"ruleId": "1179", "severity": 1, "message": "1248", "line": 40, "column": 11, "nodeType": "1181", "messageId": "1182", "endLine": 40, "endColumn": 19}, {"ruleId": "1214", "severity": 1, "message": "1249", "line": 310, "column": 6, "nodeType": "1216", "endLine": 310, "endColumn": 54, "suggestions": "1250"}, {"ruleId": "1179", "severity": 1, "message": "1251", "line": 367, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 367, "endColumn": 26}, {"ruleId": "1179", "severity": 1, "message": "1252", "line": 40, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 40, "endColumn": 14}, {"ruleId": "1253", "severity": 1, "message": "1254", "line": 93, "column": 66, "nodeType": "1189", "messageId": "1255", "endLine": 93, "endColumn": 67, "suggestions": "1256"}, {"ruleId": "1253", "severity": 1, "message": "1254", "line": 93, "column": 75, "nodeType": "1189", "messageId": "1255", "endLine": 93, "endColumn": 76, "suggestions": "1257"}, {"ruleId": "1253", "severity": 1, "message": "1258", "line": 93, "column": 77, "nodeType": "1189", "messageId": "1255", "endLine": 93, "endColumn": 78, "suggestions": "1259"}, {"ruleId": "1179", "severity": 1, "message": "1260", "line": 37, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 37, "endColumn": 15}, {"ruleId": "1179", "severity": 1, "message": "1260", "line": 37, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 37, "endColumn": 15}, {"ruleId": "1179", "severity": 1, "message": "1261", "line": 1, "column": 27, "nodeType": "1181", "messageId": "1182", "endLine": 1, "endColumn": 33}, {"ruleId": "1179", "severity": 1, "message": "1262", "line": 30, "column": 13, "nodeType": "1181", "messageId": "1182", "endLine": 30, "endColumn": 23}, {"ruleId": "1214", "severity": 1, "message": "1263", "line": 110, "column": 6, "nodeType": "1216", "endLine": 110, "endColumn": 37, "suggestions": "1264"}, {"ruleId": "1179", "severity": 1, "message": "1265", "line": 2, "column": 23, "nodeType": "1181", "messageId": "1182", "endLine": 2, "endColumn": 27}, {"ruleId": "1179", "severity": 1, "message": "1266", "line": 23, "column": 15, "nodeType": "1181", "messageId": "1182", "endLine": 23, "endColumn": 27}, {"ruleId": "1179", "severity": 1, "message": "1260", "line": 40, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 40, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1267", "line": 32, "column": 15, "nodeType": "1181", "messageId": "1182", "endLine": 32, "endColumn": 27}, {"ruleId": "1179", "severity": 1, "message": "1268", "line": 36, "column": 18, "nodeType": "1181", "messageId": "1182", "endLine": 36, "endColumn": 33}, {"ruleId": "1179", "severity": 1, "message": "1269", "line": 37, "column": 20, "nodeType": "1181", "messageId": "1182", "endLine": 37, "endColumn": 37}, {"ruleId": "1179", "severity": 1, "message": "1270", "line": 27, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 27, "endColumn": 8}, {"ruleId": "1214", "severity": 1, "message": "1271", "line": 82, "column": 6, "nodeType": "1216", "endLine": 82, "endColumn": 41, "suggestions": "1272"}, {"ruleId": "1179", "severity": 1, "message": "1273", "line": 15, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 15, "endColumn": 19}, {"ruleId": "1179", "severity": 1, "message": "1274", "line": 26, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 26, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1275", "line": 27, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 27, "endColumn": 13}, {"ruleId": "1179", "severity": 1, "message": "1276", "line": 28, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 28, "endColumn": 9}, {"ruleId": "1179", "severity": 1, "message": "1277", "line": 29, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 29, "endColumn": 11}, {"ruleId": "1214", "severity": 1, "message": "1278", "line": 84, "column": 6, "nodeType": "1216", "endLine": 84, "endColumn": 41, "suggestions": "1279"}, {"ruleId": "1179", "severity": 1, "message": "1239", "line": 21, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 21, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1240", "line": 22, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 22, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1246", "line": 32, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 32, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1280", "line": 39, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 39, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1281", "line": 58, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 58, "endColumn": 16}, {"ruleId": "1179", "severity": 1, "message": "1282", "line": 70, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 70, "endColumn": 25}, {"ruleId": "1179", "severity": 1, "message": "1283", "line": 190, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 190, "endColumn": 30}, {"ruleId": "1179", "severity": 1, "message": "1284", "line": 194, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 194, "endColumn": 31}, {"ruleId": "1253", "severity": 1, "message": "1254", "line": 203, "column": 66, "nodeType": "1189", "messageId": "1255", "endLine": 203, "endColumn": 67, "suggestions": "1285"}, {"ruleId": "1253", "severity": 1, "message": "1254", "line": 203, "column": 75, "nodeType": "1189", "messageId": "1255", "endLine": 203, "endColumn": 76, "suggestions": "1286"}, {"ruleId": "1253", "severity": 1, "message": "1258", "line": 203, "column": 77, "nodeType": "1189", "messageId": "1255", "endLine": 203, "endColumn": 78, "suggestions": "1287"}, {"ruleId": "1179", "severity": 1, "message": "1288", "line": 253, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 253, "endColumn": 22}, {"ruleId": "1214", "severity": 1, "message": "1289", "line": 43, "column": 6, "nodeType": "1216", "endLine": 43, "endColumn": 8, "suggestions": "1290"}, {"ruleId": "1214", "severity": 1, "message": "1291", "line": 43, "column": 6, "nodeType": "1216", "endLine": 43, "endColumn": 8, "suggestions": "1292"}, {"ruleId": "1179", "severity": 1, "message": "1293", "line": 39, "column": 11, "nodeType": "1181", "messageId": "1182", "endLine": 39, "endColumn": 12}, {"ruleId": "1214", "severity": 1, "message": "1294", "line": 88, "column": 6, "nodeType": "1216", "endLine": 88, "endColumn": 39, "suggestions": "1295"}, {"ruleId": "1179", "severity": 1, "message": "1296", "line": 40, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 40, "endColumn": 24}, {"ruleId": "1214", "severity": 1, "message": "1297", "line": 107, "column": 6, "nodeType": "1216", "endLine": 107, "endColumn": 15, "suggestions": "1298"}, {"ruleId": "1214", "severity": 1, "message": "1299", "line": 45, "column": 6, "nodeType": "1216", "endLine": 45, "endColumn": 45, "suggestions": "1300"}, {"ruleId": "1179", "severity": 1, "message": "1301", "line": 33, "column": 12, "nodeType": "1181", "messageId": "1182", "endLine": 33, "endColumn": 21}, {"ruleId": "1214", "severity": 1, "message": "1302", "line": 103, "column": 6, "nodeType": "1216", "endLine": 103, "endColumn": 42, "suggestions": "1303"}, {"ruleId": "1214", "severity": 1, "message": "1304", "line": 50, "column": 6, "nodeType": "1216", "endLine": 50, "endColumn": 25, "suggestions": "1305"}, {"ruleId": "1179", "severity": 1, "message": "1306", "line": 54, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 54, "endColumn": 17}, {"ruleId": "1214", "severity": 1, "message": "1307", "line": 60, "column": 6, "nodeType": "1216", "endLine": 60, "endColumn": 39, "suggestions": "1308"}, {"ruleId": "1179", "severity": 1, "message": "1309", "line": 31, "column": 24, "nodeType": "1181", "messageId": "1182", "endLine": 31, "endColumn": 34}, {"ruleId": "1214", "severity": 1, "message": "1310", "line": 110, "column": 6, "nodeType": "1216", "endLine": 110, "endColumn": 22, "suggestions": "1311"}, {"ruleId": "1214", "severity": 1, "message": "1310", "line": 133, "column": 6, "nodeType": "1216", "endLine": 133, "endColumn": 19, "suggestions": "1312"}, {"ruleId": "1214", "severity": 1, "message": "1310", "line": 145, "column": 6, "nodeType": "1216", "endLine": 145, "endColumn": 19, "suggestions": "1313"}, {"ruleId": "1214", "severity": 1, "message": "1310", "line": 162, "column": 6, "nodeType": "1216", "endLine": 162, "endColumn": 19, "suggestions": "1314"}, {"ruleId": "1179", "severity": 1, "message": "1315", "line": 10, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 10, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1316", "line": 13, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 13, "endColumn": 8}, {"ruleId": "1179", "severity": 1, "message": "1317", "line": 14, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 14, "endColumn": 12}, {"ruleId": "1179", "severity": 1, "message": "1318", "line": 15, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 15, "endColumn": 12}, {"ruleId": "1179", "severity": 1, "message": "1319", "line": 16, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 16, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1320", "line": 17, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 17, "endColumn": 12}, {"ruleId": "1179", "severity": 1, "message": "1321", "line": 18, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 18, "endColumn": 11}, {"ruleId": "1179", "severity": 1, "message": "1224", "line": 20, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 20, "endColumn": 13}, {"ruleId": "1179", "severity": 1, "message": "1246", "line": 22, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 22, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1322", "line": 31, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 31, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1323", "line": 52, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 52, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1324", "line": 52, "column": 23, "nodeType": "1181", "messageId": "1182", "endLine": 52, "endColumn": 37}, {"ruleId": "1179", "severity": 1, "message": "1325", "line": 53, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 53, "endColumn": 22}, {"ruleId": "1179", "severity": 1, "message": "1326", "line": 53, "column": 24, "nodeType": "1181", "messageId": "1182", "endLine": 53, "endColumn": 39}, {"ruleId": "1179", "severity": 1, "message": "1327", "line": 227, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 227, "endColumn": 24}, {"ruleId": "1179", "severity": 1, "message": "1328", "line": 236, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 236, "endColumn": 23}, {"ruleId": "1179", "severity": 1, "message": "1329", "line": 245, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 245, "endColumn": 29}, {"ruleId": "1179", "severity": 1, "message": "1330", "line": 258, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 258, "endColumn": 28}, {"ruleId": "1179", "severity": 1, "message": "1331", "line": 2, "column": 23, "nodeType": "1181", "messageId": "1182", "endLine": 2, "endColumn": 34}, {"ruleId": "1179", "severity": 1, "message": "1332", "line": 4, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 4, "endColumn": 13}, {"ruleId": "1179", "severity": 1, "message": "1224", "line": 13, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 13, "endColumn": 13}, {"ruleId": "1179", "severity": 1, "message": "1246", "line": 15, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 15, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1323", "line": 45, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 45, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1324", "line": 45, "column": 23, "nodeType": "1181", "messageId": "1182", "endLine": 45, "endColumn": 37}, {"ruleId": "1179", "severity": 1, "message": "1325", "line": 46, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 46, "endColumn": 22}, {"ruleId": "1179", "severity": 1, "message": "1326", "line": 46, "column": 24, "nodeType": "1181", "messageId": "1182", "endLine": 46, "endColumn": 39}, {"ruleId": "1179", "severity": 1, "message": "1333", "line": 5, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 5, "endColumn": 9}, {"ruleId": "1179", "severity": 1, "message": "1244", "line": 7, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 7, "endColumn": 12}, {"ruleId": "1179", "severity": 1, "message": "1239", "line": 9, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 9, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1240", "line": 10, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 10, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1315", "line": 11, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 11, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1274", "line": 16, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 16, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1275", "line": 17, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 17, "endColumn": 13}, {"ruleId": "1179", "severity": 1, "message": "1276", "line": 18, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 18, "endColumn": 9}, {"ruleId": "1179", "severity": 1, "message": "1277", "line": 19, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 19, "endColumn": 11}, {"ruleId": "1179", "severity": 1, "message": "1334", "line": 33, "column": 18, "nodeType": "1181", "messageId": "1182", "endLine": 33, "endColumn": 33}, {"ruleId": "1179", "severity": 1, "message": "1335", "line": 46, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 46, "endColumn": 33}, {"ruleId": "1179", "severity": 1, "message": "1336", "line": 46, "column": 35, "nodeType": "1181", "messageId": "1182", "endLine": 46, "endColumn": 59}, {"ruleId": "1179", "severity": 1, "message": "1180", "line": 51, "column": 11, "nodeType": "1181", "messageId": "1182", "endLine": 51, "endColumn": 22}, {"ruleId": "1179", "severity": 1, "message": "1337", "line": 58, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 58, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1338", "line": 110, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 110, "endColumn": 23}, {"ruleId": "1179", "severity": 1, "message": "1339", "line": 154, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 154, "endColumn": 23}, {"ruleId": "1179", "severity": 1, "message": "1340", "line": 215, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 215, "endColumn": 26}, {"ruleId": "1179", "severity": 1, "message": "1274", "line": 21, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 21, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1275", "line": 22, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 22, "endColumn": 13}, {"ruleId": "1179", "severity": 1, "message": "1276", "line": 23, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 23, "endColumn": 9}, {"ruleId": "1179", "severity": 1, "message": "1277", "line": 24, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 24, "endColumn": 11}, {"ruleId": "1179", "severity": 1, "message": "1335", "line": 46, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 46, "endColumn": 33}, {"ruleId": "1179", "severity": 1, "message": "1336", "line": 46, "column": 64, "nodeType": "1181", "messageId": "1182", "endLine": 46, "endColumn": 88}, {"ruleId": "1179", "severity": 1, "message": "1337", "line": 67, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 67, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1341", "line": 72, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 72, "endColumn": 30}, {"ruleId": "1179", "severity": 1, "message": "1323", "line": 73, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 73, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1342", "line": 74, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 74, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1343", "line": 75, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 75, "endColumn": 22}, {"ruleId": "1179", "severity": 1, "message": "1344", "line": 75, "column": 24, "nodeType": "1181", "messageId": "1182", "endLine": 75, "endColumn": 39}, {"ruleId": "1214", "severity": 1, "message": "1345", "line": 171, "column": 6, "nodeType": "1216", "endLine": 171, "endColumn": 16, "suggestions": "1346"}, {"ruleId": "1214", "severity": 1, "message": "1347", "line": 178, "column": 6, "nodeType": "1216", "endLine": 178, "endColumn": 31, "suggestions": "1348"}, {"ruleId": "1179", "severity": 1, "message": "1349", "line": 478, "column": 13, "nodeType": "1181", "messageId": "1182", "endLine": 478, "endColumn": 21}, {"ruleId": "1214", "severity": 1, "message": "1249", "line": 180, "column": 6, "nodeType": "1216", "endLine": 180, "endColumn": 28, "suggestions": "1350"}, {"ruleId": "1214", "severity": 1, "message": "1351", "line": 217, "column": 6, "nodeType": "1216", "endLine": 217, "endColumn": 28, "suggestions": "1352"}, {"ruleId": "1214", "severity": 1, "message": "1353", "line": 257, "column": 6, "nodeType": "1216", "endLine": 257, "endColumn": 14, "suggestions": "1354"}, {"ruleId": "1214", "severity": 1, "message": "1355", "line": 324, "column": 6, "nodeType": "1216", "endLine": 324, "endColumn": 53, "suggestions": "1356"}, {"ruleId": "1179", "severity": 1, "message": "1246", "line": 14, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 14, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1227", "line": 30, "column": 15, "nodeType": "1181", "messageId": "1182", "endLine": 30, "endColumn": 27}, {"ruleId": "1253", "severity": 1, "message": "1254", "line": 71, "column": 64, "nodeType": "1189", "messageId": "1255", "endLine": 71, "endColumn": 65, "suggestions": "1357"}, {"ruleId": "1253", "severity": 1, "message": "1254", "line": 71, "column": 73, "nodeType": "1189", "messageId": "1255", "endLine": 71, "endColumn": 74, "suggestions": "1358"}, {"ruleId": "1253", "severity": 1, "message": "1258", "line": 71, "column": 75, "nodeType": "1189", "messageId": "1255", "endLine": 71, "endColumn": 76, "suggestions": "1359"}, {"ruleId": "1179", "severity": 1, "message": "1309", "line": 92, "column": 17, "nodeType": "1181", "messageId": "1182", "endLine": 92, "endColumn": 27}, {"ruleId": "1179", "severity": 1, "message": "1260", "line": 93, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 93, "endColumn": 15}, {"ruleId": "1179", "severity": 1, "message": "1360", "line": 93, "column": 17, "nodeType": "1181", "messageId": "1182", "endLine": 93, "endColumn": 25}, {"ruleId": "1179", "severity": 1, "message": "1361", "line": 25, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 25, "endColumn": 12}, {"ruleId": "1179", "severity": 1, "message": "1268", "line": 22, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 22, "endColumn": 23}, {"ruleId": "1214", "severity": 1, "message": "1299", "line": 46, "column": 6, "nodeType": "1216", "endLine": 46, "endColumn": 45, "suggestions": "1362"}, {"ruleId": "1214", "severity": 1, "message": "1363", "line": 65, "column": 6, "nodeType": "1216", "endLine": 65, "endColumn": 45, "suggestions": "1364"}, {"ruleId": "1214", "severity": 1, "message": "1302", "line": 83, "column": 6, "nodeType": "1216", "endLine": 83, "endColumn": 32, "suggestions": "1365"}, {"ruleId": "1179", "severity": 1, "message": "1366", "line": 17, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 17, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1367", "line": 29, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 29, "endColumn": 11}, {"ruleId": "1179", "severity": 1, "message": "1248", "line": 32, "column": 11, "nodeType": "1181", "messageId": "1182", "endLine": 32, "endColumn": 19}, {"ruleId": "1179", "severity": 1, "message": "1368", "line": 33, "column": 17, "nodeType": "1181", "messageId": "1182", "endLine": 33, "endColumn": 31}, {"ruleId": "1179", "severity": 1, "message": "1369", "line": 40, "column": 17, "nodeType": "1181", "messageId": "1182", "endLine": 40, "endColumn": 31}, {"ruleId": "1179", "severity": 1, "message": "1370", "line": 41, "column": 17, "nodeType": "1181", "messageId": "1182", "endLine": 41, "endColumn": 31}, {"ruleId": "1179", "severity": 1, "message": "1260", "line": 49, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 49, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1371", "line": 68, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 68, "endColumn": 25}, {"ruleId": "1179", "severity": 1, "message": "1372", "line": 289, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 289, "endColumn": 29}, {"ruleId": "1179", "severity": 1, "message": "1246", "line": 4, "column": 29, "nodeType": "1181", "messageId": "1182", "endLine": 4, "endColumn": 36}, {"ruleId": "1179", "severity": 1, "message": "1180", "line": 28, "column": 11, "nodeType": "1181", "messageId": "1182", "endLine": 28, "endColumn": 22}, {"ruleId": "1179", "severity": 1, "message": "1244", "line": 14, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 14, "endColumn": 12}, {"ruleId": "1179", "severity": 1, "message": "1245", "line": 31, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 31, "endColumn": 9}, {"ruleId": "1179", "severity": 1, "message": "1246", "line": 36, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 36, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1247", "line": 39, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 39, "endColumn": 19}, {"ruleId": "1179", "severity": 1, "message": "1373", "line": 42, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 42, "endColumn": 9}, {"ruleId": "1179", "severity": 1, "message": "1374", "line": 43, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 43, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1375", "line": 44, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 44, "endColumn": 16}, {"ruleId": "1179", "severity": 1, "message": "1376", "line": 45, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 45, "endColumn": 16}, {"ruleId": "1179", "severity": 1, "message": "1248", "line": 51, "column": 11, "nodeType": "1181", "messageId": "1182", "endLine": 51, "endColumn": 19}, {"ruleId": "1179", "severity": 1, "message": "1306", "line": 174, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 174, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1377", "line": 203, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 203, "endColumn": 19}, {"ruleId": "1214", "severity": 1, "message": "1278", "line": 298, "column": 6, "nodeType": "1216", "endLine": 298, "endColumn": 48, "suggestions": "1378"}, {"ruleId": "1179", "severity": 1, "message": "1251", "line": 337, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 337, "endColumn": 26}, {"ruleId": "1253", "severity": 1, "message": "1254", "line": 422, "column": 68, "nodeType": "1189", "messageId": "1255", "endLine": 422, "endColumn": 69, "suggestions": "1379"}, {"ruleId": "1253", "severity": 1, "message": "1254", "line": 422, "column": 77, "nodeType": "1189", "messageId": "1255", "endLine": 422, "endColumn": 78, "suggestions": "1380"}, {"ruleId": "1253", "severity": 1, "message": "1258", "line": 422, "column": 79, "nodeType": "1189", "messageId": "1255", "endLine": 422, "endColumn": 80, "suggestions": "1381"}, {"ruleId": "1179", "severity": 1, "message": "1239", "line": 15, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 15, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1240", "line": 16, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 16, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1376", "line": 30, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 30, "endColumn": 16}, {"ruleId": "1179", "severity": 1, "message": "1382", "line": 31, "column": 17, "nodeType": "1181", "messageId": "1182", "endLine": 31, "endColumn": 30}, {"ruleId": "1179", "severity": 1, "message": "1234", "line": 63, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 63, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1383", "line": 71, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 71, "endColumn": 24}, {"ruleId": "1179", "severity": 1, "message": "1384", "line": 72, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 72, "endColumn": 22}, {"ruleId": "1385", "severity": 1, "message": "1386", "line": 200, "column": 7, "nodeType": "1181", "messageId": "1387", "endLine": 200, "endColumn": 13}, {"ruleId": "1214", "severity": 1, "message": "1388", "line": 315, "column": 6, "nodeType": "1216", "endLine": 315, "endColumn": 39, "suggestions": "1389"}, {"ruleId": "1179", "severity": 1, "message": "1390", "line": 397, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 397, "endColumn": 23}, {"ruleId": "1253", "severity": 1, "message": "1254", "line": 487, "column": 68, "nodeType": "1189", "messageId": "1255", "endLine": 487, "endColumn": 69, "suggestions": "1391"}, {"ruleId": "1253", "severity": 1, "message": "1254", "line": 487, "column": 77, "nodeType": "1189", "messageId": "1255", "endLine": 487, "endColumn": 78, "suggestions": "1392"}, {"ruleId": "1253", "severity": 1, "message": "1258", "line": 487, "column": 79, "nodeType": "1189", "messageId": "1255", "endLine": 487, "endColumn": 80, "suggestions": "1393"}, {"ruleId": "1179", "severity": 1, "message": "1394", "line": 7, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 7, "endColumn": 13}, {"ruleId": "1179", "severity": 1, "message": "1395", "line": 22, "column": 19, "nodeType": "1181", "messageId": "1182", "endLine": 22, "endColumn": 30}, {"ruleId": "1214", "severity": 1, "message": "1249", "line": 196, "column": 6, "nodeType": "1216", "endLine": 196, "endColumn": 28, "suggestions": "1396"}, {"ruleId": "1214", "severity": 1, "message": "1351", "line": 233, "column": 6, "nodeType": "1216", "endLine": 233, "endColumn": 28, "suggestions": "1397"}, {"ruleId": "1214", "severity": 1, "message": "1353", "line": 273, "column": 6, "nodeType": "1216", "endLine": 273, "endColumn": 14, "suggestions": "1398"}, {"ruleId": "1179", "severity": 1, "message": "1399", "line": 24, "column": 24, "nodeType": "1181", "messageId": "1182", "endLine": 24, "endColumn": 29}, {"ruleId": "1214", "severity": 1, "message": "1302", "line": 84, "column": 6, "nodeType": "1216", "endLine": 84, "endColumn": 32, "suggestions": "1400"}, {"ruleId": "1179", "severity": 1, "message": "1315", "line": 5, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 5, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1239", "line": 6, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 6, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1265", "line": 20, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 20, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1401", "line": 27, "column": 18, "nodeType": "1181", "messageId": "1182", "endLine": 27, "endColumn": 27}, {"ruleId": "1179", "severity": 1, "message": "1402", "line": 29, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 29, "endColumn": 15}, {"ruleId": "1179", "severity": 1, "message": "1337", "line": 44, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 44, "endColumn": 21}, {"ruleId": "1214", "severity": 1, "message": "1403", "line": 66, "column": 6, "nodeType": "1216", "endLine": 66, "endColumn": 19, "suggestions": "1404"}, {"ruleId": "1179", "severity": 1, "message": "1405", "line": 127, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 127, "endColumn": 40}, {"ruleId": "1179", "severity": 1, "message": "1406", "line": 135, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 135, "endColumn": 43}, {"ruleId": "1179", "severity": 1, "message": "1407", "line": 147, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 147, "endColumn": 22}, {"ruleId": "1179", "severity": 1, "message": "1408", "line": 156, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 156, "endColumn": 40}, {"ruleId": "1179", "severity": 1, "message": "1409", "line": 184, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 184, "endColumn": 23}, {"ruleId": "1214", "severity": 1, "message": "1299", "line": 74, "column": 6, "nodeType": "1216", "endLine": 74, "endColumn": 45, "suggestions": "1410"}, {"ruleId": "1179", "severity": 1, "message": "1366", "line": 14, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 14, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1411", "line": 17, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 17, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1246", "line": 18, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 18, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1412", "line": 19, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 19, "endColumn": 9}, {"ruleId": "1179", "severity": 1, "message": "1413", "line": 49, "column": 40, "nodeType": "1181", "messageId": "1182", "endLine": 49, "endColumn": 49}, {"ruleId": "1179", "severity": 1, "message": "1414", "line": 51, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 51, "endColumn": 32}, {"ruleId": "1179", "severity": 1, "message": "1336", "line": 51, "column": 34, "nodeType": "1181", "messageId": "1182", "endLine": 51, "endColumn": 58}, {"ruleId": "1179", "severity": 1, "message": "1335", "line": 51, "column": 89, "nodeType": "1181", "messageId": "1182", "endLine": 51, "endColumn": 112}, {"ruleId": "1179", "severity": 1, "message": "1306", "line": 59, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 59, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1234", "line": 62, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 62, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1408", "line": 278, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 278, "endColumn": 40}, {"ruleId": "1179", "severity": 1, "message": "1415", "line": 409, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 409, "endColumn": 28}, {"ruleId": "1179", "severity": 1, "message": "1416", "line": 428, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 428, "endColumn": 35}, {"ruleId": "1179", "severity": 1, "message": "1417", "line": 451, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 451, "endColumn": 36}, {"ruleId": "1179", "severity": 1, "message": "1418", "line": 712, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 712, "endColumn": 29}, {"ruleId": "1179", "severity": 1, "message": "1274", "line": 25, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 25, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1275", "line": 26, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 26, "endColumn": 13}, {"ruleId": "1179", "severity": 1, "message": "1276", "line": 27, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 27, "endColumn": 9}, {"ruleId": "1179", "severity": 1, "message": "1277", "line": 28, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 28, "endColumn": 11}, {"ruleId": "1179", "severity": 1, "message": "1419", "line": 33, "column": 13, "nodeType": "1181", "messageId": "1182", "endLine": 33, "endColumn": 23}, {"ruleId": "1179", "severity": 1, "message": "1341", "line": 88, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 88, "endColumn": 30}, {"ruleId": "1179", "severity": 1, "message": "1323", "line": 89, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 89, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1342", "line": 90, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 90, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1343", "line": 91, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 91, "endColumn": 22}, {"ruleId": "1179", "severity": 1, "message": "1344", "line": 91, "column": 24, "nodeType": "1181", "messageId": "1182", "endLine": 91, "endColumn": 39}, {"ruleId": "1179", "severity": 1, "message": "1349", "line": 285, "column": 13, "nodeType": "1181", "messageId": "1182", "endLine": 285, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1420", "line": 622, "column": 12, "nodeType": "1181", "messageId": "1182", "endLine": 622, "endColumn": 20}, {"ruleId": "1179", "severity": 1, "message": "1349", "line": 643, "column": 13, "nodeType": "1181", "messageId": "1182", "endLine": 643, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1421", "line": 666, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 666, "endColumn": 23}, {"ruleId": "1179", "severity": 1, "message": "1248", "line": 27, "column": 11, "nodeType": "1181", "messageId": "1182", "endLine": 27, "endColumn": 19}, {"ruleId": "1179", "severity": 1, "message": "1306", "line": 36, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 36, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1260", "line": 37, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 37, "endColumn": 14}, {"ruleId": "1214", "severity": 1, "message": "1422", "line": 467, "column": 6, "nodeType": "1216", "endLine": 467, "endColumn": 32, "suggestions": "1423"}, {"ruleId": "1179", "severity": 1, "message": "1424", "line": 18, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 18, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1280", "line": 22, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 22, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1265", "line": 34, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 34, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1425", "line": 49, "column": 20, "nodeType": "1181", "messageId": "1182", "endLine": 49, "endColumn": 37}, {"ruleId": "1214", "severity": 1, "message": "1426", "line": 111, "column": 6, "nodeType": "1216", "endLine": 111, "endColumn": 38, "suggestions": "1427"}, {"ruleId": "1179", "severity": 1, "message": "1366", "line": 8, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 8, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1224", "line": 17, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 17, "endColumn": 13}, {"ruleId": "1179", "severity": 1, "message": "1428", "line": 21, "column": 17, "nodeType": "1181", "messageId": "1182", "endLine": 21, "endColumn": 31}, {"ruleId": "1179", "severity": 1, "message": "1429", "line": 28, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 28, "endColumn": 14}, {"ruleId": "1179", "severity": 1, "message": "1430", "line": 18, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 18, "endColumn": 23}, {"ruleId": "1179", "severity": 1, "message": "1431", "line": 148, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 148, "endColumn": 25}, {"ruleId": "1179", "severity": 1, "message": "1432", "line": 158, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 158, "endColumn": 24}, {"ruleId": "1179", "severity": 1, "message": "1433", "line": 177, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 177, "endColumn": 29}, {"ruleId": "1179", "severity": 1, "message": "1434", "line": 306, "column": 17, "nodeType": "1181", "messageId": "1182", "endLine": 306, "endColumn": 30}, {"ruleId": "1179", "severity": 1, "message": "1246", "line": 11, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 11, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1435", "line": 26, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 26, "endColumn": 29}, {"ruleId": "1179", "severity": 1, "message": "1436", "line": 27, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 27, "endColumn": 12}, {"ruleId": "1179", "severity": 1, "message": "1435", "line": 21, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 21, "endColumn": 29}, {"ruleId": "1179", "severity": 1, "message": "1436", "line": 22, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 22, "endColumn": 12}, {"ruleId": "1179", "severity": 1, "message": "1437", "line": 18, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 18, "endColumn": 12}, {"ruleId": "1179", "severity": 1, "message": "1438", "line": 19, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 19, "endColumn": 19}, {"ruleId": "1179", "severity": 1, "message": "1439", "line": 20, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 20, "endColumn": 19}, {"ruleId": "1179", "severity": 1, "message": "1247", "line": 21, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 21, "endColumn": 19}, {"ruleId": "1179", "severity": 1, "message": "1440", "line": 22, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 22, "endColumn": 11}, {"ruleId": "1179", "severity": 1, "message": "1441", "line": 24, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 24, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1246", "line": 34, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 34, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1369", "line": 43, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 43, "endColumn": 22}, {"ruleId": "1179", "severity": 1, "message": "1442", "line": 44, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 44, "endColumn": 21}, {"ruleId": "1179", "severity": 1, "message": "1443", "line": 45, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 45, "endColumn": 16}, {"ruleId": "1179", "severity": 1, "message": "1444", "line": 127, "column": 9, "nodeType": "1181", "messageId": "1182", "endLine": 127, "endColumn": 18}, {"ruleId": "1179", "severity": 1, "message": "1261", "line": 1, "column": 38, "nodeType": "1181", "messageId": "1182", "endLine": 1, "endColumn": 44}, {"ruleId": "1179", "severity": 1, "message": "1445", "line": 1, "column": 46, "nodeType": "1181", "messageId": "1182", "endLine": 1, "endColumn": 53}, {"ruleId": "1179", "severity": 1, "message": "1446", "line": 4, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 4, "endColumn": 18}, {"ruleId": "1179", "severity": 1, "message": "1447", "line": 5, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 5, "endColumn": 13}, {"ruleId": "1179", "severity": 1, "message": "1448", "line": 6, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 6, "endColumn": 17}, {"ruleId": "1179", "severity": 1, "message": "1449", "line": 9, "column": 46, "nodeType": "1181", "messageId": "1182", "endLine": 9, "endColumn": 71}, {"ruleId": "1179", "severity": 1, "message": "1450", "line": 9, "column": 73, "nodeType": "1181", "messageId": "1182", "endLine": 9, "endColumn": 88}, {"ruleId": "1179", "severity": 1, "message": "1451", "line": 14, "column": 8, "nodeType": "1181", "messageId": "1182", "endLine": 14, "endColumn": 27}, {"ruleId": "1214", "severity": 1, "message": "1452", "line": 49, "column": 6, "nodeType": "1216", "endLine": 49, "endColumn": 22, "suggestions": "1453"}, {"ruleId": "1214", "severity": 1, "message": "1454", "line": 40, "column": 8, "nodeType": "1216", "endLine": 40, "endColumn": 33, "suggestions": "1455"}, {"ruleId": "1214", "severity": 1, "message": "1456", "line": 128, "column": 6, "nodeType": "1216", "endLine": 128, "endColumn": 33, "suggestions": "1457"}, {"ruleId": "1214", "severity": 1, "message": "1458", "line": 132, "column": 6, "nodeType": "1216", "endLine": 132, "endColumn": 37, "suggestions": "1459"}, {"ruleId": "1214", "severity": 1, "message": "1460", "line": 137, "column": 6, "nodeType": "1216", "endLine": 137, "endColumn": 8, "suggestions": "1461"}, {"ruleId": "1214", "severity": 1, "message": "1462", "line": 300, "column": 6, "nodeType": "1216", "endLine": 300, "endColumn": 8, "suggestions": "1463"}, {"ruleId": "1214", "severity": 1, "message": "1458", "line": 403, "column": 6, "nodeType": "1216", "endLine": 403, "endColumn": 8, "suggestions": "1464"}, {"ruleId": "1214", "severity": 1, "message": "1465", "line": 28, "column": 6, "nodeType": "1216", "endLine": 28, "endColumn": 8, "suggestions": "1466"}, {"ruleId": "1214", "severity": 1, "message": "1467", "line": 230, "column": 6, "nodeType": "1216", "endLine": 230, "endColumn": 34, "suggestions": "1468"}, {"ruleId": "1214", "severity": 1, "message": "1469", "line": 220, "column": 6, "nodeType": "1216", "endLine": 220, "endColumn": 50, "suggestions": "1470"}, {"ruleId": "1214", "severity": 1, "message": "1471", "line": 59, "column": 6, "nodeType": "1216", "endLine": 59, "endColumn": 23, "suggestions": "1472"}, {"ruleId": "1473", "severity": 1, "message": "1474", "line": 148, "column": 37, "nodeType": "1475", "messageId": "1194", "endLine": 148, "endColumn": 39}, {"ruleId": "1473", "severity": 1, "message": "1476", "line": 173, "column": 82, "nodeType": "1475", "messageId": "1194", "endLine": 173, "endColumn": 84}, {"ruleId": "1473", "severity": 1, "message": "1476", "line": 228, "column": 104, "nodeType": "1475", "messageId": "1194", "endLine": 228, "endColumn": 106}, {"ruleId": "1473", "severity": 1, "message": "1476", "line": 257, "column": 44, "nodeType": "1475", "messageId": "1194", "endLine": 257, "endColumn": 46}, {"ruleId": "1473", "severity": 1, "message": "1476", "line": 261, "column": 44, "nodeType": "1475", "messageId": "1194", "endLine": 261, "endColumn": 46}, {"ruleId": "1473", "severity": 1, "message": "1476", "line": 265, "column": 44, "nodeType": "1475", "messageId": "1194", "endLine": 265, "endColumn": 46}, {"ruleId": "1473", "severity": 1, "message": "1476", "line": 271, "column": 44, "nodeType": "1475", "messageId": "1194", "endLine": 271, "endColumn": 46}, {"ruleId": "1477", "severity": 1, "message": "1478", "line": 9, "column": 23, "nodeType": "1479", "messageId": "1194", "endLine": 9, "endColumn": 26}, {"ruleId": "1214", "severity": 1, "message": "1480", "line": 32, "column": 6, "nodeType": "1216", "endLine": 32, "endColumn": 28, "suggestions": "1481"}, {"ruleId": "1179", "severity": 1, "message": "1366", "line": 17, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 17, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1482", "line": 23, "column": 15, "nodeType": "1181", "messageId": "1182", "endLine": 23, "endColumn": 27}, {"ruleId": "1179", "severity": 1, "message": "1483", "line": 25, "column": 14, "nodeType": "1181", "messageId": "1182", "endLine": 25, "endColumn": 25}, {"ruleId": "1179", "severity": 1, "message": "1484", "line": 28, "column": 14, "nodeType": "1181", "messageId": "1182", "endLine": 28, "endColumn": 25}, {"ruleId": "1179", "severity": 1, "message": "1428", "line": 30, "column": 17, "nodeType": "1181", "messageId": "1182", "endLine": 30, "endColumn": 31}, {"ruleId": "1179", "severity": 1, "message": "1485", "line": 31, "column": 17, "nodeType": "1181", "messageId": "1182", "endLine": 31, "endColumn": 31}, {"ruleId": "1179", "severity": 1, "message": "1366", "line": 15, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 15, "endColumn": 7}, {"ruleId": "1179", "severity": 1, "message": "1270", "line": 20, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 20, "endColumn": 8}, {"ruleId": "1179", "severity": 1, "message": "1482", "line": 23, "column": 15, "nodeType": "1181", "messageId": "1182", "endLine": 23, "endColumn": 27}, {"ruleId": "1179", "severity": 1, "message": "1484", "line": 26, "column": 14, "nodeType": "1181", "messageId": "1182", "endLine": 26, "endColumn": 25}, {"ruleId": "1179", "severity": 1, "message": "1419", "line": 28, "column": 13, "nodeType": "1181", "messageId": "1182", "endLine": 28, "endColumn": 23}, {"ruleId": "1179", "severity": 1, "message": "1486", "line": 29, "column": 13, "nodeType": "1181", "messageId": "1182", "endLine": 29, "endColumn": 23}, {"ruleId": "1179", "severity": 1, "message": "1335", "line": 19, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 19, "endColumn": 33}, {"ruleId": "1179", "severity": 1, "message": "1482", "line": 33, "column": 15, "nodeType": "1181", "messageId": "1182", "endLine": 33, "endColumn": 27}, {"ruleId": "1179", "severity": 1, "message": "1246", "line": 20, "column": 3, "nodeType": "1181", "messageId": "1182", "endLine": 20, "endColumn": 10}, {"ruleId": "1179", "severity": 1, "message": "1487", "line": 104, "column": 11, "nodeType": "1181", "messageId": "1182", "endLine": 104, "endColumn": 25}, {"ruleId": "1214", "severity": 1, "message": "1488", "line": 225, "column": 7, "nodeType": "1489", "endLine": 225, "endColumn": 48}, {"ruleId": "1179", "severity": 1, "message": "1490", "line": 836, "column": 33, "nodeType": "1181", "messageId": "1182", "endLine": 836, "endColumn": 42}, {"ruleId": "1179", "severity": 1, "message": "1491", "line": 49, "column": 10, "nodeType": "1181", "messageId": "1182", "endLine": 49, "endColumn": 17}, {"ruleId": "1214", "severity": 1, "message": "1492", "line": 93, "column": 6, "nodeType": "1216", "endLine": 93, "endColumn": 45, "suggestions": "1493"}, "no-unused-vars", "'currentUser' is assigned a value but never used.", "Identifier", "unusedVar", "'isAuthenticated' is assigned a value but never used.", "'location' is assigned a value but never used.", "'meetingIssuesEn' is defined but never used.", "'meetingIssuesAr' is defined but never used.", "no-template-curly-in-string", "Unexpected template string expression.", "Literal", "unexpectedTemplateExpression", "no-dupe-keys", "Duplicate key 'details'.", "ObjectExpression", "unexpected", "Duplicate key 'editInfo'.", "Duplicate key 'common'.", "Duplicate key 'noTeachersFound'.", "Duplicate key 'booking'.", "Duplicate key 'selectDuration'.", "Duplicate key 'currentBooking'.", "Duplicate key 'invalidCode'.", "Duplicate key 'verificationFailed'.", "Duplicate key 'updateProfile'.", "Duplicate key 'nativeLanguage'.", "Duplicate key 'teachingLanguages'.", "Duplicate key 'qualifications'.", "Duplicate key 'formHasErrors'.", "Duplicate key 'allowedFormats'.", "Duplicate key 'maxFileSize'.", "Duplicate key 'admin'.", "Duplicate key 'about'.", "Duplicate key 'errorCancelling'.", "Duplicate key 'earnings'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleLogout'. Either include it or remove the dependency array.", "ArrayExpression", ["1494"], "React Hook useEffect has a missing dependency: 'socket'. Either include it or remove the dependency array.", ["1495"], "React Hook useEffect has a missing dependency: 'fetchUnreadCount'. Either include it or remove the dependency array.", ["1496"], "Duplicate key 'gap'.", "'CardMedia' is defined but never used.", "'IconButton' is defined but never used.", "'Slide' is defined but never used.", "'Zoom' is defined but never used.", "'LanguageIcon' is defined but never used.", "'Star' is defined but never used.", "'Timeline' is defined but never used.", "'LocalLibrary' is defined but never used.", "'Language' is defined but never used.", "'TranslateIcon' is defined but never used.", "'useAuth' is defined but never used.", "'isMobile' is assigned a value but never used.", "'handleLanguageChange' is assigned a value but never used.", "no-const-assign", "'isRtl' is constant.", "const", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'PeopleIcon' is defined but never used.", "'PublicIcon' is defined but never used.", "'SecurityIcon' is defined but never used.", "'TextField' is defined but never used.", "'Drawer' is defined but never used.", "'Divider' is defined but never used.", "'FormControlLabel' is defined but never used.", "'StarIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", ["1497"], "'getPriceRangeText' is assigned a value but never used.", "'theme' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\&.", "unnecessaryEscape", ["1498", "1499"], ["1500", "1501"], "Unnecessary escape character: \\?.", ["1502", "1503"], "'isRtl' is assigned a value but never used.", "'useRef' is defined but never used.", "'GoogleIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'location'. Either include it or remove the dependency array.", ["1504"], "'Link' is defined but never used.", "'VerifiedIcon' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'ArrowUpwardIcon' is defined but never used.", "'ArrowDownwardIcon' is defined but never used.", "'Stack' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["1505"], "'CircularProgress' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTeachers'. Either include it or remove the dependency array.", ["1506"], "'Tooltip' is defined but never used.", "'debounce' is defined but never used.", "'openVideoDialog' is assigned a value but never used.", "'handleOpenVideoDialog' is assigned a value but never used.", "'handleCloseVideoDialog' is assigned a value but never used.", ["1507", "1508"], ["1509", "1510"], ["1511", "1512"], "'safeParseJSON' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLanguages'. Either include it or remove the dependency array.", ["1513"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", ["1514"], "'t' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUpdates'. Either include it or remove the dependency array.", ["1515"], "'recentSessions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSessions'. Either include it or remove the dependency array.", ["1516"], "React Hook useEffect has missing dependencies: 'fetchBalance' and 'fetchTransactions'. Either include them or remove the dependency array.", ["1517"], "'CheckIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", ["1518"], "React Hook useEffect has a missing dependency: 'fetchEarnings'. Either include it or remove the dependency array.", ["1519"], "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchWithdrawals'. Either include it or remove the dependency array.", ["1520"], "'updateUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchProfileData'. Either include it or remove the dependency array.", ["1521"], ["1522"], ["1523"], ["1524"], "'Grid' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'EventBusyIcon' is defined but never used.", "'selectedDay' is assigned a value but never used.", "'setSelectedDay' is assigned a value but never used.", "'selectedHour' is assigned a value but never used.", "'setSelectedHour' is assigned a value but never used.", "'selectAllForDay' is assigned a value but never used.", "'clearAllForDay' is assigned a value but never used.", "'selectTimeForAllDays' is assigned a value but never used.", "'clearTimeForAllDays' is assigned a value but never used.", "'useLocation' is defined but never used.", "'axios' is defined but never used.", "'Button' is defined but never used.", "'ContentCopyIcon' is defined but never used.", "'convertFromDatabaseTime' is defined but never used.", "'getCurrentTimeInTimezone' is defined but never used.", "'currentTime' is assigned a value but never used.", "'calculatePrice' is assigned a value but never used.", "'handleCopyLink' is assigned a value but never used.", "'getMeetingActions' is assigned a value but never used.", "'availableTimesForDay' is assigned a value but never used.", "'loadingDays' is assigned a value but never used.", "'loadingTimes' is assigned a value but never used.", "'setLoadingTimes' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAvailableHours' and 'fetchWeeklyBreaks'. Either include them or remove the dependency array.", ["1525"], "React Hook useEffect has a missing dependency: 'fetchWeeklyBreaks'. Either include it or remove the dependency array.", ["1526"], "'response' is assigned a value but never used.", ["1527"], "React Hook useEffect has a missing dependency: 'decreaseUnreadCount'. Either include it or remove the dependency array.", ["1528"], "React Hook useCallback has a missing dependency: 'decreaseUnreadCount'. Either include it or remove the dependency array.", ["1529"], "React Hook useCallback has an unnecessary dependency: 'messages'. Either exclude it or remove the dependency array.", ["1530"], ["1531", "1532"], ["1533", "1534"], ["1535", "1536"], "'setIsRtl' is assigned a value but never used.", "'i18n' is defined but never used.", ["1537"], "React Hook useEffect has missing dependencies: 'fetchBalance', 'fetchSettings', and 'fetchWithdrawals'. Either include them or remove the dependency array.", ["1538"], ["1539"], "'Chip' is defined but never used.", "'Collapse' is defined but never used.", "'StarBorderIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'ExpandLessIcon' is defined but never used.", "'expandedReplies' is assigned a value but never used.", "'toggleReplyExpansion' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'fullScreen' is assigned a value but never used.", ["1540"], ["1541", "1542"], ["1543", "1544"], ["1545", "1546"], "'MuiIconButton' is defined but never used.", "'availableSlots' is assigned a value but never used.", "'loadingSlots' is assigned a value but never used.", "no-use-before-define", "'socket' was used before it was defined.", "usedBeforeDefined", "React Hook useCallback has an unnecessary dependency: 'id'. Either exclude it or remove the dependency array.", ["1547"], "'handleBookSlot' is assigned a value but never used.", ["1548", "1549"], ["1550", "1551"], ["1552", "1553"], "'Typography' is defined but never used.", "'isConnected' is assigned a value but never used.", ["1554"], ["1555"], ["1556"], "'token' is assigned a value but never used.", ["1557"], "'MoneyIcon' is defined but never used.", "'toast' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMeetings'. Either include it or remove the dependency array.", ["1558"], "'getMeetingDateInStudentTimezone' is assigned a value but never used.", "'formatMeetingDateInStudentTimezone' is assigned a value but never used.", "'dateFnsFormat' is assigned a value but never used.", "'getCurrentTimeInStudentTimezone' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", ["1559"], "'CardActions' is defined but never used.", "'Avatar' is defined but never used.", "'isSameDay' is defined but never used.", "'convertBookingDateTime' is defined but never used.", "'isFullHourAvailable' is assigned a value but never used.", "'checkCrossHourAvailability' is assigned a value but never used.", "'checkSecondHalfAvailability' is assigned a value but never used.", "'renderBookingSuccess' is assigned a value but never used.", "'PersonIcon' is defined but never used.", "'datePart' is assigned a value but never used.", "'renderBookings' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'teachers'. Either include it or remove the dependency array.", ["1560"], "'ListItemButton' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPending'. Either include it or remove the dependency array.", ["1561"], "'AccessTimeIcon' is defined but never used.", "'moment' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'isPartOfFullHour' is assigned a value but never used.", "'isFullHourStart' is assigned a value but never used.", "'isFullHourSecondSlot' is assigned a value but never used.", "'formattedDate' is defined but never used.", "'formatDistanceToNow' is defined but never used.", "'ar' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'Checkbox' is defined but never used.", "'FormHelperText' is defined but never used.", "'VideoFileIcon' is defined but never used.", "'LinkIcon' is defined but never used.", "'timeSlots' is assigned a value but never used.", "'useMemo' is defined but never used.", "'MeetingConsumer' is defined but never used.", "'useMeeting' is defined but never used.", "'useParticipant' is defined but never used.", "'createMeetingWithCustomId' is defined but never used.", "'validateMeeting' is defined but never used.", "'WaitingToJoinScreen' is defined but never used.", "React Hook useEffect has a missing dependency: 'isExemptPage'. Either include it or remove the dependency array.", ["1562"], "React Hook useEffect has an unnecessary dependency: 'raisedHandsParticipants'. Either exclude it or remove the dependency array. Outer scope values like 'raisedHandsParticipants' aren't valid dependencies because mutating them doesn't re-render the component.", ["1563"], "React Hook useEffect has a missing dependency: 'getCameraDevices'. Either include it or remove the dependency array.", ["1564"], "React Hook useEffect has a missing dependency: 'getAudioDevices'. Either include it or remove the dependency array.", ["1565"], "React Hook useEffect has a missing dependency: 'checkMediaPermission'. Either include it or remove the dependency array.", ["1566"], "React Hook useEffect has a missing dependency: 'onDeviceChanged'. Either include it or remove the dependency array.", ["1567"], ["1568"], "React Hook useEffect has a missing dependency: 'waitingMessages'. Either include it or remove the dependency array.", ["1569"], "React Hook useEffect has missing dependencies: 'leave', 'onClose', and 'setIsMeetingLeft'. Either include them or remove the dependency array. If 'setIsMeetingLeft' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1570"], "React Hook useEffect has a missing dependency: 'updateStats'. Either include it or remove the dependency array.", ["1571"], "React Hook useEffect has a missing dependency: 'setDidDeviceChange'. Either include it or remove the dependency array. If 'setDidDeviceChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1572"], "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "Expected '===' and instead saw '=='.", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "React Hook useEffect has a missing dependency: 'meetingId'. Either include it or remove the dependency array.", ["1573"], "'ScheduleIcon' is defined but never used.", "'PaymentIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'CreditCardIcon' is defined but never used.", "'SchoolIcon' is defined but never used.", "'oneHourFromNow' is assigned a value but never used.", "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "'isEvening' is assigned a value but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1574"], {"desc": "1575", "fix": "1576"}, {"desc": "1577", "fix": "1578"}, {"desc": "1579", "fix": "1580"}, {"desc": "1581", "fix": "1582"}, {"messageId": "1583", "fix": "1584", "desc": "1585"}, {"messageId": "1586", "fix": "1587", "desc": "1588"}, {"messageId": "1583", "fix": "1589", "desc": "1585"}, {"messageId": "1586", "fix": "1590", "desc": "1588"}, {"messageId": "1583", "fix": "1591", "desc": "1585"}, {"messageId": "1586", "fix": "1592", "desc": "1588"}, {"desc": "1593", "fix": "1594"}, {"desc": "1595", "fix": "1596"}, {"desc": "1597", "fix": "1598"}, {"messageId": "1583", "fix": "1599", "desc": "1585"}, {"messageId": "1586", "fix": "1600", "desc": "1588"}, {"messageId": "1583", "fix": "1601", "desc": "1585"}, {"messageId": "1586", "fix": "1602", "desc": "1588"}, {"messageId": "1583", "fix": "1603", "desc": "1585"}, {"messageId": "1586", "fix": "1604", "desc": "1588"}, {"desc": "1605", "fix": "1606"}, {"desc": "1607", "fix": "1608"}, {"desc": "1609", "fix": "1610"}, {"desc": "1611", "fix": "1612"}, {"desc": "1613", "fix": "1614"}, {"desc": "1615", "fix": "1616"}, {"desc": "1617", "fix": "1618"}, {"desc": "1619", "fix": "1620"}, {"desc": "1621", "fix": "1622"}, {"desc": "1623", "fix": "1624"}, {"desc": "1623", "fix": "1625"}, {"desc": "1623", "fix": "1626"}, {"desc": "1627", "fix": "1628"}, {"desc": "1629", "fix": "1630"}, {"desc": "1631", "fix": "1632"}, {"desc": "1633", "fix": "1634"}, {"desc": "1635", "fix": "1636"}, {"desc": "1637", "fix": "1638"}, {"messageId": "1583", "fix": "1639", "desc": "1585"}, {"messageId": "1586", "fix": "1640", "desc": "1588"}, {"messageId": "1583", "fix": "1641", "desc": "1585"}, {"messageId": "1586", "fix": "1642", "desc": "1588"}, {"messageId": "1583", "fix": "1643", "desc": "1585"}, {"messageId": "1586", "fix": "1644", "desc": "1588"}, {"desc": "1613", "fix": "1645"}, {"desc": "1646", "fix": "1647"}, {"desc": "1648", "fix": "1649"}, {"desc": "1650", "fix": "1651"}, {"messageId": "1583", "fix": "1652", "desc": "1585"}, {"messageId": "1586", "fix": "1653", "desc": "1588"}, {"messageId": "1583", "fix": "1654", "desc": "1585"}, {"messageId": "1586", "fix": "1655", "desc": "1588"}, {"messageId": "1583", "fix": "1656", "desc": "1585"}, {"messageId": "1586", "fix": "1657", "desc": "1588"}, {"desc": "1658", "fix": "1659"}, {"messageId": "1583", "fix": "1660", "desc": "1585"}, {"messageId": "1586", "fix": "1661", "desc": "1588"}, {"messageId": "1583", "fix": "1662", "desc": "1585"}, {"messageId": "1586", "fix": "1663", "desc": "1588"}, {"messageId": "1583", "fix": "1664", "desc": "1585"}, {"messageId": "1586", "fix": "1665", "desc": "1588"}, {"desc": "1631", "fix": "1666"}, {"desc": "1633", "fix": "1667"}, {"desc": "1635", "fix": "1668"}, {"desc": "1648", "fix": "1669"}, {"desc": "1670", "fix": "1671"}, {"desc": "1613", "fix": "1672"}, {"desc": "1673", "fix": "1674"}, {"desc": "1675", "fix": "1676"}, {"desc": "1677", "fix": "1678"}, {"desc": "1679", "fix": "1680"}, {"desc": "1681", "fix": "1682"}, {"desc": "1683", "fix": "1684"}, {"desc": "1685", "fix": "1686"}, {"desc": "1687", "fix": "1688"}, {"desc": "1689", "fix": "1690"}, {"desc": "1691", "fix": "1692"}, {"desc": "1693", "fix": "1694"}, {"desc": "1695", "fix": "1696"}, {"desc": "1697", "fix": "1698"}, {"desc": "1699", "fix": "1700"}, {"desc": "1701", "fix": "1702"}, "Update the dependencies array to be: [handleLogout]", {"range": "1703", "text": "1704"}, "Update the dependencies array to be: [isAuthenticated, token, currentUser, socket]", {"range": "1705", "text": "1706"}, "Update the dependencies array to be: [socket, isConnected, currentUser, fetchUnreadCount]", {"range": "1707", "text": "1708"}, "Update the dependencies array to be: [appliedFilters, page, searchFilters.priceRange, t]", {"range": "1709", "text": "1710"}, "removeEscape", {"range": "1711", "text": "1712"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1713", "text": "1714"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1715", "text": "1712"}, {"range": "1716", "text": "1714"}, {"range": "1717", "text": "1712"}, {"range": "1718", "text": "1714"}, "Update the dependencies array to be: [location.state, i18n.language, location]", {"range": "1719", "text": "1720"}, "Update the dependencies array to be: [fetchStudents, page, rowsPerPage, searchQuery, t]", {"range": "1721", "text": "1722"}, "Update the dependencies array to be: [fetchTeachers, page, rowsPerPage, searchQuery, t]", {"range": "1723", "text": "1724"}, {"range": "1725", "text": "1712"}, {"range": "1726", "text": "1714"}, {"range": "1727", "text": "1712"}, {"range": "1728", "text": "1714"}, {"range": "1729", "text": "1712"}, {"range": "1730", "text": "1714"}, "Update the dependencies array to be: [fetchLanguages]", {"range": "1731", "text": "1732"}, "Update the dependencies array to be: [fetchCategories]", {"range": "1733", "text": "1734"}, "Update the dependencies array to be: [fetchUpdates, page, rowsPerPage, statusFilter]", {"range": "1735", "text": "1736"}, "Update the dependencies array to be: [fetchSessions, filters]", {"range": "1737", "text": "1738"}, "Update the dependencies array to be: [currentUser, token, page, rowsPerPage, fetchBalance, fetchTransactions]", {"range": "1739", "text": "1740"}, "Update the dependencies array to be: [token, tabValue, page, rowsPerPage, fetchMessages]", {"range": "1741", "text": "1742"}, "Update the dependencies array to be: [fetchEarnings, page, rowsPerPage]", {"range": "1743", "text": "1744"}, "Update the dependencies array to be: [fetchWithdrawals, page, rowsPerPage, statusFilter]", {"range": "1745", "text": "1746"}, "Update the dependencies array to be: [t, currentUser, fetchProfileData]", {"range": "1747", "text": "1748"}, "Update the dependencies array to be: [currentUser, fetchProfileData]", {"range": "1749", "text": "1750"}, {"range": "1751", "text": "1750"}, {"range": "1752", "text": "1750"}, "Update the dependencies array to be: [token, t, fetchAvailableHours, fetchWeeklyBreaks]", {"range": "1753", "text": "1754"}, "Update the dependencies array to be: [currentWeekStart, fetchWeeklyBreaks, token]", {"range": "1755", "text": "1756"}, "Update the dependencies array to be: [socket, selectedChat, t]", {"range": "1757", "text": "1758"}, "Update the dependencies array to be: [socket, selectedChat, decreaseUnreadCount]", {"range": "1759", "text": "1760"}, "Update the dependencies array to be: [decreaseUnreadCount, socket]", {"range": "1761", "text": "1762"}, "Update the dependencies array to be: [socket, isConnected, currentUser, t]", {"range": "1763", "text": "1764"}, {"range": "1765", "text": "1712"}, {"range": "1766", "text": "1714"}, {"range": "1767", "text": "1712"}, {"range": "1768", "text": "1714"}, {"range": "1769", "text": "1712"}, {"range": "1770", "text": "1714"}, {"range": "1771", "text": "1740"}, "Update the dependencies array to be: [currentUser, token, page, rowsPerPage, fetchBalance, fetchWithdrawals, fetchSettings]", {"range": "1772", "text": "1773"}, "Update the dependencies array to be: [token, page, rowsPerPage, fetchMessages]", {"range": "1774", "text": "1775"}, "Update the dependencies array to be: [page, currentUser, token, appliedFilters, fetchTeachers]", {"range": "1776", "text": "1777"}, {"range": "1778", "text": "1712"}, {"range": "1779", "text": "1714"}, {"range": "1780", "text": "1712"}, {"range": "1781", "text": "1714"}, {"range": "1782", "text": "1712"}, {"range": "1783", "text": "1714"}, "Update the dependencies array to be: [socket, chatId, currentUser]", {"range": "1784", "text": "1785"}, {"range": "1786", "text": "1712"}, {"range": "1787", "text": "1714"}, {"range": "1788", "text": "1712"}, {"range": "1789", "text": "1714"}, {"range": "1790", "text": "1712"}, {"range": "1791", "text": "1714"}, {"range": "1792", "text": "1758"}, {"range": "1793", "text": "1760"}, {"range": "1794", "text": "1762"}, {"range": "1795", "text": "1775"}, "Update the dependencies array to be: [currentUser, fetchMeetings]", {"range": "1796", "text": "1797"}, {"range": "1798", "text": "1740"}, "Update the dependencies array to be: [success, editingReviewId, teachers]", {"range": "1799", "text": "1800"}, "Update the dependencies array to be: [currentUser, fetchPending, location.pathname]", {"range": "1801", "text": "1802"}, "Update the dependencies array to be: [t, exemptPages, isExemptPage]", {"range": "1803", "text": "1804"}, "Update the dependencies array to be: []", {"range": "1805", "text": "1806"}, "Update the dependencies array to be: [getCameraDevices, isCameraPermissionAllowed]", {"range": "1807", "text": "1808"}, "Update the dependencies array to be: [getAudioDevices, isMicrophonePermissionAllowed]", {"range": "1809", "text": "1810"}, "Update the dependencies array to be: [checkMediaPermission]", {"range": "1811", "text": "1812"}, "Update the dependencies array to be: [onDeviceChanged]", {"range": "1813", "text": "1814"}, "Update the dependencies array to be: [getAudioDevices]", {"range": "1815", "text": "1816"}, "Update the dependencies array to be: [waitingMessages]", {"range": "1817", "text": "1818"}, "Update the dependencies array to be: [leave, meetingData, onClose, participantTz, setIsMeetingLeft]", {"range": "1819", "text": "1820"}, "Update the dependencies array to be: [webcamStream, micStream, screenShareStream, updateStats]", {"range": "1821", "text": "1822"}, "Update the dependencies array to be: [didDeviceChange, setDidDeviceChange]", {"range": "1823", "text": "1824"}, "Update the dependencies array to be: [teacherId, studentId, meetingId]", {"range": "1825", "text": "1826"}, "Update the dependencies array to be: [page, rowsPerPage, search, roleFilter, fetchUsers]", {"range": "1827", "text": "1828"}, [3798, 3800], "[handleLogout]", [3199, 3236], "[isAuthenticated, token, currentUser, socket]", [3029, 3063], "[socket, isConnected, currentUser, fetchUnreadCount]", [9275, 9323], "[appliedFilters, page, searchFilters.priceRange, t]", [2348, 2349], "", [2348, 2348], "\\", [2357, 2358], [2357, 2357], [2359, 2360], [2359, 2359], [3432, 3463], "[location.state, i18n.language, location]", [2116, 2151], "[fetchStudents, page, rowsPerPage, searchQuery, t]", [2095, 2130], "[fetchTeachers, page, rowsPerPage, searchQuery, t]", [5698, 5699], [5698, 5698], [5707, 5708], [5707, 5707], [5709, 5710], [5709, 5709], [1057, 1059], "[fetchLanguages]", [1069, 1071], "[fetchCategories]", [2347, 2380], "[fetchUpdates, page, rowsPerPage, statusFilter]", [2831, 2840], "[fetchSessions, filters]", [1299, 1338], "[currentUser, token, page, rowsPerPage, fetchBalance, fetchTransactions]", [2708, 2744], "[token, tabValue, page, rowsPerPage, fetchMessages]", [1259, 1278], "[fetchEarnings, page, rowsPerPage]", [1628, 1661], "[fetchWithdrawals, page, rowsPerPage, statusFilter]", [3445, 3461], "[t, currentUser, fetchProfileData]", [4176, 4189], "[currentUser, fetchProfileData]", [4567, 4580], [5070, 5083], [5750, 5760], "[token, t, fetchAvailableHours, fetchWeeklyBreaks]", [5882, 5907], "[currentWeekStart, fetchWeeklyBreaks, token]", [6755, 6777], "[socket, selectedChat, t]", [8233, 8255], "[socket, selectedChat, decreaseUnreadCount]", [9760, 9768], "[decreaseUnreadCount, socket]", [11815, 11862], "[socket, isConnected, currentUser, t]", [1819, 1820], [1819, 1819], [1828, 1829], [1828, 1828], [1830, 1831], [1830, 1830], [1366, 1405], [1858, 1897], "[currentUser, token, page, rowsPerPage, fetchBalance, fetchWithdrawals, fetchSettings]", [2027, 2053], "[token, page, rowsPerPage, fetchMessages]", [8622, 8664], "[page, currentUser, token, appliedFilters, fetchTeachers]", [12227, 12228], [12227, 12227], [12236, 12237], [12236, 12236], [12238, 12239], [12238, 12238], [9071, 9104], "[socket, chatId, currentUser]", [14109, 14110], [14109, 14109], [14118, 14119], [14118, 14118], [14120, 14121], [14120, 14120], [7027, 7049], [8505, 8527], [10032, 10040], [2113, 2139], [1892, 1905], "[current<PERSON><PERSON>, fetchMeetings]", [2364, 2403], [20708, 20734], "[success, editing<PERSON><PERSON><PERSON>wId, teachers]", [3096, 3128], "[currentUser, fetchPending, location.pathname]", [1680, 1696], "[t, exemptPages, isExemptPage]", [1678, 1703], "[]", [4052, 4079], "[getCameraDevices, isCameraPermissionAllowed]", [4135, 4166], "[getAudioDevices, isMicrophonePermissionAllowed]", [4250, 4252], "[checkMediaPermission]", [9249, 9251], "[onDevice<PERSON>hanged]", [12542, 12544], "[getAudioDevices]", [845, 847], "[waitingMessages]", [6972, 7000], "[leave, meeting<PERSON><PERSON>, onClose, participantTz, setIsMeetingLeft]", [6514, 6558], "[webcamStream, micStream, screenShareStream, updateStats]", [1859, 1876], "[didD<PERSON><PERSON><PERSON><PERSON><PERSON>, setDidDeviceChange]", [1198, 1220], "[teacherId, studentId, meetingId]", [2323, 2362], "[page, rowsPerPage, search, roleFilter, fetchUsers]"]