{"ast": null, "code": "import React,{useEffect,useState}from'react';import{Navigate,useLocation}from'react-router-dom';import{useAuth}from'../contexts/AuthContext';import UserStatusHandler from'../utils/userStatusHandler';import{Box,CircularProgress,Alert,AlertTitle}from'@mui/material';import{Warning as WarningIcon}from'@mui/icons-material';/**\n * مكون حماية الطرق - يتحقق من تسجيل الدخول وحالة المستخدم\n */import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children,allowPendingDeletion=false}=_ref;const{isAuthenticated,currentUser,handleLogout}=useAuth();const location=useLocation();const[statusCheck,setStatusCheck]=useState({loading:true});useEffect(()=>{const checkUserStatus=async()=>{// إذا لم يكن مسجل دخول، لا نحتاج للتحقق من الحالة\nif(!isAuthenticated||!currentUser){setStatusCheck({loading:false,valid:false,reason:'not_authenticated'});return;}// التحقق من حالة المستخدم للمستخدمين المسجلين فقط\nconst result=await UserStatusHandler.checkUserStatus();if(!result.valid){if(result.reason==='deleted'||result.reason==='unauthorized'){// حفظ رسالة الحالة في localStorage\nlocalStorage.setItem('accountStatusMessage',JSON.stringify({message:result.message||'تم حذف هذا الحساب',message_en:result.message_en||'Account has been deleted',accountStatus:result.reason,deleteScheduledAt:result.deleteScheduledAt}));// حساب محذوف أو توكن منتهي الصلاحية - تسجيل خروج فوري\nhandleLogout();setStatusCheck({loading:false,valid:false,reason:result.reason,message:result.message||'تم حذف هذا الحساب'});return;}if(result.reason==='error'){// خطأ في الشبكة أو الخادم\nsetStatusCheck({loading:false,valid:false,reason:'error',message:result.message});return;}}// التعامل مع المستخدمين المجدولين للحذف\nif(result.reason==='pending_deletion'&&!allowPendingDeletion){// حساب مجدول للحذف ولا يُسمح بالوصول\nsetStatusCheck({loading:false,valid:false,reason:'pending_deletion',message:'هذا الحساب مجدول للحذف. يمكنك الوصول فقط لصفحة الملف الشخصي لإلغاء الحذف.'});return;}setStatusCheck({loading:false,valid:true});};checkUserStatus();},[isAuthenticated,currentUser,allowPendingDeletion,handleLogout]);// غير مسجل دخول - إعادة توجيه فورية بدون loading\nif(!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}// جاري التحميل للمستخدمين المسجلين فقط\nif(statusCheck.loading){return/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",flexDirection:\"column\",gap:2,children:[/*#__PURE__*/_jsx(CircularProgress,{size:40}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628...\"})]});}// حساب محذوف - إعادة توجيه لصفحة تسجيل الدخول\nif(statusCheck.reason==='deleted'||statusCheck.reason==='unauthorized'){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}// حساب مجدول للحذف - إعادة توجيه لصفحة الحذف المجدول\nif(statusCheck.reason==='pending_deletion'&&!allowPendingDeletion){return/*#__PURE__*/_jsx(Navigate,{to:\"/pending-deletion\",state:{message:statusCheck.message,accountStatus:'pending_deletion',deleteScheduledAt:statusCheck.deleteScheduledAt},replace:true});}// كل شيء على ما يرام\nreturn children;};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Navigate", "useLocation", "useAuth", "UserStatusHandler", "Box", "CircularProgress", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Warning", "WarningIcon", "jsx", "_jsx", "jsxs", "_jsxs", "ProtectedRoute", "_ref", "children", "allowPendingDeletion", "isAuthenticated", "currentUser", "handleLogout", "location", "statusCheck", "setStatusCheck", "loading", "checkUserStatus", "valid", "reason", "result", "localStorage", "setItem", "JSON", "stringify", "message", "message_en", "accountStatus", "deleteScheduledAt", "to", "state", "from", "replace", "display", "justifyContent", "alignItems", "minHeight", "flexDirection", "gap", "size"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport UserStatusHandler from '../utils/userStatusHandler';\nimport { Box, CircularProgress, Alert, AlertTitle } from '@mui/material';\nimport { Warning as WarningIcon } from '@mui/icons-material';\n\n/**\n * مكون حماية الطرق - يتحقق من تسجيل الدخول وحالة المستخدم\n */\nconst ProtectedRoute = ({ children, allowPendingDeletion = false }) => {\n  const { isAuthenticated, currentUser, handleLogout } = useAuth();\n  const location = useLocation();\n  const [statusCheck, setStatusCheck] = useState({ loading: true });\n\n  useEffect(() => {\n    const checkUserStatus = async () => {\n      // إذا لم يكن مسجل دخول، لا نحتاج للتحقق من الحالة\n      if (!isAuthenticated || !currentUser) {\n        setStatusCheck({ loading: false, valid: false, reason: 'not_authenticated' });\n        return;\n      }\n\n      // التحقق من حالة المستخدم للمستخدمين المسجلين فقط\n      const result = await UserStatusHandler.checkUserStatus();\n      \n      if (!result.valid) {\n        if (result.reason === 'deleted' || result.reason === 'unauthorized') {\n          // حفظ رسالة الحالة في localStorage\n          localStorage.setItem('accountStatusMessage', JSON.stringify({\n            message: result.message || 'تم حذف هذا الحساب',\n            message_en: result.message_en || 'Account has been deleted',\n            accountStatus: result.reason,\n            deleteScheduledAt: result.deleteScheduledAt\n          }));\n\n          // حساب محذوف أو توكن منتهي الصلاحية - تسجيل خروج فوري\n          handleLogout();\n          setStatusCheck({\n            loading: false,\n            valid: false,\n            reason: result.reason,\n            message: result.message || 'تم حذف هذا الحساب'\n          });\n          return;\n        }\n\n        if (result.reason === 'error') {\n          // خطأ في الشبكة أو الخادم\n          setStatusCheck({\n            loading: false,\n            valid: false,\n            reason: 'error',\n            message: result.message\n          });\n          return;\n        }\n      }\n\n      // التعامل مع المستخدمين المجدولين للحذف\n      if (result.reason === 'pending_deletion' && !allowPendingDeletion) {\n        // حساب مجدول للحذف ولا يُسمح بالوصول\n        setStatusCheck({\n          loading: false,\n          valid: false,\n          reason: 'pending_deletion',\n          message: 'هذا الحساب مجدول للحذف. يمكنك الوصول فقط لصفحة الملف الشخصي لإلغاء الحذف.'\n        });\n        return;\n      }\n\n      setStatusCheck({ loading: false, valid: true });\n    };\n\n    checkUserStatus();\n  }, [isAuthenticated, currentUser, allowPendingDeletion, handleLogout]);\n\n  // غير مسجل دخول - إعادة توجيه فورية بدون loading\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // جاري التحميل للمستخدمين المسجلين فقط\n  if (statusCheck.loading) {\n    return (\n      <Box\n        display=\"flex\"\n        justifyContent=\"center\"\n        alignItems=\"center\"\n        minHeight=\"400px\"\n        flexDirection=\"column\"\n        gap={2}\n      >\n        <CircularProgress size={40} />\n        <div>جاري التحقق من حالة الحساب...</div>\n      </Box>\n    );\n  }\n\n  // حساب محذوف - إعادة توجيه لصفحة تسجيل الدخول\n  if (statusCheck.reason === 'deleted' || statusCheck.reason === 'unauthorized') {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // حساب مجدول للحذف - إعادة توجيه لصفحة الحذف المجدول\n  if (statusCheck.reason === 'pending_deletion' && !allowPendingDeletion) {\n    return <Navigate to=\"/pending-deletion\" state={{\n      message: statusCheck.message,\n      accountStatus: 'pending_deletion',\n      deleteScheduledAt: statusCheck.deleteScheduledAt\n    }} replace />;\n  }\n\n  // كل شيء على ما يرام\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,QAAQ,CAAEC,WAAW,KAAQ,kBAAkB,CACxD,OAASC,OAAO,KAAQ,yBAAyB,CACjD,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAC1D,OAASC,GAAG,CAAEC,gBAAgB,CAAEC,KAAK,CAAEC,UAAU,KAAQ,eAAe,CACxE,OAASC,OAAO,GAAI,CAAAC,WAAW,KAAQ,qBAAqB,CAE5D;AACA;AACA,GAFA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGA,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAgD,IAA/C,CAAEC,QAAQ,CAAEC,oBAAoB,CAAG,KAAM,CAAC,CAAAF,IAAA,CAChE,KAAM,CAAEG,eAAe,CAAEC,WAAW,CAAEC,YAAa,CAAC,CAAGlB,OAAO,CAAC,CAAC,CAChE,KAAM,CAAAmB,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACqB,WAAW,CAAEC,cAAc,CAAC,CAAGxB,QAAQ,CAAC,CAAEyB,OAAO,CAAE,IAAK,CAAC,CAAC,CAEjE1B,SAAS,CAAC,IAAM,CACd,KAAM,CAAA2B,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC;AACA,GAAI,CAACP,eAAe,EAAI,CAACC,WAAW,CAAE,CACpCI,cAAc,CAAC,CAAEC,OAAO,CAAE,KAAK,CAAEE,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,mBAAoB,CAAC,CAAC,CAC7E,OACF,CAEA;AACA,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAzB,iBAAiB,CAACsB,eAAe,CAAC,CAAC,CAExD,GAAI,CAACG,MAAM,CAACF,KAAK,CAAE,CACjB,GAAIE,MAAM,CAACD,MAAM,GAAK,SAAS,EAAIC,MAAM,CAACD,MAAM,GAAK,cAAc,CAAE,CACnE;AACAE,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAEC,IAAI,CAACC,SAAS,CAAC,CAC1DC,OAAO,CAAEL,MAAM,CAACK,OAAO,EAAI,mBAAmB,CAC9CC,UAAU,CAAEN,MAAM,CAACM,UAAU,EAAI,0BAA0B,CAC3DC,aAAa,CAAEP,MAAM,CAACD,MAAM,CAC5BS,iBAAiB,CAAER,MAAM,CAACQ,iBAC5B,CAAC,CAAC,CAAC,CAEH;AACAhB,YAAY,CAAC,CAAC,CACdG,cAAc,CAAC,CACbC,OAAO,CAAE,KAAK,CACdE,KAAK,CAAE,KAAK,CACZC,MAAM,CAAEC,MAAM,CAACD,MAAM,CACrBM,OAAO,CAAEL,MAAM,CAACK,OAAO,EAAI,mBAC7B,CAAC,CAAC,CACF,OACF,CAEA,GAAIL,MAAM,CAACD,MAAM,GAAK,OAAO,CAAE,CAC7B;AACAJ,cAAc,CAAC,CACbC,OAAO,CAAE,KAAK,CACdE,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,OAAO,CACfM,OAAO,CAAEL,MAAM,CAACK,OAClB,CAAC,CAAC,CACF,OACF,CACF,CAEA;AACA,GAAIL,MAAM,CAACD,MAAM,GAAK,kBAAkB,EAAI,CAACV,oBAAoB,CAAE,CACjE;AACAM,cAAc,CAAC,CACbC,OAAO,CAAE,KAAK,CACdE,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,kBAAkB,CAC1BM,OAAO,CAAE,2EACX,CAAC,CAAC,CACF,OACF,CAEAV,cAAc,CAAC,CAAEC,OAAO,CAAE,KAAK,CAAEE,KAAK,CAAE,IAAK,CAAC,CAAC,CACjD,CAAC,CAEDD,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,CAACP,eAAe,CAAEC,WAAW,CAAEF,oBAAoB,CAAEG,YAAY,CAAC,CAAC,CAEtE;AACA,GAAI,CAACF,eAAe,CAAE,CACpB,mBAAOP,IAAA,CAACX,QAAQ,EAACqC,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAElB,QAAS,CAAE,CAACmB,OAAO,MAAE,CAAC,CACpE,CAEA;AACA,GAAIlB,WAAW,CAACE,OAAO,CAAE,CACvB,mBACEX,KAAA,CAACT,GAAG,EACFqC,OAAO,CAAC,MAAM,CACdC,cAAc,CAAC,QAAQ,CACvBC,UAAU,CAAC,QAAQ,CACnBC,SAAS,CAAC,OAAO,CACjBC,aAAa,CAAC,QAAQ,CACtBC,GAAG,CAAE,CAAE,CAAA9B,QAAA,eAEPL,IAAA,CAACN,gBAAgB,EAAC0C,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9BpC,IAAA,QAAAK,QAAA,CAAK,6IAA6B,CAAK,CAAC,EACrC,CAAC,CAEV,CAEA;AACA,GAAIM,WAAW,CAACK,MAAM,GAAK,SAAS,EAAIL,WAAW,CAACK,MAAM,GAAK,cAAc,CAAE,CAC7E,mBAAOhB,IAAA,CAACX,QAAQ,EAACqC,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CACzC,CAEA;AACA,GAAIlB,WAAW,CAACK,MAAM,GAAK,kBAAkB,EAAI,CAACV,oBAAoB,CAAE,CACtE,mBAAON,IAAA,CAACX,QAAQ,EAACqC,EAAE,CAAC,mBAAmB,CAACC,KAAK,CAAE,CAC7CL,OAAO,CAAEX,WAAW,CAACW,OAAO,CAC5BE,aAAa,CAAE,kBAAkB,CACjCC,iBAAiB,CAAEd,WAAW,CAACc,iBACjC,CAAE,CAACI,OAAO,MAAE,CAAC,CACf,CAEA;AACA,MAAO,CAAAxB,QAAQ,CACjB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}