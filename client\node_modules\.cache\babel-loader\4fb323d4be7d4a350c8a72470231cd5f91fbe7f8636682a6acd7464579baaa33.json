{"ast": null, "code": "import React,{useEffect,useState}from'react';import{Navigate,useLocation,useNavigate}from'react-router-dom';import{useAuth}from'../contexts/AuthContext';import UserStatusHandler from'../utils/userStatusHandler';import{Box,CircularProgress,Alert,AlertTitle,Button,Typography,Stack}from'@mui/material';import{Warning as WarningIcon,ExitToApp as LogoutIcon,Cancel as CancelIcon,Schedule as ScheduleIcon}from'@mui/icons-material';import{useTranslation}from'react-i18next';import{format,differenceInDays,differenceInHours,differenceInMinutes}from'date-fns';import{ar}from'date-fns/locale';import axios from'../utils/axios';/**\n * مكون حماية الطرق - يتحقق من تسجيل الدخول وحالة المستخدم\n */import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children,allowPendingDeletion=false}=_ref;const{isAuthenticated,currentUser,handleLogout}=useAuth();const location=useLocation();const navigate=useNavigate();const{t,i18n}=useTranslation();const[statusCheck,setStatusCheck]=useState({loading:true});const[cancelLoading,setCancelLoading]=useState(false);// دالة لحساب الوقت المتبقي للحذف\nconst getTimeRemaining=deleteScheduledAt=>{if(!deleteScheduledAt)return null;const now=new Date();const deleteDate=new Date(deleteScheduledAt);const days=differenceInDays(deleteDate,now);const hours=differenceInHours(deleteDate,now)%24;const minutes=differenceInMinutes(deleteDate,now)%60;if(days>0){const dayText=days===1?t('pendingDeletion.day'):t('pendingDeletion.days');const hourText=hours===1?t('pendingDeletion.hour'):t('pendingDeletion.hours');return`${days} ${dayText} و ${hours} ${hourText}`;}else if(hours>0){const hourText=hours===1?t('pendingDeletion.hour'):t('pendingDeletion.hours');const minuteText=minutes===1?t('pendingDeletion.minute'):t('pendingDeletion.minutes');return`${hours} ${hourText} و ${minutes} ${minuteText}`;}else if(minutes>0){const minuteText=minutes===1?t('pendingDeletion.minute'):t('pendingDeletion.minutes');return`${minutes} ${minuteText}`;}else{return t('pendingDeletion.lessThanMinute');}};// دالة إلغاء الحذف\nconst handleCancelDeletion=async()=>{try{setCancelLoading(true);await axios.post('/users/cancel-delete');// إعادة تحميل بيانات المستخدم\nwindow.location.reload();}catch(error){console.error('Error cancelling deletion:',error);alert('حدث خطأ أثناء إلغاء الحذف. يرجى المحاولة مرة أخرى.');}finally{setCancelLoading(false);}};useEffect(()=>{const checkUserStatus=async()=>{// إذا لم يكن مسجل دخول، لا نحتاج للتحقق من الحالة\nif(!isAuthenticated||!currentUser){setStatusCheck({loading:false,valid:false,reason:'not_authenticated'});return;}// التحقق من حالة المستخدم للمستخدمين المسجلين فقط\nconst result=await UserStatusHandler.checkUserStatus();if(!result.valid){if(result.reason==='deleted'||result.reason==='unauthorized'){// حفظ رسالة الحالة في localStorage\nlocalStorage.setItem('accountStatusMessage',JSON.stringify({message:result.message||'تم حذف هذا الحساب',message_en:result.message_en||'Account has been deleted',accountStatus:result.reason,deleteScheduledAt:result.deleteScheduledAt}));// حساب محذوف أو توكن منتهي الصلاحية - تسجيل خروج فوري\nhandleLogout();setStatusCheck({loading:false,valid:false,reason:result.reason,message:result.message||'تم حذف هذا الحساب'});return;}if(result.reason==='error'){// خطأ في الشبكة أو الخادم\nsetStatusCheck({loading:false,valid:false,reason:'error',message:result.message});return;}}// التعامل مع المستخدمين المجدولين للحذف\nif(result.reason==='pending_deletion'&&!allowPendingDeletion){// حساب مجدول للحذف ولا يُسمح بالوصول\nsetStatusCheck({loading:false,valid:false,reason:'pending_deletion',message:t('pendingDeletion.message')});return;}setStatusCheck({loading:false,valid:true});};checkUserStatus();},[isAuthenticated,currentUser,allowPendingDeletion,handleLogout]);// غير مسجل دخول - إعادة توجيه فورية بدون loading\nif(!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}// جاري التحميل للمستخدمين المسجلين فقط\nif(statusCheck.loading){return/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",flexDirection:\"column\",gap:2,children:[/*#__PURE__*/_jsx(CircularProgress,{size:40}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628...\"})]});}// حساب محذوف - إعادة توجيه لصفحة تسجيل الدخول\nif(statusCheck.reason==='deleted'||statusCheck.reason==='unauthorized'){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}// حساب مجدول للحذف ولا يُسمح بالوصول\nif(statusCheck.reason==='pending_deletion'&&!allowPendingDeletion){const timeRemaining=getTimeRemaining(currentUser===null||currentUser===void 0?void 0:currentUser.delete_scheduled_at);return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"100vh\",p:3,sx:{backgroundColor:'#f5f5f5',position:'fixed',top:0,left:0,right:0,bottom:0,zIndex:9999},children:/*#__PURE__*/_jsxs(Alert,{severity:\"warning\",icon:/*#__PURE__*/_jsx(WarningIcon,{}),sx:{maxWidth:600,width:'100%'},children:[/*#__PURE__*/_jsxs(AlertTitle,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(ScheduleIcon,{}),t('pendingDeletion.title')]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2},children:statusCheck.message}),timeRemaining&&/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{mb:2,color:'error.main',fontWeight:'bold'},children:[\"\\u23F0 \",t('pendingDeletion.timeRemaining'),\": \",timeRemaining]}),(currentUser===null||currentUser===void 0?void 0:currentUser.delete_scheduled_at)&&/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",sx:{mb:3,display:'block',color:'text.secondary'},children:[\"\\uD83D\\uDCC5 \",t('pendingDeletion.scheduledDate'),\": \",format(new Date(currentUser.delete_scheduled_at),'dd/MM/yyyy HH:mm',{locale:i18n.language==='ar'?ar:undefined})]}),/*#__PURE__*/_jsxs(Stack,{direction:\"row\",spacing:3,sx:{mt:2},children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"success\",startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),onClick:handleCancelDeletion,disabled:cancelLoading,size:\"medium\",children:cancelLoading?t('pendingDeletion.cancelling'):t('pendingDeletion.cancelDeletion')}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"error\",startIcon:/*#__PURE__*/_jsx(LogoutIcon,{}),onClick:handleLogout,size:\"medium\",children:t('pendingDeletion.logout')})]})]})});}// كل شيء على ما يرام\nreturn children;};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Navigate", "useLocation", "useNavigate", "useAuth", "UserStatusHandler", "Box", "CircularProgress", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Warning", "WarningIcon", "ExitToApp", "LogoutIcon", "Cancel", "CancelIcon", "Schedule", "ScheduleIcon", "useTranslation", "format", "differenceInDays", "differenceInHours", "differenceInMinutes", "ar", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "ProtectedRoute", "_ref", "children", "allowPendingDeletion", "isAuthenticated", "currentUser", "handleLogout", "location", "navigate", "t", "i18n", "statusCheck", "setStatusCheck", "loading", "cancelLoading", "setCancelLoading", "getTimeRemaining", "deleteScheduledAt", "now", "Date", "deleteDate", "days", "hours", "minutes", "dayText", "hourText", "minuteText", "handleCancelDeletion", "post", "window", "reload", "error", "console", "alert", "checkUserStatus", "valid", "reason", "result", "localStorage", "setItem", "JSON", "stringify", "message", "message_en", "accountStatus", "to", "state", "from", "replace", "display", "justifyContent", "alignItems", "minHeight", "flexDirection", "gap", "size", "timeRemaining", "delete_scheduled_at", "p", "sx", "backgroundColor", "position", "top", "left", "right", "bottom", "zIndex", "severity", "icon", "max<PERSON><PERSON><PERSON>", "width", "variant", "mb", "color", "fontWeight", "locale", "language", "undefined", "direction", "spacing", "mt", "startIcon", "onClick", "disabled"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Navigate, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport UserStatusHandler from '../utils/userStatusHandler';\nimport { Box, CircularProgress, Alert, AlertTitle, Button, Typography, Stack } from '@mui/material';\nimport { Warning as WarningIcon, ExitToApp as LogoutIcon, Cancel as CancelIcon, Schedule as ScheduleIcon } from '@mui/icons-material';\nimport { useTranslation } from 'react-i18next';\nimport { format, differenceInDays, differenceInHours, differenceInMinutes } from 'date-fns';\nimport { ar } from 'date-fns/locale';\nimport axios from '../utils/axios';\n\n/**\n * مكون حماية الطرق - يتحقق من تسجيل الدخول وحالة المستخدم\n */\nconst ProtectedRoute = ({ children, allowPendingDeletion = false }) => {\n  const { isAuthenticated, currentUser, handleLogout } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { t, i18n } = useTranslation();\n  const [statusCheck, setStatusCheck] = useState({ loading: true });\n  const [cancelLoading, setCancelLoading] = useState(false);\n\n  // دالة لحساب الوقت المتبقي للحذف\n  const getTimeRemaining = (deleteScheduledAt) => {\n    if (!deleteScheduledAt) return null;\n\n    const now = new Date();\n    const deleteDate = new Date(deleteScheduledAt);\n\n    const days = differenceInDays(deleteDate, now);\n    const hours = differenceInHours(deleteDate, now) % 24;\n    const minutes = differenceInMinutes(deleteDate, now) % 60;\n\n    if (days > 0) {\n      const dayText = days === 1 ? t('pendingDeletion.day') : t('pendingDeletion.days');\n      const hourText = hours === 1 ? t('pendingDeletion.hour') : t('pendingDeletion.hours');\n      return `${days} ${dayText} و ${hours} ${hourText}`;\n    } else if (hours > 0) {\n      const hourText = hours === 1 ? t('pendingDeletion.hour') : t('pendingDeletion.hours');\n      const minuteText = minutes === 1 ? t('pendingDeletion.minute') : t('pendingDeletion.minutes');\n      return `${hours} ${hourText} و ${minutes} ${minuteText}`;\n    } else if (minutes > 0) {\n      const minuteText = minutes === 1 ? t('pendingDeletion.minute') : t('pendingDeletion.minutes');\n      return `${minutes} ${minuteText}`;\n    } else {\n      return t('pendingDeletion.lessThanMinute');\n    }\n  };\n\n  // دالة إلغاء الحذف\n  const handleCancelDeletion = async () => {\n    try {\n      setCancelLoading(true);\n      await axios.post('/users/cancel-delete');\n\n      // إعادة تحميل بيانات المستخدم\n      window.location.reload();\n    } catch (error) {\n      console.error('Error cancelling deletion:', error);\n      alert('حدث خطأ أثناء إلغاء الحذف. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setCancelLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    const checkUserStatus = async () => {\n      // إذا لم يكن مسجل دخول، لا نحتاج للتحقق من الحالة\n      if (!isAuthenticated || !currentUser) {\n        setStatusCheck({ loading: false, valid: false, reason: 'not_authenticated' });\n        return;\n      }\n\n      // التحقق من حالة المستخدم للمستخدمين المسجلين فقط\n      const result = await UserStatusHandler.checkUserStatus();\n      \n      if (!result.valid) {\n        if (result.reason === 'deleted' || result.reason === 'unauthorized') {\n          // حفظ رسالة الحالة في localStorage\n          localStorage.setItem('accountStatusMessage', JSON.stringify({\n            message: result.message || 'تم حذف هذا الحساب',\n            message_en: result.message_en || 'Account has been deleted',\n            accountStatus: result.reason,\n            deleteScheduledAt: result.deleteScheduledAt\n          }));\n\n          // حساب محذوف أو توكن منتهي الصلاحية - تسجيل خروج فوري\n          handleLogout();\n          setStatusCheck({\n            loading: false,\n            valid: false,\n            reason: result.reason,\n            message: result.message || 'تم حذف هذا الحساب'\n          });\n          return;\n        }\n\n        if (result.reason === 'error') {\n          // خطأ في الشبكة أو الخادم\n          setStatusCheck({\n            loading: false,\n            valid: false,\n            reason: 'error',\n            message: result.message\n          });\n          return;\n        }\n      }\n\n      // التعامل مع المستخدمين المجدولين للحذف\n      if (result.reason === 'pending_deletion' && !allowPendingDeletion) {\n        // حساب مجدول للحذف ولا يُسمح بالوصول\n        setStatusCheck({\n          loading: false,\n          valid: false,\n          reason: 'pending_deletion',\n          message: t('pendingDeletion.message')\n        });\n        return;\n      }\n\n      setStatusCheck({ loading: false, valid: true });\n    };\n\n    checkUserStatus();\n  }, [isAuthenticated, currentUser, allowPendingDeletion, handleLogout]);\n\n  // غير مسجل دخول - إعادة توجيه فورية بدون loading\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // جاري التحميل للمستخدمين المسجلين فقط\n  if (statusCheck.loading) {\n    return (\n      <Box\n        display=\"flex\"\n        justifyContent=\"center\"\n        alignItems=\"center\"\n        minHeight=\"400px\"\n        flexDirection=\"column\"\n        gap={2}\n      >\n        <CircularProgress size={40} />\n        <div>جاري التحقق من حالة الحساب...</div>\n      </Box>\n    );\n  }\n\n  // حساب محذوف - إعادة توجيه لصفحة تسجيل الدخول\n  if (statusCheck.reason === 'deleted' || statusCheck.reason === 'unauthorized') {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // حساب مجدول للحذف ولا يُسمح بالوصول\n  if (statusCheck.reason === 'pending_deletion' && !allowPendingDeletion) {\n    const timeRemaining = getTimeRemaining(currentUser?.delete_scheduled_at);\n\n    return (\n      <Box\n        display=\"flex\"\n        justifyContent=\"center\"\n        alignItems=\"center\"\n        minHeight=\"100vh\"\n        p={3}\n        sx={{\n          backgroundColor: '#f5f5f5',\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          zIndex: 9999\n        }}\n      >\n        <Alert\n          severity=\"warning\"\n          icon={<WarningIcon />}\n          sx={{ maxWidth: 600, width: '100%' }}\n        >\n          <AlertTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <ScheduleIcon />\n            {t('pendingDeletion.title')}\n          </AlertTitle>\n\n          <Typography variant=\"body1\" sx={{ mb: 2 }}>\n            {statusCheck.message}\n          </Typography>\n\n          {timeRemaining && (\n            <Typography variant=\"body2\" sx={{ mb: 2, color: 'error.main', fontWeight: 'bold' }}>\n              ⏰ {t('pendingDeletion.timeRemaining')}: {timeRemaining}\n            </Typography>\n          )}\n\n          {currentUser?.delete_scheduled_at && (\n            <Typography variant=\"caption\" sx={{ mb: 3, display: 'block', color: 'text.secondary' }}>\n              📅 {t('pendingDeletion.scheduledDate')}: {format(new Date(currentUser.delete_scheduled_at), 'dd/MM/yyyy HH:mm', {\n                locale: i18n.language === 'ar' ? ar : undefined\n              })}\n            </Typography>\n          )}\n\n          <Stack direction=\"row\" spacing={3} sx={{ mt: 2 }}>\n            <Button\n              variant=\"contained\"\n              color=\"success\"\n              startIcon={<CancelIcon />}\n              onClick={handleCancelDeletion}\n              disabled={cancelLoading}\n              size=\"medium\"\n            >\n              {cancelLoading ? t('pendingDeletion.cancelling') : t('pendingDeletion.cancelDeletion')}\n            </Button>\n\n            <Button\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<LogoutIcon />}\n              onClick={handleLogout}\n              size=\"medium\"\n            >\n              {t('pendingDeletion.logout')}\n            </Button>\n          </Stack>\n        </Alert>\n      </Box>\n    );\n  }\n\n  // كل شيء على ما يرام\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,QAAQ,CAAEC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CACrE,OAASC,OAAO,KAAQ,yBAAyB,CACjD,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAC1D,OAASC,GAAG,CAAEC,gBAAgB,CAAEC,KAAK,CAAEC,UAAU,CAAEC,MAAM,CAAEC,UAAU,CAAEC,KAAK,KAAQ,eAAe,CACnG,OAASC,OAAO,GAAI,CAAAC,WAAW,CAAEC,SAAS,GAAI,CAAAC,UAAU,CAAEC,MAAM,GAAI,CAAAC,UAAU,CAAEC,QAAQ,GAAI,CAAAC,YAAY,KAAQ,qBAAqB,CACrI,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,MAAM,CAAEC,gBAAgB,CAAEC,iBAAiB,CAAEC,mBAAmB,KAAQ,UAAU,CAC3F,OAASC,EAAE,KAAQ,iBAAiB,CACpC,MAAO,CAAAC,KAAK,KAAM,gBAAgB,CAElC;AACA;AACA,GAFA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGA,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAgD,IAA/C,CAAEC,QAAQ,CAAEC,oBAAoB,CAAG,KAAM,CAAC,CAAAF,IAAA,CAChE,KAAM,CAAEG,eAAe,CAAEC,WAAW,CAAEC,YAAa,CAAC,CAAGlC,OAAO,CAAC,CAAC,CAChE,KAAM,CAAAmC,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsC,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEsC,CAAC,CAAEC,IAAK,CAAC,CAAGrB,cAAc,CAAC,CAAC,CACpC,KAAM,CAACsB,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAC,CAAE6C,OAAO,CAAE,IAAK,CAAC,CAAC,CACjE,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAEzD;AACA,KAAM,CAAAgD,gBAAgB,CAAIC,iBAAiB,EAAK,CAC9C,GAAI,CAACA,iBAAiB,CAAE,MAAO,KAAI,CAEnC,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAD,IAAI,CAACF,iBAAiB,CAAC,CAE9C,KAAM,CAAAI,IAAI,CAAG9B,gBAAgB,CAAC6B,UAAU,CAAEF,GAAG,CAAC,CAC9C,KAAM,CAAAI,KAAK,CAAG9B,iBAAiB,CAAC4B,UAAU,CAAEF,GAAG,CAAC,CAAG,EAAE,CACrD,KAAM,CAAAK,OAAO,CAAG9B,mBAAmB,CAAC2B,UAAU,CAAEF,GAAG,CAAC,CAAG,EAAE,CAEzD,GAAIG,IAAI,CAAG,CAAC,CAAE,CACZ,KAAM,CAAAG,OAAO,CAAGH,IAAI,GAAK,CAAC,CAAGZ,CAAC,CAAC,qBAAqB,CAAC,CAAGA,CAAC,CAAC,sBAAsB,CAAC,CACjF,KAAM,CAAAgB,QAAQ,CAAGH,KAAK,GAAK,CAAC,CAAGb,CAAC,CAAC,sBAAsB,CAAC,CAAGA,CAAC,CAAC,uBAAuB,CAAC,CACrF,MAAO,GAAGY,IAAI,IAAIG,OAAO,MAAMF,KAAK,IAAIG,QAAQ,EAAE,CACpD,CAAC,IAAM,IAAIH,KAAK,CAAG,CAAC,CAAE,CACpB,KAAM,CAAAG,QAAQ,CAAGH,KAAK,GAAK,CAAC,CAAGb,CAAC,CAAC,sBAAsB,CAAC,CAAGA,CAAC,CAAC,uBAAuB,CAAC,CACrF,KAAM,CAAAiB,UAAU,CAAGH,OAAO,GAAK,CAAC,CAAGd,CAAC,CAAC,wBAAwB,CAAC,CAAGA,CAAC,CAAC,yBAAyB,CAAC,CAC7F,MAAO,GAAGa,KAAK,IAAIG,QAAQ,MAAMF,OAAO,IAAIG,UAAU,EAAE,CAC1D,CAAC,IAAM,IAAIH,OAAO,CAAG,CAAC,CAAE,CACtB,KAAM,CAAAG,UAAU,CAAGH,OAAO,GAAK,CAAC,CAAGd,CAAC,CAAC,wBAAwB,CAAC,CAAGA,CAAC,CAAC,yBAAyB,CAAC,CAC7F,MAAO,GAAGc,OAAO,IAAIG,UAAU,EAAE,CACnC,CAAC,IAAM,CACL,MAAO,CAAAjB,CAAC,CAAC,gCAAgC,CAAC,CAC5C,CACF,CAAC,CAED;AACA,KAAM,CAAAkB,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACFZ,gBAAgB,CAAC,IAAI,CAAC,CACtB,KAAM,CAAApB,KAAK,CAACiC,IAAI,CAAC,sBAAsB,CAAC,CAExC;AACAC,MAAM,CAACtB,QAAQ,CAACuB,MAAM,CAAC,CAAC,CAC1B,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDE,KAAK,CAAC,oDAAoD,CAAC,CAC7D,CAAC,OAAS,CACRlB,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAEDhD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmE,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC;AACA,GAAI,CAAC9B,eAAe,EAAI,CAACC,WAAW,CAAE,CACpCO,cAAc,CAAC,CAAEC,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,mBAAoB,CAAC,CAAC,CAC7E,OACF,CAEA;AACA,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAhE,iBAAiB,CAAC6D,eAAe,CAAC,CAAC,CAExD,GAAI,CAACG,MAAM,CAACF,KAAK,CAAE,CACjB,GAAIE,MAAM,CAACD,MAAM,GAAK,SAAS,EAAIC,MAAM,CAACD,MAAM,GAAK,cAAc,CAAE,CACnE;AACAE,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAEC,IAAI,CAACC,SAAS,CAAC,CAC1DC,OAAO,CAAEL,MAAM,CAACK,OAAO,EAAI,mBAAmB,CAC9CC,UAAU,CAAEN,MAAM,CAACM,UAAU,EAAI,0BAA0B,CAC3DC,aAAa,CAAEP,MAAM,CAACD,MAAM,CAC5BnB,iBAAiB,CAAEoB,MAAM,CAACpB,iBAC5B,CAAC,CAAC,CAAC,CAEH;AACAX,YAAY,CAAC,CAAC,CACdM,cAAc,CAAC,CACbC,OAAO,CAAE,KAAK,CACdsB,KAAK,CAAE,KAAK,CACZC,MAAM,CAAEC,MAAM,CAACD,MAAM,CACrBM,OAAO,CAAEL,MAAM,CAACK,OAAO,EAAI,mBAC7B,CAAC,CAAC,CACF,OACF,CAEA,GAAIL,MAAM,CAACD,MAAM,GAAK,OAAO,CAAE,CAC7B;AACAxB,cAAc,CAAC,CACbC,OAAO,CAAE,KAAK,CACdsB,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,OAAO,CACfM,OAAO,CAAEL,MAAM,CAACK,OAClB,CAAC,CAAC,CACF,OACF,CACF,CAEA;AACA,GAAIL,MAAM,CAACD,MAAM,GAAK,kBAAkB,EAAI,CAACjC,oBAAoB,CAAE,CACjE;AACAS,cAAc,CAAC,CACbC,OAAO,CAAE,KAAK,CACdsB,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,kBAAkB,CAC1BM,OAAO,CAAEjC,CAAC,CAAC,yBAAyB,CACtC,CAAC,CAAC,CACF,OACF,CAEAG,cAAc,CAAC,CAAEC,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,IAAK,CAAC,CAAC,CACjD,CAAC,CAEDD,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,CAAC9B,eAAe,CAAEC,WAAW,CAAEF,oBAAoB,CAAEG,YAAY,CAAC,CAAC,CAEtE;AACA,GAAI,CAACF,eAAe,CAAE,CACpB,mBAAOP,IAAA,CAAC5B,QAAQ,EAAC4E,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAExC,QAAS,CAAE,CAACyC,OAAO,MAAE,CAAC,CACpE,CAEA;AACA,GAAIrC,WAAW,CAACE,OAAO,CAAE,CACvB,mBACEd,KAAA,CAACzB,GAAG,EACF2E,OAAO,CAAC,MAAM,CACdC,cAAc,CAAC,QAAQ,CACvBC,UAAU,CAAC,QAAQ,CACnBC,SAAS,CAAC,OAAO,CACjBC,aAAa,CAAC,QAAQ,CACtBC,GAAG,CAAE,CAAE,CAAApD,QAAA,eAEPL,IAAA,CAACtB,gBAAgB,EAACgF,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9B1D,IAAA,QAAAK,QAAA,CAAK,6IAA6B,CAAK,CAAC,EACrC,CAAC,CAEV,CAEA;AACA,GAAIS,WAAW,CAACyB,MAAM,GAAK,SAAS,EAAIzB,WAAW,CAACyB,MAAM,GAAK,cAAc,CAAE,CAC7E,mBAAOvC,IAAA,CAAC5B,QAAQ,EAAC4E,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CACzC,CAEA;AACA,GAAIrC,WAAW,CAACyB,MAAM,GAAK,kBAAkB,EAAI,CAACjC,oBAAoB,CAAE,CACtE,KAAM,CAAAqD,aAAa,CAAGxC,gBAAgB,CAACX,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEoD,mBAAmB,CAAC,CAExE,mBACE5D,IAAA,CAACvB,GAAG,EACF2E,OAAO,CAAC,MAAM,CACdC,cAAc,CAAC,QAAQ,CACvBC,UAAU,CAAC,QAAQ,CACnBC,SAAS,CAAC,OAAO,CACjBM,CAAC,CAAE,CAAE,CACLC,EAAE,CAAE,CACFC,eAAe,CAAE,SAAS,CAC1BC,QAAQ,CAAE,OAAO,CACjBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,IACV,CAAE,CAAAhE,QAAA,cAEFH,KAAA,CAACvB,KAAK,EACJ2F,QAAQ,CAAC,SAAS,CAClBC,IAAI,cAAEvE,IAAA,CAACf,WAAW,GAAE,CAAE,CACtB6E,EAAE,CAAE,CAAEU,QAAQ,CAAE,GAAG,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAApE,QAAA,eAErCH,KAAA,CAACtB,UAAU,EAACkF,EAAE,CAAE,CAAEV,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEG,GAAG,CAAE,CAAE,CAAE,CAAApD,QAAA,eAChEL,IAAA,CAACT,YAAY,GAAE,CAAC,CACfqB,CAAC,CAAC,uBAAuB,CAAC,EACjB,CAAC,cAEbZ,IAAA,CAAClB,UAAU,EAAC4F,OAAO,CAAC,OAAO,CAACZ,EAAE,CAAE,CAAEa,EAAE,CAAE,CAAE,CAAE,CAAAtE,QAAA,CACvCS,WAAW,CAAC+B,OAAO,CACV,CAAC,CAEZc,aAAa,eACZzD,KAAA,CAACpB,UAAU,EAAC4F,OAAO,CAAC,OAAO,CAACZ,EAAE,CAAE,CAAEa,EAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,YAAY,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAAxE,QAAA,EAAC,SAChF,CAACO,CAAC,CAAC,+BAA+B,CAAC,CAAC,IAAE,CAAC+C,aAAa,EAC5C,CACb,CAEA,CAAAnD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEoD,mBAAmB,gBAC/B1D,KAAA,CAACpB,UAAU,EAAC4F,OAAO,CAAC,SAAS,CAACZ,EAAE,CAAE,CAAEa,EAAE,CAAE,CAAC,CAAEvB,OAAO,CAAE,OAAO,CAAEwB,KAAK,CAAE,gBAAiB,CAAE,CAAAvE,QAAA,EAAC,eACnF,CAACO,CAAC,CAAC,+BAA+B,CAAC,CAAC,IAAE,CAACnB,MAAM,CAAC,GAAI,CAAA6B,IAAI,CAACd,WAAW,CAACoD,mBAAmB,CAAC,CAAE,kBAAkB,CAAE,CAC9GkB,MAAM,CAAEjE,IAAI,CAACkE,QAAQ,GAAK,IAAI,CAAGlF,EAAE,CAAGmF,SACxC,CAAC,CAAC,EACQ,CACb,cAED9E,KAAA,CAACnB,KAAK,EAACkG,SAAS,CAAC,KAAK,CAACC,OAAO,CAAE,CAAE,CAACpB,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAA9E,QAAA,eAC/CL,IAAA,CAACnB,MAAM,EACL6F,OAAO,CAAC,WAAW,CACnBE,KAAK,CAAC,SAAS,CACfQ,SAAS,cAAEpF,IAAA,CAACX,UAAU,GAAE,CAAE,CAC1BgG,OAAO,CAAEvD,oBAAqB,CAC9BwD,QAAQ,CAAErE,aAAc,CACxByC,IAAI,CAAC,QAAQ,CAAArD,QAAA,CAEZY,aAAa,CAAGL,CAAC,CAAC,4BAA4B,CAAC,CAAGA,CAAC,CAAC,gCAAgC,CAAC,CAChF,CAAC,cAETZ,IAAA,CAACnB,MAAM,EACL6F,OAAO,CAAC,UAAU,CAClBE,KAAK,CAAC,OAAO,CACbQ,SAAS,cAAEpF,IAAA,CAACb,UAAU,GAAE,CAAE,CAC1BkG,OAAO,CAAE5E,YAAa,CACtBiD,IAAI,CAAC,QAAQ,CAAArD,QAAA,CAEZO,CAAC,CAAC,wBAAwB,CAAC,CACtB,CAAC,EACJ,CAAC,EACH,CAAC,CACL,CAAC,CAEV,CAEA;AACA,MAAO,CAAAP,QAAQ,CACjB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}