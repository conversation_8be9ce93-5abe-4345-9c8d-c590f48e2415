const express = require('express');
const router = express.Router();
const db = require('../../config/db');
const { deleteUserCompletely } = require('../../scripts/processScheduledDeletions');
const bcrypt = require('bcryptjs');

// Get all students with pagination and filters
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';

    let query = `
      SELECT 
        u.id,
        u.full_name,
        u.email,
        u.gender,
        u.profile_picture_url,
        u.created_at,
        scd.native_language,
        scd.islam_learning_language,
        scd.arabic_learning_language,
        scd.age,
        scd.country,
        scd.timezone,
        scd.arabic_proficiency_level,
        scd.private_tutoring_preference,
        scd.is_completed
      FROM users u
      LEFT JOIN student_completion_data scd ON u.id = scd.user_id
      WHERE u.role = 'student'
    `;

    let countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      WHERE u.role = 'student'
    `;

    const queryParams = [];
    if (search) {
      query += ` AND (u.full_name LIKE ? OR u.email LIKE ?)`;
      countQuery += ` AND (u.full_name LIKE ? OR u.email LIKE ?)`;
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    query += ` ORDER BY u.created_at DESC LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    const [students] = await db.pool.query(query, queryParams);
    const [totalCount] = await db.pool.query(countQuery, queryParams.slice(0, -2));

    res.json({
      students,
      total: totalCount[0].total
    });
  } catch (error) {
    console.error('Error fetching students:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Get student details
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [student] = await db.pool.query(
      `SELECT 
        u.id,
        u.full_name,
        u.email,
        u.gender,
        u.profile_picture_url,
        u.created_at,
        scd.native_language,
        scd.islam_learning_language,
        scd.arabic_learning_language,
        scd.age,
        scd.country,
        scd.timezone,
        scd.arabic_proficiency_level,
        scd.private_tutoring_preference,
        scd.is_completed
      FROM users u
      LEFT JOIN student_completion_data scd ON u.id = scd.user_id
      WHERE u.id = ? AND u.role = 'student'`,
      [id]
    );

    if (!student.length) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json(student[0]);
  } catch (error) {
    console.error('Error fetching student details:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Delete student
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من أن المستخدم طالب
    const [userCheck] = await db.pool.query(
      'SELECT id, email, full_name, role FROM users WHERE id = ? AND role = "student"',
      [id]
    );

    if (userCheck.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    const student = userCheck[0];

    // Start transaction
    const connection = await db.pool.getConnection();
    await connection.beginTransaction();

    try {
      console.log(`Admin deleting student: ${student.email} (ID: ${student.id})`);

      // استخدام الدالة الشاملة للحذف
      await deleteUserCompletely(connection, student.id, student.email);

      await connection.commit();

      console.log(`Successfully deleted student: ${student.email}`);
      res.json({
        success: true,
        message: 'Student deleted successfully'
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error deleting student:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Create sample student (for testing)
router.post('/sample', async (req, res) => {
  try {
    // Start transaction
    const connection = await db.pool.getConnection();
    await connection.beginTransaction();

    try {
      // Create user
      const hashedPassword = await bcrypt.hash('password123', 10);
      const [userResult] = await connection.query(
        `INSERT INTO users (full_name, email, password, role, gender, profile_picture_url)
         VALUES (?, ?, ?, 'student', 'male', NULL)`,
        ['Ahmed Student', '<EMAIL>', hashedPassword]
      );

      // Create student completion data
      await connection.query(
        `INSERT INTO student_completion_data 
         (user_id, native_language, islam_learning_language, arabic_learning_language, 
          age, country, timezone, arabic_proficiency_level, private_tutoring_preference, is_completed)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          userResult.insertId,
          'Arabic',
          'English',
          'English',
          25,
          'Egypt',
          'Africa/Cairo',
          'beginner',
          true,
          true
        ]
      );

      await connection.commit();
      res.json({ message: 'Sample student created successfully' });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error creating sample student:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

module.exports = router;
