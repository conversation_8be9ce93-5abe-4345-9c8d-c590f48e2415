const express = require('express');
const router = express.Router();
const { authenticateToken, isAdmin } = require('../middleware/auth');
const db = require('../config/db');

// Import admin route handlers
const teacherApplicationsRouter = require('./admin/applications');
const teachersRouter = require('./admin/teachers');
const studentsRouter = require('./admin/students');
const categoriesRouter = require('./admin/categories');
const languagesRouter = require('./admin/languages');
const deletedUsersRouter = require('./admin/deleted-users');
const adminProfileUpdatesController = require('../controllers/admin.profile.updates.controller');
const adminWithdrawalController = require('../controllers/admin.withdrawal.controller');

// Test route without authentication
router.get('/earnings-test', async (req, res) => {
  console.log('[ADMIN ROUTES] Test earnings route hit (no auth)');
  res.json({
    success: true,
    message: 'Admin earnings route is working!',
    timestamp: new Date().toISOString()
  });
});

// Apply authentication and admin middleware to all routes
router.use(authenticateToken);
router.use(isAdmin);

// Register admin routes
router.use('/applications', teacherApplicationsRouter);
router.use('/teachers', teachersRouter);
router.use('/students', studentsRouter);
router.use('/categories', categoriesRouter);
router.use('/languages', languagesRouter);
router.use('/deleted-users', deletedUsersRouter);

// Profile updates routes
router.get('/profile-updates', adminProfileUpdatesController.getAllProfileUpdates);
router.get('/profile-updates/:id', adminProfileUpdatesController.getProfileUpdateDetails);
router.post('/profile-updates/:id/approve', adminProfileUpdatesController.approveProfileUpdate);
router.post('/profile-updates/:id/reject', adminProfileUpdatesController.rejectProfileUpdate);

// Withdrawal management routes
router.get('/withdrawals', adminWithdrawalController.getAllWithdrawals);
router.post('/withdrawals/process', adminWithdrawalController.processWithdrawal);

// Admin earnings routes
router.get('/earnings', async (req, res) => {
  console.log('[ADMIN ROUTES] Admin earnings route hit');
  console.log('[ADMIN ROUTES] User from auth:', req.user);
  console.log('[ADMIN ROUTES] Headers:', req.headers.authorization);
  try {
    const { startDate, endDate, page = 0, limit = 10 } = req.query;
    const offset = page * limit;

    let whereClause = '';
    let queryParams = [];

    if (startDate && endDate) {
      whereClause = 'WHERE ae.created_at BETWEEN ? AND ?';
      queryParams.push(startDate, endDate);
    }

    // Check if admin_earnings table exists
    const [tableCheck] = await db.pool.execute(`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'admin_earnings'
    `);

    if (tableCheck[0].count === 0) {
      // Table doesn't exist, return empty data
      return res.json({
        success: true,
        data: {
          summary: {
            total_lessons: 0,
            total_commission: 0,
            total_lesson_amount: 0,
            total_teacher_earnings: 0,
            avg_commission_rate: 0
          },
          earnings: [],
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: 0,
            totalPages: 0
          }
        }
      });
    }

    // Get total earnings
    const [totalResult] = await db.pool.query(
      `SELECT
         COUNT(*) as total_lessons,
         COALESCE(SUM(commission_amount), 0) as total_commission,
         COALESCE(SUM(lesson_amount), 0) as total_lesson_amount,
         COALESCE(SUM(teacher_earnings), 0) as total_teacher_earnings,
         COALESCE(AVG(commission_rate), 0) as avg_commission_rate
       FROM admin_earnings ae ${whereClause}`,
      queryParams
    );

    // Get detailed earnings with pagination
    const [earnings] = await db.pool.query(
      `SELECT
         ae.*,
         t.full_name as teacher_name,
         s.full_name as student_name,
         m.meeting_name
       FROM admin_earnings ae
       JOIN users t ON ae.teacher_id = t.id
       JOIN users s ON ae.student_id = s.id
       JOIN meetings m ON ae.meeting_id = m.id
       ${whereClause}
       ORDER BY ae.created_at DESC
       LIMIT ? OFFSET ?`,
      [...queryParams, parseInt(limit), parseInt(offset)]
    );

    // Get total count for pagination
    const [countResult] = await db.pool.query(
      `SELECT COUNT(*) as count FROM admin_earnings ae ${whereClause}`,
      queryParams
    );

    res.json({
      success: true,
      data: {
        summary: totalResult[0] || {
          total_lessons: 0,
          total_commission: 0,
          total_lesson_amount: 0,
          total_teacher_earnings: 0,
          avg_commission_rate: 0
        },
        earnings: earnings || [],
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: countResult[0].count,
          totalPages: Math.ceil(countResult[0].count / limit)
        }
      }
    });

  } catch (error) {
    console.error('[ADMIN ROUTES] Error fetching admin earnings:', error);
    res.status(500).json({
      success: false,
      error: 'Error fetching admin earnings',
      details: error.message
    });
  }
});

// Meeting sessions routes for admin
router.get('/meeting-sessions', async (req, res) => {
  try {
    console.log('Admin meeting-sessions endpoint called with query:', req.query);
    const { page = 1, limit = 10, meetingId, userId, userRole } = req.query;
    const offset = (page - 1) * limit;

    // Try a very simple query first to test if table exists
    const [sessions] = await db.pool.query(`
      SELECT
        ms.*,
        u.full_name,
        u.email,
        m.meeting_name
      FROM meeting_sessions ms
      LEFT JOIN users u ON ms.user_id = u.id
      LEFT JOIN meetings m ON ms.meeting_id = m.id
      ORDER BY ms.join_time DESC
      LIMIT 10
    `);

    const [totalCount] = await db.pool.query(`
      SELECT COUNT(*) as total
      FROM meeting_sessions ms
    `);

    console.log('Sessions found:', sessions.length);
    console.log('Total count:', totalCount[0]?.total);

    res.json({
      success: true,
      sessions: sessions || [],
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount[0]?.total || 0,
        pages: Math.ceil((totalCount[0]?.total || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Error getting meeting sessions for admin:', error);
    res.status(500).json({ success: false, message: 'Failed to get meeting sessions' });
  }
});

// Get meeting time statistics for admin
router.get('/meeting-time-stats', async (req, res) => {
  try {
    // Check if meeting_sessions table exists and has data
    const [tableCheck] = await db.pool.execute(`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'meeting_sessions'
    `);

    if (tableCheck[0].count === 0) {
      // Table doesn't exist, return empty stats
      return res.json({
        success: true,
        stats: {
          total_meetings: 0,
          total_sessions: 0,
          total_duration_seconds: 0,
          avg_session_duration: 0,
          teacher_total_time: 0,
          student_total_time: 0
        },
        recentSessions: []
      });
    }

    const [stats] = await db.pool.execute(`
      SELECT
        COUNT(DISTINCT m.id) as total_meetings,
        COUNT(ms.id) as total_sessions,
        COALESCE(SUM(ms.duration_seconds), 0) as total_duration_seconds,
        COALESCE(AVG(ms.duration_seconds), 0) as avg_session_duration,
        COALESCE(SUM(CASE WHEN ms.user_role = 'teacher' THEN ms.duration_seconds ELSE 0 END), 0) as teacher_total_time,
        COALESCE(SUM(CASE WHEN ms.user_role = 'student' THEN ms.duration_seconds ELSE 0 END), 0) as student_total_time
      FROM meeting_sessions ms
      JOIN meetings m ON ms.meeting_id = m.id
      WHERE ms.session_status = 'ended'
    `);

    const [recentSessions] = await db.pool.execute(`
      SELECT
        ms.*,
        u.full_name,
        u.email,
        m.meeting_name
      FROM meeting_sessions ms
      JOIN users u ON ms.user_id = u.id
      JOIN meetings m ON ms.meeting_id = m.id
      WHERE ms.session_status = 'ended'
      ORDER BY ms.leave_time DESC
      LIMIT 10
    `);

    res.json({
      success: true,
      stats: stats[0] || {
        total_meetings: 0,
        total_sessions: 0,
        total_duration_seconds: 0,
        avg_session_duration: 0,
        teacher_total_time: 0,
        student_total_time: 0
      },
      recentSessions: recentSessions || []
    });
  } catch (error) {
    console.error('Error getting meeting time stats:', error);
    res.status(500).json({ success: false, message: 'Failed to get meeting time stats' });
  }
});

// Dashboard routes
router.get('/stats', async (req, res) => {
  try {
    // Get total counts
    const [teacherCount] = await db.pool.query(
      'SELECT COUNT(*) as count FROM users WHERE role = ?',
      ['platform_teacher']
    );

    const [studentCount] = await db.pool.query(
      'SELECT COUNT(*) as count FROM users WHERE role = ?',
      ['student']
    );

    const [applicationCount] = await db.pool.query(
      'SELECT COUNT(*) as count FROM users u INNER JOIN teacher_profiles tp ON u.id = tp.user_id WHERE u.role = ? AND tp.status = ?',
      ['new_teacher', 'pending']
    );

    // Get categories count from teacher_categories
    const [categoryCount] = await db.pool.query(
      'SELECT COUNT(DISTINCT category_id) as count FROM teacher_categories'
    );

    // Get languages count from teacher_languages
    const [languageCount] = await db.pool.query(
      'SELECT COUNT(DISTINCT language_id) as count FROM teacher_languages'
    );

    // Get booking stats
    const [bookingCount] = await db.pool.query(
      'SELECT COUNT(*) as count FROM bookings'
    );

    // Calculate total revenue from bookings
    const [revenueResult] = await db.pool.query(
      'SELECT SUM(amount) as total FROM meetings WHERE status = ?',
      ['completed']
    );

    const totalRevenue = revenueResult[0].total || 0;

    res.json({
      totalTeachers: teacherCount[0].count,
      totalStudents: studentCount[0].count,
      pendingApplications: applicationCount[0].count,
      totalCourseCategories: categoryCount[0].count,
      totalLanguages: languageCount[0].count,
      totalBookings: bookingCount[0].count,
      totalRevenue: totalRevenue
    });
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Recent applications route
router.get('/recent-applications', async (req, res) => {
  try {
    const [applications] = await db.pool.query(
      `SELECT
        u.id,
        u.full_name,
        u.email,
        u.gender,
        tp.status,
        tp.created_at
      FROM users u
      INNER JOIN teacher_profiles tp ON u.id = tp.user_id
      WHERE u.role = 'new_teacher'
      ORDER BY tp.created_at DESC
      LIMIT 5`,
      []
    );

    res.json(applications);
  } catch (error) {
    console.error('Error getting recent applications:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Recent students route
router.get('/recent-students', async (req, res) => {
  try {
    // Get student data from users table and student_completion_data
    const [students] = await db.pool.query(
      `SELECT
        u.id,
        u.full_name,
        u.email,
        u.gender,
        u.created_at,
        IFNULL(scd.country, 'Unknown') as country
      FROM users u
      LEFT JOIN student_completion_data scd ON u.id = scd.user_id
      WHERE u.role = 'student'
      ORDER BY u.created_at DESC
      LIMIT 5`,
      []
    );

    res.json(students);
  } catch (error) {
    console.error('Error getting recent students:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Teachers by language
router.get('/teachers-by-language', async (req, res) => {
  try {
    // Extract language data from teacher_profiles JSON field
    const [teacherProfiles] = await db.pool.query(
      `SELECT teaching_languages FROM teacher_profiles WHERE status = 'approved'`
    );

    // Process the JSON data to count languages
    const languageCounts = {};

    teacherProfiles.forEach(profile => {
      try {
        // Parse the JSON string to array
        const languages = JSON.parse(profile.teaching_languages);

        // Count each language
        languages.forEach(lang => {
          if (typeof lang === 'string') {
            languageCounts[lang] = (languageCounts[lang] || 0) + 1;
          } else if (typeof lang === 'number') {
            // If it's a number (ID), we'll just use it as is
            languageCounts[`Language ${lang}`] = (languageCounts[`Language ${lang}`] || 0) + 1;
          }
        });
      } catch (err) {
        console.error('Error parsing teaching_languages JSON:', err);
      }
    });

    // Convert to array format expected by frontend
    const result = Object.entries(languageCounts)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 5);

    // Return actual data (empty array if no data)
    res.json(result);
  } catch (error) {
    console.error('Error getting teachers by language:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Students by country
router.get('/students-by-country', async (req, res) => {
  try {
    // Get country data from student_completion_data
    const [studentsByCountry] = await db.pool.query(
      `SELECT
        country as name,
        COUNT(user_id) as value
      FROM student_completion_data
      WHERE is_completed = 1
      GROUP BY country
      ORDER BY value DESC
      LIMIT 5`,
      []
    );

    // Return actual data (empty array if no data)
    res.json(studentsByCountry);
  } catch (error) {
    console.error('Error getting students by country:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Booking statistics by month
router.get('/booking-stats', async (req, res) => {
  try {
    // Use meetings table instead of bookings
    const [meetingStats] = await db.pool.query(
      `SELECT
        DATE_FORMAT(meeting_date, '%Y-%m') as month,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
        SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as pending
      FROM meetings
      WHERE meeting_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
      GROUP BY DATE_FORMAT(meeting_date, '%Y-%m')
      ORDER BY month ASC`,
      []
    );

    // Format the data for the frontend
    const formattedStats = meetingStats.map(stat => {
      const [year, month] = stat.month.split('-');
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return {
        name: monthNames[parseInt(month) - 1],
        completed: stat.completed,
        cancelled: stat.cancelled,
        pending: stat.pending
      };
    });

    // Return actual data (empty array if no data)
    res.json(formattedStats);
  } catch (error) {
    console.error('Error getting booking stats:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

module.exports = router;
