{"ast": null, "code": "import React,{useEffect,useState}from'react';import{Navigate,useLocation,useNavigate}from'react-router-dom';import{useAuth}from'../contexts/AuthContext';import UserStatusHandler from'../utils/userStatusHandler';import{Box,CircularProgress,Alert,AlertTitle,Button,Typography,Stack}from'@mui/material';import{Warning as WarningIcon,ExitToApp as LogoutIcon,Cancel as CancelIcon,Schedule as ScheduleIcon}from'@mui/icons-material';import{useTranslation}from'react-i18next';import{format,differenceInDays,differenceInHours,differenceInMinutes}from'date-fns';import{ar}from'date-fns/locale';import axios from'../utils/axios';/**\n * مكون حماية الطرق - يتحقق من تسجيل الدخول وحالة المستخدم\n */import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children,allowPendingDeletion=false}=_ref;const{isAuthenticated,currentUser,handleLogout}=useAuth();const location=useLocation();const navigate=useNavigate();const{t,i18n}=useTranslation();const[statusCheck,setStatusCheck]=useState({loading:true});const[cancelLoading,setCancelLoading]=useState(false);// دالة لحساب الوقت المتبقي للحذف\nconst getTimeRemaining=deleteScheduledAt=>{if(!deleteScheduledAt)return null;const now=new Date();const deleteDate=new Date(deleteScheduledAt);const days=differenceInDays(deleteDate,now);const hours=differenceInHours(deleteDate,now)%24;const minutes=differenceInMinutes(deleteDate,now)%60;if(days>0){return`${days} ${days===1?'يوم':'أيام'} و ${hours} ${hours===1?'ساعة':'ساعات'}`;}else if(hours>0){return`${hours} ${hours===1?'ساعة':'ساعات'} و ${minutes} ${minutes===1?'دقيقة':'دقائق'}`;}else if(minutes>0){return`${minutes} ${minutes===1?'دقيقة':'دقائق'}`;}else{return'أقل من دقيقة';}};// دالة إلغاء الحذف\nconst handleCancelDeletion=async()=>{try{setCancelLoading(true);await axios.post('/users/cancel-delete');// إعادة تحميل بيانات المستخدم\nwindow.location.reload();}catch(error){console.error('Error cancelling deletion:',error);alert('حدث خطأ أثناء إلغاء الحذف. يرجى المحاولة مرة أخرى.');}finally{setCancelLoading(false);}};useEffect(()=>{const checkUserStatus=async()=>{// إذا لم يكن مسجل دخول، لا نحتاج للتحقق من الحالة\nif(!isAuthenticated||!currentUser){setStatusCheck({loading:false,valid:false,reason:'not_authenticated'});return;}// التحقق من حالة المستخدم للمستخدمين المسجلين فقط\nconst result=await UserStatusHandler.checkUserStatus();if(!result.valid){if(result.reason==='deleted'||result.reason==='unauthorized'){// حفظ رسالة الحالة في localStorage\nlocalStorage.setItem('accountStatusMessage',JSON.stringify({message:result.message||'تم حذف هذا الحساب',message_en:result.message_en||'Account has been deleted',accountStatus:result.reason,deleteScheduledAt:result.deleteScheduledAt}));// حساب محذوف أو توكن منتهي الصلاحية - تسجيل خروج فوري\nhandleLogout();setStatusCheck({loading:false,valid:false,reason:result.reason,message:result.message||'تم حذف هذا الحساب'});return;}if(result.reason==='error'){// خطأ في الشبكة أو الخادم\nsetStatusCheck({loading:false,valid:false,reason:'error',message:result.message});return;}}// التعامل مع المستخدمين المجدولين للحذف\nif(result.reason==='pending_deletion'&&!allowPendingDeletion){// حساب مجدول للحذف ولا يُسمح بالوصول\nsetStatusCheck({loading:false,valid:false,reason:'pending_deletion',message:'هذا الحساب مجدول للحذف.'});return;}setStatusCheck({loading:false,valid:true});};checkUserStatus();},[isAuthenticated,currentUser,allowPendingDeletion,handleLogout]);// غير مسجل دخول - إعادة توجيه فورية بدون loading\nif(!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}// جاري التحميل للمستخدمين المسجلين فقط\nif(statusCheck.loading){return/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",flexDirection:\"column\",gap:2,children:[/*#__PURE__*/_jsx(CircularProgress,{size:40}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062A\\u062D\\u0642\\u0642 \\u0645\\u0646 \\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628...\"})]});}// حساب محذوف - إعادة توجيه لصفحة تسجيل الدخول\nif(statusCheck.reason==='deleted'||statusCheck.reason==='unauthorized'){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}// حساب مجدول للحذف ولا يُسمح بالوصول\nif(statusCheck.reason==='pending_deletion'&&!allowPendingDeletion){const timeRemaining=getTimeRemaining(currentUser===null||currentUser===void 0?void 0:currentUser.delete_scheduled_at);return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",p:3,children:/*#__PURE__*/_jsxs(Alert,{severity:\"warning\",icon:/*#__PURE__*/_jsx(WarningIcon,{}),sx:{maxWidth:600,width:'100%'},children:[/*#__PURE__*/_jsxs(AlertTitle,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(ScheduleIcon,{}),\"\\u062D\\u0633\\u0627\\u0628 \\u0645\\u062C\\u062F\\u0648\\u0644 \\u0644\\u0644\\u062D\\u0630\\u0641\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2},children:statusCheck.message}),timeRemaining&&/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{mb:2,color:'error.main',fontWeight:'bold'},children:[\"\\u23F0 \\u0627\\u0644\\u0648\\u0642\\u062A \\u0627\\u0644\\u0645\\u062A\\u0628\\u0642\\u064A \\u0644\\u0644\\u062D\\u0630\\u0641: \",timeRemaining]}),(currentUser===null||currentUser===void 0?void 0:currentUser.delete_scheduled_at)&&/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",sx:{mb:3,display:'block',color:'text.secondary'},children:[\"\\uD83D\\uDCC5 \\u0645\\u0648\\u0639\\u062F \\u0627\\u0644\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0645\\u062C\\u062F\\u0648\\u0644: \",format(new Date(currentUser.delete_scheduled_at),'dd/MM/yyyy HH:mm',{locale:i18n.language==='ar'?ar:undefined})]}),/*#__PURE__*/_jsxs(Stack,{direction:\"row\",spacing:2,sx:{mt:2},children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"success\",startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),onClick:handleCancelDeletion,disabled:cancelLoading,size:\"small\",children:cancelLoading?'جاري الإلغاء...':'إلغاء الحذف'}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"error\",startIcon:/*#__PURE__*/_jsx(LogoutIcon,{}),onClick:handleLogout,size:\"small\",children:\"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C\"})]})]})});}// كل شيء على ما يرام\nreturn children;};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Navigate", "useLocation", "useNavigate", "useAuth", "UserStatusHandler", "Box", "CircularProgress", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Warning", "WarningIcon", "ExitToApp", "LogoutIcon", "Cancel", "CancelIcon", "Schedule", "ScheduleIcon", "useTranslation", "format", "differenceInDays", "differenceInHours", "differenceInMinutes", "ar", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "ProtectedRoute", "_ref", "children", "allowPendingDeletion", "isAuthenticated", "currentUser", "handleLogout", "location", "navigate", "t", "i18n", "statusCheck", "setStatusCheck", "loading", "cancelLoading", "setCancelLoading", "getTimeRemaining", "deleteScheduledAt", "now", "Date", "deleteDate", "days", "hours", "minutes", "handleCancelDeletion", "post", "window", "reload", "error", "console", "alert", "checkUserStatus", "valid", "reason", "result", "localStorage", "setItem", "JSON", "stringify", "message", "message_en", "accountStatus", "to", "state", "from", "replace", "display", "justifyContent", "alignItems", "minHeight", "flexDirection", "gap", "size", "timeRemaining", "delete_scheduled_at", "p", "severity", "icon", "sx", "max<PERSON><PERSON><PERSON>", "width", "variant", "mb", "color", "fontWeight", "locale", "language", "undefined", "direction", "spacing", "mt", "startIcon", "onClick", "disabled"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Navigate, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport UserStatusHandler from '../utils/userStatusHandler';\nimport { Box, CircularProgress, Alert, AlertTitle, Button, Typography, Stack } from '@mui/material';\nimport { Warning as WarningIcon, ExitToApp as LogoutIcon, Cancel as CancelIcon, Schedule as ScheduleIcon } from '@mui/icons-material';\nimport { useTranslation } from 'react-i18next';\nimport { format, differenceInDays, differenceInHours, differenceInMinutes } from 'date-fns';\nimport { ar } from 'date-fns/locale';\nimport axios from '../utils/axios';\n\n/**\n * مكون حماية الطرق - يتحقق من تسجيل الدخول وحالة المستخدم\n */\nconst ProtectedRoute = ({ children, allowPendingDeletion = false }) => {\n  const { isAuthenticated, currentUser, handleLogout } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { t, i18n } = useTranslation();\n  const [statusCheck, setStatusCheck] = useState({ loading: true });\n  const [cancelLoading, setCancelLoading] = useState(false);\n\n  // دالة لحساب الوقت المتبقي للحذف\n  const getTimeRemaining = (deleteScheduledAt) => {\n    if (!deleteScheduledAt) return null;\n\n    const now = new Date();\n    const deleteDate = new Date(deleteScheduledAt);\n\n    const days = differenceInDays(deleteDate, now);\n    const hours = differenceInHours(deleteDate, now) % 24;\n    const minutes = differenceInMinutes(deleteDate, now) % 60;\n\n    if (days > 0) {\n      return `${days} ${days === 1 ? 'يوم' : 'أيام'} و ${hours} ${hours === 1 ? 'ساعة' : 'ساعات'}`;\n    } else if (hours > 0) {\n      return `${hours} ${hours === 1 ? 'ساعة' : 'ساعات'} و ${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;\n    } else if (minutes > 0) {\n      return `${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;\n    } else {\n      return 'أقل من دقيقة';\n    }\n  };\n\n  // دالة إلغاء الحذف\n  const handleCancelDeletion = async () => {\n    try {\n      setCancelLoading(true);\n      await axios.post('/users/cancel-delete');\n\n      // إعادة تحميل بيانات المستخدم\n      window.location.reload();\n    } catch (error) {\n      console.error('Error cancelling deletion:', error);\n      alert('حدث خطأ أثناء إلغاء الحذف. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setCancelLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    const checkUserStatus = async () => {\n      // إذا لم يكن مسجل دخول، لا نحتاج للتحقق من الحالة\n      if (!isAuthenticated || !currentUser) {\n        setStatusCheck({ loading: false, valid: false, reason: 'not_authenticated' });\n        return;\n      }\n\n      // التحقق من حالة المستخدم للمستخدمين المسجلين فقط\n      const result = await UserStatusHandler.checkUserStatus();\n      \n      if (!result.valid) {\n        if (result.reason === 'deleted' || result.reason === 'unauthorized') {\n          // حفظ رسالة الحالة في localStorage\n          localStorage.setItem('accountStatusMessage', JSON.stringify({\n            message: result.message || 'تم حذف هذا الحساب',\n            message_en: result.message_en || 'Account has been deleted',\n            accountStatus: result.reason,\n            deleteScheduledAt: result.deleteScheduledAt\n          }));\n\n          // حساب محذوف أو توكن منتهي الصلاحية - تسجيل خروج فوري\n          handleLogout();\n          setStatusCheck({\n            loading: false,\n            valid: false,\n            reason: result.reason,\n            message: result.message || 'تم حذف هذا الحساب'\n          });\n          return;\n        }\n\n        if (result.reason === 'error') {\n          // خطأ في الشبكة أو الخادم\n          setStatusCheck({\n            loading: false,\n            valid: false,\n            reason: 'error',\n            message: result.message\n          });\n          return;\n        }\n      }\n\n      // التعامل مع المستخدمين المجدولين للحذف\n      if (result.reason === 'pending_deletion' && !allowPendingDeletion) {\n        // حساب مجدول للحذف ولا يُسمح بالوصول\n        setStatusCheck({\n          loading: false,\n          valid: false,\n          reason: 'pending_deletion',\n          message: 'هذا الحساب مجدول للحذف.'\n        });\n        return;\n      }\n\n      setStatusCheck({ loading: false, valid: true });\n    };\n\n    checkUserStatus();\n  }, [isAuthenticated, currentUser, allowPendingDeletion, handleLogout]);\n\n  // غير مسجل دخول - إعادة توجيه فورية بدون loading\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // جاري التحميل للمستخدمين المسجلين فقط\n  if (statusCheck.loading) {\n    return (\n      <Box\n        display=\"flex\"\n        justifyContent=\"center\"\n        alignItems=\"center\"\n        minHeight=\"400px\"\n        flexDirection=\"column\"\n        gap={2}\n      >\n        <CircularProgress size={40} />\n        <div>جاري التحقق من حالة الحساب...</div>\n      </Box>\n    );\n  }\n\n  // حساب محذوف - إعادة توجيه لصفحة تسجيل الدخول\n  if (statusCheck.reason === 'deleted' || statusCheck.reason === 'unauthorized') {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // حساب مجدول للحذف ولا يُسمح بالوصول\n  if (statusCheck.reason === 'pending_deletion' && !allowPendingDeletion) {\n    const timeRemaining = getTimeRemaining(currentUser?.delete_scheduled_at);\n\n    return (\n      <Box\n        display=\"flex\"\n        justifyContent=\"center\"\n        alignItems=\"center\"\n        minHeight=\"400px\"\n        p={3}\n      >\n        <Alert\n          severity=\"warning\"\n          icon={<WarningIcon />}\n          sx={{ maxWidth: 600, width: '100%' }}\n        >\n          <AlertTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <ScheduleIcon />\n            حساب مجدول للحذف\n          </AlertTitle>\n\n          <Typography variant=\"body1\" sx={{ mb: 2 }}>\n            {statusCheck.message}\n          </Typography>\n\n          {timeRemaining && (\n            <Typography variant=\"body2\" sx={{ mb: 2, color: 'error.main', fontWeight: 'bold' }}>\n              ⏰ الوقت المتبقي للحذف: {timeRemaining}\n            </Typography>\n          )}\n\n          {currentUser?.delete_scheduled_at && (\n            <Typography variant=\"caption\" sx={{ mb: 3, display: 'block', color: 'text.secondary' }}>\n              📅 موعد الحذف المجدول: {format(new Date(currentUser.delete_scheduled_at), 'dd/MM/yyyy HH:mm', {\n                locale: i18n.language === 'ar' ? ar : undefined\n              })}\n            </Typography>\n          )}\n\n          <Stack direction=\"row\" spacing={2} sx={{ mt: 2 }}>\n            <Button\n              variant=\"contained\"\n              color=\"success\"\n              startIcon={<CancelIcon />}\n              onClick={handleCancelDeletion}\n              disabled={cancelLoading}\n              size=\"small\"\n            >\n              {cancelLoading ? 'جاري الإلغاء...' : 'إلغاء الحذف'}\n            </Button>\n\n            <Button\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<LogoutIcon />}\n              onClick={handleLogout}\n              size=\"small\"\n            >\n              تسجيل الخروج\n            </Button>\n          </Stack>\n        </Alert>\n      </Box>\n    );\n  }\n\n  // كل شيء على ما يرام\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,QAAQ,CAAEC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CACrE,OAASC,OAAO,KAAQ,yBAAyB,CACjD,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAC1D,OAASC,GAAG,CAAEC,gBAAgB,CAAEC,KAAK,CAAEC,UAAU,CAAEC,MAAM,CAAEC,UAAU,CAAEC,KAAK,KAAQ,eAAe,CACnG,OAASC,OAAO,GAAI,CAAAC,WAAW,CAAEC,SAAS,GAAI,CAAAC,UAAU,CAAEC,MAAM,GAAI,CAAAC,UAAU,CAAEC,QAAQ,GAAI,CAAAC,YAAY,KAAQ,qBAAqB,CACrI,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,MAAM,CAAEC,gBAAgB,CAAEC,iBAAiB,CAAEC,mBAAmB,KAAQ,UAAU,CAC3F,OAASC,EAAE,KAAQ,iBAAiB,CACpC,MAAO,CAAAC,KAAK,KAAM,gBAAgB,CAElC;AACA;AACA,GAFA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGA,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAgD,IAA/C,CAAEC,QAAQ,CAAEC,oBAAoB,CAAG,KAAM,CAAC,CAAAF,IAAA,CAChE,KAAM,CAAEG,eAAe,CAAEC,WAAW,CAAEC,YAAa,CAAC,CAAGlC,OAAO,CAAC,CAAC,CAChE,KAAM,CAAAmC,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsC,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEsC,CAAC,CAAEC,IAAK,CAAC,CAAGrB,cAAc,CAAC,CAAC,CACpC,KAAM,CAACsB,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAC,CAAE6C,OAAO,CAAE,IAAK,CAAC,CAAC,CACjE,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAEzD;AACA,KAAM,CAAAgD,gBAAgB,CAAIC,iBAAiB,EAAK,CAC9C,GAAI,CAACA,iBAAiB,CAAE,MAAO,KAAI,CAEnC,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAD,IAAI,CAACF,iBAAiB,CAAC,CAE9C,KAAM,CAAAI,IAAI,CAAG9B,gBAAgB,CAAC6B,UAAU,CAAEF,GAAG,CAAC,CAC9C,KAAM,CAAAI,KAAK,CAAG9B,iBAAiB,CAAC4B,UAAU,CAAEF,GAAG,CAAC,CAAG,EAAE,CACrD,KAAM,CAAAK,OAAO,CAAG9B,mBAAmB,CAAC2B,UAAU,CAAEF,GAAG,CAAC,CAAG,EAAE,CAEzD,GAAIG,IAAI,CAAG,CAAC,CAAE,CACZ,MAAO,GAAGA,IAAI,IAAIA,IAAI,GAAK,CAAC,CAAG,KAAK,CAAG,MAAM,MAAMC,KAAK,IAAIA,KAAK,GAAK,CAAC,CAAG,MAAM,CAAG,OAAO,EAAE,CAC9F,CAAC,IAAM,IAAIA,KAAK,CAAG,CAAC,CAAE,CACpB,MAAO,GAAGA,KAAK,IAAIA,KAAK,GAAK,CAAC,CAAG,MAAM,CAAG,OAAO,MAAMC,OAAO,IAAIA,OAAO,GAAK,CAAC,CAAG,OAAO,CAAG,OAAO,EAAE,CACvG,CAAC,IAAM,IAAIA,OAAO,CAAG,CAAC,CAAE,CACtB,MAAO,GAAGA,OAAO,IAAIA,OAAO,GAAK,CAAC,CAAG,OAAO,CAAG,OAAO,EAAE,CAC1D,CAAC,IAAM,CACL,MAAO,cAAc,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAAC,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACFT,gBAAgB,CAAC,IAAI,CAAC,CACtB,KAAM,CAAApB,KAAK,CAAC8B,IAAI,CAAC,sBAAsB,CAAC,CAExC;AACAC,MAAM,CAACnB,QAAQ,CAACoB,MAAM,CAAC,CAAC,CAC1B,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDE,KAAK,CAAC,oDAAoD,CAAC,CAC7D,CAAC,OAAS,CACRf,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAEDhD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgE,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC;AACA,GAAI,CAAC3B,eAAe,EAAI,CAACC,WAAW,CAAE,CACpCO,cAAc,CAAC,CAAEC,OAAO,CAAE,KAAK,CAAEmB,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,mBAAoB,CAAC,CAAC,CAC7E,OACF,CAEA;AACA,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAA7D,iBAAiB,CAAC0D,eAAe,CAAC,CAAC,CAExD,GAAI,CAACG,MAAM,CAACF,KAAK,CAAE,CACjB,GAAIE,MAAM,CAACD,MAAM,GAAK,SAAS,EAAIC,MAAM,CAACD,MAAM,GAAK,cAAc,CAAE,CACnE;AACAE,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAEC,IAAI,CAACC,SAAS,CAAC,CAC1DC,OAAO,CAAEL,MAAM,CAACK,OAAO,EAAI,mBAAmB,CAC9CC,UAAU,CAAEN,MAAM,CAACM,UAAU,EAAI,0BAA0B,CAC3DC,aAAa,CAAEP,MAAM,CAACD,MAAM,CAC5BhB,iBAAiB,CAAEiB,MAAM,CAACjB,iBAC5B,CAAC,CAAC,CAAC,CAEH;AACAX,YAAY,CAAC,CAAC,CACdM,cAAc,CAAC,CACbC,OAAO,CAAE,KAAK,CACdmB,KAAK,CAAE,KAAK,CACZC,MAAM,CAAEC,MAAM,CAACD,MAAM,CACrBM,OAAO,CAAEL,MAAM,CAACK,OAAO,EAAI,mBAC7B,CAAC,CAAC,CACF,OACF,CAEA,GAAIL,MAAM,CAACD,MAAM,GAAK,OAAO,CAAE,CAC7B;AACArB,cAAc,CAAC,CACbC,OAAO,CAAE,KAAK,CACdmB,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,OAAO,CACfM,OAAO,CAAEL,MAAM,CAACK,OAClB,CAAC,CAAC,CACF,OACF,CACF,CAEA;AACA,GAAIL,MAAM,CAACD,MAAM,GAAK,kBAAkB,EAAI,CAAC9B,oBAAoB,CAAE,CACjE;AACAS,cAAc,CAAC,CACbC,OAAO,CAAE,KAAK,CACdmB,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,kBAAkB,CAC1BM,OAAO,CAAE,yBACX,CAAC,CAAC,CACF,OACF,CAEA3B,cAAc,CAAC,CAAEC,OAAO,CAAE,KAAK,CAAEmB,KAAK,CAAE,IAAK,CAAC,CAAC,CACjD,CAAC,CAEDD,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,CAAC3B,eAAe,CAAEC,WAAW,CAAEF,oBAAoB,CAAEG,YAAY,CAAC,CAAC,CAEtE;AACA,GAAI,CAACF,eAAe,CAAE,CACpB,mBAAOP,IAAA,CAAC5B,QAAQ,EAACyE,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAErC,QAAS,CAAE,CAACsC,OAAO,MAAE,CAAC,CACpE,CAEA;AACA,GAAIlC,WAAW,CAACE,OAAO,CAAE,CACvB,mBACEd,KAAA,CAACzB,GAAG,EACFwE,OAAO,CAAC,MAAM,CACdC,cAAc,CAAC,QAAQ,CACvBC,UAAU,CAAC,QAAQ,CACnBC,SAAS,CAAC,OAAO,CACjBC,aAAa,CAAC,QAAQ,CACtBC,GAAG,CAAE,CAAE,CAAAjD,QAAA,eAEPL,IAAA,CAACtB,gBAAgB,EAAC6E,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9BvD,IAAA,QAAAK,QAAA,CAAK,6IAA6B,CAAK,CAAC,EACrC,CAAC,CAEV,CAEA;AACA,GAAIS,WAAW,CAACsB,MAAM,GAAK,SAAS,EAAItB,WAAW,CAACsB,MAAM,GAAK,cAAc,CAAE,CAC7E,mBAAOpC,IAAA,CAAC5B,QAAQ,EAACyE,EAAE,CAAC,QAAQ,CAACG,OAAO,MAAE,CAAC,CACzC,CAEA;AACA,GAAIlC,WAAW,CAACsB,MAAM,GAAK,kBAAkB,EAAI,CAAC9B,oBAAoB,CAAE,CACtE,KAAM,CAAAkD,aAAa,CAAGrC,gBAAgB,CAACX,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEiD,mBAAmB,CAAC,CAExE,mBACEzD,IAAA,CAACvB,GAAG,EACFwE,OAAO,CAAC,MAAM,CACdC,cAAc,CAAC,QAAQ,CACvBC,UAAU,CAAC,QAAQ,CACnBC,SAAS,CAAC,OAAO,CACjBM,CAAC,CAAE,CAAE,CAAArD,QAAA,cAELH,KAAA,CAACvB,KAAK,EACJgF,QAAQ,CAAC,SAAS,CAClBC,IAAI,cAAE5D,IAAA,CAACf,WAAW,GAAE,CAAE,CACtB4E,EAAE,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAA1D,QAAA,eAErCH,KAAA,CAACtB,UAAU,EAACiF,EAAE,CAAE,CAAEZ,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEG,GAAG,CAAE,CAAE,CAAE,CAAAjD,QAAA,eAChEL,IAAA,CAACT,YAAY,GAAE,CAAC,yFAElB,EAAY,CAAC,cAEbS,IAAA,CAAClB,UAAU,EAACkF,OAAO,CAAC,OAAO,CAACH,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAA5D,QAAA,CACvCS,WAAW,CAAC4B,OAAO,CACV,CAAC,CAEZc,aAAa,eACZtD,KAAA,CAACpB,UAAU,EAACkF,OAAO,CAAC,OAAO,CAACH,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,YAAY,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAA9D,QAAA,EAAC,mHAC3D,CAACmD,aAAa,EAC3B,CACb,CAEA,CAAAhD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEiD,mBAAmB,gBAC/BvD,KAAA,CAACpB,UAAU,EAACkF,OAAO,CAAC,SAAS,CAACH,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAC,CAAEhB,OAAO,CAAE,OAAO,CAAEiB,KAAK,CAAE,gBAAiB,CAAE,CAAA7D,QAAA,EAAC,mHAC/D,CAACZ,MAAM,CAAC,GAAI,CAAA6B,IAAI,CAACd,WAAW,CAACiD,mBAAmB,CAAC,CAAE,kBAAkB,CAAE,CAC5FW,MAAM,CAAEvD,IAAI,CAACwD,QAAQ,GAAK,IAAI,CAAGxE,EAAE,CAAGyE,SACxC,CAAC,CAAC,EACQ,CACb,cAEDpE,KAAA,CAACnB,KAAK,EAACwF,SAAS,CAAC,KAAK,CAACC,OAAO,CAAE,CAAE,CAACX,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAApE,QAAA,eAC/CL,IAAA,CAACnB,MAAM,EACLmF,OAAO,CAAC,WAAW,CACnBE,KAAK,CAAC,SAAS,CACfQ,SAAS,cAAE1E,IAAA,CAACX,UAAU,GAAE,CAAE,CAC1BsF,OAAO,CAAEhD,oBAAqB,CAC9BiD,QAAQ,CAAE3D,aAAc,CACxBsC,IAAI,CAAC,OAAO,CAAAlD,QAAA,CAEXY,aAAa,CAAG,iBAAiB,CAAG,aAAa,CAC5C,CAAC,cAETjB,IAAA,CAACnB,MAAM,EACLmF,OAAO,CAAC,UAAU,CAClBE,KAAK,CAAC,OAAO,CACbQ,SAAS,cAAE1E,IAAA,CAACb,UAAU,GAAE,CAAE,CAC1BwF,OAAO,CAAElE,YAAa,CACtB8C,IAAI,CAAC,OAAO,CAAAlD,QAAA,CACb,qEAED,CAAQ,CAAC,EACJ,CAAC,EACH,CAAC,CACL,CAAC,CAEV,CAEA;AACA,MAAO,CAAAA,QAAQ,CACjB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}