{"ast": null, "code": "import React,{createContext,useState,useContext,useEffect}from'react';import axios from'../utils/axios';import{useNavigate}from'react-router-dom';import{useTranslation}from'react-i18next';import{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext(null);export const AuthProvider=_ref=>{let{children}=_ref;const[currentUser,setCurrentUser]=useState(null);const[token,setToken]=useState(localStorage.getItem('token'));const[loading,setLoading]=useState(true);const[initialized,setInitialized]=useState(false);const[isAuthenticated,setIsAuthenticated]=useState(false);const navigate=useNavigate();const{t}=useTranslation();useEffect(()=>{// Check if user is logged in on mount\nconst storedToken=localStorage.getItem('token');if(storedToken){// Set token in axios headers\naxios.defaults.headers.common['Authorization']=`Bearer ${storedToken}`;// Verify token and get user data\nconsole.log('Verifying token...');axios.get('/auth/verify').then(response=>{console.log('Token verification response:',response.data);if(response.data&&response.data.success&&response.data.user){const userData={...response.data.user,full_name:response.data.user.full_name||null,email:response.data.user.email||null,gender:response.data.user.gender||null,status:response.data.user.status||'active',delete_scheduled_at:response.data.user.delete_scheduled_at||null};// التحقق من حالة المستخدم\nif(userData.status==='deleted'){console.log('User account is deleted - logging out');handleLogout();return;}if(userData.status==='pending_deletion'){console.log('User account is pending deletion - allowing limited access');// السماح بالوصول ولكن مع تحذير\n}setCurrentUser(userData);setToken(storedToken);setIsAuthenticated(true);}else{console.log('Invalid response from verify endpoint');handleLogout();}}).catch(error=>{console.error('Token verification error:',error);// التحقق من نوع الخطأ\nif(error.response&&error.response.status===401){const errorData=error.response.data;// إذا كان هناك معلومات عن حالة الحساب، حفظها للعرض في صفحة تسجيل الدخول\nif(errorData&&errorData.accountStatus){if(process.env.NODE_ENV==='development'){console.log('Account status detected in AuthContext:',errorData.accountStatus);}// حفظ الرسالة المترجمة\nconst messageToSave={message:errorData.message,// الرسالة بالعربية\nmessage_en:errorData.message_en,// الرسالة بالإنجليزية\naccountStatus:errorData.accountStatus,deleteScheduledAt:errorData.deleteScheduledAt};localStorage.setItem('accountStatusMessage',JSON.stringify(messageToSave));// منع الدخول للمنصة - تسجيل خروج فوري\nhandleLogout();// منع أي طلبات إضافية\nsetLoading(false);setInitialized(true);return;}}handleLogout();}).finally(()=>{setLoading(false);setInitialized(true);});}else{console.log('No token found in localStorage');setLoading(false);setInitialized(true);}},[]);const handleLogout=async()=>{try{if(token){await axios.post('/auth/logout');}}catch(error){console.error('Logout request failed:',error);}finally{localStorage.removeItem('token');setCurrentUser(null);setToken(null);setIsAuthenticated(false);delete axios.defaults.headers.common['Authorization'];navigate('/login');}};const redirectToDashboard=user=>{switch(user.role){case'admin':navigate('/admin/dashboard');break;case'platform_teacher':navigate('/teacher/dashboard');break;case'new_teacher':navigate('/teacher/application');break;case'student':navigate('/student/dashboard');break;default:navigate('/');}};const login=async(emailOrToken,passwordOrUser)=>{try{// If called with token and user (from email verification)\nif(typeof emailOrToken==='string'&&emailOrToken.length>50&&typeof passwordOrUser==='object'){const token=emailOrToken;const user=passwordOrUser;const userData={...user,full_name:user.full_name||null,email:user.email||null,gender:user.gender||null};localStorage.setItem('token',token);axios.defaults.headers.common['Authorization']=`Bearer ${token}`;setCurrentUser(userData);setToken(token);setIsAuthenticated(true);redirectToDashboard(userData);return{success:true};}// Normal login with email and password\nconst response=await axios.post('/auth/login',{email:emailOrToken||null,password:passwordOrUser||null});if(response.data&&response.data.success&&response.data.token&&response.data.user){const{token,user}=response.data;const userData={...user,full_name:user.full_name||null,email:user.email||null,gender:user.gender||null};localStorage.setItem('token',token);axios.defaults.headers.common['Authorization']=`Bearer ${token}`;setCurrentUser(userData);setToken(token);setIsAuthenticated(true);redirectToDashboard(userData);return{success:true};}// If login failed, throw error to be caught by the calling component\nconst errorMessage=response.data.message||'Login failed';throw new Error(errorMessage);}catch(error){var _error$response,_error$response2;console.error('Login error in AuthContext:',{error:error,response:error.response,data:(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.data,status:(_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.status,message:error.message});// Re-throw the error as-is since axios interceptor already enhanced it\nthrow error;}};const register=async userData=>{try{const response=await axios.post('/auth/register',userData);// Check if registration was successful (email verification required)\nif(response.data&&response.data.success){return{success:true,email:response.data.email,message:response.data.message};}return{success:false,error:response.data.message||'Registration failed'};}catch(error){var _error$response3,_error$response3$data;return{success:false,error:((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.message)||'Failed to register'};}};const updateUser=userData=>{setCurrentUser(prevUser=>({...prevUser,...userData}));};const googleLogin=async credentialResponse=>{try{const response=await axios.post('/auth/google',{credential:credentialResponse.credential});if(response.data&&response.data.success&&response.data.token&&response.data.user){const{token,user}=response.data;const userData={...user,full_name:user.full_name||null,email:user.email||null,gender:user.gender||null};localStorage.setItem('token',token);axios.defaults.headers.common['Authorization']=`Bearer ${token}`;setCurrentUser(userData);setToken(token);setIsAuthenticated(true);return{success:true,user:userData};}return{success:false,message:response.data.message};}catch(error){console.error('Google login error:',error);// Handle specific error cases\nif(error.response&&error.response.data){const errorData=error.response.data;// Handle deleted account\nif(errorData.errorType==='ACCOUNT_DELETED'){return{success:false,message:t('auth.accountDeleted'),errorType:'ACCOUNT_DELETED',accountStatus:'deleted'};}// Handle other server errors\nreturn{success:false,message:errorData.message||t('auth.loginFailed')};}// Handle network or other errors\nthrow error;}};const googleRegister=async _ref2=>{let{credential,role,gender}=_ref2;try{const response=await axios.post('/auth/google/register',{credential,role,gender});// Check if registration was successful (email verification required)\nif(response.data&&response.data.success){return{success:true,email:response.data.email,message:response.data.message};}return{success:false,error:response.data.message||'Registration failed'};}catch(error){var _error$response4,_error$response4$data;console.error('Google registration error:',error);return{success:false,error:((_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.message)||'Failed to register with Google'};}};const value={currentUser,token,loading,initialized,isAuthenticated,login,register,handleLogout,updateUser,googleLogin,googleRegister};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};export const useAuth=()=>{const context=useContext(AuthContext);if(!context){throw new Error('useAuth must be used within an AuthProvider');}return context;};export default AuthContext;", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "axios", "useNavigate", "useTranslation", "jsx", "_jsx", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "currentUser", "setCurrentUser", "token", "setToken", "localStorage", "getItem", "loading", "setLoading", "initialized", "setInitialized", "isAuthenticated", "setIsAuthenticated", "navigate", "t", "storedToken", "defaults", "headers", "common", "console", "log", "get", "then", "response", "data", "success", "user", "userData", "full_name", "email", "gender", "status", "delete_scheduled_at", "handleLogout", "catch", "error", "errorData", "accountStatus", "process", "env", "NODE_ENV", "messageToSave", "message", "message_en", "deleteScheduledAt", "setItem", "JSON", "stringify", "finally", "post", "removeItem", "redirectToDashboard", "role", "login", "emailOrToken", "passwordOrUser", "length", "password", "errorMessage", "Error", "_error$response", "_error$response2", "register", "_error$response3", "_error$response3$data", "updateUser", "prevUser", "googleLogin", "credentialResponse", "credential", "errorType", "googleRegister", "_ref2", "_error$response4", "_error$response4$data", "value", "Provider", "useAuth", "context"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect } from 'react';\nimport axios from '../utils/axios';\nimport { useNavigate } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\n\nconst AuthContext = createContext(null);\n\nexport const AuthProvider = ({ children }) => {\n  const [currentUser, setCurrentUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n  const [initialized, setInitialized] = useState(false);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const navigate = useNavigate();\n  const { t } = useTranslation();\n\n  useEffect(() => {\n    // Check if user is logged in on mount\n    const storedToken = localStorage.getItem('token');\n    if (storedToken) {\n      // Set token in axios headers\n      axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;\n      \n      // Verify token and get user data\n      console.log('Verifying token...');\n      axios.get('/auth/verify')\n        .then(response => {\n          console.log('Token verification response:', response.data);\n          if (response.data && response.data.success && response.data.user) {\n            const userData = {\n              ...response.data.user,\n              full_name: response.data.user.full_name || null,\n              email: response.data.user.email || null,\n              gender: response.data.user.gender || null,\n              status: response.data.user.status || 'active',\n              delete_scheduled_at: response.data.user.delete_scheduled_at || null\n            };\n\n            // التحقق من حالة المستخدم\n            if (userData.status === 'deleted') {\n              console.log('User account is deleted - logging out');\n              handleLogout();\n              return;\n            }\n\n            if (userData.status === 'pending_deletion') {\n              console.log('User account is pending deletion - allowing limited access');\n              // السماح بالوصول ولكن مع تحذير\n            }\n\n            setCurrentUser(userData);\n            setToken(storedToken);\n            setIsAuthenticated(true);\n          } else {\n            console.log('Invalid response from verify endpoint');\n            handleLogout();\n          }\n        })\n        .catch(error => {\n          console.error('Token verification error:', error);\n\n          // التحقق من نوع الخطأ\n          if (error.response && error.response.status === 401) {\n            const errorData = error.response.data;\n\n            // إذا كان هناك معلومات عن حالة الحساب، حفظها للعرض في صفحة تسجيل الدخول\n            if (errorData && errorData.accountStatus) {\n              if (process.env.NODE_ENV === 'development') {\n                console.log('Account status detected in AuthContext:', errorData.accountStatus);\n              }\n\n              // حفظ الرسالة المترجمة\n              const messageToSave = {\n                message: errorData.message, // الرسالة بالعربية\n                message_en: errorData.message_en, // الرسالة بالإنجليزية\n                accountStatus: errorData.accountStatus,\n                deleteScheduledAt: errorData.deleteScheduledAt\n              };\n\n              localStorage.setItem('accountStatusMessage', JSON.stringify(messageToSave));\n\n              // منع الدخول للمنصة - تسجيل خروج فوري\n              handleLogout();\n\n              // منع أي طلبات إضافية\n              setLoading(false);\n              setInitialized(true);\n              return;\n            }\n          }\n\n          handleLogout();\n        })\n        .finally(() => {\n          setLoading(false);\n          setInitialized(true);\n        });\n    } else {\n      console.log('No token found in localStorage');\n      setLoading(false);\n      setInitialized(true);\n    }\n  }, []);\n\n  const handleLogout = async () => {\n    try {\n      if (token) {\n        await axios.post('/auth/logout');\n      }\n    } catch (error) {\n      console.error('Logout request failed:', error);\n    } finally {\n      localStorage.removeItem('token');\n      setCurrentUser(null);\n      setToken(null);\n      setIsAuthenticated(false);\n      delete axios.defaults.headers.common['Authorization'];\n      navigate('/login');\n    }\n  };\n\n  const redirectToDashboard = (user) => {\n    switch (user.role) {\n      case 'admin':\n        navigate('/admin/dashboard');\n        break;\n      case 'platform_teacher':\n        navigate('/teacher/dashboard');\n        break;\n      case 'new_teacher':\n        navigate('/teacher/application');\n        break;\n      case 'student':\n        navigate('/student/dashboard');\n        break;\n      default:\n        navigate('/');\n    }\n  };\n\n  const login = async (emailOrToken, passwordOrUser) => {\n    try {\n      // If called with token and user (from email verification)\n      if (typeof emailOrToken === 'string' && emailOrToken.length > 50 && typeof passwordOrUser === 'object') {\n        const token = emailOrToken;\n        const user = passwordOrUser;\n\n        const userData = {\n          ...user,\n          full_name: user.full_name || null,\n          email: user.email || null,\n          gender: user.gender || null\n        };\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        setCurrentUser(userData);\n        setToken(token);\n        setIsAuthenticated(true);\n        redirectToDashboard(userData);\n        return { success: true };\n      }\n\n      // Normal login with email and password\n      const response = await axios.post('/auth/login', {\n        email: emailOrToken || null,\n        password: passwordOrUser || null\n      });\n\n      if (response.data && response.data.success && response.data.token && response.data.user) {\n        const { token, user } = response.data;\n        const userData = {\n          ...user,\n          full_name: user.full_name || null,\n          email: user.email || null,\n          gender: user.gender || null\n        };\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        setCurrentUser(userData);\n        setToken(token);\n        setIsAuthenticated(true);\n        redirectToDashboard(userData);\n        return { success: true };\n      }\n      // If login failed, throw error to be caught by the calling component\n      const errorMessage = response.data.message || 'Login failed';\n      throw new Error(errorMessage);\n    } catch (error) {\n      console.error('Login error in AuthContext:', {\n        error: error,\n        response: error.response,\n        data: error.response?.data,\n        status: error.response?.status,\n        message: error.message\n      });\n\n      // Re-throw the error as-is since axios interceptor already enhanced it\n      throw error;\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await axios.post('/auth/register', userData);\n\n      // Check if registration was successful (email verification required)\n      if (response.data && response.data.success) {\n        return {\n          success: true,\n          email: response.data.email,\n          message: response.data.message\n        };\n      }\n\n      return {\n        success: false,\n        error: response.data.message || 'Registration failed'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.response?.data?.message || 'Failed to register'\n      };\n    }\n  };\n\n  const updateUser = (userData) => {\n    setCurrentUser(prevUser => ({\n      ...prevUser,\n      ...userData\n    }));\n  };\n\n  const googleLogin = async (credentialResponse) => {\n    try {\n      const response = await axios.post('/auth/google', {\n        credential: credentialResponse.credential\n      });\n\n      if (response.data && response.data.success && response.data.token && response.data.user) {\n        const { token, user } = response.data;\n        const userData = {\n          ...user,\n          full_name: user.full_name || null,\n          email: user.email || null,\n          gender: user.gender || null\n        };\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        setCurrentUser(userData);\n        setToken(token);\n        setIsAuthenticated(true);\n        return { success: true, user: userData };\n      }\n      return { success: false, message: response.data.message };\n    } catch (error) {\n      console.error('Google login error:', error);\n\n      // Handle specific error cases\n      if (error.response && error.response.data) {\n        const errorData = error.response.data;\n\n        // Handle deleted account\n        if (errorData.errorType === 'ACCOUNT_DELETED') {\n          return {\n            success: false,\n            message: t('auth.accountDeleted'),\n            errorType: 'ACCOUNT_DELETED',\n            accountStatus: 'deleted'\n          };\n        }\n\n        // Handle other server errors\n        return {\n          success: false,\n          message: errorData.message || t('auth.loginFailed')\n        };\n      }\n\n      // Handle network or other errors\n      throw error;\n    }\n  };\n\n  const googleRegister = async ({ credential, role, gender }) => {\n    try {\n      const response = await axios.post('/auth/google/register', {\n        credential,\n        role,\n        gender\n      });\n\n      // Check if registration was successful (email verification required)\n      if (response.data && response.data.success) {\n        return {\n          success: true,\n          email: response.data.email,\n          message: response.data.message\n        };\n      }\n\n      return { success: false, error: response.data.message || 'Registration failed' };\n    } catch (error) {\n      console.error('Google registration error:', error);\n      return {\n        success: false,\n        error: error.response?.data?.message || 'Failed to register with Google'\n      };\n    }\n  };\n\n  const value = {\n    currentUser,\n    token,\n    loading,\n    initialized,\n    isAuthenticated,\n    login,\n    register,\n    handleLogout,\n    updateUser,\n    googleLogin,\n    googleRegister\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,QAAQ,CAAEC,UAAU,CAAEC,SAAS,KAAQ,OAAO,CAC7E,MAAO,CAAAC,KAAK,KAAM,gBAAgB,CAClC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE/C,KAAM,CAAAC,WAAW,cAAGT,aAAa,CAAC,IAAI,CAAC,CAEvC,MAAO,MAAM,CAAAU,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACc,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAACgB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC,CACjE,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACoB,WAAW,CAAEC,cAAc,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACsB,eAAe,CAAEC,kBAAkB,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAAwB,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEqB,CAAE,CAAC,CAAGpB,cAAc,CAAC,CAAC,CAE9BH,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAwB,WAAW,CAAGV,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CACjD,GAAIS,WAAW,CAAE,CACf;AACAvB,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUH,WAAW,EAAE,CAExE;AACAI,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC5B,KAAK,CAAC6B,GAAG,CAAC,cAAc,CAAC,CACtBC,IAAI,CAACC,QAAQ,EAAI,CAChBJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEG,QAAQ,CAACC,IAAI,CAAC,CAC1D,GAAID,QAAQ,CAACC,IAAI,EAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAIF,QAAQ,CAACC,IAAI,CAACE,IAAI,CAAE,CAChE,KAAM,CAAAC,QAAQ,CAAG,CACf,GAAGJ,QAAQ,CAACC,IAAI,CAACE,IAAI,CACrBE,SAAS,CAAEL,QAAQ,CAACC,IAAI,CAACE,IAAI,CAACE,SAAS,EAAI,IAAI,CAC/CC,KAAK,CAAEN,QAAQ,CAACC,IAAI,CAACE,IAAI,CAACG,KAAK,EAAI,IAAI,CACvCC,MAAM,CAAEP,QAAQ,CAACC,IAAI,CAACE,IAAI,CAACI,MAAM,EAAI,IAAI,CACzCC,MAAM,CAAER,QAAQ,CAACC,IAAI,CAACE,IAAI,CAACK,MAAM,EAAI,QAAQ,CAC7CC,mBAAmB,CAAET,QAAQ,CAACC,IAAI,CAACE,IAAI,CAACM,mBAAmB,EAAI,IACjE,CAAC,CAED;AACA,GAAIL,QAAQ,CAACI,MAAM,GAAK,SAAS,CAAE,CACjCZ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CACpDa,YAAY,CAAC,CAAC,CACd,OACF,CAEA,GAAIN,QAAQ,CAACI,MAAM,GAAK,kBAAkB,CAAE,CAC1CZ,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC,CACzE;AACF,CAEAlB,cAAc,CAACyB,QAAQ,CAAC,CACxBvB,QAAQ,CAACW,WAAW,CAAC,CACrBH,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,IAAM,CACLO,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CACpDa,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,EAAI,CACdhB,OAAO,CAACgB,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CAEjD;AACA,GAAIA,KAAK,CAACZ,QAAQ,EAAIY,KAAK,CAACZ,QAAQ,CAACQ,MAAM,GAAK,GAAG,CAAE,CACnD,KAAM,CAAAK,SAAS,CAAGD,KAAK,CAACZ,QAAQ,CAACC,IAAI,CAErC;AACA,GAAIY,SAAS,EAAIA,SAAS,CAACC,aAAa,CAAE,CACxC,GAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC1CrB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAEgB,SAAS,CAACC,aAAa,CAAC,CACjF,CAEA;AACA,KAAM,CAAAI,aAAa,CAAG,CACpBC,OAAO,CAAEN,SAAS,CAACM,OAAO,CAAE;AAC5BC,UAAU,CAAEP,SAAS,CAACO,UAAU,CAAE;AAClCN,aAAa,CAAED,SAAS,CAACC,aAAa,CACtCO,iBAAiB,CAAER,SAAS,CAACQ,iBAC/B,CAAC,CAEDvC,YAAY,CAACwC,OAAO,CAAC,sBAAsB,CAAEC,IAAI,CAACC,SAAS,CAACN,aAAa,CAAC,CAAC,CAE3E;AACAR,YAAY,CAAC,CAAC,CAEd;AACAzB,UAAU,CAAC,KAAK,CAAC,CACjBE,cAAc,CAAC,IAAI,CAAC,CACpB,OACF,CACF,CAEAuB,YAAY,CAAC,CAAC,CAChB,CAAC,CAAC,CACDe,OAAO,CAAC,IAAM,CACbxC,UAAU,CAAC,KAAK,CAAC,CACjBE,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAAC,CACN,CAAC,IAAM,CACLS,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAC7CZ,UAAU,CAAC,KAAK,CAAC,CACjBE,cAAc,CAAC,IAAI,CAAC,CACtB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAuB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,GAAI9B,KAAK,CAAE,CACT,KAAM,CAAAX,KAAK,CAACyD,IAAI,CAAC,cAAc,CAAC,CAClC,CACF,CAAE,MAAOd,KAAK,CAAE,CACdhB,OAAO,CAACgB,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CAAC,OAAS,CACR9B,YAAY,CAAC6C,UAAU,CAAC,OAAO,CAAC,CAChChD,cAAc,CAAC,IAAI,CAAC,CACpBE,QAAQ,CAAC,IAAI,CAAC,CACdQ,kBAAkB,CAAC,KAAK,CAAC,CACzB,MAAO,CAAApB,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CACrDL,QAAQ,CAAC,QAAQ,CAAC,CACpB,CACF,CAAC,CAED,KAAM,CAAAsC,mBAAmB,CAAIzB,IAAI,EAAK,CACpC,OAAQA,IAAI,CAAC0B,IAAI,EACf,IAAK,OAAO,CACVvC,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,IAAK,kBAAkB,CACrBA,QAAQ,CAAC,oBAAoB,CAAC,CAC9B,MACF,IAAK,aAAa,CAChBA,QAAQ,CAAC,sBAAsB,CAAC,CAChC,MACF,IAAK,SAAS,CACZA,QAAQ,CAAC,oBAAoB,CAAC,CAC9B,MACF,QACEA,QAAQ,CAAC,GAAG,CAAC,CACjB,CACF,CAAC,CAED,KAAM,CAAAwC,KAAK,CAAG,KAAAA,CAAOC,YAAY,CAAEC,cAAc,GAAK,CACpD,GAAI,CACF;AACA,GAAI,MAAO,CAAAD,YAAY,GAAK,QAAQ,EAAIA,YAAY,CAACE,MAAM,CAAG,EAAE,EAAI,MAAO,CAAAD,cAAc,GAAK,QAAQ,CAAE,CACtG,KAAM,CAAApD,KAAK,CAAGmD,YAAY,CAC1B,KAAM,CAAA5B,IAAI,CAAG6B,cAAc,CAE3B,KAAM,CAAA5B,QAAQ,CAAG,CACf,GAAGD,IAAI,CACPE,SAAS,CAAEF,IAAI,CAACE,SAAS,EAAI,IAAI,CACjCC,KAAK,CAAEH,IAAI,CAACG,KAAK,EAAI,IAAI,CACzBC,MAAM,CAAEJ,IAAI,CAACI,MAAM,EAAI,IACzB,CAAC,CACDzB,YAAY,CAACwC,OAAO,CAAC,OAAO,CAAE1C,KAAK,CAAC,CACpCX,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUf,KAAK,EAAE,CAClED,cAAc,CAACyB,QAAQ,CAAC,CACxBvB,QAAQ,CAACD,KAAK,CAAC,CACfS,kBAAkB,CAAC,IAAI,CAAC,CACxBuC,mBAAmB,CAACxB,QAAQ,CAAC,CAC7B,MAAO,CAAEF,OAAO,CAAE,IAAK,CAAC,CAC1B,CAEA;AACA,KAAM,CAAAF,QAAQ,CAAG,KAAM,CAAA/B,KAAK,CAACyD,IAAI,CAAC,aAAa,CAAE,CAC/CpB,KAAK,CAAEyB,YAAY,EAAI,IAAI,CAC3BG,QAAQ,CAAEF,cAAc,EAAI,IAC9B,CAAC,CAAC,CAEF,GAAIhC,QAAQ,CAACC,IAAI,EAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAIF,QAAQ,CAACC,IAAI,CAACrB,KAAK,EAAIoB,QAAQ,CAACC,IAAI,CAACE,IAAI,CAAE,CACvF,KAAM,CAAEvB,KAAK,CAAEuB,IAAK,CAAC,CAAGH,QAAQ,CAACC,IAAI,CACrC,KAAM,CAAAG,QAAQ,CAAG,CACf,GAAGD,IAAI,CACPE,SAAS,CAAEF,IAAI,CAACE,SAAS,EAAI,IAAI,CACjCC,KAAK,CAAEH,IAAI,CAACG,KAAK,EAAI,IAAI,CACzBC,MAAM,CAAEJ,IAAI,CAACI,MAAM,EAAI,IACzB,CAAC,CACDzB,YAAY,CAACwC,OAAO,CAAC,OAAO,CAAE1C,KAAK,CAAC,CACpCX,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUf,KAAK,EAAE,CAClED,cAAc,CAACyB,QAAQ,CAAC,CACxBvB,QAAQ,CAACD,KAAK,CAAC,CACfS,kBAAkB,CAAC,IAAI,CAAC,CACxBuC,mBAAmB,CAACxB,QAAQ,CAAC,CAC7B,MAAO,CAAEF,OAAO,CAAE,IAAK,CAAC,CAC1B,CACA;AACA,KAAM,CAAAiC,YAAY,CAAGnC,QAAQ,CAACC,IAAI,CAACkB,OAAO,EAAI,cAAc,CAC5D,KAAM,IAAI,CAAAiB,KAAK,CAACD,YAAY,CAAC,CAC/B,CAAE,MAAOvB,KAAK,CAAE,KAAAyB,eAAA,CAAAC,gBAAA,CACd1C,OAAO,CAACgB,KAAK,CAAC,6BAA6B,CAAE,CAC3CA,KAAK,CAAEA,KAAK,CACZZ,QAAQ,CAAEY,KAAK,CAACZ,QAAQ,CACxBC,IAAI,EAAAoC,eAAA,CAAEzB,KAAK,CAACZ,QAAQ,UAAAqC,eAAA,iBAAdA,eAAA,CAAgBpC,IAAI,CAC1BO,MAAM,EAAA8B,gBAAA,CAAE1B,KAAK,CAACZ,QAAQ,UAAAsC,gBAAA,iBAAdA,gBAAA,CAAgB9B,MAAM,CAC9BW,OAAO,CAAEP,KAAK,CAACO,OACjB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAP,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAA2B,QAAQ,CAAG,KAAO,CAAAnC,QAAQ,EAAK,CACnC,GAAI,CACF,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAA/B,KAAK,CAACyD,IAAI,CAAC,gBAAgB,CAAEtB,QAAQ,CAAC,CAE7D;AACA,GAAIJ,QAAQ,CAACC,IAAI,EAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,CAAE,CAC1C,MAAO,CACLA,OAAO,CAAE,IAAI,CACbI,KAAK,CAAEN,QAAQ,CAACC,IAAI,CAACK,KAAK,CAC1Ba,OAAO,CAAEnB,QAAQ,CAACC,IAAI,CAACkB,OACzB,CAAC,CACH,CAEA,MAAO,CACLjB,OAAO,CAAE,KAAK,CACdU,KAAK,CAAEZ,QAAQ,CAACC,IAAI,CAACkB,OAAO,EAAI,qBAClC,CAAC,CACH,CAAE,MAAOP,KAAK,CAAE,KAAA4B,gBAAA,CAAAC,qBAAA,CACd,MAAO,CACLvC,OAAO,CAAE,KAAK,CACdU,KAAK,CAAE,EAAA4B,gBAAA,CAAA5B,KAAK,CAACZ,QAAQ,UAAAwC,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBvC,IAAI,UAAAwC,qBAAA,iBAApBA,qBAAA,CAAsBtB,OAAO,GAAI,oBAC1C,CAAC,CACH,CACF,CAAC,CAED,KAAM,CAAAuB,UAAU,CAAItC,QAAQ,EAAK,CAC/BzB,cAAc,CAACgE,QAAQ,GAAK,CAC1B,GAAGA,QAAQ,CACX,GAAGvC,QACL,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAwC,WAAW,CAAG,KAAO,CAAAC,kBAAkB,EAAK,CAChD,GAAI,CACF,KAAM,CAAA7C,QAAQ,CAAG,KAAM,CAAA/B,KAAK,CAACyD,IAAI,CAAC,cAAc,CAAE,CAChDoB,UAAU,CAAED,kBAAkB,CAACC,UACjC,CAAC,CAAC,CAEF,GAAI9C,QAAQ,CAACC,IAAI,EAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAIF,QAAQ,CAACC,IAAI,CAACrB,KAAK,EAAIoB,QAAQ,CAACC,IAAI,CAACE,IAAI,CAAE,CACvF,KAAM,CAAEvB,KAAK,CAAEuB,IAAK,CAAC,CAAGH,QAAQ,CAACC,IAAI,CACrC,KAAM,CAAAG,QAAQ,CAAG,CACf,GAAGD,IAAI,CACPE,SAAS,CAAEF,IAAI,CAACE,SAAS,EAAI,IAAI,CACjCC,KAAK,CAAEH,IAAI,CAACG,KAAK,EAAI,IAAI,CACzBC,MAAM,CAAEJ,IAAI,CAACI,MAAM,EAAI,IACzB,CAAC,CACDzB,YAAY,CAACwC,OAAO,CAAC,OAAO,CAAE1C,KAAK,CAAC,CACpCX,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUf,KAAK,EAAE,CAClED,cAAc,CAACyB,QAAQ,CAAC,CACxBvB,QAAQ,CAACD,KAAK,CAAC,CACfS,kBAAkB,CAAC,IAAI,CAAC,CACxB,MAAO,CAAEa,OAAO,CAAE,IAAI,CAAEC,IAAI,CAAEC,QAAS,CAAC,CAC1C,CACA,MAAO,CAAEF,OAAO,CAAE,KAAK,CAAEiB,OAAO,CAAEnB,QAAQ,CAACC,IAAI,CAACkB,OAAQ,CAAC,CAC3D,CAAE,MAAOP,KAAK,CAAE,CACdhB,OAAO,CAACgB,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAE3C;AACA,GAAIA,KAAK,CAACZ,QAAQ,EAAIY,KAAK,CAACZ,QAAQ,CAACC,IAAI,CAAE,CACzC,KAAM,CAAAY,SAAS,CAAGD,KAAK,CAACZ,QAAQ,CAACC,IAAI,CAErC;AACA,GAAIY,SAAS,CAACkC,SAAS,GAAK,iBAAiB,CAAE,CAC7C,MAAO,CACL7C,OAAO,CAAE,KAAK,CACdiB,OAAO,CAAE5B,CAAC,CAAC,qBAAqB,CAAC,CACjCwD,SAAS,CAAE,iBAAiB,CAC5BjC,aAAa,CAAE,SACjB,CAAC,CACH,CAEA;AACA,MAAO,CACLZ,OAAO,CAAE,KAAK,CACdiB,OAAO,CAAEN,SAAS,CAACM,OAAO,EAAI5B,CAAC,CAAC,kBAAkB,CACpD,CAAC,CACH,CAEA;AACA,KAAM,CAAAqB,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAAoC,cAAc,CAAG,MAAAC,KAAA,EAAwC,IAAjC,CAAEH,UAAU,CAAEjB,IAAI,CAAEtB,MAAO,CAAC,CAAA0C,KAAA,CACxD,GAAI,CACF,KAAM,CAAAjD,QAAQ,CAAG,KAAM,CAAA/B,KAAK,CAACyD,IAAI,CAAC,uBAAuB,CAAE,CACzDoB,UAAU,CACVjB,IAAI,CACJtB,MACF,CAAC,CAAC,CAEF;AACA,GAAIP,QAAQ,CAACC,IAAI,EAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,CAAE,CAC1C,MAAO,CACLA,OAAO,CAAE,IAAI,CACbI,KAAK,CAAEN,QAAQ,CAACC,IAAI,CAACK,KAAK,CAC1Ba,OAAO,CAAEnB,QAAQ,CAACC,IAAI,CAACkB,OACzB,CAAC,CACH,CAEA,MAAO,CAAEjB,OAAO,CAAE,KAAK,CAAEU,KAAK,CAAEZ,QAAQ,CAACC,IAAI,CAACkB,OAAO,EAAI,qBAAsB,CAAC,CAClF,CAAE,MAAOP,KAAK,CAAE,KAAAsC,gBAAA,CAAAC,qBAAA,CACdvD,OAAO,CAACgB,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,CACLV,OAAO,CAAE,KAAK,CACdU,KAAK,CAAE,EAAAsC,gBAAA,CAAAtC,KAAK,CAACZ,QAAQ,UAAAkD,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBjD,IAAI,UAAAkD,qBAAA,iBAApBA,qBAAA,CAAsBhC,OAAO,GAAI,gCAC1C,CAAC,CACH,CACF,CAAC,CAED,KAAM,CAAAiC,KAAK,CAAG,CACZ1E,WAAW,CACXE,KAAK,CACLI,OAAO,CACPE,WAAW,CACXE,eAAe,CACf0C,KAAK,CACLS,QAAQ,CACR7B,YAAY,CACZgC,UAAU,CACVE,WAAW,CACXI,cACF,CAAC,CAED,mBACE3E,IAAA,CAACC,WAAW,CAAC+E,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAA3E,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC,CAED,MAAO,MAAM,CAAA6E,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGxF,UAAU,CAACO,WAAW,CAAC,CACvC,GAAI,CAACiF,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAnB,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAmB,OAAO,CAChB,CAAC,CAED,cAAe,CAAAjF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}