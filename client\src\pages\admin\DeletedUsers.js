import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Button,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Tooltip,
  Card,
  CardContent,
  Grid
} from '@mui/material';
import {
  Restore as RestoreIcon,
  DeleteForever as DeleteForeverIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

const DeletedUsers = () => {
  const { t, i18n } = useTranslation();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [viewDialog, setViewDialog] = useState(false);
  const [restoreDialog, setRestoreDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [stats, setStats] = useState({});
  const [actionLoading, setActionLoading] = useState(false);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        search,
        role: roleFilter
      };

      const response = await axios.get('/admin/deleted-users', { params });
      setUsers(response.data.users);
      setTotal(response.data.total);
    } catch (error) {
      console.error('Error fetching deleted users:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/admin/deleted-users/stats/overview');
      setStats(response.data.stats);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [page, rowsPerPage, search, roleFilter]);

  useEffect(() => {
    fetchStats();
  }, []);

  const handleRestore = async (userId) => {
    try {
      setActionLoading(true);
      await axios.post(`/admin/deleted-users/${userId}/restore`);
      setRestoreDialog(false);
      setSelectedUser(null);
      fetchUsers();
      fetchStats();
    } catch (error) {
      console.error('Error restoring user:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handlePermanentDelete = async (userId) => {
    try {
      setActionLoading(true);
      await axios.delete(`/admin/deleted-users/${userId}/permanent`);
      setDeleteDialog(false);
      setSelectedUser(null);
      fetchUsers();
      fetchStats();
    } catch (error) {
      console.error('Error permanently deleting user:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const getRoleLabel = (role) => {
    switch (role) {
      case 'student': return 'طالب';
      case 'platform_teacher': return 'معلم';
      case 'new_teacher': return 'معلم جديد';
      default: return role;
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'student': return 'primary';
      case 'platform_teacher': return 'success';
      case 'new_teacher': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        المستخدمون المحذوفون
      </Typography>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                إجمالي المحذوفين
              </Typography>
              <Typography variant="h4">
                {stats.total_deleted || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                الطلاب المحذوفين
              </Typography>
              <Typography variant="h4" color="primary">
                {stats.deleted_students || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                المعلمين المحذوفين
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.deleted_teachers || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                المستخدمين النشطين
              </Typography>
              <Typography variant="h4" color="info.main">
                {stats.total_active || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
          <TextField
            label="البحث"
            variant="outlined"
            size="small"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
            sx={{ minWidth: 200 }}
          />
          
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>النوع</InputLabel>
            <Select
              value={roleFilter}
              label="النوع"
              onChange={(e) => setRoleFilter(e.target.value)}
            >
              <MenuItem value="">الكل</MenuItem>
              <MenuItem value="student">طلاب</MenuItem>
              <MenuItem value="platform_teacher">معلمين</MenuItem>
              <MenuItem value="new_teacher">معلمين جدد</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => {
              fetchUsers();
              fetchStats();
            }}
          >
            تحديث
          </Button>
        </Box>
      </Paper>

      {/* Users Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>المستخدم</TableCell>
              <TableCell>النوع</TableCell>
              <TableCell>تاريخ الحذف</TableCell>
              <TableCell>سبب الحذف</TableCell>
              <TableCell>حُذف بواسطة</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar src={user.profile_picture_url}>
                      {user.full_name?.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle2">
                        {user.full_name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {user.email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getRoleLabel(user.role)}
                    color={getRoleColor(user.role)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {format(new Date(user.deleted_at), 'dd/MM/yyyy HH:mm', {
                    locale: i18n.language === 'ar' ? ar : undefined
                  })}
                </TableCell>
                <TableCell>
                  <Typography variant="caption">
                    {user.deletion_reason || 'غير محدد'}
                  </Typography>
                </TableCell>
                <TableCell>
                  {user.deleted_by_name || 'حذف ذاتي'}
                </TableCell>
                <TableCell>
                  <Tooltip title="عرض التفاصيل">
                    <IconButton
                      size="small"
                      onClick={() => {
                        setSelectedUser(user);
                        setViewDialog(true);
                      }}
                    >
                      <ViewIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="استرداد المستخدم">
                    <IconButton
                      size="small"
                      color="success"
                      onClick={() => {
                        setSelectedUser(user);
                        setRestoreDialog(true);
                      }}
                    >
                      <RestoreIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="حذف نهائي">
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => {
                        setSelectedUser(user);
                        setDeleteDialog(true);
                      }}
                    >
                      <DeleteForeverIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          component="div"
          count={total}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
          labelRowsPerPage="عدد الصفوف:"
          labelDisplayedRows={({ from, to, count }) => 
            `${from}-${to} من ${count !== -1 ? count : `أكثر من ${to}`}`
          }
        />
      </TableContainer>

      {/* Restore Dialog */}
      <Dialog open={restoreDialog} onClose={() => setRestoreDialog(false)}>
        <DialogTitle>استرداد المستخدم</DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من استرداد المستخدم "{selectedUser?.full_name}"؟
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            سيتمكن المستخدم من تسجيل الدخول والوصول للنظام مرة أخرى.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRestoreDialog(false)}>إلغاء</Button>
          <Button
            onClick={() => handleRestore(selectedUser?.id)}
            color="success"
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? 'جاري الاسترداد...' : 'استرداد'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Permanent Delete Dialog */}
      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
        <DialogTitle>حذف نهائي</DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 2 }}>
            تحذير: هذا الإجراء لا يمكن التراجع عنه!
          </Alert>
          <Typography>
            هل أنت متأكد من الحذف النهائي للمستخدم "{selectedUser?.full_name}"؟
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            سيتم حذف جميع البيانات المرتبطة بهذا المستخدم نهائياً.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>إلغاء</Button>
          <Button
            onClick={() => handlePermanentDelete(selectedUser?.id)}
            color="error"
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? 'جاري الحذف...' : 'حذف نهائي'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DeletedUsers;
