import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Button,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Tooltip,
  Card,
  CardContent,
  Grid,
  Container
} from '@mui/material';
import {
  Restore as RestoreIcon,
  DeleteForever as DeleteForeverIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import Layout from '../../components/Layout';

const DeletedUsers = () => {
  const { t, i18n } = useTranslation();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [viewDialog, setViewDialog] = useState(false);
  const [restoreDialog, setRestoreDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [stats, setStats] = useState({});
  const [actionLoading, setActionLoading] = useState(false);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        search,
        role: roleFilter
      };

      const response = await axios.get('/admin/deleted-users', { params });
      setUsers(response.data.users);
      setTotal(response.data.total);
    } catch (error) {
      console.error('Error fetching deleted users:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/admin/deleted-users/stats/overview');
      setStats(response.data.stats);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [page, rowsPerPage, search, roleFilter]);

  useEffect(() => {
    fetchStats();
  }, []);

  const handleRestore = async (userId) => {
    try {
      setActionLoading(true);
      await axios.post(`/admin/deleted-users/${userId}/restore`);
      setRestoreDialog(false);
      setSelectedUser(null);
      fetchUsers();
      fetchStats();
    } catch (error) {
      console.error('Error restoring user:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handlePermanentDelete = async (userId) => {
    try {
      setActionLoading(true);
      await axios.delete(`/admin/deleted-users/${userId}/permanent`);
      setDeleteDialog(false);
      setSelectedUser(null);
      fetchUsers();
      fetchStats();
    } catch (error) {
      console.error('Error permanently deleting user:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const getRoleLabel = (role) => {
    switch (role) {
      case 'student': return t('deletedUsers.student');
      case 'platform_teacher': return t('deletedUsers.teacher');
      case 'new_teacher': return t('deletedUsers.newTeacher');
      default: return role;
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'student': return 'primary';
      case 'platform_teacher': return 'success';
      case 'new_teacher': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" gutterBottom>
          {t('deletedUsers.title')}
        </Typography>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                {t('deletedUsers.totalDeleted')}
              </Typography>
              <Typography variant="h4">
                {stats.total_deleted || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                {t('deletedUsers.deletedStudents')}
              </Typography>
              <Typography variant="h4" color="primary">
                {stats.deleted_students || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                {t('deletedUsers.deletedTeachers')}
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.deleted_teachers || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                {t('deletedUsers.activeUsers')}
              </Typography>
              <Typography variant="h4" color="info.main">
                {stats.total_active || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
          <TextField
            label={t('deletedUsers.search')}
            variant="outlined"
            size="small"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
            sx={{ minWidth: 200 }}
          />
          
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>{t('deletedUsers.type')}</InputLabel>
            <Select
              value={roleFilter}
              label={t('deletedUsers.type')}
              onChange={(e) => setRoleFilter(e.target.value)}
            >
              <MenuItem value="">{t('deletedUsers.all')}</MenuItem>
              <MenuItem value="student">{t('deletedUsers.students')}</MenuItem>
              <MenuItem value="platform_teacher">{t('deletedUsers.teachers')}</MenuItem>
              <MenuItem value="new_teacher">{t('deletedUsers.newTeachers')}</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => {
              fetchUsers();
              fetchStats();
            }}
          >
            {t('deletedUsers.refresh')}
          </Button>
        </Box>
      </Paper>

      {/* Users Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('deletedUsers.user')}</TableCell>
              <TableCell>{t('deletedUsers.role')}</TableCell>
              <TableCell>{t('deletedUsers.deletionDate')}</TableCell>
              <TableCell>{t('deletedUsers.deletionReason')}</TableCell>
              <TableCell>{t('deletedUsers.deletedBy')}</TableCell>
              <TableCell>{t('deletedUsers.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar src={user.profile_picture_url}>
                      {user.full_name?.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle2">
                        {user.full_name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {user.email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getRoleLabel(user.role)}
                    color={getRoleColor(user.role)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {format(new Date(user.deleted_at), 'dd/MM/yyyy HH:mm', {
                    locale: i18n.language === 'ar' ? ar : undefined
                  })}
                </TableCell>
                <TableCell>
                  <Typography variant="caption">
                    {user.deletion_reason || t('deletedUsers.notSpecified')}
                  </Typography>
                </TableCell>
                <TableCell>
                  {user.deleted_by_name || t('deletedUsers.selfDeleted')}
                </TableCell>
                <TableCell>
                  <Tooltip title={t('deletedUsers.viewDetails')}>
                    <IconButton
                      size="small"
                      onClick={() => {
                        setSelectedUser(user);
                        setViewDialog(true);
                      }}
                    >
                      <ViewIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title={t('deletedUsers.restoreUser')}>
                    <IconButton
                      size="small"
                      color="success"
                      onClick={() => {
                        setSelectedUser(user);
                        setRestoreDialog(true);
                      }}
                    >
                      <RestoreIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title={t('deletedUsers.permanentDelete')}>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => {
                        setSelectedUser(user);
                        setDeleteDialog(true);
                      }}
                    >
                      <DeleteForeverIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          component="div"
          count={total}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
          labelRowsPerPage={t('deletedUsers.rowsPerPage')}
          labelDisplayedRows={({ from, to, count }) =>
            t('deletedUsers.displayedRows', { from, to, count: count !== -1 ? count : `${t('common.moreThan')} ${to}` })
          }
        />
      </TableContainer>

      {/* View Details Dialog */}
      <Dialog open={viewDialog} onClose={() => setViewDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{t('deletedUsers.viewDetails')}</DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Grid container spacing={3}>
              {/* User Info */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom color="primary">
                      {t('common.userInfo')}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        src={selectedUser.profile_picture_url}
                        sx={{ width: 60, height: 60, mr: 2 }}
                      >
                        {selectedUser.full_name?.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="h6">{selectedUser.full_name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {selectedUser.email}
                        </Typography>
                        <Chip
                          label={getRoleLabel(selectedUser.role)}
                          color={getRoleColor(selectedUser.role)}
                          size="small"
                          sx={{ mt: 1 }}
                        />
                      </Box>
                    </Box>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>{t('common.gender')}:</strong> {selectedUser.gender === 'male' ? t('common.male') : t('common.female')}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>{t('common.joinDate')}:</strong> {format(new Date(selectedUser.created_at), 'dd/MM/yyyy', {
                        locale: i18n.language === 'ar' ? ar : undefined
                      })}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* Deletion Info */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom color="error">
                      {t('deletedUsers.deletionInfo')}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>{t('deletedUsers.deletionDate')}:</strong> {format(new Date(selectedUser.deleted_at), 'dd/MM/yyyy HH:mm', {
                        locale: i18n.language === 'ar' ? ar : undefined
                      })}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>{t('deletedUsers.deletionReason')}:</strong> {selectedUser.deletion_reason || t('deletedUsers.notSpecified')}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>{t('deletedUsers.deletedBy')}:</strong> {selectedUser.deleted_by_name || t('deletedUsers.selfDeleted')}
                    </Typography>
                    {selectedUser.delete_scheduled_at && (
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        <strong>{t('deletedUsers.scheduledDeletion')}:</strong> {format(new Date(selectedUser.delete_scheduled_at), 'dd/MM/yyyy HH:mm', {
                          locale: i18n.language === 'ar' ? ar : undefined
                        })}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Additional Info for Teachers */}
              {(selectedUser.role === 'platform_teacher' || selectedUser.role === 'new_teacher') && (
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="success.main">
                        {t('common.teacherInfo')}
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>{t('common.totalLessons')}:</strong> {selectedUser.total_lessons || 0}
                          </Typography>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>{t('common.totalStudents')}:</strong> {selectedUser.total_students || 0}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>{t('common.rating')}:</strong> {selectedUser.average_rating ? `${selectedUser.average_rating}/5` : t('common.noRating')}
                          </Typography>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>{t('common.totalEarnings')}:</strong> {selectedUser.total_earnings || 0} {t('common.currency')}
                          </Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              )}

              {/* Additional Info for Students */}
              {selectedUser.role === 'student' && (
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        {t('common.studentInfo')}
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>{t('common.totalBookings')}:</strong> {selectedUser.total_bookings || 0}
                          </Typography>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>{t('common.completedLessons')}:</strong> {selectedUser.completed_lessons || 0}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>{t('common.totalSpent')}:</strong> {selectedUser.total_spent || 0} {t('common.currency')}
                          </Typography>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>{t('common.timezone')}:</strong> {selectedUser.timezone || t('common.notSet')}
                          </Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialog(false)}>{t('common.close')}</Button>
          <Button
            onClick={() => {
              setViewDialog(false);
              setRestoreDialog(true);
            }}
            color="success"
            variant="contained"
          >
            {t('deletedUsers.restoreUser')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Restore Dialog */}
      <Dialog open={restoreDialog} onClose={() => setRestoreDialog(false)}>
        <DialogTitle>{t('deletedUsers.restoreConfirmTitle')}</DialogTitle>
        <DialogContent>
          <Typography>
            {t('deletedUsers.restoreConfirmMessage', { name: selectedUser?.full_name })}
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            {t('deletedUsers.restoreConfirmNote')}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRestoreDialog(false)}>{t('deletedUsers.cancel')}</Button>
          <Button
            onClick={() => handleRestore(selectedUser?.id)}
            color="success"
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? t('deletedUsers.restoring') : t('deletedUsers.restore')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Permanent Delete Dialog */}
      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
        <DialogTitle>{t('deletedUsers.permanentDeleteTitle')}</DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 2 }}>
            {t('deletedUsers.permanentDeleteWarning')}
          </Alert>
          <Typography>
            {t('deletedUsers.permanentDeleteMessage', { name: selectedUser?.full_name })}
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            {t('deletedUsers.permanentDeleteNote')}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>{t('deletedUsers.cancel')}</Button>
          <Button
            onClick={() => handlePermanentDelete(selectedUser?.id)}
            color="error"
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? t('deletedUsers.deleting') : t('deletedUsers.permanentDeleteButton')}
          </Button>
        </DialogActions>
      </Dialog>
      </Container>
    </Layout>
  );
};

export default DeletedUsers;
