{"ast": null, "code": "import axios from'axios';/**\n * معالج حالة المستخدم - للتعامل مع الحسابات المحذوفة أو المجدولة للحذف\n */class UserStatusHandler{static handleStatusError(error,navigate,logout){if(error.response&&error.response.data){const{accountStatus,requiresLogout,message,deleteScheduledAt}=error.response.data;// إذا كان الحساب محذوف أو مجدول للحذف\nif(accountStatus==='deleted'||accountStatus==='pending_deletion'){if(requiresLogout){// حفظ رسالة الحالة في localStorage\nlocalStorage.setItem('accountStatusMessage',JSON.stringify({message:message,message_en:error.response.data.message_en||message,accountStatus:accountStatus,deleteScheduledAt:deleteScheduledAt}));// تسجيل الخروج\nlogout();// إعادة التوجيه حسب حالة الحساب\nif(accountStatus==='pending_deletion'){navigate('/pending-deletion',{state:{message:message,accountStatus:accountStatus,deleteScheduledAt:deleteScheduledAt}});}else{navigate('/login');}return true;// تم التعامل مع الخطأ\n}}}return false;// لم يتم التعامل مع الخطأ\n}/**\n   * إضافة interceptor لـ axios للتعامل مع أخطاء حالة المستخدم تلقائياً\n   */static setupAxiosInterceptor(navigate,logout){axios.interceptors.response.use(response=>response,error=>{// التحقق من أخطاء حالة المستخدم\nif(this.handleStatusError(error,navigate,logout)){return Promise.reject(error);}// إذا لم يكن خطأ حالة مستخدم، تمرير الخطأ كما هو\nreturn Promise.reject(error);});}/**\n   * التحقق من حالة المستخدم قبل تحميل الصفحة\n   */static async checkUserStatus(){try{const token=localStorage.getItem('token');if(!token)return{valid:false,reason:'no_token'};// استخدام /auth/verify بدلاً من /users/profile للحصول على معلومات المستخدم مع الحالة\nconst response=await axios.get('/api/auth/verify',{headers:{Authorization:`Bearer ${token}`}});const user=response.data.user;if(!user)return{valid:false,reason:'no_user'};// التحقق من حالة الحساب\nif(user.status==='deleted'){// حذف التوكن من localStorage\nlocalStorage.removeItem('token');// حفظ رسالة الحالة لعرضها في صفحة تسجيل الدخول\nlocalStorage.setItem('accountStatusMessage',JSON.stringify({message:'تم حذف هذا الحساب',message_en:'Account has been deleted',accountStatus:'deleted'}));return{valid:false,reason:'deleted',message:'تم حذف هذا الحساب'};}if(user.status==='pending_deletion'){return{valid:true,// السماح بالوصول مع تحذير\nreason:'pending_deletion',message:'هذا الحساب مجدول للحذف',deleteScheduledAt:user.delete_scheduled_at,user};}return{valid:true,user};}catch(error){console.error('Error checking user status:',error);// إذا كان الخطأ 401، قد يكون التوكن منتهي الصلاحية أو المستخدم محذوف\nif(error.response&&error.response.status===401){localStorage.removeItem('token');return{valid:false,reason:'unauthorized',message:'انتهت صلاحية جلسة تسجيل الدخول'};}return{valid:false,reason:'error',message:'خطأ في التحقق من حالة الحساب'};}}/**\n   * عرض رسالة تحذيرية للمستخدمين المجدولين للحذف\n   */static showPendingDeletionWarning(deleteScheduledAt){const deleteDate=new Date(deleteScheduledAt);const now=new Date();const daysLeft=Math.ceil((deleteDate-now)/(1000*60*60*24));return{show:true,message:`تحذير: حسابك مجدول للحذف خلال ${daysLeft} أيام. يمكنك إلغاء عملية الحذف من صفحة الملف الشخصي.`,daysLeft,deleteDate:deleteDate.toLocaleDateString('ar-EG')};}}export default UserStatusHandler;", "map": {"version": 3, "names": ["axios", "UserStatusHandler", "handleStatusError", "error", "navigate", "logout", "response", "data", "accountStatus", "requiresLogout", "message", "deleteScheduledAt", "localStorage", "setItem", "JSON", "stringify", "message_en", "state", "setupAxiosInterceptor", "interceptors", "use", "Promise", "reject", "checkUserStatus", "token", "getItem", "valid", "reason", "get", "headers", "Authorization", "user", "status", "removeItem", "delete_scheduled_at", "console", "showPendingDeletionWarning", "deleteDate", "Date", "now", "daysLeft", "Math", "ceil", "show", "toLocaleDateString"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/utils/userStatusHandler.js"], "sourcesContent": ["import axios from 'axios';\n\n/**\n * معالج حالة المستخدم - للتعامل مع الحسابات المحذوفة أو المجدولة للحذف\n */\nclass UserStatusHandler {\n  static handleStatusError(error, navigate, logout) {\n    if (error.response && error.response.data) {\n      const { accountStatus, requiresLogout, message, deleteScheduledAt } = error.response.data;\n\n      // إذا كان الحساب محذوف أو مجدول للحذف\n      if (accountStatus === 'deleted' || accountStatus === 'pending_deletion') {\n        if (requiresLogout) {\n          // حفظ رسالة الحالة في localStorage\n          localStorage.setItem('accountStatusMessage', JSON.stringify({\n            message: message,\n            message_en: error.response.data.message_en || message,\n            accountStatus: accountStatus,\n            deleteScheduledAt: deleteScheduledAt\n          }));\n\n          // تسجيل الخروج\n          logout();\n\n          // إعادة التوجيه حسب حالة الحساب\n          if (accountStatus === 'pending_deletion') {\n            navigate('/pending-deletion', {\n              state: {\n                message: message,\n                accountStatus: accountStatus,\n                deleteScheduledAt: deleteScheduledAt\n              }\n            });\n          } else {\n            navigate('/login');\n          }\n\n          return true; // تم التعامل مع الخطأ\n        }\n      }\n    }\n    return false; // لم يتم التعامل مع الخطأ\n  }\n\n  /**\n   * إضافة interceptor لـ axios للتعامل مع أخطاء حالة المستخدم تلقائياً\n   */\n  static setupAxiosInterceptor(navigate, logout) {\n    axios.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        // التحقق من أخطاء حالة المستخدم\n        if (this.handleStatusError(error, navigate, logout)) {\n          return Promise.reject(error);\n        }\n        \n        // إذا لم يكن خطأ حالة مستخدم، تمرير الخطأ كما هو\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  /**\n   * التحقق من حالة المستخدم قبل تحميل الصفحة\n   */\n  static async checkUserStatus() {\n    try {\n      const token = localStorage.getItem('token');\n      if (!token) return { valid: false, reason: 'no_token' };\n\n      // استخدام /auth/verify بدلاً من /users/profile للحصول على معلومات المستخدم مع الحالة\n      const response = await axios.get('/api/auth/verify', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      const user = response.data.user;\n      if (!user) return { valid: false, reason: 'no_user' };\n\n      // التحقق من حالة الحساب\n      if (user.status === 'deleted') {\n        // حذف التوكن من localStorage\n        localStorage.removeItem('token');\n\n        // حفظ رسالة الحالة لعرضها في صفحة تسجيل الدخول\n        localStorage.setItem('accountStatusMessage', JSON.stringify({\n          message: 'تم حذف هذا الحساب',\n          message_en: 'Account has been deleted',\n          accountStatus: 'deleted'\n        }));\n\n        return {\n          valid: false,\n          reason: 'deleted',\n          message: 'تم حذف هذا الحساب'\n        };\n      }\n\n      if (user.status === 'pending_deletion') {\n        return {\n          valid: true, // السماح بالوصول مع تحذير\n          reason: 'pending_deletion',\n          message: 'هذا الحساب مجدول للحذف',\n          deleteScheduledAt: user.delete_scheduled_at,\n          user\n        };\n      }\n\n      return { valid: true, user };\n    } catch (error) {\n      console.error('Error checking user status:', error);\n\n      // إذا كان الخطأ 401، قد يكون التوكن منتهي الصلاحية أو المستخدم محذوف\n      if (error.response && error.response.status === 401) {\n        localStorage.removeItem('token');\n        return {\n          valid: false,\n          reason: 'unauthorized',\n          message: 'انتهت صلاحية جلسة تسجيل الدخول'\n        };\n      }\n\n      return {\n        valid: false,\n        reason: 'error',\n        message: 'خطأ في التحقق من حالة الحساب'\n      };\n    }\n  }\n\n  /**\n   * عرض رسالة تحذيرية للمستخدمين المجدولين للحذف\n   */\n  static showPendingDeletionWarning(deleteScheduledAt) {\n    const deleteDate = new Date(deleteScheduledAt);\n    const now = new Date();\n    const daysLeft = Math.ceil((deleteDate - now) / (1000 * 60 * 60 * 24));\n    \n    return {\n      show: true,\n      message: `تحذير: حسابك مجدول للحذف خلال ${daysLeft} أيام. يمكنك إلغاء عملية الحذف من صفحة الملف الشخصي.`,\n      daysLeft,\n      deleteDate: deleteDate.toLocaleDateString('ar-EG')\n    };\n  }\n}\n\nexport default UserStatusHandler;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB;AACA;AACA,GACA,KAAM,CAAAC,iBAAkB,CACtB,MAAO,CAAAC,iBAAiBA,CAACC,KAAK,CAAEC,QAAQ,CAAEC,MAAM,CAAE,CAChD,GAAIF,KAAK,CAACG,QAAQ,EAAIH,KAAK,CAACG,QAAQ,CAACC,IAAI,CAAE,CACzC,KAAM,CAAEC,aAAa,CAAEC,cAAc,CAAEC,OAAO,CAAEC,iBAAkB,CAAC,CAAGR,KAAK,CAACG,QAAQ,CAACC,IAAI,CAEzF;AACA,GAAIC,aAAa,GAAK,SAAS,EAAIA,aAAa,GAAK,kBAAkB,CAAE,CACvE,GAAIC,cAAc,CAAE,CAClB;AACAG,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAEC,IAAI,CAACC,SAAS,CAAC,CAC1DL,OAAO,CAAEA,OAAO,CAChBM,UAAU,CAAEb,KAAK,CAACG,QAAQ,CAACC,IAAI,CAACS,UAAU,EAAIN,OAAO,CACrDF,aAAa,CAAEA,aAAa,CAC5BG,iBAAiB,CAAEA,iBACrB,CAAC,CAAC,CAAC,CAEH;AACAN,MAAM,CAAC,CAAC,CAER;AACA,GAAIG,aAAa,GAAK,kBAAkB,CAAE,CACxCJ,QAAQ,CAAC,mBAAmB,CAAE,CAC5Ba,KAAK,CAAE,CACLP,OAAO,CAAEA,OAAO,CAChBF,aAAa,CAAEA,aAAa,CAC5BG,iBAAiB,CAAEA,iBACrB,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLP,QAAQ,CAAC,QAAQ,CAAC,CACpB,CAEA,MAAO,KAAI,CAAE;AACf,CACF,CACF,CACA,MAAO,MAAK,CAAE;AAChB,CAEA;AACF;AACA,KACE,MAAO,CAAAc,qBAAqBA,CAACd,QAAQ,CAAEC,MAAM,CAAE,CAC7CL,KAAK,CAACmB,YAAY,CAACb,QAAQ,CAACc,GAAG,CAC5Bd,QAAQ,EAAKA,QAAQ,CACrBH,KAAK,EAAK,CACT;AACA,GAAI,IAAI,CAACD,iBAAiB,CAACC,KAAK,CAAEC,QAAQ,CAAEC,MAAM,CAAC,CAAE,CACnD,MAAO,CAAAgB,OAAO,CAACC,MAAM,CAACnB,KAAK,CAAC,CAC9B,CAEA;AACA,MAAO,CAAAkB,OAAO,CAACC,MAAM,CAACnB,KAAK,CAAC,CAC9B,CACF,CAAC,CACH,CAEA;AACF;AACA,KACE,YAAa,CAAAoB,eAAeA,CAAA,CAAG,CAC7B,GAAI,CACF,KAAM,CAAAC,KAAK,CAAGZ,YAAY,CAACa,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAI,CAACD,KAAK,CAAE,MAAO,CAAEE,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,UAAW,CAAC,CAEvD;AACA,KAAM,CAAArB,QAAQ,CAAG,KAAM,CAAAN,KAAK,CAAC4B,GAAG,CAAC,kBAAkB,CAAE,CACnDC,OAAO,CAAE,CAAEC,aAAa,CAAE,UAAUN,KAAK,EAAG,CAC9C,CAAC,CAAC,CAEF,KAAM,CAAAO,IAAI,CAAGzB,QAAQ,CAACC,IAAI,CAACwB,IAAI,CAC/B,GAAI,CAACA,IAAI,CAAE,MAAO,CAAEL,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,SAAU,CAAC,CAErD;AACA,GAAII,IAAI,CAACC,MAAM,GAAK,SAAS,CAAE,CAC7B;AACApB,YAAY,CAACqB,UAAU,CAAC,OAAO,CAAC,CAEhC;AACArB,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAEC,IAAI,CAACC,SAAS,CAAC,CAC1DL,OAAO,CAAE,mBAAmB,CAC5BM,UAAU,CAAE,0BAA0B,CACtCR,aAAa,CAAE,SACjB,CAAC,CAAC,CAAC,CAEH,MAAO,CACLkB,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,SAAS,CACjBjB,OAAO,CAAE,mBACX,CAAC,CACH,CAEA,GAAIqB,IAAI,CAACC,MAAM,GAAK,kBAAkB,CAAE,CACtC,MAAO,CACLN,KAAK,CAAE,IAAI,CAAE;AACbC,MAAM,CAAE,kBAAkB,CAC1BjB,OAAO,CAAE,wBAAwB,CACjCC,iBAAiB,CAAEoB,IAAI,CAACG,mBAAmB,CAC3CH,IACF,CAAC,CACH,CAEA,MAAO,CAAEL,KAAK,CAAE,IAAI,CAAEK,IAAK,CAAC,CAC9B,CAAE,MAAO5B,KAAK,CAAE,CACdgC,OAAO,CAAChC,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CAEnD;AACA,GAAIA,KAAK,CAACG,QAAQ,EAAIH,KAAK,CAACG,QAAQ,CAAC0B,MAAM,GAAK,GAAG,CAAE,CACnDpB,YAAY,CAACqB,UAAU,CAAC,OAAO,CAAC,CAChC,MAAO,CACLP,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,cAAc,CACtBjB,OAAO,CAAE,gCACX,CAAC,CACH,CAEA,MAAO,CACLgB,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,OAAO,CACfjB,OAAO,CAAE,8BACX,CAAC,CACH,CACF,CAEA;AACF;AACA,KACE,MAAO,CAAA0B,0BAA0BA,CAACzB,iBAAiB,CAAE,CACnD,KAAM,CAAA0B,UAAU,CAAG,GAAI,CAAAC,IAAI,CAAC3B,iBAAiB,CAAC,CAC9C,KAAM,CAAA4B,GAAG,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAE,QAAQ,CAAGC,IAAI,CAACC,IAAI,CAAC,CAACL,UAAU,CAAGE,GAAG,GAAK,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CAEtE,MAAO,CACLI,IAAI,CAAE,IAAI,CACVjC,OAAO,CAAE,iCAAiC8B,QAAQ,sDAAsD,CACxGA,QAAQ,CACRH,UAAU,CAAEA,UAAU,CAACO,kBAAkB,CAAC,OAAO,CACnD,CAAC,CACH,CACF,CAEA,cAAe,CAAA3C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}