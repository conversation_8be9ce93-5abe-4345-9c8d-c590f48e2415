const express = require('express');
const router = express.Router();
const { authenticateToken, isAdmin } = require('../../middleware/auth');

// Import admin route handlers
const teacherApplicationsRouter = require('./applications');
const teachersRouter = require('./teachers');
const studentsRouter = require('./students');
const categoriesRouter = require('./categories');
const languagesRouter = require('./languages');
const profileUpdatesRouter = require('./profile-updates');
const deletedUsersRouter = require('./deleted-users');

// Apply authentication and admin middleware to all routes
router.use(authenticateToken);
router.use(isAdmin);

// Register admin routes
router.use('/applications', teacherApplicationsRouter);
router.use('/teachers', teachersRouter);
router.use('/students', studentsRouter);
router.use('/categories', categoriesRouter);
router.use('/languages', languagesRouter);
router.use('/profile-updates', profileUpdatesRouter);
router.use('/deleted-users', deletedUsersRouter);

module.exports = router;
