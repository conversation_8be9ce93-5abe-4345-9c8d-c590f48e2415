{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate,useLocation}from'react-router-dom';import{useTranslation}from'react-i18next';import{Box,Container,Paper,Typography,Button,Alert,AlertTitle,CircularProgress,Divider,useTheme,alpha}from'@mui/material';import{Warning as WarningIcon,Schedule as ScheduleIcon,Cancel as CancelIcon,CheckCircle as CheckCircleIcon}from'@mui/icons-material';import{useAuth}from'../contexts/AuthContext';import axios from'axios';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PendingDeletion=()=>{const{t,i18n}=useTranslation();const navigate=useNavigate();const location=useLocation();const theme=useTheme();const{currentUser,handleLogout}=useAuth();const[loading,setLoading]=useState(false);const[timeLeft,setTimeLeft]=useState('');const[deleteScheduledAt,setDeleteScheduledAt]=useState(null);const[success,setSuccess]=useState(false);const[error,setError]=useState('');// حساب الوقت المتبقي\nconst calculateTimeLeft=scheduledDate=>{if(!scheduledDate)return'';const now=new Date();const deleteDate=new Date(scheduledDate);const timeDiff=deleteDate-now;if(timeDiff<=0){return i18n.language==='ar'?'انتهت المهلة':'Time expired';}const days=Math.floor(timeDiff/(1000*60*60*24));const hours=Math.floor(timeDiff%(1000*60*60*24)/(1000*60*60));const minutes=Math.floor(timeDiff%(1000*60*60)/(1000*60));if(i18n.language==='ar'){return`${days} أيام، ${hours} ساعات، ${minutes} دقائق`;}else{return`${days} days, ${hours} hours, ${minutes} minutes`;}};// تحديث الوقت كل دقيقة\nuseEffect(()=>{const interval=setInterval(()=>{if(deleteScheduledAt){setTimeLeft(calculateTimeLeft(deleteScheduledAt));}},60000);// كل دقيقة\nreturn()=>clearInterval(interval);},[deleteScheduledAt,i18n.language]);// جلب معلومات الحذف المجدول\nuseEffect(()=>{const fetchDeletionInfo=()=>{// من location state\nif(location.state&&location.state.deleteScheduledAt){const scheduledAt=location.state.deleteScheduledAt;setDeleteScheduledAt(scheduledAt);setTimeLeft(calculateTimeLeft(scheduledAt));return;}// من currentUser\nif(currentUser&&currentUser.delete_scheduled_at){const scheduledAt=currentUser.delete_scheduled_at;setDeleteScheduledAt(scheduledAt);setTimeLeft(calculateTimeLeft(scheduledAt));return;}// من localStorage\nconst savedMessage=localStorage.getItem('accountStatusMessage');if(savedMessage){try{const messageData=JSON.parse(savedMessage);if(messageData.deleteScheduledAt){const scheduledAt=messageData.deleteScheduledAt;setDeleteScheduledAt(scheduledAt);setTimeLeft(calculateTimeLeft(scheduledAt));return;}}catch(e){console.error('Error parsing saved message:',e);}}// إذا لم نجد معلومات، إعادة توجيه للملف الشخصي\nnavigate('/student/profile');};fetchDeletionInfo();},[location.state,currentUser,navigate,i18n.language]);// إلغاء الحذف\nconst handleCancelDeletion=async()=>{setLoading(true);setError('');try{const response=await axios.post('/api/users/cancel-delete');if(response.data.success){setSuccess(true);// حذف رسالة الحالة من localStorage\nlocalStorage.removeItem('accountStatusMessage');// إعادة التوجيه بعد 3 ثوان\nsetTimeout(()=>{navigate('/student/dashboard');},3000);}}catch(error){console.error('Cancel deletion error:',error);if(error.response&&error.response.data){setError(error.response.data.message||'فشل في إلغاء الحذف');}else{setError('خطأ في الشبكة. حاول مرة أخرى.');}}finally{setLoading(false);}};if(success){return/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',display:'flex',alignItems:'center',background:`linear-gradient(to bottom, ${alpha(theme.palette.success.main,0.1)}, ${alpha(theme.palette.success.light,0.05)})`,py:4},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"md\",children:/*#__PURE__*/_jsxs(Paper,{elevation:8,sx:{p:4,textAlign:'center',borderRadius:3,border:`2px solid ${theme.palette.success.main}`},children:[/*#__PURE__*/_jsx(CheckCircleIcon,{sx:{fontSize:80,color:theme.palette.success.main,mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,color:\"success.main\",children:i18n.language==='ar'?'تم إلغاء الحذف بنجاح!':'Deletion Cancelled Successfully!'}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:3},children:i18n.language==='ar'?'حسابك الآن نشط ولن يتم حذفه. سيتم إعادة توجيهك للوحة التحكم...':'Your account is now active and will not be deleted. Redirecting to dashboard...'}),/*#__PURE__*/_jsx(CircularProgress,{size:30,color:\"success\"})]})})});}return/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',display:'flex',alignItems:'center',background:`linear-gradient(to bottom, ${alpha(theme.palette.warning.main,0.1)}, ${alpha(theme.palette.error.light,0.05)})`,py:4},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"md\",children:/*#__PURE__*/_jsxs(Paper,{elevation:8,sx:{p:4,borderRadius:3,border:`2px solid ${theme.palette.warning.main}`},children:[/*#__PURE__*/_jsxs(Box,{textAlign:\"center\",mb:4,children:[/*#__PURE__*/_jsx(WarningIcon,{sx:{fontSize:80,color:theme.palette.warning.main,mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,color:\"warning.main\",children:i18n.language==='ar'?'حساب مجدول للحذف':'Account Scheduled for Deletion'})]}),/*#__PURE__*/_jsxs(Alert,{severity:\"warning\",sx:{mb:4},children:[/*#__PURE__*/_jsx(AlertTitle,{children:i18n.language==='ar'?'تحذير مهم':'Important Warning'}),i18n.language==='ar'?'حسابك مجدول للحذف النهائي. سيتم حذف جميع بياناتك نهائياً بعد انتهاء المهلة المحددة.':'Your account is scheduled for permanent deletion. All your data will be permanently deleted after the specified time period.']}),/*#__PURE__*/_jsxs(Paper,{sx:{p:3,mb:4,backgroundColor:alpha(theme.palette.error.main,0.05),border:`1px solid ${alpha(theme.palette.error.main,0.2)}`},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"center\",mb:2,children:[/*#__PURE__*/_jsx(ScheduleIcon,{sx:{mr:1,color:theme.palette.error.main}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"error.main\",children:i18n.language==='ar'?'الوقت المتبقي':'Time Remaining'})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",textAlign:\"center\",color:\"error.main\",sx:{fontWeight:'bold',fontFamily:'monospace'},children:timeLeft||(i18n.language==='ar'?'جاري الحساب...':'Calculating...')})]}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:3},children:error}),/*#__PURE__*/_jsxs(Box,{textAlign:\"center\",children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"success\",size:\"large\",startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),onClick:handleCancelDeletion,disabled:loading,sx:{px:4,py:1.5,fontSize:'1.1rem',mb:2,minWidth:200},children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:24,color:\"inherit\"}):i18n.language==='ar'?'إلغاء الحذف':'Cancel Deletion'}),/*#__PURE__*/_jsx(Divider,{sx:{my:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:i18n.language==='ar'?'إذا لم تقم بإلغاء الحذف، سيتم حذف حسابك نهائياً بعد انتهاء المهلة':'If you do not cancel the deletion, your account will be permanently deleted after the time expires'})]})]})})});};export default PendingDeletion;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "useTranslation", "Box", "Container", "Paper", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CircularProgress", "Divider", "useTheme", "alpha", "Warning", "WarningIcon", "Schedule", "ScheduleIcon", "Cancel", "CancelIcon", "CheckCircle", "CheckCircleIcon", "useAuth", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "PendingDeletion", "t", "i18n", "navigate", "location", "theme", "currentUser", "handleLogout", "loading", "setLoading", "timeLeft", "setTimeLeft", "deleteScheduledAt", "setDeleteScheduledAt", "success", "setSuccess", "error", "setError", "calculateTimeLeft", "scheduledDate", "now", "Date", "deleteDate", "timeDiff", "language", "days", "Math", "floor", "hours", "minutes", "interval", "setInterval", "clearInterval", "fetchDeletionInfo", "state", "scheduledAt", "delete_scheduled_at", "savedMessage", "localStorage", "getItem", "messageData", "JSON", "parse", "e", "console", "handleCancelDeletion", "response", "post", "data", "removeItem", "setTimeout", "message", "sx", "minHeight", "display", "alignItems", "background", "palette", "main", "light", "py", "children", "max<PERSON><PERSON><PERSON>", "elevation", "p", "textAlign", "borderRadius", "border", "fontSize", "color", "mb", "variant", "gutterBottom", "size", "warning", "severity", "backgroundColor", "justifyContent", "mr", "fontWeight", "fontFamily", "startIcon", "onClick", "disabled", "px", "min<PERSON><PERSON><PERSON>", "my"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/PendingDeletion.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport {\n  Box,\n  Container,\n  Paper,\n  Typography,\n  Button,\n  Alert,\n  AlertTitle,\n  CircularProgress,\n  Divider,\n  useTheme,\n  alpha\n} from '@mui/material';\nimport {\n  Warning as WarningIcon,\n  Schedule as ScheduleIcon,\n  Cancel as CancelIcon,\n  CheckCircle as CheckCircleIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\n\nconst PendingDeletion = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const theme = useTheme();\n  const { currentUser, handleLogout } = useAuth();\n  \n  const [loading, setLoading] = useState(false);\n  const [timeLeft, setTimeLeft] = useState('');\n  const [deleteScheduledAt, setDeleteScheduledAt] = useState(null);\n  const [success, setSuccess] = useState(false);\n  const [error, setError] = useState('');\n\n  // حساب الوقت المتبقي\n  const calculateTimeLeft = (scheduledDate) => {\n    if (!scheduledDate) return '';\n    \n    const now = new Date();\n    const deleteDate = new Date(scheduledDate);\n    const timeDiff = deleteDate - now;\n    \n    if (timeDiff <= 0) {\n      return i18n.language === 'ar' ? 'انتهت المهلة' : 'Time expired';\n    }\n    \n    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));\n    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));\n    \n    if (i18n.language === 'ar') {\n      return `${days} أيام، ${hours} ساعات، ${minutes} دقائق`;\n    } else {\n      return `${days} days, ${hours} hours, ${minutes} minutes`;\n    }\n  };\n\n  // تحديث الوقت كل دقيقة\n  useEffect(() => {\n    const interval = setInterval(() => {\n      if (deleteScheduledAt) {\n        setTimeLeft(calculateTimeLeft(deleteScheduledAt));\n      }\n    }, 60000); // كل دقيقة\n\n    return () => clearInterval(interval);\n  }, [deleteScheduledAt, i18n.language]);\n\n  // جلب معلومات الحذف المجدول\n  useEffect(() => {\n    const fetchDeletionInfo = () => {\n      // من location state\n      if (location.state && location.state.deleteScheduledAt) {\n        const scheduledAt = location.state.deleteScheduledAt;\n        setDeleteScheduledAt(scheduledAt);\n        setTimeLeft(calculateTimeLeft(scheduledAt));\n        return;\n      }\n\n      // من currentUser\n      if (currentUser && currentUser.delete_scheduled_at) {\n        const scheduledAt = currentUser.delete_scheduled_at;\n        setDeleteScheduledAt(scheduledAt);\n        setTimeLeft(calculateTimeLeft(scheduledAt));\n        return;\n      }\n\n      // من localStorage\n      const savedMessage = localStorage.getItem('accountStatusMessage');\n      if (savedMessage) {\n        try {\n          const messageData = JSON.parse(savedMessage);\n          if (messageData.deleteScheduledAt) {\n            const scheduledAt = messageData.deleteScheduledAt;\n            setDeleteScheduledAt(scheduledAt);\n            setTimeLeft(calculateTimeLeft(scheduledAt));\n            return;\n          }\n        } catch (e) {\n          console.error('Error parsing saved message:', e);\n        }\n      }\n\n      // إذا لم نجد معلومات، إعادة توجيه للملف الشخصي\n      navigate('/student/profile');\n    };\n\n    fetchDeletionInfo();\n  }, [location.state, currentUser, navigate, i18n.language]);\n\n  // إلغاء الحذف\n  const handleCancelDeletion = async () => {\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await axios.post('/api/users/cancel-delete');\n      \n      if (response.data.success) {\n        setSuccess(true);\n        \n        // حذف رسالة الحالة من localStorage\n        localStorage.removeItem('accountStatusMessage');\n        \n        // إعادة التوجيه بعد 3 ثوان\n        setTimeout(() => {\n          navigate('/student/dashboard');\n        }, 3000);\n      }\n    } catch (error) {\n      console.error('Cancel deletion error:', error);\n      \n      if (error.response && error.response.data) {\n        setError(error.response.data.message || 'فشل في إلغاء الحذف');\n      } else {\n        setError('خطأ في الشبكة. حاول مرة أخرى.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (success) {\n    return (\n      <Box\n        sx={{\n          minHeight: '100vh',\n          display: 'flex',\n          alignItems: 'center',\n          background: `linear-gradient(to bottom, ${alpha(theme.palette.success.main, 0.1)}, ${alpha(theme.palette.success.light, 0.05)})`,\n          py: 4\n        }}\n      >\n        <Container maxWidth=\"md\">\n          <Paper\n            elevation={8}\n            sx={{\n              p: 4,\n              textAlign: 'center',\n              borderRadius: 3,\n              border: `2px solid ${theme.palette.success.main}`\n            }}\n          >\n            <CheckCircleIcon \n              sx={{ \n                fontSize: 80, \n                color: theme.palette.success.main, \n                mb: 2 \n              }} \n            />\n            \n            <Typography variant=\"h4\" gutterBottom color=\"success.main\">\n              {i18n.language === 'ar' ? 'تم إلغاء الحذف بنجاح!' : 'Deletion Cancelled Successfully!'}\n            </Typography>\n            \n            <Typography variant=\"body1\" sx={{ mb: 3 }}>\n              {i18n.language === 'ar' \n                ? 'حسابك الآن نشط ولن يتم حذفه. سيتم إعادة توجيهك للوحة التحكم...'\n                : 'Your account is now active and will not be deleted. Redirecting to dashboard...'\n              }\n            </Typography>\n            \n            <CircularProgress size={30} color=\"success\" />\n          </Paper>\n        </Container>\n      </Box>\n    );\n  }\n\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        display: 'flex',\n        alignItems: 'center',\n        background: `linear-gradient(to bottom, ${alpha(theme.palette.warning.main, 0.1)}, ${alpha(theme.palette.error.light, 0.05)})`,\n        py: 4\n      }}\n    >\n      <Container maxWidth=\"md\">\n        <Paper\n          elevation={8}\n          sx={{\n            p: 4,\n            borderRadius: 3,\n            border: `2px solid ${theme.palette.warning.main}`\n          }}\n        >\n          {/* Header */}\n          <Box textAlign=\"center\" mb={4}>\n            <WarningIcon \n              sx={{ \n                fontSize: 80, \n                color: theme.palette.warning.main, \n                mb: 2 \n              }} \n            />\n            \n            <Typography variant=\"h4\" gutterBottom color=\"warning.main\">\n              {i18n.language === 'ar' ? 'حساب مجدول للحذف' : 'Account Scheduled for Deletion'}\n            </Typography>\n          </Box>\n\n          {/* Alert */}\n          <Alert severity=\"warning\" sx={{ mb: 4 }}>\n            <AlertTitle>\n              {i18n.language === 'ar' ? 'تحذير مهم' : 'Important Warning'}\n            </AlertTitle>\n            {i18n.language === 'ar' \n              ? 'حسابك مجدول للحذف النهائي. سيتم حذف جميع بياناتك نهائياً بعد انتهاء المهلة المحددة.'\n              : 'Your account is scheduled for permanent deletion. All your data will be permanently deleted after the specified time period.'\n            }\n          </Alert>\n\n          {/* Time Left */}\n          <Paper\n            sx={{\n              p: 3,\n              mb: 4,\n              backgroundColor: alpha(theme.palette.error.main, 0.05),\n              border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`\n            }}\n          >\n            <Box display=\"flex\" alignItems=\"center\" justifyContent=\"center\" mb={2}>\n              <ScheduleIcon sx={{ mr: 1, color: theme.palette.error.main }} />\n              <Typography variant=\"h6\" color=\"error.main\">\n                {i18n.language === 'ar' ? 'الوقت المتبقي' : 'Time Remaining'}\n              </Typography>\n            </Box>\n            \n            <Typography \n              variant=\"h4\" \n              textAlign=\"center\" \n              color=\"error.main\"\n              sx={{ fontWeight: 'bold', fontFamily: 'monospace' }}\n            >\n              {timeLeft || (i18n.language === 'ar' ? 'جاري الحساب...' : 'Calculating...')}\n            </Typography>\n          </Paper>\n\n          {/* Error Message */}\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 3 }}>\n              {error}\n            </Alert>\n          )}\n\n          {/* Actions */}\n          <Box textAlign=\"center\">\n            <Button\n              variant=\"contained\"\n              color=\"success\"\n              size=\"large\"\n              startIcon={<CancelIcon />}\n              onClick={handleCancelDeletion}\n              disabled={loading}\n              sx={{ \n                px: 4, \n                py: 1.5, \n                fontSize: '1.1rem',\n                mb: 2,\n                minWidth: 200\n              }}\n            >\n              {loading ? (\n                <CircularProgress size={24} color=\"inherit\" />\n              ) : (\n                i18n.language === 'ar' ? 'إلغاء الحذف' : 'Cancel Deletion'\n              )}\n            </Button>\n\n            <Divider sx={{ my: 2 }} />\n\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              {i18n.language === 'ar' \n                ? 'إذا لم تقم بإلغاء الحذف، سيتم حذف حسابك نهائياً بعد انتهاء المهلة'\n                : 'If you do not cancel the deletion, your account will be permanently deleted after the time expires'\n              }\n            </Typography>\n          </Box>\n        </Paper>\n      </Container>\n    </Box>\n  );\n};\n\nexport default PendingDeletion;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OAASC,cAAc,KAAQ,eAAe,CAC9C,OACEC,GAAG,CACHC,SAAS,CACTC,KAAK,CACLC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,UAAU,CACVC,gBAAgB,CAChBC,OAAO,CACPC,QAAQ,CACRC,KAAK,KACA,eAAe,CACtB,OACEC,OAAO,GAAI,CAAAC,WAAW,CACtBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,WAAW,GAAI,CAAAC,eAAe,KACzB,qBAAqB,CAC5B,OAASC,OAAO,KAAQ,yBAAyB,CACjD,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAG5B,cAAc,CAAC,CAAC,CACpC,KAAM,CAAA6B,QAAQ,CAAG/B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAgC,QAAQ,CAAG/B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAgC,KAAK,CAAGrB,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAEsB,WAAW,CAAEC,YAAa,CAAC,CAAGb,OAAO,CAAC,CAAC,CAE/C,KAAM,CAACc,OAAO,CAAEC,UAAU,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACwC,QAAQ,CAAEC,WAAW,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC0C,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3C,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAAC4C,OAAO,CAAEC,UAAU,CAAC,CAAG7C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC8C,KAAK,CAAEC,QAAQ,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAEtC;AACA,KAAM,CAAAgD,iBAAiB,CAAIC,aAAa,EAAK,CAC3C,GAAI,CAACA,aAAa,CAAE,MAAO,EAAE,CAE7B,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAD,IAAI,CAACF,aAAa,CAAC,CAC1C,KAAM,CAAAI,QAAQ,CAAGD,UAAU,CAAGF,GAAG,CAEjC,GAAIG,QAAQ,EAAI,CAAC,CAAE,CACjB,MAAO,CAAArB,IAAI,CAACsB,QAAQ,GAAK,IAAI,CAAG,cAAc,CAAG,cAAc,CACjE,CAEA,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACJ,QAAQ,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CACzD,KAAM,CAAAK,KAAK,CAAGF,IAAI,CAACC,KAAK,CAAEJ,QAAQ,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,EAAK,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CAC/E,KAAM,CAAAM,OAAO,CAAGH,IAAI,CAACC,KAAK,CAAEJ,QAAQ,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,EAAK,IAAI,CAAG,EAAE,CAAC,CAAC,CAEvE,GAAIrB,IAAI,CAACsB,QAAQ,GAAK,IAAI,CAAE,CAC1B,MAAO,GAAGC,IAAI,UAAUG,KAAK,WAAWC,OAAO,QAAQ,CACzD,CAAC,IAAM,CACL,MAAO,GAAGJ,IAAI,UAAUG,KAAK,WAAWC,OAAO,UAAU,CAC3D,CACF,CAAC,CAED;AACA1D,SAAS,CAAC,IAAM,CACd,KAAM,CAAA2D,QAAQ,CAAGC,WAAW,CAAC,IAAM,CACjC,GAAInB,iBAAiB,CAAE,CACrBD,WAAW,CAACO,iBAAiB,CAACN,iBAAiB,CAAC,CAAC,CACnD,CACF,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAMoB,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,CAAClB,iBAAiB,CAAEV,IAAI,CAACsB,QAAQ,CAAC,CAAC,CAEtC;AACArD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8D,iBAAiB,CAAGA,CAAA,GAAM,CAC9B;AACA,GAAI7B,QAAQ,CAAC8B,KAAK,EAAI9B,QAAQ,CAAC8B,KAAK,CAACtB,iBAAiB,CAAE,CACtD,KAAM,CAAAuB,WAAW,CAAG/B,QAAQ,CAAC8B,KAAK,CAACtB,iBAAiB,CACpDC,oBAAoB,CAACsB,WAAW,CAAC,CACjCxB,WAAW,CAACO,iBAAiB,CAACiB,WAAW,CAAC,CAAC,CAC3C,OACF,CAEA;AACA,GAAI7B,WAAW,EAAIA,WAAW,CAAC8B,mBAAmB,CAAE,CAClD,KAAM,CAAAD,WAAW,CAAG7B,WAAW,CAAC8B,mBAAmB,CACnDvB,oBAAoB,CAACsB,WAAW,CAAC,CACjCxB,WAAW,CAACO,iBAAiB,CAACiB,WAAW,CAAC,CAAC,CAC3C,OACF,CAEA;AACA,KAAM,CAAAE,YAAY,CAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CACjE,GAAIF,YAAY,CAAE,CAChB,GAAI,CACF,KAAM,CAAAG,WAAW,CAAGC,IAAI,CAACC,KAAK,CAACL,YAAY,CAAC,CAC5C,GAAIG,WAAW,CAAC5B,iBAAiB,CAAE,CACjC,KAAM,CAAAuB,WAAW,CAAGK,WAAW,CAAC5B,iBAAiB,CACjDC,oBAAoB,CAACsB,WAAW,CAAC,CACjCxB,WAAW,CAACO,iBAAiB,CAACiB,WAAW,CAAC,CAAC,CAC3C,OACF,CACF,CAAE,MAAOQ,CAAC,CAAE,CACVC,OAAO,CAAC5B,KAAK,CAAC,8BAA8B,CAAE2B,CAAC,CAAC,CAClD,CACF,CAEA;AACAxC,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CAAC,CAED8B,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,CAAC7B,QAAQ,CAAC8B,KAAK,CAAE5B,WAAW,CAAEH,QAAQ,CAAED,IAAI,CAACsB,QAAQ,CAAC,CAAC,CAE1D;AACA,KAAM,CAAAqB,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvCpC,UAAU,CAAC,IAAI,CAAC,CAChBQ,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF,KAAM,CAAA6B,QAAQ,CAAG,KAAM,CAAAnD,KAAK,CAACoD,IAAI,CAAC,0BAA0B,CAAC,CAE7D,GAAID,QAAQ,CAACE,IAAI,CAAClC,OAAO,CAAE,CACzBC,UAAU,CAAC,IAAI,CAAC,CAEhB;AACAuB,YAAY,CAACW,UAAU,CAAC,sBAAsB,CAAC,CAE/C;AACAC,UAAU,CAAC,IAAM,CACf/C,QAAQ,CAAC,oBAAoB,CAAC,CAChC,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CAAE,MAAOa,KAAK,CAAE,CACd4B,OAAO,CAAC5B,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAE9C,GAAIA,KAAK,CAAC8B,QAAQ,EAAI9B,KAAK,CAAC8B,QAAQ,CAACE,IAAI,CAAE,CACzC/B,QAAQ,CAACD,KAAK,CAAC8B,QAAQ,CAACE,IAAI,CAACG,OAAO,EAAI,oBAAoB,CAAC,CAC/D,CAAC,IAAM,CACLlC,QAAQ,CAAC,+BAA+B,CAAC,CAC3C,CACF,CAAC,OAAS,CACRR,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIK,OAAO,CAAE,CACX,mBACEjB,IAAA,CAACtB,GAAG,EACF6E,EAAE,CAAE,CACFC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,UAAU,CAAE,8BAA8BvE,KAAK,CAACoB,KAAK,CAACoD,OAAO,CAAC3C,OAAO,CAAC4C,IAAI,CAAE,GAAG,CAAC,KAAKzE,KAAK,CAACoB,KAAK,CAACoD,OAAO,CAAC3C,OAAO,CAAC6C,KAAK,CAAE,IAAI,CAAC,GAAG,CAChIC,EAAE,CAAE,CACN,CAAE,CAAAC,QAAA,cAEFhE,IAAA,CAACrB,SAAS,EAACsF,QAAQ,CAAC,IAAI,CAAAD,QAAA,cACtB9D,KAAA,CAACtB,KAAK,EACJsF,SAAS,CAAE,CAAE,CACbX,EAAE,CAAE,CACFY,CAAC,CAAE,CAAC,CACJC,SAAS,CAAE,QAAQ,CACnBC,YAAY,CAAE,CAAC,CACfC,MAAM,CAAE,aAAa9D,KAAK,CAACoD,OAAO,CAAC3C,OAAO,CAAC4C,IAAI,EACjD,CAAE,CAAAG,QAAA,eAEFhE,IAAA,CAACJ,eAAe,EACd2D,EAAE,CAAE,CACFgB,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAEhE,KAAK,CAACoD,OAAO,CAAC3C,OAAO,CAAC4C,IAAI,CACjCY,EAAE,CAAE,CACN,CAAE,CACH,CAAC,cAEFzE,IAAA,CAACnB,UAAU,EAAC6F,OAAO,CAAC,IAAI,CAACC,YAAY,MAACH,KAAK,CAAC,cAAc,CAAAR,QAAA,CACvD3D,IAAI,CAACsB,QAAQ,GAAK,IAAI,CAAG,uBAAuB,CAAG,kCAAkC,CAC5E,CAAC,cAEb3B,IAAA,CAACnB,UAAU,EAAC6F,OAAO,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEkB,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,CACvC3D,IAAI,CAACsB,QAAQ,GAAK,IAAI,CACnB,gEAAgE,CAChE,iFAAiF,CAE3E,CAAC,cAEb3B,IAAA,CAACf,gBAAgB,EAAC2F,IAAI,CAAE,EAAG,CAACJ,KAAK,CAAC,SAAS,CAAE,CAAC,EACzC,CAAC,CACC,CAAC,CACT,CAAC,CAEV,CAEA,mBACExE,IAAA,CAACtB,GAAG,EACF6E,EAAE,CAAE,CACFC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,UAAU,CAAE,8BAA8BvE,KAAK,CAACoB,KAAK,CAACoD,OAAO,CAACiB,OAAO,CAAChB,IAAI,CAAE,GAAG,CAAC,KAAKzE,KAAK,CAACoB,KAAK,CAACoD,OAAO,CAACzC,KAAK,CAAC2C,KAAK,CAAE,IAAI,CAAC,GAAG,CAC9HC,EAAE,CAAE,CACN,CAAE,CAAAC,QAAA,cAEFhE,IAAA,CAACrB,SAAS,EAACsF,QAAQ,CAAC,IAAI,CAAAD,QAAA,cACtB9D,KAAA,CAACtB,KAAK,EACJsF,SAAS,CAAE,CAAE,CACbX,EAAE,CAAE,CACFY,CAAC,CAAE,CAAC,CACJE,YAAY,CAAE,CAAC,CACfC,MAAM,CAAE,aAAa9D,KAAK,CAACoD,OAAO,CAACiB,OAAO,CAAChB,IAAI,EACjD,CAAE,CAAAG,QAAA,eAGF9D,KAAA,CAACxB,GAAG,EAAC0F,SAAS,CAAC,QAAQ,CAACK,EAAE,CAAE,CAAE,CAAAT,QAAA,eAC5BhE,IAAA,CAACV,WAAW,EACViE,EAAE,CAAE,CACFgB,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAEhE,KAAK,CAACoD,OAAO,CAACiB,OAAO,CAAChB,IAAI,CACjCY,EAAE,CAAE,CACN,CAAE,CACH,CAAC,cAEFzE,IAAA,CAACnB,UAAU,EAAC6F,OAAO,CAAC,IAAI,CAACC,YAAY,MAACH,KAAK,CAAC,cAAc,CAAAR,QAAA,CACvD3D,IAAI,CAACsB,QAAQ,GAAK,IAAI,CAAG,kBAAkB,CAAG,gCAAgC,CACrE,CAAC,EACV,CAAC,cAGNzB,KAAA,CAACnB,KAAK,EAAC+F,QAAQ,CAAC,SAAS,CAACvB,EAAE,CAAE,CAAEkB,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,eACtChE,IAAA,CAAChB,UAAU,EAAAgF,QAAA,CACR3D,IAAI,CAACsB,QAAQ,GAAK,IAAI,CAAG,WAAW,CAAG,mBAAmB,CACjD,CAAC,CACZtB,IAAI,CAACsB,QAAQ,GAAK,IAAI,CACnB,qFAAqF,CACrF,8HAA8H,EAE7H,CAAC,cAGRzB,KAAA,CAACtB,KAAK,EACJ2E,EAAE,CAAE,CACFY,CAAC,CAAE,CAAC,CACJM,EAAE,CAAE,CAAC,CACLM,eAAe,CAAE3F,KAAK,CAACoB,KAAK,CAACoD,OAAO,CAACzC,KAAK,CAAC0C,IAAI,CAAE,IAAI,CAAC,CACtDS,MAAM,CAAE,aAAalF,KAAK,CAACoB,KAAK,CAACoD,OAAO,CAACzC,KAAK,CAAC0C,IAAI,CAAE,GAAG,CAAC,EAC3D,CAAE,CAAAG,QAAA,eAEF9D,KAAA,CAACxB,GAAG,EAAC+E,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAACsB,cAAc,CAAC,QAAQ,CAACP,EAAE,CAAE,CAAE,CAAAT,QAAA,eACpEhE,IAAA,CAACR,YAAY,EAAC+D,EAAE,CAAE,CAAE0B,EAAE,CAAE,CAAC,CAAET,KAAK,CAAEhE,KAAK,CAACoD,OAAO,CAACzC,KAAK,CAAC0C,IAAK,CAAE,CAAE,CAAC,cAChE7D,IAAA,CAACnB,UAAU,EAAC6F,OAAO,CAAC,IAAI,CAACF,KAAK,CAAC,YAAY,CAAAR,QAAA,CACxC3D,IAAI,CAACsB,QAAQ,GAAK,IAAI,CAAG,eAAe,CAAG,gBAAgB,CAClD,CAAC,EACV,CAAC,cAEN3B,IAAA,CAACnB,UAAU,EACT6F,OAAO,CAAC,IAAI,CACZN,SAAS,CAAC,QAAQ,CAClBI,KAAK,CAAC,YAAY,CAClBjB,EAAE,CAAE,CAAE2B,UAAU,CAAE,MAAM,CAAEC,UAAU,CAAE,WAAY,CAAE,CAAAnB,QAAA,CAEnDnD,QAAQ,GAAKR,IAAI,CAACsB,QAAQ,GAAK,IAAI,CAAG,gBAAgB,CAAG,gBAAgB,CAAC,CACjE,CAAC,EACR,CAAC,CAGPR,KAAK,eACJnB,IAAA,CAACjB,KAAK,EAAC+F,QAAQ,CAAC,OAAO,CAACvB,EAAE,CAAE,CAAEkB,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,CACnC7C,KAAK,CACD,CACR,cAGDjB,KAAA,CAACxB,GAAG,EAAC0F,SAAS,CAAC,QAAQ,CAAAJ,QAAA,eACrBhE,IAAA,CAAClB,MAAM,EACL4F,OAAO,CAAC,WAAW,CACnBF,KAAK,CAAC,SAAS,CACfI,IAAI,CAAC,OAAO,CACZQ,SAAS,cAAEpF,IAAA,CAACN,UAAU,GAAE,CAAE,CAC1B2F,OAAO,CAAErC,oBAAqB,CAC9BsC,QAAQ,CAAE3E,OAAQ,CAClB4C,EAAE,CAAE,CACFgC,EAAE,CAAE,CAAC,CACLxB,EAAE,CAAE,GAAG,CACPQ,QAAQ,CAAE,QAAQ,CAClBE,EAAE,CAAE,CAAC,CACLe,QAAQ,CAAE,GACZ,CAAE,CAAAxB,QAAA,CAEDrD,OAAO,cACNX,IAAA,CAACf,gBAAgB,EAAC2F,IAAI,CAAE,EAAG,CAACJ,KAAK,CAAC,SAAS,CAAE,CAAC,CAE9CnE,IAAI,CAACsB,QAAQ,GAAK,IAAI,CAAG,aAAa,CAAG,iBAC1C,CACK,CAAC,cAET3B,IAAA,CAACd,OAAO,EAACqE,EAAE,CAAE,CAAEkC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1BzF,IAAA,CAACnB,UAAU,EAAC6F,OAAO,CAAC,OAAO,CAACF,KAAK,CAAC,gBAAgB,CAAAR,QAAA,CAC/C3D,IAAI,CAACsB,QAAQ,GAAK,IAAI,CACnB,mEAAmE,CACnE,oGAAoG,CAE9F,CAAC,EACV,CAAC,EACD,CAAC,CACC,CAAC,CACT,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}