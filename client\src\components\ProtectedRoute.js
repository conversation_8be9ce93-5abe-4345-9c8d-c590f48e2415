import React, { useEffect, useState } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import UserStatusHandler from '../utils/userStatusHandler';
import { Box, CircularProgress, Alert, AlertTitle, Button, Typography, Stack } from '@mui/material';
import { Warning as WarningIcon, ExitToApp as LogoutIcon, Cancel as CancelIcon, Schedule as ScheduleIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { format, differenceInDays, differenceInHours, differenceInMinutes } from 'date-fns';
import { ar } from 'date-fns/locale';
import axios from '../utils/axios';

/**
 * مكون حماية الطرق - يتحقق من تسجيل الدخول وحالة المستخدم
 */
const ProtectedRoute = ({ children, allowPendingDeletion = false }) => {
  const { isAuthenticated, currentUser, handleLogout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const [statusCheck, setStatusCheck] = useState({ loading: true });
  const [cancelLoading, setCancelLoading] = useState(false);

  // دالة لحساب الوقت المتبقي للحذف
  const getTimeRemaining = (deleteScheduledAt) => {
    if (!deleteScheduledAt) return null;

    const now = new Date();
    const deleteDate = new Date(deleteScheduledAt);

    const days = differenceInDays(deleteDate, now);
    const hours = differenceInHours(deleteDate, now) % 24;
    const minutes = differenceInMinutes(deleteDate, now) % 60;

    if (days > 0) {
      const dayText = days === 1 ? t('pendingDeletion.day') : t('pendingDeletion.days');
      const hourText = hours === 1 ? t('pendingDeletion.hour') : t('pendingDeletion.hours');
      return `${days} ${dayText} و ${hours} ${hourText}`;
    } else if (hours > 0) {
      const hourText = hours === 1 ? t('pendingDeletion.hour') : t('pendingDeletion.hours');
      const minuteText = minutes === 1 ? t('pendingDeletion.minute') : t('pendingDeletion.minutes');
      return `${hours} ${hourText} و ${minutes} ${minuteText}`;
    } else if (minutes > 0) {
      const minuteText = minutes === 1 ? t('pendingDeletion.minute') : t('pendingDeletion.minutes');
      return `${minutes} ${minuteText}`;
    } else {
      return t('pendingDeletion.lessThanMinute');
    }
  };

  // دالة إلغاء الحذف
  const handleCancelDeletion = async () => {
    try {
      setCancelLoading(true);
      await axios.post('/users/cancel-delete');

      // إعادة تحميل بيانات المستخدم
      window.location.reload();
    } catch (error) {
      console.error('Error cancelling deletion:', error);
      alert('حدث خطأ أثناء إلغاء الحذف. يرجى المحاولة مرة أخرى.');
    } finally {
      setCancelLoading(false);
    }
  };

  useEffect(() => {
    const checkUserStatus = async () => {
      // إذا لم يكن مسجل دخول، لا نحتاج للتحقق من الحالة
      if (!isAuthenticated || !currentUser) {
        setStatusCheck({ loading: false, valid: false, reason: 'not_authenticated' });
        return;
      }

      // التحقق من حالة المستخدم للمستخدمين المسجلين فقط
      const result = await UserStatusHandler.checkUserStatus();
      
      if (!result.valid) {
        if (result.reason === 'deleted' || result.reason === 'unauthorized') {
          // حفظ رسالة الحالة في localStorage
          localStorage.setItem('accountStatusMessage', JSON.stringify({
            message: result.message || 'تم حذف هذا الحساب',
            message_en: result.message_en || 'Account has been deleted',
            accountStatus: result.reason,
            deleteScheduledAt: result.deleteScheduledAt
          }));

          // حساب محذوف أو توكن منتهي الصلاحية - تسجيل خروج فوري
          handleLogout();
          setStatusCheck({
            loading: false,
            valid: false,
            reason: result.reason,
            message: result.message || 'تم حذف هذا الحساب'
          });
          return;
        }

        if (result.reason === 'error') {
          // خطأ في الشبكة أو الخادم
          setStatusCheck({
            loading: false,
            valid: false,
            reason: 'error',
            message: result.message
          });
          return;
        }
      }

      // التعامل مع المستخدمين المجدولين للحذف
      if (result.reason === 'pending_deletion' && !allowPendingDeletion) {
        // حساب مجدول للحذف ولا يُسمح بالوصول
        setStatusCheck({
          loading: false,
          valid: false,
          reason: 'pending_deletion',
          message: t('pendingDeletion.message')
        });
        return;
      }

      setStatusCheck({ loading: false, valid: true });
    };

    checkUserStatus();
  }, [isAuthenticated, currentUser, allowPendingDeletion, handleLogout]);

  // غير مسجل دخول - إعادة توجيه فورية بدون loading
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // جاري التحميل للمستخدمين المسجلين فقط
  if (statusCheck.loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
        flexDirection="column"
        gap={2}
      >
        <CircularProgress size={40} />
        <div>جاري التحقق من حالة الحساب...</div>
      </Box>
    );
  }

  // حساب محذوف - إعادة توجيه لصفحة تسجيل الدخول
  if (statusCheck.reason === 'deleted' || statusCheck.reason === 'unauthorized') {
    return <Navigate to="/login" replace />;
  }

  // حساب مجدول للحذف ولا يُسمح بالوصول
  if (statusCheck.reason === 'pending_deletion' && !allowPendingDeletion) {
    const timeRemaining = getTimeRemaining(currentUser?.delete_scheduled_at);

    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        p={3}
        sx={{
          backgroundColor: '#f5f5f5',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999
        }}
      >
        <Alert
          severity="warning"
          icon={<WarningIcon />}
          sx={{ maxWidth: 600, width: '100%' }}
        >
          <AlertTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ScheduleIcon />
            {t('pendingDeletion.title')}
          </AlertTitle>

          <Typography variant="body1" sx={{ mb: 2 }}>
            {statusCheck.message}
          </Typography>

          {timeRemaining && (
            <Typography variant="body2" sx={{ mb: 2, color: 'error.main', fontWeight: 'bold' }}>
              ⏰ {t('pendingDeletion.timeRemaining')}: {timeRemaining}
            </Typography>
          )}

          {currentUser?.delete_scheduled_at && (
            <Typography variant="caption" sx={{ mb: 3, display: 'block', color: 'text.secondary' }}>
              📅 {t('pendingDeletion.scheduledDate')}: {format(new Date(currentUser.delete_scheduled_at), 'dd/MM/yyyy HH:mm', {
                locale: i18n.language === 'ar' ? ar : undefined
              })}
            </Typography>
          )}

          <Stack direction="row" spacing={3} sx={{ mt: 2 }}>
            <Button
              variant="contained"
              color="success"
              startIcon={<CancelIcon />}
              onClick={handleCancelDeletion}
              disabled={cancelLoading}
              size="medium"
            >
              {cancelLoading ? t('pendingDeletion.cancelling') : t('pendingDeletion.cancelDeletion')}
            </Button>

            <Button
              variant="outlined"
              color="error"
              startIcon={<LogoutIcon />}
              onClick={handleLogout}
              size="medium"
            >
              {t('pendingDeletion.logout')}
            </Button>
          </Stack>
        </Alert>
      </Box>
    );
  }

  // كل شيء على ما يرام
  return children;
};

export default ProtectedRoute;
