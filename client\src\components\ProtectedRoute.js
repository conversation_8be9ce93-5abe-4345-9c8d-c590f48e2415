import React, { useEffect, useState } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import UserStatusHandler from '../utils/userStatusHandler';
import { Box, CircularProgress, Alert, AlertTitle, Button, Typography, Stack } from '@mui/material';
import { Warning as WarningIcon, ExitToApp as LogoutIcon, Cancel as CancelIcon, Schedule as ScheduleIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { format, differenceInDays, differenceInHours, differenceInMinutes } from 'date-fns';
import { ar } from 'date-fns/locale';
import axios from '../utils/axios';

/**
 * مكون حماية الطرق - يتحقق من تسجيل الدخول وحالة المستخدم
 */
const ProtectedRoute = ({ children, allowPendingDeletion = false }) => {
  const { isAuthenticated, currentUser, handleLogout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const [statusCheck, setStatusCheck] = useState({ loading: true });
  const [cancelLoading, setCancelLoading] = useState(false);

  // دالة لحساب الوقت المتبقي للحذف
  const getTimeRemaining = (deleteScheduledAt) => {
    if (!deleteScheduledAt) return null;

    const now = new Date();
    const deleteDate = new Date(deleteScheduledAt);

    const days = differenceInDays(deleteDate, now);
    const hours = differenceInHours(deleteDate, now) % 24;
    const minutes = differenceInMinutes(deleteDate, now) % 60;

    if (days > 0) {
      return `${days} ${days === 1 ? 'يوم' : 'أيام'} و ${hours} ${hours === 1 ? 'ساعة' : 'ساعات'}`;
    } else if (hours > 0) {
      return `${hours} ${hours === 1 ? 'ساعة' : 'ساعات'} و ${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;
    } else if (minutes > 0) {
      return `${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;
    } else {
      return 'أقل من دقيقة';
    }
  };

  // دالة إلغاء الحذف
  const handleCancelDeletion = async () => {
    try {
      setCancelLoading(true);
      await axios.post('/users/cancel-delete');

      // إعادة تحميل بيانات المستخدم
      window.location.reload();
    } catch (error) {
      console.error('Error cancelling deletion:', error);
      alert('حدث خطأ أثناء إلغاء الحذف. يرجى المحاولة مرة أخرى.');
    } finally {
      setCancelLoading(false);
    }
  };

  useEffect(() => {
    const checkUserStatus = async () => {
      // إذا لم يكن مسجل دخول، لا نحتاج للتحقق من الحالة
      if (!isAuthenticated || !currentUser) {
        setStatusCheck({ loading: false, valid: false, reason: 'not_authenticated' });
        return;
      }

      // التحقق من حالة المستخدم للمستخدمين المسجلين فقط
      const result = await UserStatusHandler.checkUserStatus();
      
      if (!result.valid) {
        if (result.reason === 'deleted' || result.reason === 'unauthorized') {
          // حفظ رسالة الحالة في localStorage
          localStorage.setItem('accountStatusMessage', JSON.stringify({
            message: result.message || 'تم حذف هذا الحساب',
            message_en: result.message_en || 'Account has been deleted',
            accountStatus: result.reason,
            deleteScheduledAt: result.deleteScheduledAt
          }));

          // حساب محذوف أو توكن منتهي الصلاحية - تسجيل خروج فوري
          handleLogout();
          setStatusCheck({
            loading: false,
            valid: false,
            reason: result.reason,
            message: result.message || 'تم حذف هذا الحساب'
          });
          return;
        }

        if (result.reason === 'error') {
          // خطأ في الشبكة أو الخادم
          setStatusCheck({
            loading: false,
            valid: false,
            reason: 'error',
            message: result.message
          });
          return;
        }
      }

      // التعامل مع المستخدمين المجدولين للحذف
      if (result.reason === 'pending_deletion' && !allowPendingDeletion) {
        // حساب مجدول للحذف ولا يُسمح بالوصول
        setStatusCheck({
          loading: false,
          valid: false,
          reason: 'pending_deletion',
          message: 'هذا الحساب مجدول للحذف.'
        });
        return;
      }

      setStatusCheck({ loading: false, valid: true });
    };

    checkUserStatus();
  }, [isAuthenticated, currentUser, allowPendingDeletion, handleLogout]);

  // غير مسجل دخول - إعادة توجيه فورية بدون loading
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // جاري التحميل للمستخدمين المسجلين فقط
  if (statusCheck.loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
        flexDirection="column"
        gap={2}
      >
        <CircularProgress size={40} />
        <div>جاري التحقق من حالة الحساب...</div>
      </Box>
    );
  }

  // حساب محذوف - إعادة توجيه لصفحة تسجيل الدخول
  if (statusCheck.reason === 'deleted' || statusCheck.reason === 'unauthorized') {
    return <Navigate to="/login" replace />;
  }

  // حساب مجدول للحذف ولا يُسمح بالوصول
  if (statusCheck.reason === 'pending_deletion' && !allowPendingDeletion) {
    const timeRemaining = getTimeRemaining(currentUser?.delete_scheduled_at);

    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
        p={3}
      >
        <Alert
          severity="warning"
          icon={<WarningIcon />}
          sx={{ maxWidth: 600, width: '100%' }}
        >
          <AlertTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ScheduleIcon />
            حساب مجدول للحذف
          </AlertTitle>

          <Typography variant="body1" sx={{ mb: 2 }}>
            {statusCheck.message}
          </Typography>

          {timeRemaining && (
            <Typography variant="body2" sx={{ mb: 2, color: 'error.main', fontWeight: 'bold' }}>
              ⏰ الوقت المتبقي للحذف: {timeRemaining}
            </Typography>
          )}

          {currentUser?.delete_scheduled_at && (
            <Typography variant="caption" sx={{ mb: 3, display: 'block', color: 'text.secondary' }}>
              📅 موعد الحذف المجدول: {format(new Date(currentUser.delete_scheduled_at), 'dd/MM/yyyy HH:mm', {
                locale: i18n.language === 'ar' ? ar : undefined
              })}
            </Typography>
          )}

          <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
            <Button
              variant="contained"
              color="success"
              startIcon={<CancelIcon />}
              onClick={handleCancelDeletion}
              disabled={cancelLoading}
              size="small"
            >
              {cancelLoading ? 'جاري الإلغاء...' : 'إلغاء الحذف'}
            </Button>

            <Button
              variant="outlined"
              color="error"
              startIcon={<LogoutIcon />}
              onClick={handleLogout}
              size="small"
            >
              تسجيل الخروج
            </Button>
          </Stack>
        </Alert>
      </Box>
    );
  }

  // كل شيء على ما يرام
  return children;
};

export default ProtectedRoute;
