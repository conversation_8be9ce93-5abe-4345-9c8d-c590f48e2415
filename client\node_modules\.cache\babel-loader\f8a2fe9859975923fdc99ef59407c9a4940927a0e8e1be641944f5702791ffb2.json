{"ast": null, "code": "import React,{createContext,useState,useContext,useEffect}from'react';import axios from'../utils/axios';import{useNavigate}from'react-router-dom';import{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext(null);export const AuthProvider=_ref=>{let{children}=_ref;const[currentUser,setCurrentUser]=useState(null);const[token,setToken]=useState(localStorage.getItem('token'));const[loading,setLoading]=useState(true);const[initialized,setInitialized]=useState(false);const[isAuthenticated,setIsAuthenticated]=useState(false);const navigate=useNavigate();useEffect(()=>{// Check if user is logged in on mount\nconst storedToken=localStorage.getItem('token');if(storedToken){// Set token in axios headers\naxios.defaults.headers.common['Authorization']=`Bearer ${storedToken}`;// Verify token and get user data\nconsole.log('Verifying token...');axios.get('/auth/verify').then(response=>{console.log('Token verification response:',response.data);if(response.data&&response.data.success&&response.data.user){const userData={...response.data.user,full_name:response.data.user.full_name||null,email:response.data.user.email||null,gender:response.data.user.gender||null,status:response.data.user.status||'active',delete_scheduled_at:response.data.user.delete_scheduled_at||null};// التحقق من حالة المستخدم\nif(userData.status==='deleted'){console.log('User account is deleted - logging out');handleLogout();return;}if(userData.status==='pending_deletion'){console.log('User account is pending deletion - this should not happen in verify');// هذا لا يجب أن يحدث لأن المستخدم المجدول للحذف لا يجب أن يحصل على token صالح\nhandleLogout();setLoading(false);setInitialized(true);return;}setCurrentUser(userData);setToken(storedToken);setIsAuthenticated(true);// تنظيف أي رسائل حالة قديمة عند تسجيل الدخول الناجح\nlocalStorage.removeItem('accountStatusMessage');}else{console.log('Invalid response from verify endpoint');handleLogout();}}).catch(error=>{console.error('Token verification error:',error);// التحقق من نوع الخطأ\nif(error.response&&error.response.status===401){const errorData=error.response.data;// إذا كان هناك معلومات عن حالة الحساب، حفظها للعرض في صفحة تسجيل الدخول\nif(errorData&&errorData.accountStatus){if(process.env.NODE_ENV==='development'){console.log('Account status detected in AuthContext:',errorData.accountStatus);}// حفظ الرسالة المترجمة\nconst messageToSave={message:errorData.message,// الرسالة بالعربية\nmessage_en:errorData.message_en,// الرسالة بالإنجليزية\naccountStatus:errorData.accountStatus,deleteScheduledAt:errorData.deleteScheduledAt};localStorage.setItem('accountStatusMessage',JSON.stringify(messageToSave));// منع الدخول للمنصة - تسجيل خروج فوري\nhandleLogout();// منع أي طلبات إضافية\nsetLoading(false);setInitialized(true);return;}}handleLogout();}).finally(()=>{setLoading(false);setInitialized(true);});}else{console.log('No token found in localStorage');// تنظيف أي رسائل حالة قديمة عند عدم وجود token\nlocalStorage.removeItem('accountStatusMessage');setLoading(false);setInitialized(true);}},[]);const handleLogout=async()=>{try{if(token){await axios.post('/auth/logout');}}catch(error){console.error('Logout request failed:',error);}finally{localStorage.removeItem('token');setCurrentUser(null);setToken(null);setIsAuthenticated(false);delete axios.defaults.headers.common['Authorization'];navigate('/login');}};const redirectToDashboard=user=>{switch(user.role){case'admin':navigate('/admin/dashboard');break;case'platform_teacher':navigate('/teacher/dashboard');break;case'new_teacher':navigate('/teacher/application');break;case'student':navigate('/student/dashboard');break;default:navigate('/');}};const login=async(emailOrToken,passwordOrUser)=>{try{// If called with token and user (from email verification)\nif(typeof emailOrToken==='string'&&emailOrToken.length>50&&typeof passwordOrUser==='object'){const token=emailOrToken;const user=passwordOrUser;// التحقق من حالة المستخدم قبل حفظ التوكن\nif(user.status==='pending_deletion'){const errorMessage='هذا الحساب مجدول للحذف';const error=new Error(errorMessage);error.response={data:{message:'هذا الحساب مجدول للحذف',message_en:'Account is scheduled for deletion',accountStatus:'pending_deletion',deleteScheduledAt:user.delete_scheduled_at}};throw error;}if(user.status==='deleted'){const errorMessage='تم حذف هذا الحساب';const error=new Error(errorMessage);error.response={data:{message:'تم حذف هذا الحساب',message_en:'Account has been deleted',accountStatus:'deleted'}};throw error;}const userData={...user,full_name:user.full_name||null,email:user.email||null,gender:user.gender||null};localStorage.setItem('token',token);// تنظيف أي رسائل حالة قديمة عند تسجيل الدخول الناجح\nlocalStorage.removeItem('accountStatusMessage');axios.defaults.headers.common['Authorization']=`Bearer ${token}`;setCurrentUser(userData);setToken(token);setIsAuthenticated(true);redirectToDashboard(userData);return{success:true};}// Normal login with email and password\nconst response=await axios.post('/auth/login',{email:emailOrToken||null,password:passwordOrUser||null});if(response.data&&response.data.success&&response.data.token&&response.data.user){const{token,user}=response.data;// التحقق من حالة المستخدم قبل حفظ التوكن\nif(user.status==='pending_deletion'){// لا نحفظ التوكن للمستخدم المجدول للحذف\nconst errorMessage='هذا الحساب مجدول للحذف';const error=new Error(errorMessage);error.response={data:{message:'هذا الحساب مجدول للحذف',message_en:'Account is scheduled for deletion',accountStatus:'pending_deletion',deleteScheduledAt:user.delete_scheduled_at}};throw error;}if(user.status==='deleted'){// لا نحفظ التوكن للمستخدم المحذوف\nconst errorMessage='تم حذف هذا الحساب';const error=new Error(errorMessage);error.response={data:{message:'تم حذف هذا الحساب',message_en:'Account has been deleted',accountStatus:'deleted'}};throw error;}const userData={...user,full_name:user.full_name||null,email:user.email||null,gender:user.gender||null};localStorage.setItem('token',token);// تنظيف أي رسائل حالة قديمة عند تسجيل الدخول الناجح\nlocalStorage.removeItem('accountStatusMessage');axios.defaults.headers.common['Authorization']=`Bearer ${token}`;setCurrentUser(userData);setToken(token);setIsAuthenticated(true);redirectToDashboard(userData);return{success:true};}else{var _response$data;// If login failed, throw error to be caught by the calling component\nconst errorMessage=((_response$data=response.data)===null||_response$data===void 0?void 0:_response$data.message)||'Login failed';throw new Error(errorMessage);}}catch(error){var _error$response,_error$response2;console.error('Login error in AuthContext:',{error:error,response:error.response,data:(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.data,status:(_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.status,message:error.message});// Re-throw the error as-is since axios interceptor already enhanced it\nthrow error;}};const register=async userData=>{try{const response=await axios.post('/auth/register',userData);// Check if registration was successful (email verification required)\nif(response.data&&response.data.success){return{success:true,email:response.data.email,message:response.data.message};}return{success:false,error:response.data.message||'Registration failed'};}catch(error){var _error$response3,_error$response3$data;return{success:false,error:((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.message)||'Failed to register'};}};const updateUser=userData=>{setCurrentUser(prevUser=>({...prevUser,...userData}));};const googleLogin=async credentialResponse=>{try{const response=await axios.post('/auth/google',{credential:credentialResponse.credential});if(response.data&&response.data.success&&response.data.token&&response.data.user){const{token,user}=response.data;const userData={...user,full_name:user.full_name||null,email:user.email||null,gender:user.gender||null};localStorage.setItem('token',token);axios.defaults.headers.common['Authorization']=`Bearer ${token}`;setCurrentUser(userData);setToken(token);setIsAuthenticated(true);return{success:true,user:userData};}return{success:false,message:response.data.message};}catch(error){console.error('Google login error:',error);throw error;}};const googleRegister=async _ref2=>{let{credential,role,gender}=_ref2;try{const response=await axios.post('/auth/google/register',{credential,role,gender});// Check if registration was successful (email verification required)\nif(response.data&&response.data.success){return{success:true,email:response.data.email,message:response.data.message};}return{success:false,error:response.data.message||'Registration failed'};}catch(error){var _error$response4,_error$response4$data;console.error('Google registration error:',error);return{success:false,error:((_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.message)||'Failed to register with Google'};}};const value={currentUser,token,loading,initialized,isAuthenticated,login,register,handleLogout,updateUser,googleLogin,googleRegister};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};export const useAuth=()=>{const context=useContext(AuthContext);if(!context){throw new Error('useAuth must be used within an AuthProvider');}return context;};export default AuthContext;", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "axios", "useNavigate", "jsx", "_jsx", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "currentUser", "setCurrentUser", "token", "setToken", "localStorage", "getItem", "loading", "setLoading", "initialized", "setInitialized", "isAuthenticated", "setIsAuthenticated", "navigate", "storedToken", "defaults", "headers", "common", "console", "log", "get", "then", "response", "data", "success", "user", "userData", "full_name", "email", "gender", "status", "delete_scheduled_at", "handleLogout", "removeItem", "catch", "error", "errorData", "accountStatus", "process", "env", "NODE_ENV", "messageToSave", "message", "message_en", "deleteScheduledAt", "setItem", "JSON", "stringify", "finally", "post", "redirectToDashboard", "role", "login", "emailOrToken", "passwordOrUser", "length", "errorMessage", "Error", "password", "_response$data", "_error$response", "_error$response2", "register", "_error$response3", "_error$response3$data", "updateUser", "prevUser", "googleLogin", "credentialResponse", "credential", "googleRegister", "_ref2", "_error$response4", "_error$response4$data", "value", "Provider", "useAuth", "context"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect } from 'react';\nimport axios from '../utils/axios';\nimport { useNavigate } from 'react-router-dom';\n\nconst AuthContext = createContext(null);\n\nexport const AuthProvider = ({ children }) => {\n  const [currentUser, setCurrentUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n  const [initialized, setInitialized] = useState(false);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    // Check if user is logged in on mount\n    const storedToken = localStorage.getItem('token');\n    if (storedToken) {\n      // Set token in axios headers\n      axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;\n      \n      // Verify token and get user data\n      console.log('Verifying token...');\n      axios.get('/auth/verify')\n        .then(response => {\n          console.log('Token verification response:', response.data);\n          if (response.data && response.data.success && response.data.user) {\n            const userData = {\n              ...response.data.user,\n              full_name: response.data.user.full_name || null,\n              email: response.data.user.email || null,\n              gender: response.data.user.gender || null,\n              status: response.data.user.status || 'active',\n              delete_scheduled_at: response.data.user.delete_scheduled_at || null\n            };\n\n            // التحقق من حالة المستخدم\n            if (userData.status === 'deleted') {\n              console.log('User account is deleted - logging out');\n              handleLogout();\n              return;\n            }\n\n            if (userData.status === 'pending_deletion') {\n              console.log('User account is pending deletion - this should not happen in verify');\n              // هذا لا يجب أن يحدث لأن المستخدم المجدول للحذف لا يجب أن يحصل على token صالح\n              handleLogout();\n              setLoading(false);\n              setInitialized(true);\n              return;\n            }\n\n            setCurrentUser(userData);\n            setToken(storedToken);\n            setIsAuthenticated(true);\n\n            // تنظيف أي رسائل حالة قديمة عند تسجيل الدخول الناجح\n            localStorage.removeItem('accountStatusMessage');\n          } else {\n            console.log('Invalid response from verify endpoint');\n            handleLogout();\n          }\n        })\n        .catch(error => {\n          console.error('Token verification error:', error);\n\n          // التحقق من نوع الخطأ\n          if (error.response && error.response.status === 401) {\n            const errorData = error.response.data;\n\n            // إذا كان هناك معلومات عن حالة الحساب، حفظها للعرض في صفحة تسجيل الدخول\n            if (errorData && errorData.accountStatus) {\n              if (process.env.NODE_ENV === 'development') {\n                console.log('Account status detected in AuthContext:', errorData.accountStatus);\n              }\n\n              // حفظ الرسالة المترجمة\n              const messageToSave = {\n                message: errorData.message, // الرسالة بالعربية\n                message_en: errorData.message_en, // الرسالة بالإنجليزية\n                accountStatus: errorData.accountStatus,\n                deleteScheduledAt: errorData.deleteScheduledAt\n              };\n\n              localStorage.setItem('accountStatusMessage', JSON.stringify(messageToSave));\n\n              // منع الدخول للمنصة - تسجيل خروج فوري\n              handleLogout();\n\n              // منع أي طلبات إضافية\n              setLoading(false);\n              setInitialized(true);\n              return;\n            }\n          }\n\n          handleLogout();\n        })\n        .finally(() => {\n          setLoading(false);\n          setInitialized(true);\n        });\n    } else {\n      console.log('No token found in localStorage');\n      // تنظيف أي رسائل حالة قديمة عند عدم وجود token\n      localStorage.removeItem('accountStatusMessage');\n      setLoading(false);\n      setInitialized(true);\n    }\n  }, []);\n\n  const handleLogout = async () => {\n    try {\n      if (token) {\n        await axios.post('/auth/logout');\n      }\n    } catch (error) {\n      console.error('Logout request failed:', error);\n    } finally {\n      localStorage.removeItem('token');\n      setCurrentUser(null);\n      setToken(null);\n      setIsAuthenticated(false);\n      delete axios.defaults.headers.common['Authorization'];\n      navigate('/login');\n    }\n  };\n\n  const redirectToDashboard = (user) => {\n    switch (user.role) {\n      case 'admin':\n        navigate('/admin/dashboard');\n        break;\n      case 'platform_teacher':\n        navigate('/teacher/dashboard');\n        break;\n      case 'new_teacher':\n        navigate('/teacher/application');\n        break;\n      case 'student':\n        navigate('/student/dashboard');\n        break;\n      default:\n        navigate('/');\n    }\n  };\n\n  const login = async (emailOrToken, passwordOrUser) => {\n    try {\n      // If called with token and user (from email verification)\n      if (typeof emailOrToken === 'string' && emailOrToken.length > 50 && typeof passwordOrUser === 'object') {\n        const token = emailOrToken;\n        const user = passwordOrUser;\n\n        // التحقق من حالة المستخدم قبل حفظ التوكن\n        if (user.status === 'pending_deletion') {\n          const errorMessage = 'هذا الحساب مجدول للحذف';\n          const error = new Error(errorMessage);\n          error.response = {\n            data: {\n              message: 'هذا الحساب مجدول للحذف',\n              message_en: 'Account is scheduled for deletion',\n              accountStatus: 'pending_deletion',\n              deleteScheduledAt: user.delete_scheduled_at\n            }\n          };\n          throw error;\n        }\n\n        if (user.status === 'deleted') {\n          const errorMessage = 'تم حذف هذا الحساب';\n          const error = new Error(errorMessage);\n          error.response = {\n            data: {\n              message: 'تم حذف هذا الحساب',\n              message_en: 'Account has been deleted',\n              accountStatus: 'deleted'\n            }\n          };\n          throw error;\n        }\n\n        const userData = {\n          ...user,\n          full_name: user.full_name || null,\n          email: user.email || null,\n          gender: user.gender || null\n        };\n        localStorage.setItem('token', token);\n        // تنظيف أي رسائل حالة قديمة عند تسجيل الدخول الناجح\n        localStorage.removeItem('accountStatusMessage');\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        setCurrentUser(userData);\n        setToken(token);\n        setIsAuthenticated(true);\n        redirectToDashboard(userData);\n        return { success: true };\n      }\n\n      // Normal login with email and password\n      const response = await axios.post('/auth/login', {\n        email: emailOrToken || null,\n        password: passwordOrUser || null\n      });\n\n      if (response.data && response.data.success && response.data.token && response.data.user) {\n        const { token, user } = response.data;\n\n        // التحقق من حالة المستخدم قبل حفظ التوكن\n        if (user.status === 'pending_deletion') {\n          // لا نحفظ التوكن للمستخدم المجدول للحذف\n          const errorMessage = 'هذا الحساب مجدول للحذف';\n          const error = new Error(errorMessage);\n          error.response = {\n            data: {\n              message: 'هذا الحساب مجدول للحذف',\n              message_en: 'Account is scheduled for deletion',\n              accountStatus: 'pending_deletion',\n              deleteScheduledAt: user.delete_scheduled_at\n            }\n          };\n          throw error;\n        }\n\n        if (user.status === 'deleted') {\n          // لا نحفظ التوكن للمستخدم المحذوف\n          const errorMessage = 'تم حذف هذا الحساب';\n          const error = new Error(errorMessage);\n          error.response = {\n            data: {\n              message: 'تم حذف هذا الحساب',\n              message_en: 'Account has been deleted',\n              accountStatus: 'deleted'\n            }\n          };\n          throw error;\n        }\n\n        const userData = {\n          ...user,\n          full_name: user.full_name || null,\n          email: user.email || null,\n          gender: user.gender || null\n        };\n        localStorage.setItem('token', token);\n        // تنظيف أي رسائل حالة قديمة عند تسجيل الدخول الناجح\n        localStorage.removeItem('accountStatusMessage');\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        setCurrentUser(userData);\n        setToken(token);\n        setIsAuthenticated(true);\n        redirectToDashboard(userData);\n        return { success: true };\n      } else {\n        // If login failed, throw error to be caught by the calling component\n        const errorMessage = response.data?.message || 'Login failed';\n        throw new Error(errorMessage);\n      }\n    } catch (error) {\n      console.error('Login error in AuthContext:', {\n        error: error,\n        response: error.response,\n        data: error.response?.data,\n        status: error.response?.status,\n        message: error.message\n      });\n\n      // Re-throw the error as-is since axios interceptor already enhanced it\n      throw error;\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await axios.post('/auth/register', userData);\n\n      // Check if registration was successful (email verification required)\n      if (response.data && response.data.success) {\n        return {\n          success: true,\n          email: response.data.email,\n          message: response.data.message\n        };\n      }\n\n      return {\n        success: false,\n        error: response.data.message || 'Registration failed'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.response?.data?.message || 'Failed to register'\n      };\n    }\n  };\n\n  const updateUser = (userData) => {\n    setCurrentUser(prevUser => ({\n      ...prevUser,\n      ...userData\n    }));\n  };\n\n  const googleLogin = async (credentialResponse) => {\n    try {\n      const response = await axios.post('/auth/google', {\n        credential: credentialResponse.credential\n      });\n\n      if (response.data && response.data.success && response.data.token && response.data.user) {\n        const { token, user } = response.data;\n        const userData = {\n          ...user,\n          full_name: user.full_name || null,\n          email: user.email || null,\n          gender: user.gender || null\n        };\n        localStorage.setItem('token', token);\n        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        setCurrentUser(userData);\n        setToken(token);\n        setIsAuthenticated(true);\n        return { success: true, user: userData };\n      }\n      return { success: false, message: response.data.message };\n    } catch (error) {\n      console.error('Google login error:', error);\n      throw error;\n    }\n  };\n\n  const googleRegister = async ({ credential, role, gender }) => {\n    try {\n      const response = await axios.post('/auth/google/register', {\n        credential,\n        role,\n        gender\n      });\n\n      // Check if registration was successful (email verification required)\n      if (response.data && response.data.success) {\n        return {\n          success: true,\n          email: response.data.email,\n          message: response.data.message\n        };\n      }\n\n      return { success: false, error: response.data.message || 'Registration failed' };\n    } catch (error) {\n      console.error('Google registration error:', error);\n      return {\n        success: false,\n        error: error.response?.data?.message || 'Failed to register with Google'\n      };\n    }\n  };\n\n  const value = {\n    currentUser,\n    token,\n    loading,\n    initialized,\n    isAuthenticated,\n    login,\n    register,\n    handleLogout,\n    updateUser,\n    googleLogin,\n    googleRegister\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,QAAQ,CAAEC,UAAU,CAAEC,SAAS,KAAQ,OAAO,CAC7E,MAAO,CAAAC,KAAK,KAAM,gBAAgB,CAClC,OAASC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE/C,KAAM,CAAAC,WAAW,cAAGR,aAAa,CAAC,IAAI,CAAC,CAEvC,MAAO,MAAM,CAAAS,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGZ,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACa,KAAK,CAAEC,QAAQ,CAAC,CAAGd,QAAQ,CAACe,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC,CACjE,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGlB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACmB,WAAW,CAAEC,cAAc,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACqB,eAAe,CAAEC,kBAAkB,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAAuB,QAAQ,CAAGnB,WAAW,CAAC,CAAC,CAE9BF,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAsB,WAAW,CAAGT,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CACjD,GAAIQ,WAAW,CAAE,CACf;AACArB,KAAK,CAACsB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUH,WAAW,EAAE,CAExE;AACAI,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC1B,KAAK,CAAC2B,GAAG,CAAC,cAAc,CAAC,CACtBC,IAAI,CAACC,QAAQ,EAAI,CAChBJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEG,QAAQ,CAACC,IAAI,CAAC,CAC1D,GAAID,QAAQ,CAACC,IAAI,EAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAIF,QAAQ,CAACC,IAAI,CAACE,IAAI,CAAE,CAChE,KAAM,CAAAC,QAAQ,CAAG,CACf,GAAGJ,QAAQ,CAACC,IAAI,CAACE,IAAI,CACrBE,SAAS,CAAEL,QAAQ,CAACC,IAAI,CAACE,IAAI,CAACE,SAAS,EAAI,IAAI,CAC/CC,KAAK,CAAEN,QAAQ,CAACC,IAAI,CAACE,IAAI,CAACG,KAAK,EAAI,IAAI,CACvCC,MAAM,CAAEP,QAAQ,CAACC,IAAI,CAACE,IAAI,CAACI,MAAM,EAAI,IAAI,CACzCC,MAAM,CAAER,QAAQ,CAACC,IAAI,CAACE,IAAI,CAACK,MAAM,EAAI,QAAQ,CAC7CC,mBAAmB,CAAET,QAAQ,CAACC,IAAI,CAACE,IAAI,CAACM,mBAAmB,EAAI,IACjE,CAAC,CAED;AACA,GAAIL,QAAQ,CAACI,MAAM,GAAK,SAAS,CAAE,CACjCZ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CACpDa,YAAY,CAAC,CAAC,CACd,OACF,CAEA,GAAIN,QAAQ,CAACI,MAAM,GAAK,kBAAkB,CAAE,CAC1CZ,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC,CAClF;AACAa,YAAY,CAAC,CAAC,CACdxB,UAAU,CAAC,KAAK,CAAC,CACjBE,cAAc,CAAC,IAAI,CAAC,CACpB,OACF,CAEAR,cAAc,CAACwB,QAAQ,CAAC,CACxBtB,QAAQ,CAACU,WAAW,CAAC,CACrBF,kBAAkB,CAAC,IAAI,CAAC,CAExB;AACAP,YAAY,CAAC4B,UAAU,CAAC,sBAAsB,CAAC,CACjD,CAAC,IAAM,CACLf,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CACpDa,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,CAAC,CACDE,KAAK,CAACC,KAAK,EAAI,CACdjB,OAAO,CAACiB,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CAEjD;AACA,GAAIA,KAAK,CAACb,QAAQ,EAAIa,KAAK,CAACb,QAAQ,CAACQ,MAAM,GAAK,GAAG,CAAE,CACnD,KAAM,CAAAM,SAAS,CAAGD,KAAK,CAACb,QAAQ,CAACC,IAAI,CAErC;AACA,GAAIa,SAAS,EAAIA,SAAS,CAACC,aAAa,CAAE,CACxC,GAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC1CtB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAEiB,SAAS,CAACC,aAAa,CAAC,CACjF,CAEA;AACA,KAAM,CAAAI,aAAa,CAAG,CACpBC,OAAO,CAAEN,SAAS,CAACM,OAAO,CAAE;AAC5BC,UAAU,CAAEP,SAAS,CAACO,UAAU,CAAE;AAClCN,aAAa,CAAED,SAAS,CAACC,aAAa,CACtCO,iBAAiB,CAAER,SAAS,CAACQ,iBAC/B,CAAC,CAEDvC,YAAY,CAACwC,OAAO,CAAC,sBAAsB,CAAEC,IAAI,CAACC,SAAS,CAACN,aAAa,CAAC,CAAC,CAE3E;AACAT,YAAY,CAAC,CAAC,CAEd;AACAxB,UAAU,CAAC,KAAK,CAAC,CACjBE,cAAc,CAAC,IAAI,CAAC,CACpB,OACF,CACF,CAEAsB,YAAY,CAAC,CAAC,CAChB,CAAC,CAAC,CACDgB,OAAO,CAAC,IAAM,CACbxC,UAAU,CAAC,KAAK,CAAC,CACjBE,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAAC,CACN,CAAC,IAAM,CACLQ,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAC7C;AACAd,YAAY,CAAC4B,UAAU,CAAC,sBAAsB,CAAC,CAC/CzB,UAAU,CAAC,KAAK,CAAC,CACjBE,cAAc,CAAC,IAAI,CAAC,CACtB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAsB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,GAAI7B,KAAK,CAAE,CACT,KAAM,CAAAV,KAAK,CAACwD,IAAI,CAAC,cAAc,CAAC,CAClC,CACF,CAAE,MAAOd,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CAAC,OAAS,CACR9B,YAAY,CAAC4B,UAAU,CAAC,OAAO,CAAC,CAChC/B,cAAc,CAAC,IAAI,CAAC,CACpBE,QAAQ,CAAC,IAAI,CAAC,CACdQ,kBAAkB,CAAC,KAAK,CAAC,CACzB,MAAO,CAAAnB,KAAK,CAACsB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CACrDJ,QAAQ,CAAC,QAAQ,CAAC,CACpB,CACF,CAAC,CAED,KAAM,CAAAqC,mBAAmB,CAAIzB,IAAI,EAAK,CACpC,OAAQA,IAAI,CAAC0B,IAAI,EACf,IAAK,OAAO,CACVtC,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,IAAK,kBAAkB,CACrBA,QAAQ,CAAC,oBAAoB,CAAC,CAC9B,MACF,IAAK,aAAa,CAChBA,QAAQ,CAAC,sBAAsB,CAAC,CAChC,MACF,IAAK,SAAS,CACZA,QAAQ,CAAC,oBAAoB,CAAC,CAC9B,MACF,QACEA,QAAQ,CAAC,GAAG,CAAC,CACjB,CACF,CAAC,CAED,KAAM,CAAAuC,KAAK,CAAG,KAAAA,CAAOC,YAAY,CAAEC,cAAc,GAAK,CACpD,GAAI,CACF;AACA,GAAI,MAAO,CAAAD,YAAY,GAAK,QAAQ,EAAIA,YAAY,CAACE,MAAM,CAAG,EAAE,EAAI,MAAO,CAAAD,cAAc,GAAK,QAAQ,CAAE,CACtG,KAAM,CAAAnD,KAAK,CAAGkD,YAAY,CAC1B,KAAM,CAAA5B,IAAI,CAAG6B,cAAc,CAE3B;AACA,GAAI7B,IAAI,CAACK,MAAM,GAAK,kBAAkB,CAAE,CACtC,KAAM,CAAA0B,YAAY,CAAG,wBAAwB,CAC7C,KAAM,CAAArB,KAAK,CAAG,GAAI,CAAAsB,KAAK,CAACD,YAAY,CAAC,CACrCrB,KAAK,CAACb,QAAQ,CAAG,CACfC,IAAI,CAAE,CACJmB,OAAO,CAAE,wBAAwB,CACjCC,UAAU,CAAE,mCAAmC,CAC/CN,aAAa,CAAE,kBAAkB,CACjCO,iBAAiB,CAAEnB,IAAI,CAACM,mBAC1B,CACF,CAAC,CACD,KAAM,CAAAI,KAAK,CACb,CAEA,GAAIV,IAAI,CAACK,MAAM,GAAK,SAAS,CAAE,CAC7B,KAAM,CAAA0B,YAAY,CAAG,mBAAmB,CACxC,KAAM,CAAArB,KAAK,CAAG,GAAI,CAAAsB,KAAK,CAACD,YAAY,CAAC,CACrCrB,KAAK,CAACb,QAAQ,CAAG,CACfC,IAAI,CAAE,CACJmB,OAAO,CAAE,mBAAmB,CAC5BC,UAAU,CAAE,0BAA0B,CACtCN,aAAa,CAAE,SACjB,CACF,CAAC,CACD,KAAM,CAAAF,KAAK,CACb,CAEA,KAAM,CAAAT,QAAQ,CAAG,CACf,GAAGD,IAAI,CACPE,SAAS,CAAEF,IAAI,CAACE,SAAS,EAAI,IAAI,CACjCC,KAAK,CAAEH,IAAI,CAACG,KAAK,EAAI,IAAI,CACzBC,MAAM,CAAEJ,IAAI,CAACI,MAAM,EAAI,IACzB,CAAC,CACDxB,YAAY,CAACwC,OAAO,CAAC,OAAO,CAAE1C,KAAK,CAAC,CACpC;AACAE,YAAY,CAAC4B,UAAU,CAAC,sBAAsB,CAAC,CAC/CxC,KAAK,CAACsB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUd,KAAK,EAAE,CAClED,cAAc,CAACwB,QAAQ,CAAC,CACxBtB,QAAQ,CAACD,KAAK,CAAC,CACfS,kBAAkB,CAAC,IAAI,CAAC,CACxBsC,mBAAmB,CAACxB,QAAQ,CAAC,CAC7B,MAAO,CAAEF,OAAO,CAAE,IAAK,CAAC,CAC1B,CAEA;AACA,KAAM,CAAAF,QAAQ,CAAG,KAAM,CAAA7B,KAAK,CAACwD,IAAI,CAAC,aAAa,CAAE,CAC/CrB,KAAK,CAAEyB,YAAY,EAAI,IAAI,CAC3BK,QAAQ,CAAEJ,cAAc,EAAI,IAC9B,CAAC,CAAC,CAEF,GAAIhC,QAAQ,CAACC,IAAI,EAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAIF,QAAQ,CAACC,IAAI,CAACpB,KAAK,EAAImB,QAAQ,CAACC,IAAI,CAACE,IAAI,CAAE,CACvF,KAAM,CAAEtB,KAAK,CAAEsB,IAAK,CAAC,CAAGH,QAAQ,CAACC,IAAI,CAErC;AACA,GAAIE,IAAI,CAACK,MAAM,GAAK,kBAAkB,CAAE,CACtC;AACA,KAAM,CAAA0B,YAAY,CAAG,wBAAwB,CAC7C,KAAM,CAAArB,KAAK,CAAG,GAAI,CAAAsB,KAAK,CAACD,YAAY,CAAC,CACrCrB,KAAK,CAACb,QAAQ,CAAG,CACfC,IAAI,CAAE,CACJmB,OAAO,CAAE,wBAAwB,CACjCC,UAAU,CAAE,mCAAmC,CAC/CN,aAAa,CAAE,kBAAkB,CACjCO,iBAAiB,CAAEnB,IAAI,CAACM,mBAC1B,CACF,CAAC,CACD,KAAM,CAAAI,KAAK,CACb,CAEA,GAAIV,IAAI,CAACK,MAAM,GAAK,SAAS,CAAE,CAC7B;AACA,KAAM,CAAA0B,YAAY,CAAG,mBAAmB,CACxC,KAAM,CAAArB,KAAK,CAAG,GAAI,CAAAsB,KAAK,CAACD,YAAY,CAAC,CACrCrB,KAAK,CAACb,QAAQ,CAAG,CACfC,IAAI,CAAE,CACJmB,OAAO,CAAE,mBAAmB,CAC5BC,UAAU,CAAE,0BAA0B,CACtCN,aAAa,CAAE,SACjB,CACF,CAAC,CACD,KAAM,CAAAF,KAAK,CACb,CAEA,KAAM,CAAAT,QAAQ,CAAG,CACf,GAAGD,IAAI,CACPE,SAAS,CAAEF,IAAI,CAACE,SAAS,EAAI,IAAI,CACjCC,KAAK,CAAEH,IAAI,CAACG,KAAK,EAAI,IAAI,CACzBC,MAAM,CAAEJ,IAAI,CAACI,MAAM,EAAI,IACzB,CAAC,CACDxB,YAAY,CAACwC,OAAO,CAAC,OAAO,CAAE1C,KAAK,CAAC,CACpC;AACAE,YAAY,CAAC4B,UAAU,CAAC,sBAAsB,CAAC,CAC/CxC,KAAK,CAACsB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUd,KAAK,EAAE,CAClED,cAAc,CAACwB,QAAQ,CAAC,CACxBtB,QAAQ,CAACD,KAAK,CAAC,CACfS,kBAAkB,CAAC,IAAI,CAAC,CACxBsC,mBAAmB,CAACxB,QAAQ,CAAC,CAC7B,MAAO,CAAEF,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAC,IAAM,KAAAmC,cAAA,CACL;AACA,KAAM,CAAAH,YAAY,CAAG,EAAAG,cAAA,CAAArC,QAAQ,CAACC,IAAI,UAAAoC,cAAA,iBAAbA,cAAA,CAAejB,OAAO,GAAI,cAAc,CAC7D,KAAM,IAAI,CAAAe,KAAK,CAACD,YAAY,CAAC,CAC/B,CACF,CAAE,MAAOrB,KAAK,CAAE,KAAAyB,eAAA,CAAAC,gBAAA,CACd3C,OAAO,CAACiB,KAAK,CAAC,6BAA6B,CAAE,CAC3CA,KAAK,CAAEA,KAAK,CACZb,QAAQ,CAAEa,KAAK,CAACb,QAAQ,CACxBC,IAAI,EAAAqC,eAAA,CAAEzB,KAAK,CAACb,QAAQ,UAAAsC,eAAA,iBAAdA,eAAA,CAAgBrC,IAAI,CAC1BO,MAAM,EAAA+B,gBAAA,CAAE1B,KAAK,CAACb,QAAQ,UAAAuC,gBAAA,iBAAdA,gBAAA,CAAgB/B,MAAM,CAC9BY,OAAO,CAAEP,KAAK,CAACO,OACjB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAP,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAA2B,QAAQ,CAAG,KAAO,CAAApC,QAAQ,EAAK,CACnC,GAAI,CACF,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAA7B,KAAK,CAACwD,IAAI,CAAC,gBAAgB,CAAEvB,QAAQ,CAAC,CAE7D;AACA,GAAIJ,QAAQ,CAACC,IAAI,EAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,CAAE,CAC1C,MAAO,CACLA,OAAO,CAAE,IAAI,CACbI,KAAK,CAAEN,QAAQ,CAACC,IAAI,CAACK,KAAK,CAC1Bc,OAAO,CAAEpB,QAAQ,CAACC,IAAI,CAACmB,OACzB,CAAC,CACH,CAEA,MAAO,CACLlB,OAAO,CAAE,KAAK,CACdW,KAAK,CAAEb,QAAQ,CAACC,IAAI,CAACmB,OAAO,EAAI,qBAClC,CAAC,CACH,CAAE,MAAOP,KAAK,CAAE,KAAA4B,gBAAA,CAAAC,qBAAA,CACd,MAAO,CACLxC,OAAO,CAAE,KAAK,CACdW,KAAK,CAAE,EAAA4B,gBAAA,CAAA5B,KAAK,CAACb,QAAQ,UAAAyC,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBxC,IAAI,UAAAyC,qBAAA,iBAApBA,qBAAA,CAAsBtB,OAAO,GAAI,oBAC1C,CAAC,CACH,CACF,CAAC,CAED,KAAM,CAAAuB,UAAU,CAAIvC,QAAQ,EAAK,CAC/BxB,cAAc,CAACgE,QAAQ,GAAK,CAC1B,GAAGA,QAAQ,CACX,GAAGxC,QACL,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAyC,WAAW,CAAG,KAAO,CAAAC,kBAAkB,EAAK,CAChD,GAAI,CACF,KAAM,CAAA9C,QAAQ,CAAG,KAAM,CAAA7B,KAAK,CAACwD,IAAI,CAAC,cAAc,CAAE,CAChDoB,UAAU,CAAED,kBAAkB,CAACC,UACjC,CAAC,CAAC,CAEF,GAAI/C,QAAQ,CAACC,IAAI,EAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAIF,QAAQ,CAACC,IAAI,CAACpB,KAAK,EAAImB,QAAQ,CAACC,IAAI,CAACE,IAAI,CAAE,CACvF,KAAM,CAAEtB,KAAK,CAAEsB,IAAK,CAAC,CAAGH,QAAQ,CAACC,IAAI,CACrC,KAAM,CAAAG,QAAQ,CAAG,CACf,GAAGD,IAAI,CACPE,SAAS,CAAEF,IAAI,CAACE,SAAS,EAAI,IAAI,CACjCC,KAAK,CAAEH,IAAI,CAACG,KAAK,EAAI,IAAI,CACzBC,MAAM,CAAEJ,IAAI,CAACI,MAAM,EAAI,IACzB,CAAC,CACDxB,YAAY,CAACwC,OAAO,CAAC,OAAO,CAAE1C,KAAK,CAAC,CACpCV,KAAK,CAACsB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUd,KAAK,EAAE,CAClED,cAAc,CAACwB,QAAQ,CAAC,CACxBtB,QAAQ,CAACD,KAAK,CAAC,CACfS,kBAAkB,CAAC,IAAI,CAAC,CACxB,MAAO,CAAEY,OAAO,CAAE,IAAI,CAAEC,IAAI,CAAEC,QAAS,CAAC,CAC1C,CACA,MAAO,CAAEF,OAAO,CAAE,KAAK,CAAEkB,OAAO,CAAEpB,QAAQ,CAACC,IAAI,CAACmB,OAAQ,CAAC,CAC3D,CAAE,MAAOP,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3C,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAAmC,cAAc,CAAG,MAAAC,KAAA,EAAwC,IAAjC,CAAEF,UAAU,CAAElB,IAAI,CAAEtB,MAAO,CAAC,CAAA0C,KAAA,CACxD,GAAI,CACF,KAAM,CAAAjD,QAAQ,CAAG,KAAM,CAAA7B,KAAK,CAACwD,IAAI,CAAC,uBAAuB,CAAE,CACzDoB,UAAU,CACVlB,IAAI,CACJtB,MACF,CAAC,CAAC,CAEF;AACA,GAAIP,QAAQ,CAACC,IAAI,EAAID,QAAQ,CAACC,IAAI,CAACC,OAAO,CAAE,CAC1C,MAAO,CACLA,OAAO,CAAE,IAAI,CACbI,KAAK,CAAEN,QAAQ,CAACC,IAAI,CAACK,KAAK,CAC1Bc,OAAO,CAAEpB,QAAQ,CAACC,IAAI,CAACmB,OACzB,CAAC,CACH,CAEA,MAAO,CAAElB,OAAO,CAAE,KAAK,CAAEW,KAAK,CAAEb,QAAQ,CAACC,IAAI,CAACmB,OAAO,EAAI,qBAAsB,CAAC,CAClF,CAAE,MAAOP,KAAK,CAAE,KAAAqC,gBAAA,CAAAC,qBAAA,CACdvD,OAAO,CAACiB,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,CACLX,OAAO,CAAE,KAAK,CACdW,KAAK,CAAE,EAAAqC,gBAAA,CAAArC,KAAK,CAACb,QAAQ,UAAAkD,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBjD,IAAI,UAAAkD,qBAAA,iBAApBA,qBAAA,CAAsB/B,OAAO,GAAI,gCAC1C,CAAC,CACH,CACF,CAAC,CAED,KAAM,CAAAgC,KAAK,CAAG,CACZzE,WAAW,CACXE,KAAK,CACLI,OAAO,CACPE,WAAW,CACXE,eAAe,CACfyC,KAAK,CACLU,QAAQ,CACR9B,YAAY,CACZiC,UAAU,CACVE,WAAW,CACXG,cACF,CAAC,CAED,mBACE1E,IAAA,CAACC,WAAW,CAAC8E,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAA1E,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC,CAED,MAAO,MAAM,CAAA4E,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGtF,UAAU,CAACM,WAAW,CAAC,CACvC,GAAI,CAACgF,OAAO,CAAE,CACZ,KAAM,IAAI,CAAApB,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAoB,OAAO,CAChB,CAAC,CAED,cAAe,CAAAhF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}