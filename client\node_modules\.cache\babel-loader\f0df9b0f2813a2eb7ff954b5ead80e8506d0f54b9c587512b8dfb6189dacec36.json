{"ast": null, "code": "import i18n from'i18next';import{initReactI18next}from'react-i18next';import meetingIssuesEn from'./adminMeetingIssues.en.json';import meetingIssuesAr from'./adminMeetingIssues.ar.json';const resources={en:{translation:{appName:'Allemnionline',aboutUs:'About Us',wallet:{title:'Wallet',balance:'Balance',currentBalance:'Current Balance',transactionHistory:'Transaction History',allTransactions:'All Transactions',noTransactions:'No transactions found',errorFetchingTransactions:'Error fetching transactions. Please try again.',date:'Date',description:'Description',amount:'Amount',status:'Status',debit:'Debit',credit:'Credit',pending:'Pending',completed:'Completed',failed:'Failed',cancelled:'Cancelled',payment:'Payment',lessonWith:'Lesson with {{teacher}}',lessonFrom:'Lesson from {{student}}',student:'Student',teacher:'Teacher',rowsPerPage:'Rows per page',addMoney:'Add Money',payWithStripe:'Pay with Credit Card',pay:'Pay',depositSuccess:'Deposit successful! Your wallet has been updated.',errorProcessingPayment:'Error processing payment. Please try again.',paypalDeposit:'PayPal Deposit',stripeDeposit:'Stripe Deposit',walletDeposit:'Wallet Deposit'},withdrawal:{title:'Withdrawal',availableBalance:'Available Balance',requestWithdrawal:'Request Withdrawal',withdrawalHistory:'Withdrawal History',noWithdrawals:'No withdrawal requests found',date:'Date',amount:'Amount',paypalEmail:'PayPal Email',status:'Status',actions:'Actions',cancel:'Cancel',submit:'Submit',pending:'Pending',processing:'Processing',completed:'Completed',failed:'Failed',cancelled:'Cancelled',fillAllFields:'Please fill all required fields',minimumAmount:'Minimum withdrawal amount is ${{amount}}',insufficientBalance:'Insufficient balance',requestSubmitted:'Withdrawal request submitted successfully',requestCancelled:'Withdrawal request cancelled successfully',errorSubmitting:'Error submitting withdrawal request',errorCancelling:'Error cancelling withdrawal request',errorFetchingWithdrawals:'Error fetching withdrawal history',paypalEmailHelp:'Enter the PayPal email where you want to receive the payment',processingTime:'Processing time: {{days}} business days',minimumWithdrawal:'Minimum withdrawal',enterOTPTitle:'Enter Verification Code',otpInstructions:'Please enter the 6-digit verification code sent to your email.',otpCode:'Verification Code',verify:'Verify',enterOTP:'Please enter the verification code',errorVerifyingOTP:'Error verifying code',requestCompleted:'Withdrawal request completed successfully',withdrawalCancelled:'Withdrawal request has been cancelled due to multiple failed attempts'},admin:{withdrawalManagement:{title:'Withdrawal Management',statusFilter:'Status Filter',all:'All',date:'Date',teacher:'Teacher',amount:'Amount',paypalEmail:'PayPal Email',status:'Status',notes:'Notes',actions:'Actions',approve:'Approve',reject:'Reject',approveWithdrawal:'Approve Withdrawal',rejectWithdrawal:'Reject Withdrawal',notesOptional:'Notes (Optional)',approveAndProcess:'Approve & Process',processingInfo:'This will process the withdrawal via PayPal Payouts API.',errorFetching:'Error fetching withdrawal requests',errorProcessing:'Error processing withdrawal'}},contactUs:{title:'Contact Us',type:'Message Type',typeQuestion:'Question',typeProblem:'Problem',typeSuggestion:'Suggestion',typePayment:'Payment Issue',typeOther:'Other',subject:'Subject',message:'Message',send:'Send Message',messageSent:'Your message has been sent successfully. We will get back to you soon.',fillAllFields:'Please fill all required fields',sendError:'Error sending message. Please try again later.',startConversation:'Start a conversation with us'},myMessages:{title:'My Messages',subject:'Subject',type:'Type',date:'Date',status:'Status',pending:'Pending',answered:'Answered',noMessages:'No messages found',fetchError:'Error fetching messages',yourMessage:'Your Message',adminReply:'Admin Reply',awaitingReply:'Awaiting reply from admin...'},booking:{title:'Book a Lesson',bookLessonWith:'Book a Lesson with',pricePerLesson:'Price per lesson',instructions:'Booking Instructions',instructionsText:'Select a time slot from the available hours below. Once you confirm your booking, the amount will be deducted from your wallet balance.',selectTimeSlot:'Select a Time Slot',noAvailableSlots:'No available slots for this day',teacher:'Teacher',day:'Day',date:'Date',time:'Time',price:'Price',confirmBooking:'Confirm Booking',confirmAndPay:'Confirm & Pay',bookingSuccessTitle:'Booking Successful!',bookingSuccessMessage:'Your lesson has been booked successfully. You can view your bookings in the My Bookings section.',backToTeacher:'Back to Teacher',currentWeek:'Current Week',weekOf:'Week of',previousWeek:'Previous Week',nextWeek:'Next Week',weekNavigation:'Week Navigation',viewMyBookings:'View My Bookings',bookAgain:'Book Again',bookingFailed:'Booking failed. Please try again.',insufficientBalance:'Insufficient balance. Please add funds to your wallet.',selectDuration:'Select Lesson Duration',duration:'Duration',fullLesson:'Full Lesson ({{duration}} minutes)',halfLesson:'Half Lesson ({{duration}} minutes)',meetingCreated:'Meeting Created',meetingAccessInfo:'You can access this meeting from your Meetings page when it\\'s time for your lesson.',viewMyMeetings:'View My Meetings'},reviews:{title:'Reviews',writeReview:'Write a Review',myReviews:'My Reviews',teacherReviews:'Teacher Reviews',rating:'Rating',comment:'Comment',submit:'Submit Review',update:'Update Review',delete:'Delete Review',confirmDelete:'Are you sure you want to delete this review?',selectTeacher:'Select a Teacher',noTeachers:'We couldn\\'t find any teachers available for review. Please make sure you have completed at least one lesson before writing a review.',noCompletedLessons:'You haven\\'t had lessons with any teachers yet. Please book and complete a lesson first.',allTeachersReviewed:'You have already reviewed all teachers you\\'ve had lessons with. You can update your existing reviews below.',allTeachersReviewedShort:'You have reviewed all your teachers',noReviews:'No reviews yet',yourReview:'Your Review',editReview:'Edit Review',reviewSuccess:'Review submitted successfully',reviewUpdateSuccess:'Review updated successfully',reviewDeleteSuccess:'Review deleted successfully',reviewError:'Error submitting review',reviewRequired:'Please provide a rating',commentPlaceholder:'Share your experience with this teacher...',averageRating:'Average Rating',totalReviews:'{{count}} reviews',oneReview:'1 review',reviewsBy:'Reviews by Students',reviewBy:'Review by',on:'on',stars:'stars',star:'star',outOf5:'out of 5',selectRating:'Select Rating',reviewsFor:'Reviews for',reviewsWritten:'Reviews Written',reviewsReceived:'Reviews Received',viewAll:'View All',filterBy:'Filter by',sortBy:'Sort by',newest:'Newest',oldest:'Oldest',highestRated:'Highest Rated',lowestRated:'Lowest Rated',noComment:'No comment provided',noReviewsWithFilter:'No reviews match the selected filter',teacherReply:'Teacher Reply',replyToReview:'Reply to Review',editReply:'Edit Reply',deleteReply:'Delete Reply',replyPlaceholder:'Write your reply to this review...',sendReply:'Send Reply',updateReply:'Update Reply',replySuccess:'Reply sent successfully',replyUpdateSuccess:'Reply updated successfully',replyDeleteSuccess:'Reply deleted successfully',replyError:'Error sending reply',confirmDeleteReply:'Are you sure you want to delete this reply?',replyRequired:'Please write a reply',sending:'Sending...'},about:{title:'About Us',intro:'Allemnionline is an online educational platform specialized in providing Arabic language teaching services and knowledge related to Arabic culture to learners from around the world, through direct one-on-one lessons delivered by qualified and professional teachers.',mission:'The platform seeks to make learning Arabic accessible and effective, while considering individual differences and special needs of each learner, using the latest educational technological tools.',whatWeOffer:'What We Offer',services:{privateLessons:'Private Arabic language lessons for all levels: from beginners to advanced.',conversationTraining:'Training in conversation, listening, reading, and writing in Arabic.',culturalElements:'Introduction to essential elements of Arabic culture that help understand the language in its natural context.',digitalPlatform:'An integrated digital platform that enables direct communication via video, lesson scheduling, and secure electronic payment.',targetAudience:'Our services are directed to children, adults, professionals, and anyone who wants to learn Arabic for academic, personal, or professional purposes.'},ourMission:'Our Mission',missionText:'To provide distinguished education in Arabic language and related cultural knowledge, with high quality, through direct teaching and modern technologies, in a manner that respects the diversity of learners and their cultural particularities.',contactUs:'Contact Us',contactText:'For any inquiries or comments, please contact us via email:',email:'<EMAIL>'},app:{name:'Allemnionline',copyright:'Allemnionline 2025',tagline:'Designed with love for Arabic education'},brand:{name:'Allemnionline'},common:{switchLanguage:'Switch Language',loading:'Loading...',settings:'Settings',profile:'Profile',cancel:'Cancel',back:'Back',continue:'Continue',confirm:'Confirm',update:'Update',success:'Success',error:'Error',save:'Save',saveAndReturn:'Save & Return',saving:'Saving...',email:'Email Address',notProvided:'Not provided',search:'Search',close:'Close',password:'Password',fullName:'Full Name',name:'Name',confirmPassword:'Confirm Password',showMore:'Show More',showLess:'Show Less',gender:'Gender',male:'Male',female:'Female',submit:'Submit',notSet:'Not Set',actions:'Actions',edit:'Edit',delete:'Delete',view:'View',details:'Details',rowsPerPage:'Rows per page',footer:{copyright:'Allemnionline 2025',tagline:'Designed with love for Arabic education'},currency:'USD',details:'Details'},profile:{title:'Profile',fullName:'Full Name',edit:'Edit Profile',save:'Save Changes',cancel:'Cancel',success:'Profile updated successfully',error:'Error updating profile',updateSuccess:'Profile updated successfully',passwordUpdateSuccess:'Password updated successfully',teachingInfoUpdateSuccess:'Teaching information updated successfully!',editInfo:'Edit Profile Information',editTeachingInfo:'Edit Teaching Info',saving:'Saving...',errors:{update:'Failed to update profile',passwordUpdate:'Failed to update password',passwordMismatch:'Passwords do not match',currentPassword:'Current password is incorrect',fetchError:'Failed to fetch profile data',updateFailed:'Update failed. Please try again.',deleteRequest:'Failed to send delete request',invalidCode:'Invalid verification code',codeRequired:'Verification code is required',cancelDelete:'Failed to cancel account deletion'},basicInfo:'Basic Information',teacherInfo:'Teacher Information',editInfo:'Edit Info',changePassword:'Change Password',email:'Email',gender:'Gender',currentPassword:'Current Password',newPassword:'New Password',confirmPassword:'Confirm Password',editPersonalInfo:'Edit Personal Information',togglePasswordVisibility:'Toggle password visibility',uploadPhoto:'Upload Photo',deleteAccount:'Delete Account',deleteAccountTitle:'Delete Account',deleteAccountWarning:'Are you sure you want to delete your account? This action cannot be undone.',deleteAccountNote:'A verification code will be sent to your email address.',sendDeleteCode:'Send Verification Code',verifyDeleteCode:'Verify Deletion Code',deleteCodeSentMessage:'A verification code has been sent to your email. Please enter it below:',deleteCode:'Verification Code',confirmDelete:'Confirm Deletion',deleteCodeSent:'Verification code sent to your email',deletePending:'Account deletion pending (10 days)',cancelDelete:'Cancel Deletion',deleteCancelled:'Account deletion cancelled successfully',deletePendingAlert:'⚠️ Account Deletion Pending',deletePendingMessage:'Your account is scheduled for deletion. You can cancel this action at any time before the scheduled deletion time.',deleteScheduledFor:'Scheduled deletion time',updateStatus:{pending:'Profile update request is under review',approved:'Profile update approved',rejected:'Profile update request rejected',requestDate:'Request Date',reviewDate:'Review Date',adminNotes:'Admin Notes',pendingNote:'Please wait for admin review. Your current data will remain active until the update is approved.'},teacher:{videoUpload:{title:'Upload Introduction Video',description:'Upload a short video introducing yourself to potential students. This video will be shown on your profile.',requirements:'Video Requirements',formatRequirement:'Allowed formats',sizeRequirement:'Maximum size',lengthRequirement:'Recommended length',maximum:'maximum',minutes:'minutes',selectVideo:'Select Video',upload:'Upload Video',uploading:'Uploading...',success:'Video uploaded successfully!',videoReady:'Your video is ready to be included in your application.',continue:'Continue to Application',delete:'Delete Video',skipForNow:'Skip for now',invalidVideoFormat:'Invalid video format. Please select MP4, WebM, or OGG file.',videoTooSmall:'Video size must be at least 1MB.',videoTooLarge:'Video size must not exceed 100MB.',noVideoSelected:'Please select a video file.',videoDeleteError:'Error deleting video. Please try again.'},uploadVideoNow:'Upload Video Now',videoReady:'Your video is ready to be included in your application',deleteVideo:'Delete Video',availableHoursDescription:'Select the hours you are available to teach. This will help students find suitable time slots for booking lessons with you.',availableHoursApplicationDescription:'Select the hours you will be available to teach if your application is approved. You can update these hours later from your profile.',availableHoursProfileDescription:'Manage your teaching availability to let students know when you are available for lessons.',viewAvailableHours:'View Available Hours',viewAvailableHoursDescription:'Here you can see all your available teaching hours organized by day.',editAvailableHours:'Edit Hours',noHoursForDay:'No available hours for this day',manageAvailableHours:'Manage Available Hours',hoursSavedSuccess:'Your available hours have been saved successfully!',myLessons:'My Lessons',studentName:'Student Name',totalLessons:'Total Lessons',completedLessons:'Completed Lessons',scheduledLessons:'Scheduled Lessons',cancelledLessons:'Cancelled Lessons',noLessonsFound:'No lessons found',errorSavingHours:'Error saving your available hours. Please try again.',errorLoadingHours:'Error loading your available hours. Please try again.',errorParsingHours:'Error parsing your available hours data. Please try again.',saveAndReturn:'Save & Return',timeSlots:'time slots',noAvailableHours:'No available hours selected. Please select your available hours.',selectAll:'Select All',clearAll:'Clear All',timeSlot:'Time Slot',available:'Available',unavailable:'Unavailable',selectAllDays:'Select this time for all days',clearAllDays:'Clear this time for all days',legend:'Legend',cellStatus:'Cell Status',actions:'Actions',changeProfilePicture:'Change profile picture',profile:{title:'Teacher Profile',personalInfo:'Personal Information',teachingInfo:'Teaching Information',phone:'Phone Number',country:'Country',residence:'Place of Residence',nativeLanguage:'Native Language',teachingLanguages:{title:'Teaching Languages',Arabic:'Arabic',English:'English',French:'French',Spanish:'Spanish',Urdu:'Urdu',Turkish:'Turkish',Indonesian:'Indonesian',Malay:'Malay',Bengali:'Bengali',Hindi:'Hindi',Persian:'Persian',German:'German',Italian:'Italian',Portuguese:'Portuguese',Russian:'Russian',Chinese:'Chinese',Japanese:'Japanese',Korean:'Korean',Thai:'Thai',Vietnamese:'Vietnamese',Swahili:'Swahili',Hausa:'Hausa',Somali:'Somali',select:'Select teaching languages',placeholder:'Select one or more languages'},courseTypes:'Course Types',qualifications:'Qualifications',teachingExperience:'Teaching Experience',availableHours:'Available Hours',pricePerLesson:'Price per Lesson',timezone:'Time Zone',paymentMethod:'Payment Method',currency:{usd:'USD',eur:'EUR',gbp:'GBP'},experience:{beginner:'Beginner (0-2 years)',intermediate:'Intermediate (2-5 years)',advanced:'Advanced (5-10 years)',expert:'Expert (10+ years)'}}},genders:{male:'Male',female:'Female'}},nav:{home:'Home',dashboard:'Dashboard',teachers:'Teachers',students:'Students',deletedUsers:'Deleted Users',categories:'Categories',languages:'Languages',applications:'Applications',profileUpdates:'Profile Updates',withdrawalManagement:'Withdrawal Management',findTeacher:'Find Teacher',myTeachers:'My Teachers',meetings:'Meetings',chat:'Chat',login:'Login',register:'Register',logout:'Logout',search:'Search',language:'Language',english:'English',arabic:'Arabic',adminDashboard:'Admin Dashboard',teacherDashboard:'Teacher Dashboard',studentDashboard:'Student Dashboard'},deletedUsers:{title:'Deleted Users',totalDeleted:'Total Deleted',deletedStudents:'Deleted Students',deletedTeachers:'Deleted Teachers',activeUsers:'Active Users',search:'Search',type:'Type',all:'All',students:'Students',teachers:'Teachers',newTeachers:'New Teachers',refresh:'Refresh',user:'User',role:'Role',deletionDate:'Deletion Date',deletionReason:'Deletion Reason',deletedBy:'Deleted By',actions:'Actions',viewDetails:'View Details',restoreUser:'Restore User',permanentDelete:'Permanent Delete',student:'Student',teacher:'Teacher',newTeacher:'New Teacher',notSpecified:'Not Specified',selfDeleted:'Self Deleted',restoreConfirmTitle:'Restore User',restoreConfirmMessage:'Are you sure you want to restore user \"{name}\"?',restoreConfirmNote:'The user will be able to login and access the system again.',cancel:'Cancel',restore:'Restore',restoring:'Restoring...',permanentDeleteTitle:'Permanent Delete',permanentDeleteWarning:'Warning: This action cannot be undone!',permanentDeleteMessage:'Are you sure you want to permanently delete user \"{name}\"?',permanentDeleteNote:'All data associated with this user will be permanently deleted.',permanentDeleteButton:'Permanent Delete',deleting:'Deleting...',rowsPerPage:'Rows per page:',displayedRows:'{from}-{to} of {count}',deletionInfo:'Deletion Information',scheduledDeletion:'Scheduled Deletion'},common:{userInfo:'User Information',teacherInfo:'Teacher Information',studentInfo:'Student Information',gender:'Gender',male:'Male',female:'Female',joinDate:'Join Date',totalLessons:'Total Lessons',totalStudents:'Total Students',rating:'Rating',noRating:'No Rating',totalEarnings:'Total Earnings',currency:'SAR',totalBookings:'Total Bookings',completedLessons:'Completed Lessons',totalSpent:'Total Spent',timezone:'Timezone',notSet:'Not Set',close:'Close'},footer:{platformName:'Allemnionline',designedBy:'Designed with love for Arabic education',followUs:'Follow Us',facebook:'Facebook',twitter:'Twitter',instagram:'Instagram',quickLinks:'Quick Links',about:'About Us',contact:'Contact Us',privacy:'Privacy Policy',terms:'Terms of Service',faq:'FAQ',support:'Support',copyright:'© 2025 Allemnionline',tagline:'Designed with love for Arabic education'},hero:{title:'Arabic in All Languages',subtitle:'A unique educational journey that combines Arabic language and culture',startLearning:'Start Learning',becomeTeacher:'Become a Teacher',imageAlt:'The Holy Quran'},home:{subjects:'Our Courses',subjectsSubtitle:'Discover a diverse range of Islamic and Arabic subjects with our expert instructors',teachers:'teachers',expertTeachers:'Expert Teachers',expertTeachersDesc:'Learn from certified scholars and native speakers',activeStudents:'Active Students',courses:'Courses',coursesDesc:'Comprehensive courses in Islamic studies and Arabic language',studentsDesc:'Students from around the world',whyChooseUs:'Why Choose Us',whyChooseUsSubtitle:'We offer a unique learning experience combining modern technology with authentic Arabic education',meetTeachers:'Meet Our Teachers',meetTeachersSubtitle:'Learn from qualified and specialized instructors in Islamic and Arabic education',features:{quality:'Quality Education',qualityDesc:'Expert instructors specialized in their fields',flexible:'Flexible Learning',flexibleDesc:'Learn anytime, anywhere at your pace',interactive:'Interactive Learning',interactiveDesc:'Interactive lessons and live discussions',certified:'Certified Courses',certifiedDesc:'Get certified in your field of study'},testimonials:'What Our Students Say',testimonialsSubtitle:'Hear from our students about their learning experience with our expert teachers',reviewFor:'Review for',learnMore:'Learn More'},testimonials:{student:'Student',reviewFor:'Review for'},search:{findTeacher:'Find a Teacher',filters:'Filters',subject:'Subject',allSubjects:'All Subjects',language:'Language',allLanguages:'All Languages',priceRange:'Price Range',rating:'Rating',anyRating:'Any Rating',andAbove:'& above',noTeachersFound:'No teachers found',yearsOfExperience:'{{years}} years of experience',bookLesson:'Book a Lesson',perHour:'/hour',subjects:'Subjects',languages:'Languages',searchButton:'Search',clearFilters:'Clear Filters',noTeachersFound:'No teachers found matching your criteria',teachingLanguages:'Teaching Languages',verifiedTeacher:'Verified Teacher',watchIntro:'Watch Intro',watchIntroVideo:'Watch Intro Video',viewProfile:'View Profile',contactAndBook:'Contact & Book'},booking:{bookLessonWith:'Book a Lesson with',pricePerLesson:'Price per lesson',instructions:'Booking Instructions',instructionsText:'Select a day and time slot from the calendar below. Once you confirm your booking, the amount will be deducted from your wallet balance.',selectTimeSlot:'Select a Time Slot',clickToSelectSlot:'Click on available time slots to book',clickToBook:'Click to book this slot',fullHourAvailable:'Full hour available',halfHourOnly:'Half hour only',fullLesson:'Full Lesson (50 min)',halfLesson:'Half Lesson (25 min)',selectDuration:'Select Lesson Duration',selectBookingOption:'Select Booking Option',bookingType:'Booking Type',regularHalfLesson:'Regular Half Lesson (25 min)',regularFullLesson:'Regular Full Lesson (50 min)',secondHalfOnly:'Second Half Only (30 min later)',fullLessonFromSecondHalf:'Full Lesson from Second Half (50 min)',crossHourLesson:'Cross-Hour Lesson (30+30 min)',regularLesson:'Regular Lesson',duration:'Duration',noAvailableSlots:'No available slots for this day',confirmBooking:'Confirm Booking',teacher:'Teacher',day:'Day',date:'Date',time:'Time',price:'Price',confirmAndPay:'Confirm & Pay',bookingSuccessTitle:'Booking Successful!',bookingSuccessMessage:'Your lesson has been booked successfully and the amount has been deducted from your wallet. You can view your bookings in the My Bookings section.',trialLessonEligible:'This is your first lesson with this teacher! Trial lesson price: ${{price}}',backToTeacher:'Back to Teacher',viewMyBookings:'View My Bookings',bookAgain:'Book Again',bookingFailed:'Booking failed. Please try again.',insufficientBalance:'Insufficient balance. Please add funds to your wallet before booking.',currency:'USD',currentWeek:'Current Week',weekOf:'Week of',previousWeek:'Previous Week',nextWeek:'Next Week',weekNavigation:'Week Navigation',selectTimeRange:'Select Time Range',startTime:'Start Time',endTime:'End Time',lessonDuration:'Lesson Duration',from:'From',to:'To',availableSlot:'Available Slot',selectDuration:'Select Duration'},bookings:{title:'My Bookings',weeklyTitle:'This Week\\'s Bookings',description:'View and manage your scheduled lessons with teachers.',weeklyDescription:'View your bookings for this week in a calendar format.',bookingDetails:'Booking Details',noBookings:'You have no bookings yet.',fetchError:'Error fetching bookings. Please try again later.',teacher:'Teacher',date:'Date',time:'Time',timeRange:'Time Range',duration:'Duration',minutes:'minutes',status:'Status',statusValues:{scheduled:'Scheduled',completed:'Completed',cancelled:'Cancelled',issue_reported:'Issue Reported',ongoing:'Ongoing'},price:'Price',cancel:'Cancel',confirmCancel:'Confirm Cancellation',cancelWarning:'Are you sure you want to cancel this booking? The amount will be refunded to your wallet.',cancellationReason:'Cancellation Reason (Optional)',cancellationReasonPlaceholder:'Please provide a reason for cancelling this booking...',confirmCancelButton:'Yes, Cancel Booking',cancelling:'Cancelling...',cancelSuccess:'Booking cancelled successfully and amount refunded to your wallet',cancelError:'Error cancelling booking. Please try again.',availableSlot:'Available Time Slot',currentTime:'Current Time',pastSlot:'Past Time',takeBreak:'Take Break',takeBreakTitle:'Take This Time as Break',takeBreakMessage:'Do you want to take this time as a break?',takeBreakNote:'This time will be hidden from students for this week only',breakTakenSuccess:'Break taken successfully',breakTakenError:'Error taking break',break:'Break',clickToTakeBreak:'Click to take break',cancelBreakTitle:'Cancel Break Time',cancelBreakMessage:'Do you want to cancel this break time?',cancelBreakNote:'This time will become available to students again',breakCancelledSuccess:'Break cancelled successfully',breakCancelledError:'Error cancelling break',reschedule:'Reschedule',rescheduleTitle:'Reschedule Booking',rescheduleDescription:'Select a new time slot for this booking from your available hours.',currentBooking:'Current Booking',selectDay:'Select Day',chooseDay:'Choose a day',selectTime:'Select Time',chooseTime:'Choose a time',loadingDays:'Loading available days...',loadingTimes:'Loading available times...',noAvailableDays:'No available days found for the next 30 days.',noAvailableTimes:'No available times for this day.',availableTimes:'available times',rescheduleReason:'Reschedule Reason (Optional)',rescheduleReasonPlaceholder:'Please provide a reason for rescheduling this booking...',rescheduleSuccess:'Booking rescheduled successfully',rescheduleError:'Error rescheduling booking. Please try again.',fetchDaysError:'Error fetching available days. Please try again.',fetchTimesError:'Error fetching available times. Please try again.',currentBooking:'Current Booking',selectNewDate:'Select New Date',selectNewTime:'Select New Time',selectDateFirst:'Please select a date from the calendar first',selectedTime:'Selected Time',confirmReschedule:'Confirm Reschedule',availableSlots:'slots available',backToSelectDay:'Back to Select Day'},teacherDetails:{backToSearch:'Back to Search',teacherNotFound:'Teacher not found',errorFetching:'Error fetching teacher details',reviews:'reviews',watchIntroVideo:'Watch Intro Video',hideIntroVideo:'Hide Intro Video',bookLesson:'Book a Lesson',teachingSubjects:'Teaching Subjects',languages:'Languages',nativeLanguage:'Native Language',teachingLanguages:'Teaching Languages',qualifications:'Qualifications',yearsOfExperience:'{{years}} years of experience',studentReviews:'Student Reviews',noReviews:'No reviews yet',contactInfo:'Contact Information',email:'Email',phone:'Phone',location:'Location',country:'Country',residence:'City of Residence',timezone:'Timezone',experience:'Experience',memberSince:'Member since',availableHours:'Available Hours',paymentInfo:'Payment Information',pricePerLesson:'Price per Lesson',paymentMethod:'Payment Method',cv:'CV/Resume',introVideo:'Introduction Video',courseTypes:'Course Types'},chat:{conversations:'Conversations',messages:'Messages',typeMessage:'Type a message...',send:'Send',noMessages:'No messages',noChats:'No conversations',startChat:'Start new conversation',loading:'Loading...',today:'Today',yesterday:'Yesterday',online:'Online',offline:'Offline',sent:'Sent',delivered:'Delivered',read:'Read',failed:'Failed to send',retry:'Retry',onlyStudents:'Only students can start a chat with teachers',no_conversations:'No conversations',type_message:'Type your message here...',select_conversation:'Select a conversation to start',last_seen:'Last seen',typing:'typing...',deleteConfirmTitle:'Delete Conversation',deleteConfirmMessage:'Are you sure you want to delete the conversation with {{name}}?',conversationDeleted:'Conversation deleted successfully',conversationDeletedByOther:'This conversation has been deleted by the other participant',deleteConversationError:'Error deleting conversation',messageDeleted:'Message deleted successfully',messageEdited:'Message edited successfully',editError:'Error editing message',deleteError:'Error deleting message',noConversations:'No conversations found',chat:'Chat'},auth:{login:'Login',logout:'Logout',logoutConfirmTitle:'Confirm Logout',logoutConfirmMessage:'Are you sure you want to log out?',welcomeBack:'Welcome Back!',email:'Email Address',forgotPassword:'Forgot Password?',forgotPasswordInstructions:'Enter your email address and we\\'ll send you a verification code to reset your password.',sendResetCode:'Send Reset Code',backToLogin:'Back to Login',resetCodeSent:'Reset code sent to your email',resetRequestFailed:'Failed to send reset code',verifyCode:'Verify Code',verifyCodeInstructions:'Enter the 6-digit verification code sent to your email:',verificationCode:'Verification Code',verifyAndContinue:'Verify & Continue',resendCode:'Resend Code',changeEmail:'Change Email',verifyEmail:'Verify Email',verificationCodeSentTo:'We sent a verification code to',verify:'Verify',verifying:'Verifying...',verificationCodeSent:'Verification code sent successfully',verificationFailed:'Email verification failed',resendFailed:'Failed to resend verification code',resendCodeIn:'Resend code in',sending:'Sending...',invalidCode:'Please enter a valid 6-digit code',fillAllFields:'Please fill in all fields',loginFailed:'Login failed. Please check your credentials.',invalidCredentials:'Invalid email or password. Please try again.',emailNotFound:'This email address is not registered. Please check your email or sign up.',wrongPassword:'Incorrect password. Please try again.',accountDeleted:'This account has been deleted and cannot be used to login.',invalidCode:'Invalid verification code',verificationFailed:'Verification failed',resetCodeResent:'Reset code has been resent to your email',resetPassword:'Reset Password',resetPasswordInstructions:'Create a new password for your account',newPassword:'New Password',confirmPassword:'Confirm Password',passwordResetSuccess:'Password Reset Successful!',passwordResetSuccessMessage:'Your password has been reset successfully.',redirectingToLogin:'Redirecting to login page...',resetPasswordFailed:'Failed to reset password',password:'Password',signIn:'Sign In',or:'OR',noAccount:\"Don't have an account?\",signUp:'Sign Up',continueWithGoogle:'Continue with Google',googleSignInError:'Google sign-in failed. Please try again.',signInWithGoogle:'Continue with Google',loginError:'Login failed. Please check your credentials.',accountNotFound:'Account not found',invalidRole:'Invalid user role',chooseAccountType:'Join Our Community',registerDescription:'Begin your journey in Arabic education. Choose your role and become part of our growing community of learners and educators.',registerAsTeacher:'Teach with Us',teacherDescription:'Share your knowledge and expertise in Islamic studies and Arabic language. Help others learn and grow while building your teaching career.',registerAsStudent:'Learn with Us',studentDescription:'Start your learning journey in Islamic studies and Arabic language with expert teachers from around the world.',alreadyHaveAccount:'Already have an account?',getStarted:'Get Started',studentRegistration:'Student Registration',teacherRegistration:'Teacher Registration',joinOurCommunity:'Join our community of learners',joinOurTeachers:'Share your knowledge with others',createAccount:'Create Account',registrationError:'Registration failed. Please try again.',selectGender:'Please select your gender',gender:'Gender',male:'Male',female:'Female'},courseTypes:{Islamic_Law:'Islamic Law',Fiqh:'Fiqh',Quran:'Quran',Tajweed:'Tajweed',Islamic_History:'Islamic History',Arabic_Grammar:'Arabic Grammar',Arabic_Speaking:'Arabic Speaking',Arabic_Writing:'Arabic Writing',Islamic_Ethics:'Islamic Ethics',Hadith:'Hadith',Aqeedah:'Islamic Creed'},languages:{Arabic:'Arabic',English:'English',French:'French',Spanish:'Spanish'},gender:{male:'Male',female:'Female'},dashboard:{welcome:'Welcome',unauthorized:'Unauthorized access to dashboard',fetchError:'Error fetching data',totalStudents:'Total Students',totalClasses:'Total Classes',averageRating:'Average Rating',totalEarnings:'Total Earnings',categories:'Teaching Categories',recentBookings:'Recent Bookings',recentReviews:'Recent Reviews',learningProgress:'Learning Progress',quickActions:'Quick Actions',recommendedTeachers:'Recommended Teachers',upcomingLessons:'Upcoming Lessons',noUpcomingLessons:'No upcoming lessons scheduled',updateProfile:'Update Profile',findTeacher:'Find a Teacher',browseCourses:'Browse Courses',viewAll:'View All',incompleteProfile:'Your Profile is Incomplete',completeProfileMessage:'Please complete your profile to access all features and find the perfect teacher for your learning journey.',completeProfileToAccess:'You need to complete your profile to access this page and its features.',completeProfile:'Complete Profile',updateProfile:'Update Profile',completeProfileNow:'Complete Profile Now'},regions:{middle_east:'Middle East',north_africa:'North Africa',sub_saharan_africa:'Sub-Saharan Africa',south_asia:'South Asia',southeast_asia:'Southeast Asia',central_asia:'Central Asia',europe:'Europe',north_america:'North America',south_america:'South America',east_asia:'East Asia',oceania:'Oceania',others:'others'},teacher:{phoneOptional:'Optional',nativeLanguage:'Native Language',teachingLanguages:{title:'Teaching Languages',Arabic:'Arabic',English:'English',French:'French',Spanish:'Spanish',Urdu:'Urdu',Turkish:'Turkish',Indonesian:'Indonesian',Malay:'Malay',Bengali:'Bengali',Hindi:'Hindi',Persian:'Persian',German:'German',Italian:'Italian',Portuguese:'Portuguese',Russian:'Russian',Chinese:'Chinese',Japanese:'Japanese',Korean:'Korean',Thai:'Thai',Vietnamese:'Vietnamese',Swahili:'Swahili',Hausa:'Hausa',Somali:'Somali',select:'Select teaching languages',placeholder:'Select one or more languages'},qualifications:'Qualifications',experience:'Experience',subjects:'Subjects',profile:{title:'Teacher Profile',personalInfo:'Personal Information',teachingInfo:'Teaching Information',phone:'Phone Number',country:'Country',residence:'Place of Residence',nativeLanguage:'Native Language',teachingLanguages:{title:'Teaching Languages',Arabic:'Arabic',English:'English',French:'French',Spanish:'Spanish',Urdu:'Urdu',Turkish:'Turkish',Indonesian:'Indonesian',Malay:'Malay',Bengali:'Bengali',Hindi:'Hindi',Persian:'Persian',German:'German',Italian:'Italian',Portuguese:'Portuguese',Russian:'Russian',Chinese:'Chinese',Japanese:'Japanese',Korean:'Korean',Thai:'Thai',Vietnamese:'Vietnamese',Swahili:'Swahili',Hausa:'Hausa',Somali:'Somali',select:'Select teaching languages',placeholder:'Select one or more languages'},courseTypes:'Course Types',qualifications:'Qualifications',teachingExperience:'Teaching Experience',availableHours:'Available Hours',pricePerLesson:'Price per Lesson',timezone:'Time Zone',paymentMethod:'Payment Method',currency:{usd:'USD',eur:'EUR',gbp:'GBP'},experience:{beginner:'Beginner (0-2 years)',intermediate:'Intermediate (2-5 years)',advanced:'Advanced (5-10 years)',expert:'Expert (10+ years)'}},application:{title:'Teacher Application',submit:'Submit Application',success:'Application submitted successfully',error:'Error submitting application',errorFetchingData:'Error fetching data',errorFetchingCategories:'Error fetching categories',statusCardTitle:'Application Status',edit:'Edit Application',editDescription:'You can edit your application details, but you will need to be approved again by the admin.',warningTitle:'Warning: Application Edit',warningMessage:'Editing your application will send a profile update request that requires admin approval.',warningDescription1:'If you proceed, you will be redirected to the application form where you can edit your details.',warningDescription2:'You will continue teaching with your current data until the admin approves your update request.',warningConfirmation:'Are you sure you want to continue?',confirmEdit:'Yes, Edit Application',editApplication:{title:'Edit Application',description:'Update your application details. Changes will be reviewed by admin before being applied.',warning:'Your current data will remain active until the admin approves your changes.'},changeVideo:'Change Video',status:{pending:'Pending Review',approved:'Approved',rejected:'Rejected'},statusMessage:{pending:'Your application is being reviewed',approved:'Congratulations! Your application has been approved',rejected:'Unfortunately, your application has been rejected',default:'Application status updated'},applicationNextSteps:{approved:{title:'Next Steps',steps:['Please log out from your account','Log in again to activate your full teacher privileges']}}},country:'Country',residence:'City of Residence',nativeLanguage:'Native Language',teachingLanguages:'Teaching Languages',courseTypes:'Course Types',qualifications:'Qualifications',qualificationsPlaceholder:'Enter your educational qualifications and certifications',teachingExperience:'Years of Teaching Experience',introVideo:'Introduction Video',introVideoUrl:'Introduction Video URL',introVideoUrlPlaceholder:'https://...',noIntroVideo:'No introduction video available',videoLinkTab:'Video Link',videoFileTab:'Upload Video',videoLink:'Link',videoUpload:'Upload',videoLinkHelp:'Enter a YouTube, Vimeo, or other video platform URL',videoLinkNote:'Share a link to your introduction video from a supported platform',videoUploadHelp:'Upload your introduction video directly to our platform',videoUploadPlaceholder:'Upload Video',videoSelected:'Selected video',videoUploadError:'Error uploading video',formHasErrors:'Please correct the errors in the form',allowedFormats:'Allowed formats',maxFileSize:'Maximum file size',recommendedPlatforms:'Recommended platforms',cv:'CV/Resume',cvPlaceholder:'write your CV',cvHelperText:'Write a brief summary of your qualifications, experience, and teaching approach (max 2000 characters)',characters:'characters',profilePicture:'Profile Picture',profilePicturePlaceholder:'Upload your profile picture',profilePictureRequired:'Profile picture is required',availableHours:'Available Teaching Hours',availableHoursDescription:'Select the available times for teaching on each day of the week',availableHoursProfileDescription:'Manage your teaching availability to let students know when you are available for lessons',viewAvailableHours:'View Available Hours',viewAvailableHoursDescription:'Review your current teaching schedule and availability',noAvailableHours:'No available hours have been set yet',timeSlot:'Time Slot',timeSlots:'time slots',available:'Available',unavailable:'Not Available',editAvailableHours:'Edit Available Hours',weeklySchedule:'Weekly Schedule',scheduleDescription:'Your teaching availability throughout the week',time:'Time',legend:'Legend',quickStats:'Quick Statistics',totalSlots:'Total Time Slots',hoursPerWeek:'Hours per Week',activeDays:'Active Days',pricePerLesson:'Price per Lesson',pricePerLessonPlaceholder:'Enter price in USD',trialLessonPrice:'Trial lesson price',trialLessonPricePlaceholder:'Enter trial lesson price in USD',trialPriceLessThanRegular:'Trial lesson price must be less than or equal to regular lesson price',timezone:'Timezone',paymentMethod:'Payment Method',phone:'Phone Number',phoneHelp:'Include country code (e.g., +1, +44, +966)',selectedHours:'hours selected',selectAll:'Select All',required:'Required field',formHasErrors:'Please correct the errors in the form',priceRange:'Price must be between $3 and $100',yourEarnings:'Your earnings after commission:',invalidUrl:'Please enter a valid URL',invalidPhone:'Please enter a valid phone number',invalidPrice:'Please enter a valid price',uploadVideoNow:'Upload Video Now',allowedFormats:'Allowed formats',maxFileSize:'Maximum file size',videoFormats:'MP4, WebM, OGG',maxVideoSize:'100MB',videoRequired:'An introduction video is required for your application',commitment:'Teacher Commitment',commitmentDescription:'Please read and agree to the following commitment',commitmentProfileDescription:'View and manage your commitment status and details',commitmentRequired:'You must agree to the commitment to continue',commitmentStatus:{accepted:'Commitment accepted',pending:'Commitment pending approval',rejected:'Commitment rejected'},commitmentTitle:'Teacher Commitment',commitmentAccepted:'Commitment accepted',readCommitment:'Read and agree to the commitment',accept:'I Agree',reject:'I Decline',manageAvailableHours:'Manage Available Hours',weeklyBookings:'This Week\\'s Student Bookings',weeklyBookingsDescription:'View your student bookings for this week in a calendar format.',fileTypes:{image:'Supported formats: JPG, PNG (max 5MB)',document:'Supported formats: PDF, DOC, DOCX (max 10MB)'},submitComplaint:'Submit Complaint',complaintReason:'Reason for Complaint',complaintType1:'Student attended but commission not transferred',complaintType2:'Student did not attend at all',complaintDetails:'Complaint Details (optional)',complaintDetailsPlaceholder:'Write any additional details if needed...',complaintStatus:'Complaint Status',complaintStatusValues:{pending:'Pending',resolved:'Resolved'}},role:{admin:'Admin',teacher:'Teacher',student:'Student'},validation:{required:'This field is required',email:'Please enter a valid email address',password:{min:'Password must be at least {{min}} characters',max:'Password must be at most {{max}} characters',match:'Passwords do not match'}},days:{monday:'Monday',tuesday:'Tuesday',wednesday:'Wednesday',thursday:'Thursday',friday:'Friday',saturday:'Saturday',sunday:'Sunday',mondayShort:'Mon',tuesdayShort:'Tue',wednesdayShort:'Wed',thursdayShort:'Thu',fridayShort:'Fri',saturdayShort:'Sat',sundayShort:'Sun'},hours:{hour1:'1:00 AM',hour2:'2:00 AM',hour3:'3:00 AM',hour4:'4:00 AM',hour5:'5:00 AM',hour6:'6:00 AM',hour7:'7:00 AM',hour8:'8:00 AM',hour9:'9:00 AM',hour10:'10:00 AM',hour11:'11:00 AM',hour12:'12:00 PM',hour13:'1:00 PM',hour14:'2:00 PM',hour15:'3:00 PM',hour16:'4:00 PM',hour17:'5:00 PM',hour18:'6:00 PM',hour19:'7:00 PM',hour20:'8:00 PM',hour21:'9:00 PM',hour22:'10:00 PM',hour23:'11:00 PM',hour24:'12:00 AM'},student:{profile:{title:'Student Profile',complete:'Complete Your Profile',languagePreferences:'Language Preferences',personalInfo:'Personal Information',learningPreferences:'Learning Preferences',nativeLanguage:'Native Language',preferredIslamicLanguage:'Preferred Language for Learning Islam',preferredArabicLanguage:'Preferred Language for Learning Arabic',islamLearningLanguage:'Language for Learning Islam',arabicLearningLanguage:'Language for Learning Arabic',age:'Age',country:'Country',timezone:'Timezone',arabicLevel:'Arabic Proficiency Level',privateTutoring:'I am interested in private tutoring',preferGroup:'NO',preferPrivate:'YES',updateSuccess:'Profile updated successfully',editInfo:'Edit Profile Information',levels:{beginner:'Beginner',intermediate:'Intermediate',advanced:'Advanced'},success:'Profile updated successfully! Redirecting to dashboard...'},myTeachers:'My Teachers',noTeachersYet:'You have no teachers yet.',lessonsTaken:'Lessons Taken'},admin:{dashboard:{title:'Dashboard',welcomeMessage:'Welcome to Admin Dashboard',overview:'Here\\'s an overview of your platform statistics and recent activities.',totalTeachers:'Total Teachers',pendingApplications:'Pending Applications',totalStudents:'Total Students',totalRevenue:'Total Revenue',totalCourseCategories:'Total Categories',totalLanguages:'Total Languages',totalBookings:'Total Bookings',bookingStats:'Booking Statistics',teachersByLanguage:'Teachers by Language',studentsByCountry:'Students by Country',recentApplications:'Recent Applications',recentStudents:'Recent Students',viewAll:'View All',viewDetails:'View Details',noApplications:'No recent applications',noStudents:'No recent students',noStudentsByCountry:'No student country data available',noTeachersByLanguage:'No teacher language data available',noBookingStats:'No booking data available',completed:'Completed',cancelled:'Cancelled',pending:'Pending',fetchError:'Error fetching dashboard data'},meetingSessions:{title:'Meeting Time Reports',overview:'Detailed view of teachers and students join/leave times in meetings',totalMeetings:'Total Meetings',totalTime:'Total Time',teacherTime:'Teacher Time',studentTime:'Student Time',filterResults:'Filter Results',userType:'User Type',all:'All',teacher:'Teacher',student:'Student',reset:'Reset',sessions:'Meeting Sessions',user:'User',type:'Type',meeting:'Meeting',joinTime:'Join Time',leaveTime:'Leave Time',duration:'Duration',status:'Status',active:'Active',ended:'Ended'},meetingIssues:{title:'Meeting Issues'},messages:{title:'Messages',from:'From',type:'Type',subject:'Subject',date:'Date',status:'Status',pending:'Pending',answered:'Answered',all:'All Messages',view:'View',reply:'Reply',delete:'Delete',search:'Search messages...',noMessages:'No messages found',fetchError:'Error fetching messages',replyTo:'Reply to',originalMessage:'Original Message',yourReply:'Your Reply',sendReply:'Send Reply',replySent:'Reply sent successfully',replyError:'Error sending reply',confirmDelete:'Confirm Delete',deleteWarning:'Are you sure you want to delete this message? This action cannot be undone.',deleteSuccess:'Message deleted successfully',deleteError:'Error deleting message'},emailTemplates:{title:'Email Templates',description:'Manage email templates for various system notifications.',instructions:'Edit the templates below to customize the emails sent to users. You can use HTML and variables to personalize the content.',approvalTemplate:'Application Approval Template',rejectionTemplate:'Application Rejection Template',save:'Save Template',saveSuccess:'Template saved successfully',saveError:'Error saving template',fetchError:'Error fetching templates',restoreDefault:'Restore Default',preview:'Preview',enterTemplate:'Enter template content here...',availableVariables:'Available Variables',variablesHelp:'These variables will be replaced with actual values when the email is sent.',testEmail:'Test Email',testEmailPlaceholder:'Enter email address to test',sendTest:'Send Test Email',testEmailSuccess:'Test email sent successfully',testEmailError:'Error sending test email',variables:{teacherName:'Teacher\\'s full name',teacherEmail:'Teacher\\'s email address',dashboardUrl:'URL to the teacher dashboard',rejectionReason:'Reason for application rejection'}},earnings:{title:'Platform Earnings',totalCommission:'Total Commission',totalLessons:'Total Lessons',totalRevenue:'Total Revenue',avgCommissionRate:'Avg Commission Rate',startDate:'Start Date',endDate:'End Date',filter:'Filter',clearFilter:'Clear Filter',date:'Date',meeting:'Meeting',teacher:'Teacher',student:'Student',lessonAmount:'Lesson Amount',commissionRate:'Commission Rate',commissionAmount:'Commission Amount',teacherEarnings:'Teacher Earnings',errorFetching:'Error fetching earnings data'},passwordManagement:{title:'Password Management',description:'Manage password reset requests and user passwords',searchPlaceholder:'Search by name or email',user:'User',email:'Email',role:'Role',requestDate:'Request Date',status:'Status',actions:'Actions',resetPassword:'Reset Password',resetPasswordFor:'Reset Password for {name}',newPassword:'New Password',generatePassword:'Generate Password',resetSuccess:'Password reset successfully for {email}',resetError:'Error resetting password',noRequests:'No password reset requests found',statusPending:'Pending',statusCompleted:'Completed',sendTestEmail:'Send Test Email',testEmailAddress:'Test Email Address',testEmailDescription:'Send a test password reset email to verify the email template and delivery',sendEmail:'Send Email',testEmailSent:'Test email sent successfully to {email}',testEmailError:'Error sending test email'},profile:{personalInfo:'Personal Information',changePassword:'Change Password',currentPassword:'Current Password',newPassword:'New Password',confirmNewPassword:'Confirm New Password',updateSuccess:'Profile updated successfully',updateError:'Error updating profile',passwordSuccess:'Password changed successfully',passwordError:'Error changing password',imageSuccess:'Profile image updated successfully',imageError:'Error updating profile image'},teacherRole:{platform_teacher:'Platform Teacher',new_teacher:'New Teacher'},teachers:{title:'Teachers',searchPlaceholder:'Search teachers...',name:'Name',email:'Email',gender:'Gender',role:'Role',actions:'Actions',viewDetails:'View Details',teacherDetails:'Teacher Details',personalInfo:'Personal Information',deleteConfirm:'Are you sure you want to delete this teacher?',deleteSuccess:'Teacher deleted successfully',deleteError:'Error deleting teacher',fetchError:'Error fetching teachers'},students:{title:'Students',searchPlaceholder:'Search students...',name:'Name',email:'Email',gender:'Gender',country:'Country',age:'Age',timezone:'Timezone',actions:'Actions',viewDetails:'View Details',studentDetails:'Student Details',personalInfo:'Personal Information',learningPreferences:'Learning Preferences',nativeLanguage:'Native Language',islamLearningLanguage:'Islam Learning Language',arabicLearningLanguage:'Arabic Learning Language',arabicProficiencyLevel:'Arabic Proficiency Level',privateTutoring:'Private Tutoring',profileCompleted:'Profile Completed',deleteConfirm:'Are you sure you want to delete this student?',deleteSuccess:'Student deleted successfully',deleteError:'Error deleting student',fetchError:'Error fetching students',noStudents:'No students found',delete:'Delete',proficiencyLevels:{beginner:'Beginner',intermediate:'Intermediate',advanced:'Advanced'}},categories:{title:'Categories',addNew:'Add New Category',editTitle:'Edit Category',addTitle:'Add Category',name:'Name',description:'Description',createdBy:'Created By',updateSuccess:'Category updated successfully',createSuccess:'Category created successfully',deleteSuccess:'Category deleted successfully',deleteConfirm:'Are you sure you want to delete this category?',fetchError:'Error fetching categories',saveError:'Error saving category',deleteError:'Error deleting category',unauthorized:'Unauthorized access',invalidId:'Invalid category ID'},languages:{title:'Languages',addNew:'Add New Language',editTitle:'Edit Language',addTitle:'Add Language',name:'Language Name',nameRequired:'Language name is required',unauthorized:'You are not authorized to perform this action',fetchError:'Error fetching languages',createSuccess:'Language created successfully',createError:'Error creating language',updateSuccess:'Language updated successfully',updateError:'Error updating language',deleteSuccess:'Language deleted successfully',deleteError:'Error deleting language',deleteConfirm:'Are you sure you want to delete this language?',invalidId:'Invalid language ID',saveError:'Error saving language'},applications:{searchPlaceholder:'Search applications...',filterByStatus:'Filter by status',name:'Name',email:'Email',phone:'Phone',country:'Country',languages:'Languages',status:'Status',actions:'Actions',statuses:{pending:'Pending',approved:'Approved',rejected:'Rejected'},title:'Teacher Applications',allStatuses:'All Statuses',viewDetails:'View Details',approve:'Approve',reject:'Reject',applicationDetails:'Application Details',personalInfo:'Personal Information',teachingInfo:'Teaching Information',documents:'Documents',nativeLanguage:'Native Language',teachingLanguages:'Teaching Languages',qualifications:'Qualifications',experience:'Experience',pricePerLesson:'Price per Lesson',viewVideo:'View Introduction Video',viewCV:'View CV',basicInfo:'Basic Info',teachingDetails:'Teaching Details',schedule:'Schedule',location:'Location',applicationDate:'Application Date',courseTypes:'Course Types',pricing:'Pricing',paymentMethod:'Payment Method',introVideo:'Introduction Video',cv:'CV/Resume',availableHours:'Available Hours',yearsOfExperience:'{{years}} years of experience'}},menu:{dashboard:'Dashboard',meetings:'Meetings',profile:'Profile',chat:'Chat',platformPolicy:'Platform Policy'},meetings:{title:'Meetings',myMeetings:'My Meetings',description:'View and manage your scheduled meetings with teachers',noMeetings:'No meetings scheduled',noMeetingsDescription:'You don\\'t have any meetings scheduled yet. Book a lesson to get started!',teacher:'Teacher',date:'Date',time:'Time',fetchError:'Error fetching meetings',with:'with',createNew:'Create New Meeting',meetingName:'Meeting Name',meetingDate:'Meeting Date',duration:'Duration',minutes:'Minutes',create:'Create Meeting',start:'Start Meeting',join:'Join Meeting',notStarted:'Meeting not started yet',ended:'Meeting ended',joinError:'Error joining meeting',cancel:'Cancel Meeting',copyLink:'Copy Room Code',linkCopied:'Room code copied to clipboard',cancelSuccess:'Meeting cancelled successfully',cancelError:'Error cancelling meeting',createSuccess:'Meeting created successfully',createError:'Error creating meeting',halfLesson:'Half Lesson',fullLesson:'Full Lesson',consecutiveSlots:'Consecutive Slots',currency:'USD',status:{pending:'Pending',ongoing:'Ongoing',completed:'Completed',cancelled:'Cancelled',scheduled:'Scheduled'},meetingCompleted:'Meeting completed successfully',errorCompletingMeeting:'Error completing the meeting',participant:'Participant',you:'You',sessionTime:'Session Time',remainingTime:'Time Remaining',timeUp:'Time Up',timeUpTitle:'Meeting Time Ended',timeUpMessage:'The scheduled meeting time has ended. The meeting will now be closed automatically.',timeUpDescription:'You will be redirected back to your bookings page.',backToBookings:'Back to Bookings',active:'Active',stopped:'Stopped',validation:{nameRequired:'Meeting name is required',dateRequired:'Meeting date is required',durationRequired:'Duration is required',allFieldsRequired:'All fields are required'},student:'Student',dateTime:'Date & Time',amount:'Amount',actions:'Actions'},privacy:{title:'Privacy Policy',intro:'This Privacy Policy explains how Allemnionline (\"we\", \"our\", or \"us\") collects, uses, and protects the personal information of users (\"you\") who visit our website or use our educational services.',section1:{title:'1. Information We Collect',subtitle:'We may collect the following types of personal information:',item1:'Name, email address, and phone number',item2:'Payment and billing details',item3:'User account information (e.g., language level, lesson history)'},section2:{title:'2. How We Use Your Information',subtitle:'We use your information to:',item1:'Provide and improve our educational services',item2:'Manage scheduling and payments',item3:'Communicate with you regarding your lessons and account',item4:'Ensure platform security and prevent fraud',item5:'Comply with legal and regulatory obligations'},section3:{title:'3. How We Share Your Information',subtitle:'We do not sell your personal information.',description:'We may share it with:',item1:'Payment processors (e.g., Stripe)',item2:'Legal authorities if required by law',item3:'Trusted service providers under confidentiality agreements'},section4:{title:'4. Data Security',content:'We implement appropriate technical and organizational measures to protect your data from unauthorized access, alteration, or disclosure.'},section5:{title:'5. Your Rights',subtitle:'You have the right to:',item1:'Access your personal data',item2:'Request correction or deletion',item3:'Withdraw consent for data processing',item4:'File a complaint with a data protection authority (where applicable)',contact:'To exercise your rights, contact us at: <EMAIL>'},section6:{title:'6. Cookies and Tracking',content:'Our website may use cookies to improve your browsing experience. You can adjust cookie settings in your browser preferences.'},section7:{title:'7. Children\\'s Privacy',content:'We do not knowingly collect data from children under 13 without parental consent. If you believe we have collected such data, please contact us immediately.'},section8:{title:'8. Third-Party Links',content:'Our site may contain links to third-party services. We are not responsible for the privacy practices of those websites.'},section9:{title:'9. Changes to This Policy',content:'We may update this policy from time to time. We encourage you to review it periodically. Any changes will be posted on this page with an updated effective date.'},section10:{title:'10. Contact Us',content:'For questions or concerns regarding this policy, please contact:',email:'<EMAIL>'}},policies:{refund:{title:'Refund Policy',section1:{title:'First: Cases where students are granted lesson refunds',description:'Students are entitled to have their lesson refunded to their platform balance for rescheduling in the following cases:',items:['If the teacher cancels the lesson or is absent at the scheduled time.','If the lesson cannot be held due to a technical failure on the part of the teacher or platform.']},section2:{title:'Second: Cases where lesson refunds or rescheduling are not granted',description:'Students are not entitled to request a lesson refund or rescheduling in the following cases:',items:['Student cancels the lesson within less than 12 hours of its scheduled time.','Student is absent from attending the lesson without prior notice.','Loss of connection due to weak internet or malfunction in the student\\'s device.','Forgetting login credentials (username or password).','Balance expiration due to not using the platform for more than 180 days.','Student voluntarily deletes their account.','Account suspension due to violation of terms of use.']},section3:{title:'Third: Refund policy updates',description:'The platform reserves the right to modify this policy at any time. Continued use of the platform after policy updates constitutes implicit agreement to what is stated therein.'},contact:{title:'Contact Us',email:'📧 <EMAIL>'}},bookingPayment:{title:'Booking and Payment Policy',subtitle:'This policy explains how lessons are scheduled, confirmed, and paid for through the Allemnionline platform.',section1:{title:'1. Lesson Booking',points:['Students can book one-on-one lessons with available tutors directly through the platform.','Lesson times are displayed according to the user\\'s local time zone.','Bookings must be made in advance and are subject to tutor availability.','Once the booking and payment are completed, a confirmation email will be sent to both the student and the tutor.']},section2:{title:'2. Payments',points:['All lessons must be paid for in advance to confirm the booking.','Payments are securely processed through trusted payment providers (e.g., Stripe, PayPal, or Wise).','Students are responsible for any transaction fees or commissions charged by their bank or payment provider.','Lesson prices are clearly displayed before completing the payment.']},section3:{title:'3. Currency and Exchange Rates',points:['Payments are made in USD by default.','Prices shown in other currencies are for reference only.','If your payment method uses a different currency, conversion fees and exchange rate differences may apply.','Allemnionline is not responsible for currency fluctuations or additional banking fees.']},section4:{title:'4. Taxes and Fees',points:['Prices may include applicable local taxes (such as VAT or service tax), depending on the student\\'s location.','All applicable fees are displayed transparently before payment is finalized.']},section5:{title:'5. Payment Confirmation and Receipts',points:['Once the payment is successfully processed, a digital receipt will be emailed to the student.','A lesson is considered confirmed only after payment is completed.']},section6:{title:'6. Failed or Delayed Payments',points:['If the payment fails or is delayed, the booking will be marked as \"pending\" and not confirmed.','Students must resolve any payment issues promptly to maintain their booking.']},section7:{title:'7. Automatic Payments or Subscriptions (if applicable)',points:['If a student chooses to activate subscriptions or auto-refill, charges will be automatically deducted from their saved payment method.','Subscriptions can be canceled or modified anytime from the account settings.']},section8:{title:'8. Support and Contact',description:'For any questions or issues related to payment, please contact our support team:',email:'<EMAIL>'},features:{securePayments:'Secure Payments',securePaymentsDesc:'All transactions are protected with advanced encryption technologies to ensure the security of your financial data.',multipleCurrencies:'Multiple Currencies',multipleCurrenciesDesc:'We support a wide range of currencies and payment methods to make the payment process convenient for you.',instantReceipts:'Instant Receipts',instantReceiptsDesc:'You receive an instant digital receipt after every successful payment transaction.',instantConfirmation:'Instant Confirmation',instantConfirmationDesc:'Bookings are confirmed immediately after successful payment completion.'}},bookingCancellation:{title:'Booking, Cancellation, and Rescheduling Policy',subtitle:'This policy outlines the rules and procedures for scheduling, canceling, and modifying one-on-one lessons by both students and tutors, in order to ensure a professional and effective learning experience.',studentPolicy:{title:'1. Student Policy',booking:{title:'1.1 Booking Lessons',points:['Students may book any available time slot from the tutor\\'s schedule.','Bookings are confirmed immediately upon successful payment through the platform.']},cancellation:{title:'1.2 Lesson Cancellation',points:['Students may cancel a lesson free of charge if the cancellation is made at least 12 hours before the scheduled time.','If the cancellation occurs less than 12 hours before the lesson, the full lesson fee will be charged.','If the student fails to attend the lesson without prior cancellation, the lesson will be considered completed and non-refundable.']},rescheduling:{title:'1.3 Rescheduling a Lesson',points:['Each lesson can be rescheduled only once, and the request must be made at least 12 hours before the original lesson time.','Students may also contact the tutor directly to request rescheduling even less than 12 hours in advance, but the decision will be at the tutor\\'s discretion.']},lateArrival:{title:'1.4 Late Arrival',points:['Students have a 15-minute grace period after the scheduled start time.','If the student does not show up within 15 minutes without prior notice, the lesson will be marked as completed and the fee will not be refunded.']}},tutorPolicy:{title:'2. Tutor Policy',availability:{title:'2.1 Availability',points:['Tutors must regularly update their schedules and clearly define their available time slots for lessons.']},cancellation:{title:'2.2 Lesson Cancellation or Rescheduling',points:['Tutors must notify the student as early as possible if they need to cancel or reschedule a lesson.','Repeated cancellations or no-shows by the tutor may lead to temporary account suspension or other administrative actions.']},rescheduling:{title:'2.3 Modifying Lesson Times',points:['Tutors may cancel or modify a lesson time only if the change is made more than 12 hours in advance.','Any rescheduling must be communicated to the student through the platform along with a clear explanation.']},lateArrival:{title:'2.4 Late Arrival',points:['Tutors are allowed a maximum delay of 15 minutes.','If the tutor fails to attend the lesson within that time, the student will be entitled to either a full refund or a free rescheduling.']}},generalNotes:{title:'General Notes',points:['All time calculations are based on the user\\'s local time zone.','The platform reserves the right to modify this policy in a way that best serves the educational process. Users will be notified of any official changes.']},summary:{forStudents:'For Students',forTutors:'For Tutors',freeCancellation:'Free cancellation 12+ hours before',gracePeriod:'15-minute grace period',oneReschedule:'One reschedule per booking',cancellationBefore:'Cancellation 12+ hours before',delayAllowance:'15-minute delay allowance',immediateNotification:'Immediate student notification',importantNote:'Important Note: All times are calculated based on the user\\'s local time zone'}}},contact:{title:'Contact Us',subtitle:'We are happy to hear from you through the following channels for any inquiries related to our educational services, lesson schedules, or technical support:',mailingAddress:{title:'📍 Mailing Address',company:'Allemnionline',address:'30 N Gould St, Ste R, Sheridan, WY 82801'},email:{title:'📧 Official Email',address:'<EMAIL>',note:'We welcome all inquiries and aim to respond within 24 hours.'},phone:{title:'☎️ Phone / WhatsApp',number:'0019179937166',note:'Available during official working hours, from 9:00 AM to 5:00 PM (Mecca Time).'},website:{title:'🌐 Website',url:'www.allemnionline.com'}},platformPolicies:'Platform Policies',about:{title:'About Us',intro:'Allemnionline is an online educational platform specialized in providing Arabic language teaching services and knowledge related to Arabic culture to learners from around the world, through direct one-on-one lessons delivered by qualified and professional teachers.',mission:'The platform seeks to make learning Arabic accessible and effective, while considering individual differences and special needs of each learner, using the latest educational technological tools.',whatWeOffer:'What We Offer',services:{privateLessons:'Private Arabic language lessons for all levels: from beginners to advanced.',conversationTraining:'Training in conversation, listening, reading, and writing in Arabic.',culturalElements:'Introduction to essential elements of Arabic culture that help understand the language in its natural context.',digitalPlatform:'An integrated digital platform that enables direct communication via video, lesson scheduling, and secure electronic payment.',targetAudience:'Our services are directed to children, adults, professionals, and anyone who wants to learn Arabic for academic, personal, or professional purposes.'},ourMission:'Our Mission',missionText:'To provide distinguished education in Arabic language and related cultural knowledge, with high quality, through direct teaching and modern technologies, in a manner that respects the diversity of learners and their cultural particularities.',contactUs:'Contact Us',contactText:'For any inquiries or comments, please contact us via email:',email:'<EMAIL>'}}},ar:{translation:{appName:'علّمني أون لاين',aboutUs:'نبذة عنا',wallet:{title:'المحفظة',balance:'الرصيد',currentBalance:'الرصيد الحالي',transactionHistory:'سجل المعاملات',allTransactions:'جميع المعاملات',noTransactions:'لم يتم العثور على معاملات',errorFetchingTransactions:'خطأ في جلب المعاملات. يرجى المحاولة مرة أخرى.',date:'التاريخ',description:'الوصف',amount:'المبلغ',status:'الحالة',debit:'خصم',credit:'إضافة',pending:'قيد الانتظار',completed:'مكتمل',failed:'فاشل',cancelled:'ملغي',payment:'دفع',lessonWith:'درس مع {{teacher}}',lessonFrom:'درس من {{student}}',student:'الطالب',teacher:'المعلم',rowsPerPage:'عدد الصفوف في الصفحة',addMoney:'إضافة أموال',payWithStripe:'الدفع بالبطاقة الائتمانية',pay:'دفع',depositSuccess:'تم الإيداع بنجاح! تم تحديث محفظتك.',errorProcessingPayment:'خطأ في معالجة الدفع. يرجى المحاولة مرة أخرى.',paypalDeposit:'إيداع PayPal',stripeDeposit:'إيداع Stripe',walletDeposit:'إيداع محفظة',enterOTPTitle:'أدخل رمز التحقق',otpInstructions:'الرجاء إدخال رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني.',otpCode:'رمز التحقق',verify:'تحقق',enterOTP:'الرجاء إدخال رمز التحقق',errorVerifyingOTP:'خطأ في التحقق من الرمز'},withdrawal:{title:'سحب الأموال',availableBalance:'الرصيد المتاح',requestWithdrawal:'طلب سحب',withdrawalHistory:'تاريخ السحب',noWithdrawals:'لا توجد طلبات سحب',date:'التاريخ',amount:'المبلغ',paypalEmail:'بريد PayPal الإلكتروني',status:'الحالة',actions:'الإجراءات',cancel:'إلغاء',submit:'إرسال',pending:'قيد الانتظار',processing:'قيد المعالجة',completed:'مكتمل',failed:'فشل',cancelled:'ملغي',fillAllFields:'يرجى ملء جميع الحقول المطلوبة',minimumAmount:'الحد الأدنى للسحب هو ${{amount}}',insufficientBalance:'رصيد غير كافي',requestSubmitted:'تم إرسال طلب السحب بنجاح',requestCancelled:'تم إلغاء طلب السحب بنجاح',errorSubmitting:'خطأ في إرسال طلب السحب',errorCancelling:'خطأ في إلغاء طلب السحب',errorFetchingWithdrawals:'خطأ في جلب تاريخ السحب',paypalEmailHelp:'أدخل بريد PayPal الإلكتروني حيث تريد استلام الدفعة',processingTime:'وقت المعالجة: {{days}} أيام عمل',minimumWithdrawal:'الحد الأدنى للسحب',enterOTPTitle:'أدخل رمز التحقق',otpInstructions:'الرجاء إدخال رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني.',otpCode:'رمز التحقق',verify:'تحقق',enterOTP:'الرجاء إدخال رمز التحقق',errorVerifyingOTP:'خطأ في التحقق من الرمز',requestCompleted:'تم اكتمال طلب السحب بنجاح',withdrawalCancelled:'تم إلغاء طلب السحب بسبب عدة محاولات فاشلة',errorCancelling:'خطأ في إلغاء طلب السحب'},admin:{withdrawalManagement:{title:'إدارة طلبات السحب',statusFilter:'تصفية الحالة',all:'الكل',date:'التاريخ',teacher:'المعلم',amount:'المبلغ',paypalEmail:'بريد PayPal الإلكتروني',status:'الحالة',notes:'الملاحظات',actions:'الإجراءات',approve:'موافقة',reject:'رفض',approveWithdrawal:'الموافقة على السحب',rejectWithdrawal:'رفض السحب',notesOptional:'ملاحظات (اختيارية)',approveAndProcess:'موافقة ومعالجة',processingInfo:'سيتم معالجة السحب عبر PayPal Payouts API.',errorFetching:'خطأ في جلب طلبات السحب',errorProcessing:'خطأ في معالجة السحب'}},contactUs:{title:'تواصل معنا',type:'نوع الرسالة',typeQuestion:'سؤال',typeProblem:'مشكلة',typeSuggestion:'اقتراح',typePayment:'مشكلة في الدفع',typeOther:'أخرى',subject:'الموضوع',message:'الرسالة',send:'إرسال الرسالة',messageSent:'تم إرسال رسالتك بنجاح. سنرد عليك قريبًا.',fillAllFields:'يرجى ملء جميع الحقول المطلوبة',sendError:'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى لاحقًا.',startConversation:'ابدأ محادثة معنا'},myMessages:{title:'رسائلي',subject:'الموضوع',type:'النوع',date:'التاريخ',status:'الحالة',pending:'قيد الانتظار',answered:'تم الرد',noMessages:'لا توجد رسائل',fetchError:'خطأ في جلب الرسائل',yourMessage:'رسالتك',adminReply:'رد الإدارة',awaitingReply:'في انتظار الرد من الإدارة...'},booking:{title:'حجز درس',bookLessonWith:'حجز درس مع',pricePerLesson:'سعر الدرس',instructions:'تعليمات الحجز',instructionsText:'اختر وقتًا من الأوقات المتاحة أدناه. بمجرد تأكيد الحجز، سيتم خصم المبلغ من رصيد محفظتك.',selectTimeSlot:'اختر وقتًا',noAvailableSlots:'لا توجد أوقات متاحة لهذا اليوم',teacher:'المعلم',day:'اليوم',date:'التاريخ',time:'الوقت',price:'السعر',confirmBooking:'تأكيد الحجز',confirmAndPay:'تأكيد والدفع',bookingSuccessTitle:'تم الحجز بنجاح!',bookingSuccessMessage:'تم حجز درسك بنجاح. يمكنك عرض حجوزاتك في قسم حجوزاتي.',backToTeacher:'العودة إلى المعلم',viewMyBookings:'عرض حجوزاتي',bookAgain:'احجز مرة أخرى',bookingFailed:'فشل الحجز. يرجى المحاولة مرة أخرى.',insufficientBalance:'رصيد غير كافٍ. يرجى إضافة أموال إلى محفظتك.',currentWeek:'الأسبوع الحالي',weekOf:'أسبوع',previousWeek:'الأسبوع السابق',nextWeek:'الأسبوع التالي',weekNavigation:'التنقل بين الأسابيع',selectTimeRange:'اختر المدة الزمنية',startTime:'وقت البداية',endTime:'وقت النهاية',lessonDuration:'مدة الدرس',from:'من',to:'إلى',availableSlot:'خانة متاحة',selectDuration:'اختر المدة',selectDuration:'اختر مدة الدرس',duration:'المدة',fullLesson:'درس كامل ({{duration}} دقيقة)',halfLesson:'نصف درس ({{duration}} دقيقة)',meetingCreated:'تم إنشاء الاجتماع',meetingAccessInfo:'يمكنك الوصول إلى هذا الاجتماع من صفحة الاجتماعات عندما يحين وقت الدرس.',viewMyMeetings:'عرض اجتماعاتي'},reviews:{title:'التقييمات',writeReview:'كتابة تقييم',myReviews:'تقييماتي',teacherReviews:'تقييمات المعلم',rating:'التقييم',comment:'التعليق',submit:'إرسال التقييم',update:'تحديث التقييم',delete:'حذف التقييم',confirmDelete:'هل أنت متأكد من رغبتك في حذف هذا التقييم؟',selectTeacher:'اختر معلمًا',noTeachers:'لم نتمكن من العثور على معلمين متاحين لكتابة مراجعات لهم. يرجى التأكد من إكمال درس واحد على الأقل قبل كتابة مراجعة.',noCompletedLessons:'لم تأخذ دروسًا مع أي معلمين بعد. يرجى حجز درس وإكماله أولاً.',allTeachersReviewed:'لقد قمت بتقييم جميع المعلمين الذين أخذت دروسًا معهم. يمكنك تحديث تقييماتك الحالية أدناه.',allTeachersReviewedShort:'لقد قمت بتقييم جميع المعلمين',noReviews:'لا توجد تقييمات حتى الآن',yourReview:'تقييمك',editReview:'تعديل التقييم',reviewSuccess:'تم إرسال التقييم بنجاح',reviewUpdateSuccess:'تم تحديث التقييم بنجاح',reviewDeleteSuccess:'تم حذف التقييم بنجاح',reviewError:'خطأ في إرسال التقييم',reviewRequired:'يرجى تقديم تقييم',commentPlaceholder:'شارك تجربتك مع هذا المعلم...',averageRating:'متوسط التقييم',totalReviews:'{{count}} تقييمات',oneReview:'تقييم واحد',reviewsBy:'تقييمات الطلاب',reviewBy:'تقييم بواسطة',on:'في',stars:'نجوم',star:'نجمة',outOf5:'من 5',selectRating:'اختر التقييم',reviewsFor:'تقييمات لـ',reviewsWritten:'التقييمات المكتوبة',reviewsReceived:'التقييمات المستلمة',viewAll:'عرض الكل',filterBy:'تصفية حسب',sortBy:'ترتيب حسب',newest:'الأحدث',oldest:'الأقدم',highestRated:'الأعلى تقييمًا',lowestRated:'الأقل تقييمًا',noComment:'لم يتم تقديم تعليق',noReviewsWithFilter:'لا توجد تقييمات تطابق التصفية المحددة',teacherReply:'رد المعلم',replyToReview:'رد على المراجعة',editReply:'تعديل الرد',deleteReply:'حذف الرد',replyPlaceholder:'اكتب ردك على هذه المراجعة...',sendReply:'إرسال الرد',updateReply:'تحديث الرد',replySuccess:'تم إرسال الرد بنجاح',replyUpdateSuccess:'تم تحديث الرد بنجاح',replyDeleteSuccess:'تم حذف الرد بنجاح',replyError:'خطأ في إرسال الرد',confirmDeleteReply:'هل أنت متأكد من حذف هذا الرد؟',replyRequired:'يرجى كتابة رد',sending:'جاري الإرسال...'},about:{title:'من نحن',intro:'منصة Allemnionline هي منصة تعليمية عبر الإنترنت متخصصة في تقديم خدمات تعليم اللغة العربية والمعرفة ذات الصلة بالثقافة العربية للمتعلمين من جميع أنحاء العالم، من خلال دروس مباشرة (واحد لواحد) يقدمها معلمون مؤهلون ومحترفون.',mission:'تسعى المنصة إلى جعل تعلم اللغة العربية ميسّرًا وفعّالًا، مع مراعاة الفروق الفردية والاحتياجات الخاصة لكل متعلم، وذلك باستخدام أحدث الوسائل التقنية التعليمية.',whatWeOffer:'ماذا نقدم؟',services:{privateLessons:'دروس خصوصية في اللغة العربية لجميع المستويات: من المبتدئين إلى المتقدمين.',conversationTraining:'تدريب على المحادثة، الاستماع، القراءة، والكتابة باللغة العربية.',culturalElements:'تعريف بالعناصر الأساسية من الثقافة العربية التي تساعد على فهم اللغة في سياقها الطبيعي.',digitalPlatform:'منصة رقمية متكاملة تتيح التواصل المباشر عبر الفيديو، وجدولة الدروس، والدفع الإلكتروني الآمن.',targetAudience:'خدماتنا موجهة للأطفال، والبالغين، والمهنيين، ولكل من يرغب في تعلم العربية لأغراض أكاديمية أو شخصية أو مهنية.'},ourMission:'رسالتنا',missionText:'أن نوفّر تعليمًا متميزًا للغة العربية والمعرفة الثقافية المرتبطة بها، بجودة عالية، ومن خلال التعليم المباشر والتقنيات الحديثة، وبأسلوب يحترم تنوع المتعلمين وخصوصياتهم الثقافية.',contactUs:'للتواصل معنا',contactText:'لأي استفسارات أو ملاحظات، يُرجى التواصل عبر البريد الإلكتروني:',email:'<EMAIL>'},app:{name:'علّمني أون لاين',copyright:'علّمني أون لاين ٢٠٢٥',tagline:'صُمم بحب للتعليم العربي'},brand:{name:'علّمني أون لاين'},common:{switchLanguage:'تغيير اللغة',loading:'جاري التحميل...',settings:'الإعدادات',profile:'الملف الشخصي',cancel:'إلغاء',back:'رجوع',continue:'متابعة',confirm:'تأكيد',update:'تحديث',success:'نجاح',error:'خطأ',save:'حفظ',saveAndReturn:'حفظ والعودة',saving:'جاري الحفظ...',email:'البريد الإلكتروني',notProvided:'غير متوفر',search:'بحث',close:'إغلاق',password:'كلمة المرور',fullName:'الاسم الكامل',name:'الاسم',confirmPassword:'تأكيد كلمة المرور',showMore:'عرض المزيد',showLess:'عرض أقل',gender:'الجنس',male:'ذكر',female:'أنثى',submit:'إرسال',notSet:'غير محدد',actions:'الإجراءات',edit:'تعديل',delete:'حذف',view:'عرض',details:'التفاصيل',rowsPerPage:'عدد الصفوف في الصفحة',footer:{copyright:'علّمني أون لاين ٢٠٢٥',tagline:'صُمم بحب للتعليم العربي'},currency:'دولار',details:'التفاصيل'},profile:{title:'الملف الشخصي',fullName:'الاسم الكامل',edit:'تعديل الملف الشخصي',save:'حفظ التغييرات',cancel:'إلغاء',success:'تم تحديث الملف الشخصي بنجاح',error:'حدث خطأ أثناء تحديث الملف الشخصي',updateSuccess:'تم تحديث الملف الشخصي بنجاح',passwordUpdateSuccess:'تم تحديث كلمة المرور بنجاح',teachingInfoUpdateSuccess:'تم تحديث معلومات التدريس بنجاح!',editTeachingInfo:'تعديل معلومات التدريس',errors:{update:'فشل تحديث الملف الشخصي',passwordUpdate:'فشل تحديث كلمة المرور',passwordMismatch:'كلمات المرور الجديدة غير متطابقة',currentPassword:'كلمة المرور الحالية غير صحيحة',fetchError:'فشل في جلب بيانات الملف الشخصي',updateFailed:'فشل التحديث. يرجى المحاولة مرة أخرى.',deleteRequest:'فشل في إرسال طلب الحذف',invalidCode:'رمز التحقق غير صحيح',codeRequired:'رمز التحقق مطلوب',cancelDelete:'فشل في إلغاء حذف الحساب'},basicInfo:'المعلومات الأساسية',teacherInfo:'معلومات المعلم',editInfo:'تعديل المعلومات',changePassword:'تغيير كلمة المرور',email:'البريد الإلكتروني',gender:'الجنس',currentPassword:'كلمة المرور الحالية',newPassword:'كلمة المرور الجديدة',confirmPassword:'تأكيد كلمة المرور',editPersonalInfo:'تعديل المعلومات الشخصية',togglePasswordVisibility:'إظهار/إخفاء كلمة المرور',uploadPhoto:'رفع صورة',deleteAccount:'حذف الحساب',deleteAccountTitle:'حذف الحساب',deleteAccountWarning:'هل أنت متأكد من رغبتك في حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه.',deleteAccountNote:'سيتم إرسال رمز تحقق إلى عنوان بريدك الإلكتروني.',sendDeleteCode:'إرسال رمز التحقق',verifyDeleteCode:'تحقق من رمز الحذف',deleteCodeSentMessage:'تم إرسال رمز التحقق إلى بريدك الإلكتروني. يرجى إدخاله أدناه:',deleteCode:'رمز التحقق',confirmDelete:'تأكيد الحذف',deleteCodeSent:'تم إرسال رمز التحقق إلى بريدك الإلكتروني',deletePending:'حذف الحساب قيد الانتظار (10 أيام)',cancelDelete:'إلغاء الحذف',deleteCancelled:'تم إلغاء حذف الحساب بنجاح',deletePendingAlert:'⚠️ حذف الحساب قيد الانتظار',deletePendingMessage:'حسابك مجدول للحذف. يمكنك إلغاء هذا الإجراء في أي وقت قبل موعد الحذف المحدد.',deleteScheduledFor:'موعد الحذف المجدول',updateStatus:{pending:'طلب تعديل البيانات قيد المراجعة',approved:'تم الموافقة على تعديل البيانات',rejected:'تم رفض طلب تعديل البيانات',requestDate:'تاريخ الطلب',reviewDate:'تاريخ المراجعة',adminNotes:'ملاحظات الإدارة',pendingNote:'يرجى انتظار مراجعة الإدارة لطلب التعديل. ستعمل بالبيانات الحالية حتى الموافقة على التعديل.'},teacher:{videoUpload:{title:'رفع فيديو تعريفي',description:'قم برفع فيديو قصير لتقديم نفسك للطلاب المحتملين. سيتم عرض هذا الفيديو على ملفك الشخصي.',requirements:'متطلبات الفيديو',formatRequirement:'الصيغ المسموح بها',sizeRequirement:'الحجم الأقصى',lengthRequirement:'المدة المقترحة',maximum:'كحد أقصى',minutes:'دقائق',selectVideo:'اختر الفيديو',upload:'رفع الفيديو',uploading:'جاري الرفع...',success:'تم رفع الفيديو بنجاح!',videoReady:'فيديو التعريف جاهز للإضافة إلى طلبك.',continue:'متابعة إلى نموذج التقديم',delete:'حذف الفيديو',skipForNow:'تخطي الآن',invalidVideoFormat:'صيغة الفيديو غير صالحة. يرجى اختيار ملف MP4 أو WebM أو OGG.',videoTooSmall:'حجم الفيديو يجب أن يكون على الأقل 1 ميجابايت.',videoTooLarge:'حجم الفيديو يجب ألا يتجاوز 100 ميجابايت.',noVideoSelected:'يرجى اختيار ملف فيديو.',videoDeleteError:'Error deleting video. Please try again.'},uploadVideoNow:'رفع الفيديو الآن',videoReady:'فيديو التعريف جاهز للإضافة إلى طلبك',deleteVideo:'حذف الفيديو',availableHoursDescription:'حدد الساعات التي تكون متاحًا فيها للتدريس. سيساعد ذلك الطلاب في العثور على أوقات مناسبة لحجز الدروس معك.',availableHoursApplicationDescription:'حدد الساعات التي ستكون متاحًا فيها للتدريس إذا تمت الموافقة على طلبك. يمكنك تحديث هذه الساعات لاحقًا من ملفك الشخصي.',availableHoursProfileDescription:'إدارة أوقات تواجدك للتدريس لإعلام الطلاب بالأوقات المتاحة للدروس.',viewAvailableHours:'عرض الساعات المتاحة',viewAvailableHoursDescription:'هنا يمكنك رؤية جميع ساعات التدريس المتاحة منظمة حسب اليوم.',editAvailableHours:'تعديل الساعات',noHoursForDay:'لا توجد ساعات متاحة لهذا اليوم',manageAvailableHours:'إدارة الساعات المتاحة',hoursSavedSuccess:'تم حفظ ساعاتك المتاحة بنجاح!',myLessons:'دروسي',studentName:'اسم الطالب',totalLessons:'إجمالي الدروس',completedLessons:'الدروس المكتملة',scheduledLessons:'الدروس المجدولة',cancelledLessons:'الدروس الملغاة',noLessonsFound:'لا توجد دروس',errorSavingHours:'خطأ في حفظ ساعاتك المتاحة. يرجى المحاولة مرة أخرى.',errorLoadingHours:'خطأ في تحميل ساعاتك المتاحة. يرجى المحاولة مرة أخرى.',errorParsingHours:'خطأ في تحليل بيانات ساعاتك المتاحة. يرجى المحاولة مرة أخرى.',saveAndReturn:'حفظ والعودة',timeSlots:'فترة زمنية',noAvailableHours:'لم يتم تحديد ساعات متاحة. يرجى تحديد ساعاتك المتاحة.',selectAll:'تحديد الكل',clearAll:'مسح الكل',timeSlot:'الفترة الزمنية',available:'متاح',unavailable:'غير متاح',selectAllDays:'تحديد هذا الوقت لجميع الأيام',clearAllDays:'مسح هذا الوقت لجميع الأيام',legend:'مفتاح الرموز',cellStatus:'حالة الخلية',actions:'الإجراءات',changeProfilePicture:'تغيير الصورة الشخصية',profile:{title:'ملف المعلم',personalInfo:'المعلومات الشخصية',teachingInfo:'معلومات التدريس',phone:'رقم الهاتف',country:'الدولة',residence:'مكان الإقامة',nativeLanguage:'اللغة الأم',teachingLanguages:{title:'لغات التدريس',Arabic:'العربية',English:'الإنجليزية',French:'الفرنسية',Spanish:'الإسبانية',Urdu:'الأوردية',Turkish:'التركية',Indonesian:'الإندونيسية',Malay:'الماليزية',Bengali:'البنغالية',Hindi:'الهندية',Persian:'الفارسية',German:'الألمانية',Italian:'الإيطالية',Portuguese:'البرتغالية',Russian:'الروسية',Chinese:'الصينية',Japanese:'اليابانية',Korean:'الكورية',Thai:'التايلاندية',Vietnamese:'الفيتنامية',Swahili:'السواحيلية',Hausa:'الهوسا',Somali:'الصومالية',select:'اختر لغات التدريس',placeholder:'اختر لغة واحدة أو أكثر'},courseTypes:'أنواع الدورات',qualifications:'المؤهلات',teachingExperience:'الخبرة في التدريس',availableHours:'الساعات المتاحة',pricePerLesson:'السعر لكل درس',timezone:'المنطقة الزمنية',paymentMethod:'طريقة الدفع',currency:{usd:'دولار أمريكي',eur:'يورو',gbp:'جنيه إسترليني'},experience:{beginner:'مبتدئ (0-2 سنوات)',intermediate:'متوسط (2-5 سنوات)',advanced:'متقدم (5-10 سنوات)',expert:'خبير (أكثر من 10 سنوات)'}}},genders:{male:'ذكر',female:'أنثى'}},nav:{home:'الرئيسية',dashboard:'لوحة التحكم',teachers:'المعلمون',students:'الطلاب',deletedUsers:'المستخدمون المحذوفون',categories:'التصنيفات',languages:'اللغات',applications:'الطلبات',profileUpdates:'تعديلات البيانات',withdrawalManagement:'إدارة طلبات السحب',findTeacher:'ابحث عن معلم',myTeachers:'أساتذتي',meetings:'الاجتماعات',chat:'المحادثات',login:'تسجيل الدخول',register:'إنشاء حساب',logout:'تسجيل الخروج',search:'بحث',language:'اللغة',english:'الإنجليزية',arabic:'العربية',adminDashboard:'لوحة تحكم المدير',teacherDashboard:'لوحة تحكم المعلم',studentDashboard:'لوحة تحكم الطالب'},deletedUsers:{title:'المستخدمون المحذوفون',totalDeleted:'إجمالي المحذوفين',deletedStudents:'الطلاب المحذوفين',deletedTeachers:'المعلمين المحذوفين',activeUsers:'المستخدمين النشطين',search:'البحث',type:'النوع',all:'الكل',students:'طلاب',teachers:'معلمين',newTeachers:'معلمين جدد',refresh:'تحديث',user:'المستخدم',role:'النوع',deletionDate:'تاريخ الحذف',deletionReason:'سبب الحذف',deletedBy:'حُذف بواسطة',actions:'الإجراءات',viewDetails:'عرض التفاصيل',restoreUser:'استرداد المستخدم',permanentDelete:'حذف نهائي',student:'طالب',teacher:'معلم',newTeacher:'معلم جديد',notSpecified:'غير محدد',selfDeleted:'حذف ذاتي',restoreConfirmTitle:'استرداد المستخدم',restoreConfirmMessage:'هل أنت متأكد من استرداد المستخدم \"{name}\"؟',restoreConfirmNote:'سيتمكن المستخدم من تسجيل الدخول والوصول للنظام مرة أخرى.',cancel:'إلغاء',restore:'استرداد',restoring:'جاري الاسترداد...',permanentDeleteTitle:'حذف نهائي',permanentDeleteWarning:'تحذير: هذا الإجراء لا يمكن التراجع عنه!',permanentDeleteMessage:'هل أنت متأكد من الحذف النهائي للمستخدم \"{name}\"؟',permanentDeleteNote:'سيتم حذف جميع البيانات المرتبطة بهذا المستخدم نهائياً.',permanentDeleteButton:'حذف نهائي',deleting:'جاري الحذف...',rowsPerPage:'عدد الصفوف:',displayedRows:'{from}-{to} من {count}',deletionInfo:'معلومات الحذف',scheduledDeletion:'الحذف المجدول'},common:{userInfo:'معلومات المستخدم',teacherInfo:'معلومات المعلم',studentInfo:'معلومات الطالب',gender:'الجنس',male:'ذكر',female:'أنثى',joinDate:'تاريخ الانضمام',totalLessons:'إجمالي الدروس',totalStudents:'إجمالي الطلاب',rating:'التقييم',noRating:'لا يوجد تقييم',totalEarnings:'إجمالي الأرباح',currency:'ريال',totalBookings:'إجمالي الحجوزات',completedLessons:'الدروس المكتملة',totalSpent:'إجمالي المصروف',timezone:'المنطقة الزمنية',notSet:'غير محدد',close:'إغلاق'},footer:{platformName:'علّمني أون لاين',designedBy:'صُمم بحب للتعليم العربي',followUs:'تابعنا',facebook:'فيسبوك',twitter:'تويتر',instagram:'انستغرام',quickLinks:'روابط سريعة',about:'من نحن',contact:'اتصل بنا',privacy:'سياسة الخصوصية',terms:'شروط الخدمة',faq:'الأسئلة الشائعة',support:'الدعم الفني',copyright:'© ٢٠٢٥ علّمني أون لاين',tagline:'صُمم بحب للتعليم العربي'},hero:{title:'العربية بجميع اللغات',subtitle:'رحلة تعليمية فريدة تجمع بين اللغة والثقافة العربية',startLearning:'ابدأ التعلم',becomeTeacher:'كن مدرساً',imageAlt:'القرآن الكريم'},home:{subjects:'موادنا الدراسية',subjectsSubtitle:'اكتشف مجموعة متنوعة من المواد الإسلامية والعربية مع نخبة من المعلمين المتخصصين',teachers:'معلم',expertTeachers:'معلمون متخصصون',expertTeachersDesc:'تعلم على يد علماء معتمدين ومتحدثين أصليين',activeStudents:'طلاب نشطون',courses:'دورات',coursesDesc:'دورات شاملة في الدراسات الإسلامية واللغة العربية',studentsDesc:'طلاب من جميع أنحاء العالم',whyChooseUs:'لماذا تختارنا',whyChooseUsSubtitle:'نقدم تجربة تعليمية فريدة تجمع بين التكنولوجيا الحديثة والتعليم الإسلامي الأصيل',meetTeachers:'تعرف على معلمينا',meetTeachersSubtitle:'نخبة من المعلمين المؤهلين والمتخصصين في تعليم الإسلام واللغة العربية',features:{quality:'تعليم عالي الجودة',qualityDesc:'معلمون متخصصون وخبراء في مجالاتهم',flexible:'مرونة في التعلم',flexibleDesc:'تعلم في أي وقت ومن أي مكان',interactive:'تعلم تفاعلي',interactiveDesc:'دروس تفاعلية ومناقشات مباشرة',certified:'شهادات معتمدة',certifiedDesc:'احصل على شهادات معتمدة في مجال دراستك'},testimonials:'ماذا يقول طلابنا',testimonialsSubtitle:'استمع إلى طلابنا حول تجربتهم التعليمية مع معلمينا المتخصصين',reviewFor:'تقييم لـ',learnMore:'اكتشف المزيد'},testimonials:{student:'طالب',reviewFor:'تقييم لـ'},search:{findTeacher:'ابحث عن معلم',filters:'التصفية',subject:'المادة',allSubjects:'جميع المواد',language:'اللغة',allLanguages:'جميع اللغات',priceRange:'نطاق السعر',rating:'التقييم',anyRating:'أي تقييم',andAbove:'وما فوق',noTeachersFound:'لم يتم العثور على معلمين',yearsOfExperience:'{{years}} سنوات من الخبرة',bookLesson:'احجز درساً',perHour:'/ساعة',subjects:'المواد',languages:'اللغات',searchButton:'بحث',clearFilters:'مسح التصفية',noTeachersFound:'لم يتم العثور على معلمين مطابقين لمعاييرك',teachingLanguages:'لغات التدريس',verifiedTeacher:'معلم موثق',watchIntro:'مشاهدة المقدمة',watchIntroVideo:'مشاهدة فيديو التعريف',viewProfile:'عرض الملف الشخصي',contactAndBook:'تواصل واحجز'},booking:{bookLessonWith:'احجز درساً مع',pricePerLesson:'سعر الدرس',instructions:'تعليمات الحجز',instructionsText:'اختر يوماً وموعداً من التقويم أدناه. بعد تأكيد الحجز، سيتم خصم المبلغ من رصيد محفظتك.',selectTimeSlot:'اختر موعداً',clickToSelectSlot:'اضغط على المواعيد المتاحة للحجز',clickToBook:'اضغط لحجز هذا الموعد',fullHour:'ساعة كاملة',fullHourAvailable:'ساعة كاملة متاحة',halfHourOnly:'نصف ساعة فقط',fullLesson:'درس كامل (50 دقيقة)',halfLesson:'نصف درس (25 دقيقة)',consecutiveSlots:'فترات متتالية',selectDuration:'اختر مدة الدرس',selectBookingOption:'اختر نوع الحجز',bookingType:'نوع الحجز',regularHalfLesson:'نصف درس عادي (25 دقيقة)',regularFullLesson:'درس كامل عادي (50 دقيقة)',secondHalfOnly:'النصف الثاني فقط (بعد 30 دقيقة)',fullLessonFromSecondHalf:'درس كامل من النصف الثاني (50 دقيقة)',crossHourLesson:'درس عبر الساعات (30+30 دقيقة)',regularLesson:'درس عادي',duration:'المدة',noAvailableSlots:'لا توجد مواعيد متاحة لهذا اليوم',confirmBooking:'تأكيد الحجز',teacher:'المعلم',day:'اليوم',date:'التاريخ',time:'الوقت',price:'السعر',confirmAndPay:'تأكيد والدفع',bookingSuccessTitle:'تم الحجز بنجاح!',bookingSuccessMessage:'تم حجز درسك بنجاح وتم خصم المبلغ من محفظتك. يمكنك عرض حجوزاتك في قسم حجوزاتي.',backToTeacher:'العودة إلى المعلم',viewMyBookings:'عرض حجوزاتي',bookAgain:'احجز مرة أخرى',bookingFailed:'فشل الحجز. يرجى المحاولة مرة أخرى.',insufficientBalance:'رصيدك غير كافٍ. يرجى إضافة رصيد إلى محفظتك قبل الحجز.',currency:'دولار',currentWeek:'الأسبوع الحالي',weekOf:'أسبوع',previousWeek:'الأسبوع السابق',nextWeek:'الأسبوع التالي',weekNavigation:'التنقل بين الأسابيع',selectTimeRange:'اختر المدة الزمنية',startTime:'وقت البداية',endTime:'وقت النهاية',lessonDuration:'مدة الدرس',from:'من',to:'إلى',availableSlot:'خانة متاحة',selectDuration:'اختر المدة'},bookings:{title:'حجوزاتي',weeklyTitle:'حجوزات هذا الأسبوع',description:'عرض وإدارة دروسك المجدولة مع المعلمين.',weeklyDescription:'عرض حجوزاتك لهذا الأسبوع في شكل تقويم.',bookingDetails:'تفاصيل الحجز',noBookings:'ليس لديك حجوزات حتى الآن.',fetchError:'خطأ في جلب الحجوزات. يرجى المحاولة مرة أخرى.',teacher:'المعلم',date:'التاريخ',time:'الوقت',timeRange:'نطاق الوقت',duration:'المدة',minutes:'دقيقة',status:'الحالة',statusValues:{scheduled:'مجدول',completed:'مكتمل',cancelled:'ملغي',issue_reported:'تم الإبلاغ عن مشكلة',ongoing:'جاري'},price:'السعر',cancel:'إلغاء',confirmCancel:'تأكيد الإلغاء',cancelWarning:'هل أنت متأكد من رغبتك في إلغاء هذا الحجز؟ سيتم إعادة المبلغ إلى محفظتك.',cancellationReason:'سبب الإلغاء (اختياري)',cancellationReasonPlaceholder:'يرجى كتابة سبب إلغاء هذا الحجز...',confirmCancelButton:'نعم، إلغاء الحجز',cancelling:'جاري الإلغاء...',cancelSuccess:'تم إلغاء الحجز بنجاح وتمت إعادة المبلغ إلى محفظتك',cancelError:'خطأ في إلغاء الحجز. يرجى المحاولة مرة أخرى.',availableSlot:'وقت متاح',currentTime:'الوقت الحالي',pastSlot:'وقت فائت',takeBreak:'أخذ راحة',takeBreakTitle:'أخذ هذا الوقت راحة',takeBreakMessage:'هل تريد أخذ هذا الوقت راحة؟',takeBreakNote:'سيتم إخفاء هذا الوقت من الطلاب لهذا الأسبوع فقط',breakTakenSuccess:'تم أخذ الراحة بنجاح',breakTakenError:'خطأ في أخذ الراحة',break:'راحة',clickToTakeBreak:'اضغط لأخذ راحة',cancelBreakTitle:'إلغاء وقت الراحة',cancelBreakMessage:'هل تريد إلغاء وقت الراحة هذا؟',cancelBreakNote:'سيصبح هذا الوقت متاحاً للطلاب مرة أخرى',breakCancelledSuccess:'تم إلغاء الراحة بنجاح',breakCancelledError:'خطأ في إلغاء الراحة',reschedule:'إعادة جدولة',rescheduleTitle:'إعادة جدولة الحجز',rescheduleDescription:'اختر موعداً جديداً لهذا الحجز من أوقاتك المتاحة.',currentBooking:'الحجز الحالي',selectDay:'اختر اليوم',chooseDay:'اختر يوماً',selectTime:'اختر الوقت',chooseTime:'اختر وقتاً',loadingDays:'جاري تحميل الأيام المتاحة...',loadingTimes:'جاري تحميل الأوقات المتاحة...',noAvailableDays:'لا توجد أيام متاحة خلال الـ 30 يوماً القادمة.',noAvailableTimes:'لا توجد أوقات متاحة في هذا اليوم.',availableTimes:'أوقات متاحة',rescheduleReason:'سبب إعادة الجدولة (اختياري)',rescheduleReasonPlaceholder:'يرجى كتابة سبب إعادة جدولة هذا الحجز...',rescheduleSuccess:'تم إعادة جدولة الحجز بنجاح',rescheduleError:'خطأ في إعادة جدولة الحجز. يرجى المحاولة مرة أخرى.',fetchDaysError:'خطأ في جلب الأيام المتاحة. يرجى المحاولة مرة أخرى.',fetchTimesError:'خطأ في جلب الأوقات المتاحة. يرجى المحاولة مرة أخرى.',currentBooking:'الحجز الحالي',selectNewDate:'اختر التاريخ الجديد',selectNewTime:'اختر الوقت الجديد',selectDateFirst:'اختر تاريخاً من التقويم أولاً',selectedTime:'الوقت المختار',confirmReschedule:'تأكيد إعادة الجدولة',availableSlots:'فترة متاحة',backToSelectDay:'العودة لاختيار اليوم'},teacherDetails:{backToSearch:'العودة إلى البحث',teacherNotFound:'لم يتم العثور على المعلم',errorFetching:'خطأ في جلب تفاصيل المعلم',reviews:'تقييمات',watchIntroVideo:'مشاهدة الفيديو التعريفي',hideIntroVideo:'إخفاء الفيديو التعريفي',bookLesson:'حجز درس',teachingSubjects:'المواد التعليمية',languages:'اللغات',nativeLanguage:'اللغة الأم',teachingLanguages:'لغات التدريس',qualifications:'المؤهلات',yearsOfExperience:'{{years}} سنوات من الخبرة',studentReviews:'تقييمات الطلاب',noReviews:'لا توجد تقييمات حتى الآن',contactInfo:'معلومات الاتصال',email:'البريد الإلكتروني',phone:'رقم الهاتف',location:'الموقع',country:'الدولة',residence:'مدينة الإقامة',timezone:'المنطقة الزمنية',experience:'الخبرة',memberSince:'عضو منذ',availableHours:'الساعات المتاحة',paymentInfo:'معلومات الدفع',pricePerLesson:'سعر الدرس',paymentMethod:'طريقة الدفع',cv:'السيرة الذاتية',introVideo:'الفيديو التعريفي',courseTypes:'أنواع الدورات'},chat:{conversations:'المحادثات',messages:'الرسائل',typeMessage:'اكتب رسالة...',send:'إرسال',noMessages:'لا توجد رسائل',noChats:'لا توجد محادثات',startChat:'ابدأ محادثة جديدة',loading:'جاري التحميل...',today:'اليوم',yesterday:'أمس',online:'متصل',offline:'غير متصل',sent:'تم الإرسال',delivered:'تم التوصيل',read:'تمت القراءة',failed:'فشل الإرسال',retry:'إعادة المحاولة',onlyStudents:'يمكن للطلاب فقط بدء محادثة مع المعلمين',no_conversations:'لا توجد محادثات',type_message:'اكتب رسالتك هنا...',select_conversation:'اختر محادثة للبدء',last_seen:'آخر ظهور',typing:'يكتب...',deleteConfirmTitle:'حذف المحادثة',deleteConfirmMessage:'هل أنت متأكد من رغبتك في حذف المحادثة مع {{name}}؟',conversationDeleted:'تم حذف المحادثة بنجاح',conversationDeletedByOther:'تم حذف هذه المحادثة من قبل المشارك الآخر',deleteConversationError:'حدث خطأ أثناء حذف المحادثة',messageDeleted:'تم حذف الرسالة بنجاح',messageEdited:'تم تعديل الرسالة بنجاح',editError:'حدث خطأ أثناء تعديل الرسالة',deleteError:'حدث خطأ أثناء حذف الرسالة',noConversations:'لا توجد محادثات',chat:'دردشة'},auth:{login:'تسجيل الدخول',logout:'تسجيل الخروج',logoutConfirmTitle:'تأكيد تسجيل الخروج',logoutConfirmMessage:'هل أنت متأكد أنك تريد تسجيل الخروج؟',welcomeBack:'مرحباً بعودتك!',email:'البريد الإلكتروني',password:'كلمة المرور',signIn:'تسجيل الدخول',or:'أو',noAccount:'ليس لديك حساب؟',forgotPassword:'نسيت كلمة المرور؟',forgotPasswordInstructions:'أدخل بريدك الإلكتروني وسنرسل لك رمز التحقق لإعادة تعيين كلمة المرور.',sendResetCode:'إرسال رمز التحقق',backToLogin:'العودة إلى تسجيل الدخول',resetCodeSent:'تم إرسال رمز التحقق إلى بريدك الإلكتروني',resetRequestFailed:'فشل إرسال رمز التحقق',verifyCode:'التحقق من الرمز',verifyCodeInstructions:'أدخل رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني:',verifyEmail:'تحقق من البريد الإلكتروني',verificationCodeSentTo:'أرسلنا رمز التحقق إلى',verify:'تحقق',verifying:'جاري التحقق...',verificationCodeSent:'تم إرسال رمز التحقق بنجاح',verificationFailed:'فشل التحقق من البريد الإلكتروني',resendFailed:'فشل إعادة إرسال رمز التحقق',resendCodeIn:'إعادة إرسال الرمز خلال',sending:'جاري الإرسال...',invalidCode:'يرجى إدخال رمز صحيح مكون من 6 أرقام',fillAllFields:'يرجى ملء جميع الحقول',loginFailed:'فشل تسجيل الدخول. يرجى التحقق من بياناتك.',invalidCredentials:'البريد الإلكتروني أو كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.',emailNotFound:'هذا البريد الإلكتروني غير مسجل. يرجى التحقق من البريد الإلكتروني أو إنشاء حساب جديد.',wrongPassword:'كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.',accountDeleted:'تم حذف هذا الحساب ولا يمكن تسجيل الدخول به.',verificationCode:'رمز التحقق',verifyAndContinue:'التحقق والمتابعة',resendCode:'إعادة إرسال الرمز',changeEmail:'تغيير البريد الإلكتروني',invalidCode:'رمز التحقق غير صالح',verificationFailed:'فشل التحقق',resetCodeResent:'تم إعادة إرسال رمز التحقق إلى بريدك الإلكتروني',resetPassword:'إعادة تعيين كلمة المرور',resetPasswordInstructions:'أنشئ كلمة مرور جديدة لحسابك',newPassword:'كلمة المرور الجديدة',confirmPassword:'تأكيد كلمة المرور',passwordResetSuccess:'تم إعادة تعيين كلمة المرور بنجاح!',passwordResetSuccessMessage:'تم إعادة تعيين كلمة المرور الخاصة بك بنجاح.',redirectingToLogin:'جاري إعادة التوجيه إلى صفحة تسجيل الدخول...',resetPasswordFailed:'فشل إعادة تعيين كلمة المرور',signUp:'إنشاء حساب',continueWithGoogle:'المتابعة باستخدام جوجل',googleSignInError:'فشل تسجيل الدخول بجوجل. يرجى المحاولة مرة أخرى.',signInWithGoogle:'المتابعة باستخدام جوجل',loginError:'فشل تسجيل الدخول. يرجى التحقق من بيانات الاعتماد الخاصة بك.',accountNotFound:'الحساب غير موجود',invalidRole:'دور المستخدم غير صالح',chooseAccountType:'انضم إلى مجتمعنا',registerDescription:'ابدأ رحلتك في التعليم الإسلامي. اختر دورك وكن جزءاً من مجتمعنا المتنامي من المتعلمين والمعلمين.',registerAsTeacher:'علّم معنا',teacherDescription:'شارك معرفتك وخبرتك في الدراسات الإسلامية واللغة العربية. ساعد الآخرين على التعلم والنمو مع تطوير مسيرتك في التعليم.',registerAsStudent:'تعلّم معنا',studentDescription:'ابدأ رحلة تعلمك في الدراسات الإسلامية واللغة العربية مع معلمين خبراء من جميع أنحاء العالم.',alreadyHaveAccount:'لديك حساب بالفعل؟',getStarted:'ابدأ الآن',studentRegistration:'تسجيل طالب جديد',teacherRegistration:'تسجيل معلم جديد',joinOurCommunity:'انضم إلى مجتمع المتعلمين',joinOurTeachers:'شارك معرفتك مع الآخرين',createAccount:'إنشاء حساب',registrationError:'فشل التسجيل. يرجى المحاولة مرة أخرى.',selectGender:'الرجاء اختيار الجنس',gender:'الجنس',male:'ذكر',female:'أنثى'},courseTypes:{Islamic_Law:'الشريعة الإسلامية',Fiqh:'الفقه',Quran:'القرآن الكريم',Tajweed:'التجويد',Islamic_History:'التاريخ الإسلامي',Arabic_Grammar:'قواعد اللغة العربية',Arabic_Speaking:'المحادثة العربية',Arabic_Writing:'الكتابة العربية',Islamic_Ethics:'الأخلاق الإسلامية',Hadith:'الحديث الشريف',Aqeedah:'العقيدة'},languages:{Arabic:'العربية',English:'الإنجليزية',French:'الفرنسية',Spanish:'الإسبانية'},gender:{male:'ذكر',female:'أنثى'},dashboard:{welcome:'مرحباً',unauthorized:'غير مصرح لك بالوصول إلى لوحة التحكم',fetchError:'حدث خطأ أثناء جلب البيانات',totalStudents:'إجمالي الطلاب',totalClasses:'إجمالي الدروس',averageRating:'متوسط التقييم',totalEarnings:'إجمالي الأرباح',categories:'فئات التدريس',recentBookings:'آخر الحجوزات',recentReviews:'آخر التقييمات',learningProgress:'تقدم التعلم',quickActions:'إجراءات سريعة',recommendedTeachers:'معلمون موصى بهم',upcomingLessons:'الدروس القادمة',noUpcomingLessons:'لا توجد دروس قادمة',updateProfile:'تحديث الملف الشخصي',findTeacher:'ابحث عن معلم',browseCourses:'تصفح الدورات',viewAll:'عرض الكل',incompleteProfile:'ملفك الشخصي غير مكتمل',completeProfileMessage:'يرجى إكمال ملفك الشخصي للوصول إلى جميع الميزات والعثور على المعلم المناسب لرحلتك التعليمية.',completeProfileToAccess:'يجب عليك إكمال ملفك الشخصي للوصول إلى هذه الصفحة وميزاتها.',completeProfile:'إكمال الملف الشخصي',updateProfile:'تحديث الملف الشخصي',completeProfileNow:'أكمل ملفك الشخصي الآن'},regions:{middle_east:'الشرق الأوسط',north_africa:'شمال أفريقيا',sub_saharan_africa:'أفريقيا جنوب الصحراء',south_asia:'جنوب آسيا',southeast_asia:'جنوب شرق آسيا',central_asia:'آسيا الوسطى',europe:'أوروبا',north_america:'أمريكا الشمالية',south_america:'أمريكا الجنوبية',east_asia:'شرق آسيا',oceania:'أوقيانوسيا',others:'أخرى'},teacher:{phoneOptional:'اختياري',nativeLanguage:'اللغة الأم',teachingLanguages:{title:'لغات التدريس',Arabic:'العربية',English:'الإنجليزية',French:'الفرنسية',Spanish:'الإسبانية',Urdu:'الأوردية',Turkish:'التركية',Indonesian:'الإندونيسية',Malay:'الماليزية',Bengali:'البنغالية',Hindi:'الهندية',Persian:'الفارسية',German:'الألمانية',Italian:'الإيطالية',Portuguese:'البرتغالية',Russian:'الروسية',Chinese:'الصينية',Japanese:'اليابانية',Korean:'الكورية',Thai:'التايلاندية',Vietnamese:'الفيتنامية',Swahili:'السواحيلية',Hausa:'الهوسا',Somali:'الصومالية',select:'اختر لغات التدريس',placeholder:'اختر لغة واحدة أو أكثر'},qualifications:'المؤهلات',experience:'الخبرة',subjects:'المواد',profile:{title:'ملف المعلم',personalInfo:'المعلومات الشخصية',teachingInfo:'معلومات التدريس',phone:'رقم الهاتف',country:'الدولة',residence:'مكان الإقامة',nativeLanguage:'اللغة الأم',teachingLanguages:{title:'لغات التدريس',Arabic:'العربية',English:'الإنجليزية',French:'الفرنسية',Spanish:'الإسبانية',Urdu:'الأوردية',Turkish:'التركية',Indonesian:'الإندونيسية',Malay:'الماليزية',Bengali:'البنغالية',Hindi:'الهندية',Persian:'الفارسية',German:'الألمانية',Italian:'الإيطالية',Portuguese:'البرتغالية',Russian:'الروسية',Chinese:'الصينية',Japanese:'اليابانية',Korean:'الكورية',Thai:'التايلاندية',Vietnamese:'الفيتنامية',Swahili:'السواحيلية',Hausa:'الهوسا',Somali:'الصومالية',select:'اختر لغات التدريس',placeholder:'اختر لغة واحدة أو أكثر'},courseTypes:'أنواع الدورات',qualifications:'المؤهلات',teachingExperience:'الخبرة في التدريس',availableHours:'الساعات المتاحة',pricePerLesson:'السعر لكل درس',timezone:'المنطقة الزمنية',paymentMethod:'طريقة الدفع',currency:{usd:'دولار أمريكي',eur:'يورو',gbp:'جنيه إسترليني'},experience:{beginner:'مبتدئ (0-2 سنوات)',intermediate:'متوسط (2-5 سنوات)',advanced:'متقدم (5-10 سنوات)',expert:'خبير (أكثر من 10 سنوات)'}},application:{title:'طلب التقديم للتدريس',submit:'تقديم الطلب',success:'تم تقديم الطلب بنجاح',error:'حدث خطأ أثناء تقديم الطلب',errorFetchingData:'حدث خطأ أثناء جلب البيانات',errorFetchingCategories:'حدث خطأ أثناء جلب التصنيفات',statusCardTitle:'حالة طلب التقديم',edit:'تعديل الطلب',editDescription:'يمكنك تعديل بيانات طلبك، ولكن ستحتاج إلى موافقة المشرف مرة أخرى.',warningTitle:'تحذير: تعديل الطلب',warningMessage:'تعديل بياناتك سيرسل طلب تحديث يتطلب موافقة الإدارة.',warningDescription1:'إذا تابعت، سيتم توجيهك إلى نموذج التعديل حيث يمكنك تحديث بياناتك.',warningDescription2:'ستستمر في التدريس بالبيانات الحالية حتى توافق الإدارة على طلب التحديث.',warningConfirmation:'هل أنت متأكد من أنك تريد المتابعة؟',confirmEdit:'نعم، تعديل الطلب',editApplication:{title:'تعديل الطلب',description:'تحديث بيانات طلبك. ستتم مراجعة التغييرات من قبل الإدارة قبل تطبيقها.',warning:'ستبقى بياناتك الحالية نشطة حتى توافق الإدارة على التغييرات.'},changeVideo:'تغيير الفيديو',status:{pending:'قيد المراجعة',approved:'تم القبول',rejected:'تم الرفض'},statusMessage:{pending:'طلبك قيد المراجعة',approved:'تهانينا! تم قبول طلبك كمدرس',rejected:'عذراً، تم رفض طلبك',default:'تم تحديث حالة الطلب'},applicationNextSteps:{approved:{title:'الخطوات التالية',steps:['الرجاء تسجيل الخروج من حسابك','تسجيل الدخول مرة أخرى لتفعيل صلاحياتك الكاملة كمدرس']}}},country:'الدولة',residence:'مدينة الإقامة',nativeLanguage:'اللغة الأم',teachingLanguages:'لغات التدريس',courseTypes:'أنواع الدورات',qualifications:'المؤهلات',qualificationsPlaceholder:'أدخل مؤهلاتك التعليمية والشهادات',teachingExperience:'سنوات الخبرة في التدريس',introVideo:'فيديو التعريف',introVideoUrl:'رابط فيديو التعريف',introVideoUrlPlaceholder:'https://...',noIntroVideo:'لا يوجد فيديو تعريفي متاح',videoLinkTab:'رابط الفيديو',videoFileTab:'رفع فيديو',videoLink:'رابط',videoUpload:'رفع',videoLinkHelp:'أدخل رابط من يوتيوب أو منصة فيديو أخرى',videoLinkNote:'شارك رابط فيديو التعريف الخاص بك من منصة مدعومة',videoUploadHelp:'قم برفع فيديو التعريف الخاص بك مباشرة إلى منصتنا',videoUploadPlaceholder:'رفع فيديو',videoSelected:'الفيديو المحدد',videoUploadError:'خطأ في رفع الفيديو',allowedFormats:'الصيغ المسموحة',maxFileSize:'الحجم الأقصى للملف',recommendedPlatforms:'المنصات الموصى بها',cv:'السيرة الذاتية',cvPlaceholder:'اكتب سيرتك الذاتية',cvHelperText:'اكتب ملخصًا موجزًا لمؤهلاتك وخبراتك ونهجك في التدريس (الحد الأقصى 2000 حرف)',characters:'حرف',profilePicture:'الصورة الشخصية',profilePicturePlaceholder:'ارفع صورتك الشخصية',profilePictureRequired:'الصورة الشخصية مطلوبة',availableHours:'ساعات التدريس المتاحة',availableHoursDescription:'حدد الأوقات المتاحة للتدريس في كل يوم من أيام الأسبوع',availableHoursProfileDescription:'إدارة أوقات تواجدك للتدريس لإعلام الطلاب بالأوقات المتاحة للدروس',viewAvailableHours:'عرض الساعات المتاحة',viewAvailableHoursDescription:'مراجعة جدولك الحالي للتدريس والأوقات المتاحة',noAvailableHours:'لم يتم تحديد ساعات متاحة بعد',timeSlot:'الفترة الزمنية',timeSlots:'فترات زمنية',available:'متاح',unavailable:'غير متاح',editAvailableHours:'تعديل الساعات المتاحة',weeklySchedule:'الجدول الأسبوعي',scheduleDescription:'أوقات تواجدك للتدريس خلال الأسبوع',time:'الوقت',legend:'مفتاح الرموز',quickStats:'إحصائيات سريعة',totalSlots:'إجمالي الفترات الزمنية',hoursPerWeek:'ساعات في الأسبوع',activeDays:'الأيام النشطة',pricePerLesson:'السعر لكل درس',pricePerLessonPlaceholder:'أدخل السعر بالدولار الأمريكي',trialLessonPrice:'سعر الدرس التجريبي',trialLessonPricePlaceholder:'أدخل سعر الدرس التجريبي بالدولار الأمريكي',trialPriceLessThanRegular:'يجب أن يكون سعر الدرس التجريبي أقل من أو يساوي سعر الدرس العادي',timezone:'المنطقة الزمنية',paymentMethod:'طريقة الدفع',phone:'رقم الهاتف',phoneHelp:'متضمناً رمز الدولة (مثال: 966+, 44+, 1+)',commitment:'التعهد',commitmentDescription:'يرجى قراءة والموافقة على التعهد التالي',commitmentProfileDescription:'عرض وإدارة حالة وتفاصيل التعهد الخاص بك',commitmentRequired:'يجب الموافقة على التعهد للمتابعة',commitmentStatus:{accepted:'تم قبول التعهد',pending:'في انتظار الموافقة على التعهد',rejected:'تم رفض التعهد'},formHasErrors:'يرجى تصحيح الأخطاء في النموذج قبل المتابعة',selectedHours:'ساعات مختارة',selectAll:'اختيار الكل',required:'هذا الحقل مطلوب',formHasErrors:'يرجى تصحيح الأخطاء في النموذج',priceRange:'السعر يجب أن يكون بين $3 و $100',yourEarnings:'ما ستحصل عليه بعد خصم العمولة:',invalidUrl:'الرجاء إدخال رابط صحيح',invalidPhone:'الرجاء إدخال رقم هاتف صحيح',invalidPrice:'الرجاء إدخال سعر صحيح',uploadVideoNow:'رفع الفيديو الآن',videoReady:'الفيديو جاهز',deleteVideo:'حذف الفيديو',manageAvailableHours:'إدارة الساعات المتاحة',weeklyBookings:'حجوزات الطلاب لهذا الأسبوع',weeklyBookingsDescription:'عرض حجوزات طلابك لهذا الأسبوع في شكل تقويم.',commitmentTitle:'تعهد المعلمين',commitmentAccepted:'تم قبول التعهد',readCommitment:'قراءة التعهد والموافقة عليه',accept:'أوافق',reject:'أرفض',allowedFormats:'الصيغ المسموحة',maxFileSize:'الحد الأقصى لحجم الملف',videoFormats:'MP4, WebM, OGG',maxVideoSize:'100 ميجابايت',videoRequired:'فيديو تعريفي مطلوب لطلب التقديم الخاص بك',fileTypes:{image:'الصيغ المدعومة: JPG, PNG (الحد الأقصى 5 ميجابايت)',document:'الصيغ المدعومة: PDF, DOC, DOCX (الحد الأقصى 10 ميجابايت)'},submitComplaint:'تقديم شكوى',complaintReason:'سبب الشكوى',complaintType1:'الطالب حضر لكن العمولة لم تُحول',complaintType2:'الطالب لم يحضر نهائياً',complaintDetails:'تفاصيل الشكوى (اختياري)',complaintDetailsPlaceholder:'اكتب أي تفاصيل إضافية إذا لزم الأمر...',complaintStatus:'حالة الشكوى',complaintStatusValues:{pending:'في الانتظار',resolved:'تم الحل'}},role:{admin:'مدير',teacher:'معلم',student:'طالب'},validation:{required:'هذا الحقل مطلوب',email:'يرجى إدخال بريد إلكتروني صحيح',password:{min:'يجب أن تكون كلمة المرور {{min}} أحرف على الأقل',max:'يجب أن تكون كلمة المرور {{max}} حرفاً على الأكثر',match:'كلمات المرور غير متطابقة'}},days:{monday:'الإثنين',tuesday:'الثلاثاء',wednesday:'الأربعاء',thursday:'الخميس',friday:'الجمعة',saturday:'السبت',sunday:'الأحد',mondayShort:'اثنين',tuesdayShort:'ثلاثاء',wednesdayShort:'أربعاء',thursdayShort:'خميس',fridayShort:'جمعة',saturdayShort:'سبت',sundayShort:'أحد'},hours:{hour1:'1:00 ص',hour2:'2:00 ص',hour3:'3:00 ص',hour4:'4:00 ص',hour5:'5:00 ص',hour6:'6:00 ص',hour7:'7:00 ص',hour8:'8:00 ص',hour9:'9:00 ص',hour10:'10:00 ص',hour11:'11:00 ص',hour12:'12:00 م',hour13:'1:00 م',hour14:'2:00 م',hour15:'3:00 م',hour16:'4:00 م',hour17:'5:00 م',hour18:'6:00 م',hour19:'7:00 م',hour20:'8:00 م',hour21:'9:00 م',hour22:'10:00 م',hour23:'11:00 م',hour24:'12:00 ص'},student:{profile:{title:'الملف الشخصي للطالب',complete:'أكمل ملفك الشخصي',languagePreferences:'تفضيلات اللغة',personalInfo:'المعلومات الشخصية',learningPreferences:'تفضيلات التعلم',nativeLanguage:'اللغة الأم',preferredIslamicLanguage:'اللغة المفضلة لتعلم الإسلام',preferredArabicLanguage:'اللغة المفضلة لتعلم العربية',islamLearningLanguage:'لغة تعلم الإسلام',arabicLearningLanguage:'لغة تعلم العربية',age:'العمر',country:'البلد',timezone:'المنطقة الزمنية',arabicLevel:'مستوى اللغة العربية',privateTutoring:'أنا مهتم بالدروس الخصوصية',preferGroup:'لا',preferPrivate:'نعم',updateSuccess:'تم تحديث الملف الشخصي بنجاح',editInfo:'تعديل معلومات الملف الشخصي',levels:{beginner:'مبتدئ',intermediate:'متوسط',advanced:'متقدم'},success:'تم تحديث الملف الشخصي بنجاح! جاري التوجيه إلى لوحة التحكم...'},myTeachers:'أساتذتي',noTeachersYet:'لم تقم بحجز أي درس بعد.',lessonsTaken:'عدد الدروس'},admin:{profile:{personalInfo:'المعلومات الشخصية',changePassword:'تغيير كلمة المرور',currentPassword:'كلمة المرور الحالية',newPassword:'كلمة المرور الجديدة',confirmNewPassword:'تأكيد كلمة المرور الجديدة',updateSuccess:'تم تحديث الملف الشخصي بنجاح',updateError:'خطأ في تحديث الملف الشخصي',passwordSuccess:'تم تغيير كلمة المرور بنجاح',passwordError:'خطأ في تغيير كلمة المرور',imageSuccess:'تم تحديث صورة الملف الشخصي بنجاح',imageError:'خطأ في تحديث صورة الملف الشخصي'},teacherRole:{platform_teacher:'معلم معتمد',new_teacher:'معلم جديد'},teachers:{title:'المعلمون',searchPlaceholder:'البحث عن معلمين...',name:'الاسم',email:'البريد الإلكتروني',gender:'الجنس',role:'الدور',actions:'الإجراءات',viewDetails:'عرض التفاصيل',teacherDetails:'تفاصيل المعلم',personalInfo:'المعلومات الشخصية',deleteConfirm:'هل أنت متأكد من حذف هذا المعلم؟',deleteSuccess:'تم حذف المعلم بنجاح',deleteError:'خطأ في حذف المعلم',fetchError:'خطأ في جلب المعلمين'},students:{title:'الطلاب',searchPlaceholder:'البحث عن طلاب...',name:'الاسم',email:'البريد الإلكتروني',gender:'الجنس',country:'الدولة',age:'العمر',timezone:'المنطقة الزمنية',actions:'الإجراءات',viewDetails:'عرض التفاصيل',studentDetails:'تفاصيل الطالب',personalInfo:'المعلومات الشخصية',learningPreferences:'تفضيلات التعلم',nativeLanguage:'اللغة الأم',islamLearningLanguage:'لغة تعلم الإسلام',arabicLearningLanguage:'لغة تعلم العربية',arabicProficiencyLevel:'مستوى إتقان اللغة العربية',privateTutoring:'التدريس الخصوصي',profileCompleted:'اكتمال الملف الشخصي',deleteConfirm:'هل أنت متأكد من حذف هذا الطالب؟',deleteSuccess:'تم حذف الطالب بنجاح',deleteError:'خطأ في حذف الطالب',fetchError:'خطأ في جلب الطلاب',noStudents:'لم يتم العثور على طلاب',delete:'حذف',proficiencyLevels:{beginner:'مبتدئ',intermediate:'متوسط',advanced:'متقدم'}},categories:{title:'التصنيفات',addNew:'إضافة تصنيف جديد',editTitle:'تعديل التصنيف',addTitle:'إضافة تصنيف',name:'الاسم',description:'الوصف',createdBy:'تم الإنشاء بواسطة',updateSuccess:'تم تحديث التصنيف بنجاح',createSuccess:'تم إنشاء التصنيف بنجاح',deleteSuccess:'تم حذف التصنيف بنجاح',deleteConfirm:'هل أنت متأكد من حذف هذا التصنيف؟',fetchError:'خطأ في جلب التصنيفات',saveError:'خطأ في حفظ التصنيف',deleteError:'خطأ في حذف التصنيف',unauthorized:'غير مصرح به',invalidId:'معرف التصنيف غير صالح'},languages:{title:'اللغات',addNew:'إضافة لغة جديدة',editTitle:'تعديل اللغة',addTitle:'إضافة لغة',name:'اسم اللغة',nameRequired:'اسم اللغة مطلوب',unauthorized:'غير مصرح لك بتنفيذ هذا الإجراء',fetchError:'خطأ في جلب اللغات',createSuccess:'تم إنشاء اللغة بنجاح',createError:'خطأ في إنشاء اللغة',updateSuccess:'تم تحديث اللغة بنجاح',updateError:'خطأ في تحديث اللغة',deleteSuccess:'تم حذف اللغة بنجاح',deleteError:'خطأ في حذف اللغة',deleteConfirm:'هل أنت متأكد أنك تريد حذف هذه اللغة؟',invalidId:'معرف اللغة غير صالح',saveError:'خطأ في حفظ اللغة'},applications:{searchPlaceholder:'البحث في الطلبات...',filterByStatus:'تصفية حسب الحالة',name:'الاسم',email:'البريد الإلكتروني',phone:'الهاتف',country:'الدولة',languages:'اللغات',status:'الحالة',actions:'الإجراءات',statuses:{pending:'قيد المراجعة',approved:'مقبول',rejected:'مرفوض'},title:'طلبات المعلمين',allStatuses:'جميع الحالات',viewDetails:'عرض التفاصيل',approve:'موافقة',reject:'رفض',applicationDetails:'تفاصيل الطلب',personalInfo:'المعلومات الشخصية',teachingInfo:'معلومات التدريس',documents:'المستندات',nativeLanguage:'اللغة الأم',teachingLanguages:'لغات التدريس',qualifications:'المؤهلات',experience:'الخبرة',pricePerLesson:'سعر الدرس',viewVideo:'عرض الفيديو التعريفي',viewCV:'عرض السيرة الذاتية',basicInfo:'المعلومات الأساسية',teachingDetails:'تفاصيل التدريس',schedule:'الجدول الزمني',location:'الموقع',applicationDate:'تاريخ التقديم',courseTypes:'أنواع الدورات',pricing:'التسعير',paymentMethod:'طريقة الدفع',introVideo:'الفيديو التعريفي',cv:'السيرة الذاتية',availableHours:'الساعات المتاحة',yearsOfExperience:'{{years}} سنوات من الخبرة'}},menu:{dashboard:'لوحة التحكم',meetings:'الاجتماعات',profile:'الملف الشخصي',chat:'المحادثات',platformPolicy:'سياسة المنصة'},meetings:{title:'الاجتماعات',myMeetings:'اجتماعاتي',description:'عرض وإدارة اجتماعاتك المجدولة مع المعلمين',noMeetings:'لا توجد اجتماعات مجدولة',noMeetingsDescription:'ليس لديك أي اجتماعات مجدولة حتى الآن. احجز درسًا للبدء!',teacher:'المعلم',date:'التاريخ',time:'الوقت',fetchError:'خطأ في جلب الاجتماعات',with:'مع',createNew:'إنشاء اجتماع جديد',meetingName:'اسم الاجتماع',meetingDate:'تاريخ الاجتماع',duration:'المدة',minutes:'دقيقة',create:'إنشاء الاجتماع',start:'بدء الاجتماع',join:'انضمام للاجتماع',notStarted:'لم يبدأ الاجتماع بعد',ended:'انتهى الاجتماع',joinError:'خطأ في الانضمام للاجتماع',cancel:'إلغاء الاجتماع',copyLink:'نسخ رمز الغرفة',linkCopied:'تم نسخ رمز الغرفة',cancelSuccess:'تم إلغاء الاجتماع بنجاح',cancelError:'حدث خطأ أثناء إلغاء الاجتماع',createSuccess:'تم إنشاء الاجتماع بنجاح',createError:'حدث خطأ أثناء إنشاء الاجتماع',halfLesson:'نصف حصة',fullLesson:'حصة كاملة',currency:'دولار',status:{pending:'قيد الانتظار',ongoing:'جاري',completed:'مكتمل',cancelled:'ملغي',scheduled:'مجدول'},meetingCompleted:'تم إنهاء الاجتماع بنجاح',errorCompletingMeeting:'حدث خطأ أثناء إنهاء الاجتماع',participant:'مشارك',you:'أنت',sessionTime:'وقت الجلسة',remainingTime:'الوقت المتبقي',timeUp:'انتهى الوقت',timeUpTitle:'انتهى وقت الاجتماع',timeUpMessage:'انتهى الوقت المحدد للاجتماع. سيتم إغلاق الاجتماع تلقائياً الآن.',timeUpDescription:'سيتم توجيهك إلى صفحة الحجوزات.',backToBookings:'العودة للحجوزات',active:'نشط',stopped:'متوقف',validation:{nameRequired:'اسم الاجتماع مطلوب',dateRequired:'تاريخ الاجتماع مطلوب',durationRequired:'مدة الاجتماع مطلوبة',allFieldsRequired:'جميع الحقول مطلوبة'},student:'الطالب',dateTime:'التاريخ والوقت',amount:'المبلغ',actions:'الإجراءات'},privacy:{title:'سياسة الخصوصية',intro:'توضح سياسة الخصوصية هذه كيف تقوم منصة Allemnionline (\"نحن\" أو \"المنصة\") بجمع المعلومات الشخصية لمستخدميها (\"أنت\") الذين يزورون موقعنا الإلكتروني أو يستخدمون خدماتنا التعليمية، وكيفية استخدام هذه المعلومات وحمايتها.',section1:{title:'1. المعلومات التي نجمعها',subtitle:'قد نقوم بجمع المعلومات التالية:',item1:'الاسم، وعنوان البريد الإلكتروني، ورقم الهاتف',item2:'بيانات الدفع والفوترة',item3:'معلومات الحساب (مثل المستوى اللغوي، وسجل الدروس)'},section2:{title:'2. كيفية استخدام المعلومات',subtitle:'نستخدم معلوماتك من أجل:',item1:'تقديم خدماتنا التعليمية وتحسينها',item2:'إدارة جدولة الدروس وعمليات الدفع',item3:'التواصل معك بخصوص دروسك وحسابك',item4:'ضمان أمان المنصة ومنع الاحتيال',item5:'الامتثال للالتزامات القانونية والتنظيمية'},section3:{title:'3. مشاركة المعلومات',subtitle:'نحن لا نبيع معلوماتك الشخصية لأي طرف ثالث.',description:'وقد نشاركها مع:',item1:'مزودي خدمات الدفع (مثل Stripe)',item2:'الجهات القانونية إذا طُلب منا ذلك بموجب القانون',item3:'مزودي الخدمات الموثوقين المرتبطين باتفاقيات سرية'},section4:{title:'4. أمن البيانات',content:'نطبق إجراءات تقنية وتنظيمية مناسبة لحماية بياناتك من الوصول أو التغيير أو الكشف غير المصرح به.'},section5:{title:'5. حقوقك',subtitle:'يحق لك:',item1:'الوصول إلى بياناتك الشخصية',item2:'طلب تصحيحها أو حذفها',item3:'سحب موافقتك على معالجتها',item4:'تقديم شكوى لجهة حماية البيانات (إن وُجدت)',contact:'لممارسة حقوقك، يُرجى التواصل معنا على البريد التالي: <EMAIL>'},section6:{title:'6. ملفات تعريف الارتباط (Cookies)',content:'قد يستخدم موقعنا ملفات تعريف الارتباط لتحسين تجربة التصفح. يمكنك تعديل إعدادات المتصفح للتحكم في ملفات تعريف الارتباط.'},section7:{title:'7. خصوصية الأطفال',content:'لا نقوم بجمع بيانات من الأطفال دون سن 13 عامًا بدون موافقة الوالدين. إذا كنت تعتقد أننا جمعنا بيانات من طفل دون إذن، يُرجى التواصل معنا فورًا.'},section8:{title:'8. الروابط الخارجية',content:'قد يحتوي موقعنا على روابط إلى مواقع خارجية. لسنا مسؤولين عن ممارسات الخصوصية الخاصة بتلك المواقع.'},section9:{title:'9. التعديلات على هذه السياسة',content:'قد نقوم بتحديث هذه السياسة من وقت لآخر. ونشجعك على مراجعتها بشكل دوري. سيتم نشر أي تغييرات على هذه الصفحة مع تاريخ نفاذ محدث.'},section10:{title:'10. للتواصل معنا',content:'إذا كانت لديك أي أسئلة أو استفسارات بخصوص هذه السياسة، يُرجى التواصل عبر:',email:'<EMAIL>'}},policies:{refund:{title:'سياسة الاسترداد',section1:{title:'أولًا: حالات يُمنح فيها الطالب استرداد الدرس',description:'يحق للطالب استرداد الدرس إلى رصيده داخل المنصة لإعادة جدولته، في الحالات التالية:',items:['إذا ألغى المعلم الدرس أو تغيب عنه في الموعد المحدد.','إذا تعذر عقد الدرس بسبب خلل تقني من طرف المعلم أو المنصة.']},section2:{title:'ثانيًا: حالات لا يُمنح فيها استرداد الدرس أو إعادة الجدولة',description:'لا يحق للطالب طلب استرداد الدرس أو إعادة جدولته في الحالات الآتية:',items:['إلغاء الطالب للدرس خلال أقل من 12 ساعة من موعده.','تغيّب الطالب عن حضور الدرس دون إشعار مسبق.','فقدان الاتصال بسبب ضعف الإنترنت أو عطل في جهاز الطالب.','نسيان بيانات الدخول (اسم المستخدم أو كلمة المرور).','انتهاء صلاحية الرصيد نتيجة عدم استخدام المنصة لمدة تتجاوز 180 يومًا.','حذف الطالب لحسابه بشكل اختياري.','إيقاف الحساب نتيجة مخالفة شروط الاستخدام.']},section3:{title:'ثالثًا: تحديث سياسة الاسترداد',description:'تحتفظ المنصة بحق تعديل هذه السياسة في أي وقت. ويُعد استمرار استخدام المنصة بعد تحديث السياسة موافقة ضمنية على ما ورد فيها.'},contact:{title:'للتواصل معنا',email:'📧 <EMAIL>'}},bookingPayment:{title:'سياسة الحجز والدفع',subtitle:'توضح هذه السياسة كيفية جدولة الدروس وتأكيدها وتسديد رسومها عبر منصة Allemnionline.',section1:{title:'1. حجز الدروس',points:['يمكن للطلاب حجز دروس فردية (واحد لواحد) مع المعلمين المتاحين مباشرة عبر المنصة.','يتم عرض أوقات الدروس حسب التوقيت المحلي للمستخدم.','يجب أن يتم الحجز مسبقًا ويخضع لتوفر المعلم.','بعد إتمام الحجز والدفع، يتم إرسال رسالة تأكيد تلقائيًا إلى الطالب والمعلم.']},section2:{title:'2. الدفع',points:['يجب دفع رسوم الدرس مسبقًا لتأكيد الحجز.','تتم عمليات الدفع بأمان عبر مزودي خدمات الدفع المعتمدين (مثل Stripe أو PayPal أو Wise).','يتحمل الطالب أي رسوم تحويل أو عمولات تفرضها بنوكه أو مزودو الدفع.','تُعرض أسعار الدروس بوضوح قبل إتمام الدفع.']},section3:{title:'3. العملة وأسعار الصرف',points:['تتم عمليات الدفع افتراضيًا بالدولار الأمريكي (USD).','تُعرض الأسعار بعملات أخرى لأغراض الإرشاد فقط.','إذا كانت وسيلة الدفع الخاصة بك تستخدم عملة مختلفة، فقد تُطبق رسوم تحويل عملة أو فروقات في سعر الصرف.','لا تتحمل Allemnionline أي مسؤولية عن فروقات الأسعار الناتجة عن تقلبات العملات أو الرسوم البنكية.']},section4:{title:'4. الضرائب والرسوم',points:['قد تشمل الأسعار ضرائب محلية (مثل ضريبة القيمة المضافة أو ضريبة الخدمات) حسب موقع المستخدم.','يتم عرض جميع الرسوم المطبقة بشكل شفاف قبل إتمام الدفع.']},section5:{title:'5. تأكيد الدفع والإيصالات',points:['بعد إتمام الدفع بنجاح، يتم إرسال إيصال إلكتروني إلى الطالب عبر البريد الإلكتروني.','يتم تأكيد الحجز فقط بعد معالجة الدفع بنجاح.']},section6:{title:'6. فشل أو تأخير الدفع',points:['في حال فشل الدفع أو تأخره، يُعتبر الحجز \"معلّقًا\" ولا يُعد مؤكدًا.','يجب على الطالب معالجة المشكلة فورًا لضمان الحفاظ على الحجز.']},section7:{title:'7. الدفع التلقائي أو الاشتراكات (إن وجدت)',points:['إذا اختار الطالب الاشتراك أو إعادة التعبئة التلقائية، سيتم خصم الرسوم تلقائيًا من وسيلة الدفع المحفوظة.','يمكن إلغاء الاشتراك أو تعديله في أي وقت من خلال إعدادات الحساب.']},section8:{title:'8. الدعم الفني والتواصل',description:'للاستفسارات أو المشكلات المتعلقة بالدفع، يرجى التواصل مع فريق الدعم عبر:',email:'<EMAIL>'},features:{securePayments:'الدفع الآمن',securePaymentsDesc:'جميع المعاملات محمية بتقنيات التشفير المتقدمة لضمان أمان بياناتك المالية.',multipleCurrencies:'عملات متعددة',multipleCurrenciesDesc:'ندعم مجموعة واسعة من العملات ووسائل الدفع لتسهيل عملية الدفع عليك.',instantReceipts:'إيصالات فورية',instantReceiptsDesc:'تحصل على إيصال إلكتروني فوري بعد كل عملية دفع ناجحة.',instantConfirmation:'تأكيد فوري',instantConfirmationDesc:'يتم تأكيد الحجز فوراً بعد إتمام الدفع بنجاح.'}},bookingCancellation:{title:'سياسة الحجز والإلغاء وإعادة الجدولة',subtitle:'توضح هذه السياسة الضوابط والإجراءات المتعلقة بحجز الدروس الفردية (واحد لواحد)، وإلغائها أو تعديلها، من قبل كل من الطالب والمدرس، وذلك بهدف ضمان الانضباط وتحقيق أفضل تجربة تعليمية.',studentPolicy:{title:'أولًا: سياسة الطالب',booking:{title:'1. حجز الدروس',points:['يمكن للطالب حجز أي وقت متاح في جدول المعلم.','يتم تأكيد الحجز فور إتمام الدفع عبر المنصة.']},cancellation:{title:'2. إلغاء الدرس',points:['يمكن للطالب إلغاء الدرس دون أي خصم بشرط أن يتم الإلغاء قبل 12 ساعة على الأقل من موعد الدرس.','في حال الإلغاء خلال أقل من 12 ساعة، يتم خصم قيمة الدرس كاملة.','إذا تغيب الطالب عن حضور الدرس دون إلغاء مسبق، يُعتبر الدرس مكتملًا ولا يحق له استرداد أي مبلغ.']},rescheduling:{title:'3. تعديل موعد الدرس',points:['يحق للطالب تعديل موعد الدرس مرة واحدة فقط لكل حجز، بشرط أن يتم التعديل قبل 12 ساعة على الأقل من الموعد الأصلي.','يمكن للطالب التواصل المباشر مع المعلم وطلب إعادة الجدولة بشكل ودي حتى قبل أقل من 12 ساعة، ويعود القرار في هذه الحالة إلى المعلم.']},lateArrival:{title:'4. التأخر في الحضور',points:['يُمنح الطالب فترة سماح مدتها 15 دقيقة بعد وقت بدء الدرس.','إذا لم يحضر الطالب خلال هذه الفترة ودون إشعار مسبق، يُعتبر الدرس لاغيًا ويُحسب كاملًا.']}},tutorPolicy:{title:'ثانيًا: سياسة المعلم',availability:{title:'1. إتاحة المواعيد للحجز',points:['يجب على المعلم تحديث جدوله بانتظام، وتحديد أوقات الدروس المتاحة بدقة وشفافية.']},cancellation:{title:'2. إلغاء أو إعادة جدولة الدروس',points:['يلتزم المعلم بإشعار الطالب فورًا في حال قرر إلغاء الدرس أو إعادة جدولته.','في حال تكرار إلغاء الدروس من طرف المعلم أو تغيب دون إشعار، تحتفظ المنصة بحق تعليق الحساب مؤقتًا أو اتخاذ ما يلزم من الإجراءات الإدارية.']},rescheduling:{title:'3. تعديل مواعيد الدروس',points:['يحق للمعلم إلغاء الدرس أو تعديله بشرط أن يتم ذلك قبل أكثر من 12 ساعة من الموعد المحدد.','يمكنه إعادة جدولة الدرس بشرط تقديم سبب واضح عبر المنصة وإبلاغ الطالب بذلك.']},lateArrival:{title:'4. التأخر في الحضور',points:['يُسمح للمعلم بفترة تأخير لا تتجاوز 15 دقيقة.','إذا لم يحضر المعلم بعد انقضاء هذه المدة، يُمنح الطالب خيار إعادة جدولة الدرس أو استرداد كامل المبلغ.']}},generalNotes:{title:'ملاحظات عامة',points:['يتم احتساب جميع المدد الزمنية حسب توقيت الطالب المحلي.','تحتفظ المنصة بحق تعديل هذه السياسة بما يحقق مصلحة العملية التعليمية، وسيتم إشعار جميع المستخدمين بأي تحديثات رسمية.']},summary:{forStudents:'للطالب',forTutors:'للمعلم',freeCancellation:'إلغاء مجاني قبل 12 ساعة',gracePeriod:'فترة سماح 15 دقيقة',oneReschedule:'تعديل مرة واحدة لكل حجز',cancellationBefore:'إلغاء قبل 12+ ساعة',delayAllowance:'فترة تأخير 15 دقيقة',immediateNotification:'إشعار فوري للطالب',importantNote:'ملاحظة مهمة: جميع الأوقات تُحسب حسب التوقيت المحلي للمستخدم'}}},platformPolicies:'سياسات المنصة',about:{title:'من نحن',intro:'منصة Allemnionline هي منصة تعليمية عبر الإنترنت متخصصة في تقديم خدمات تعليم اللغة العربية والمعرفة ذات الصلة بالثقافة العربية للمتعلمين من جميع أنحاء العالم، من خلال دروس مباشرة (واحد لواحد) يقدمها معلمون مؤهلون ومحترفون.',mission:'تسعى المنصة إلى جعل تعلم اللغة العربية ميسّرًا وفعّالًا، مع مراعاة الفروق الفردية والاحتياجات الخاصة لكل متعلم، وذلك باستخدام أحدث الوسائل التقنية التعليمية.',whatWeOffer:'ماذا نقدم؟',services:{privateLessons:'دروس خصوصية في اللغة العربية لجميع المستويات: من المبتدئين إلى المتقدمين.',conversationTraining:'تدريب على المحادثة، الاستماع، القراءة، والكتابة باللغة العربية.',culturalElements:'تعريف بالعناصر الأساسية من الثقافة العربية التي تساعد على فهم اللغة في سياقها الطبيعي.',digitalPlatform:'منصة رقمية متكاملة تتيح التواصل المباشر عبر الفيديو، وجدولة الدروس، والدفع الإلكتروني الآمن.',targetAudience:'خدماتنا موجهة للأطفال، والبالغين، والمهنيين، ولكل من يرغب في تعلم العربية لأغراض أكاديمية أو شخصية أو مهنية.'},ourMission:'رسالتنا',missionText:'أن نوفّر تعليمًا متميزًا للغة العربية والمعرفة الثقافية المرتبطة بها، بجودة عالية، ومن خلال التعليم المباشر والتقنيات الحديثة، وبأسلوب يحترم تنوع المتعلمين وخصوصياتهم الثقافية.',contactUs:'للتواصل معنا',contactText:'لأي استفسارات أو ملاحظات، يُرجى التواصل عبر البريد الإلكتروني:',email:'<EMAIL>'}}}};// Define the complete merged resources\nconst mergedResources={en:{translation:{...resources.en.translation,privacyPolicy:{title:'Privacy Policy & Terms of Service',intro:'Welcome to TeachMeIslam platform. Before registering, please read and agree to our privacy policy and terms of service, which include details about commission rates, booking and cancellation policies.',bookingPolicy:{title:'Booking and Cancellation Policy'},studentPolicy:{title:'Student Policy',booking:{title:'Booking Lessons',description:'Students can book a lesson at any available time in the teacher\\'s schedule. Booking is confirmed immediately upon payment through the platform.'},cancellation:{title:'Cancelling Lessons',description:'Students can cancel a lesson without penalty at least 12 hours before the scheduled time. If cancelled less than 12 hours before, the full lesson fee will be charged. If a student is absent without cancellation, the lesson is considered completed and no refund will be issued.'},rescheduling:{title:'Rescheduling Lessons',description:'Lessons can be rescheduled once per lesson, provided the change is made at least 12 hours before the original scheduled time.'},attendance:{title:'Attendance',description:'If a student is more than 15 minutes late without notice, they will be considered absent and the lesson will be counted as completed.'}},teacherPolicy:{title:'Teacher Policy',availability:{title:'Availability',description:'Teachers must regularly update their schedule and accurately indicate available times.'},cancellation:{title:'Cancelling or Rescheduling',description:'Teachers must notify students when cancelling or rescheduling any lesson as soon as possible. Repeated cancellations or absences without notice may result in temporary account suspension or appropriate administrative action.'},rescheduling:{title:'Rescheduling Lessons',description:'Teachers may suggest rescheduling with prior agreement from the student through the platform.'},attendance:{title:'Attendance',description:'Teachers are allowed a maximum delay of 15 minutes. If a teacher does not attend after 15 minutes, the student has the right to reschedule the lesson or receive a full refund.'}},generalNotes:{title:'General Notes',timeZone:'All time periods are calculated according to the student\\'s time zone.',policyChanges:'The platform reserves the right to modify these policies to benefit the educational process, with notification to users of any changes.'},commissionPolicy:{title:'Commission Policy',description:'The platform applies the following commission rates on lesson fees:',rates:{3:'For $3 lessons: 33.3% commission',4:'For $4 lessons: 25% commission',5:'For $5 lessons: 20% commission',6:'For $6 lessons: 16.7% commission',7:'For $7 and above lessons: 15% commission'},explanation:'The current commission rates have been set to be fair and motivating, and may be subject to periodic review to achieve a balance between service quality and operating costs, with teachers being notified of any changes in advance.'},agreement:{checkbox:'I have read and agree to the Privacy Policy and Terms of Service',required:'You must agree to the Privacy Policy and Terms of Service to register'}},admin:{...resources.en.translation.admin},teacher:{...resources.en.translation.teacher,myLessons:'My Lessons',studentName:'Student Name',totalLessons:'Total Lessons',noLessonsFound:'No lessons found',submitComplaint:'Submit Complaint',complaintReason:'Reason for Complaint',complaintType1:'Student attended but commission not transferred',complaintType2:'Student did not attend at all',complaintDetails:'Complaint Details (optional)',complaintDetailsPlaceholder:'Write any additional details if needed...',complaintStatus:'Complaint Status',complaintStatusValues:{pending:'Pending',resolved:'Resolved'},videoUpload:{title:'Upload Introduction Video',description:'Upload a short video introducing yourself to potential students. This video will be shown on your profile.',requirements:'Video Requirements',formatRequirement:'Allowed formats',sizeRequirement:'Maximum size',lengthRequirement:'Recommended length',minutes:'minutes',selectVideo:'Select Video',upload:'Upload Video',uploading:'Uploading...',success:'Video uploaded successfully!',videoReady:'Your video is ready. You can now continue to the application form or upload a different video.',continue:'Continue to Application',delete:'Delete Video',skipForNow:'Skip for now'},editVideoUpload:{title:'Edit Introduction Video',description:'You can change your introduction video here. The new video will replace the current one.',requirements:'Video Requirements',formatRequirement:'Format',sizeRequirement:'Size',lengthRequirement:'Duration',minutes:'minutes',selectVideo:'Select New Video',upload:'Upload Video',uploading:'Uploading...',success:'Video uploaded successfully!',videoReady:'Video is ready and saved',saveAndReturn:'Save and Return',delete:'Delete Video',invalidVideoFormat:'Invalid video format. Please select MP4, WebM, or OGG file.',videoTooSmall:'Video size must be at least 1MB.',videoTooLarge:'Video size must not exceed 100MB.',noVideoSelected:'Please select a video file.',videoDeleteError:'Error deleting video. Please try again.'},commitment:'Teacher Commitment',commitmentDescription:'Please read and agree to the following commitment',commitmentProfileDescription:'View and manage your commitment status and details',commitmentRequired:'You must agree to the commitment to continue',commitmentStatus:{accepted:'Commitment accepted',pending:'Commitment pending approval',rejected:'Commitment rejected'},commitmentTitle:'Teacher Commitment',commitmentAccepted:'Commitment accepted',readCommitment:'Read and agree to the commitment',accept:'I Agree',reject:'I Decline',yourEarnings:'Your earnings after commission:',commitmentText:{intro:'I, the teacher applying to teach through the \"Allemnionline in All Languages\" platform, hereby acknowledge and pledge the following:',point1:'To uphold honesty, integrity, and sincerity in fulfilling my educational mission, and to adhere to noble Islamic ethics in my dealings with students and the administration.',point2:'To ensure that the teaching of Islam and Arabic is in accordance with the methodology of Ahl al-Sunnah wa al-Jama\\'ah, and to respect the consensus of recognized scholars.',point3:'To adhere to the methodology of the righteous predecessors (Salaf) in creed, methodology, and conduct, and not to deviate from what is found in the Quran and Sunnah as understood by the Companions (may Allah be pleased with them) and those who followed them in righteousness.',point4:'To refrain from praising or promoting any deviant, extremist groups or ideas, or those with partisan or political orientations that contradict the methodology of the Salaf.',point5:'Not to exploit the platform to spread ideas or orientations that contradict its religious and scholarly identity, and to limit teaching to what is approved and prescribed by the platform administration.',point6:'To cooperate with the platform administration with complete transparency and respect, and to update data, work schedules, and respond to students in a timely manner.',conclusion:'I acknowledge that violating what is stated in this commitment authorizes the platform administration to take appropriate measures as it deems fit, including suspending the account or terminating cooperation.'}}}},ar:{translation:{...resources.ar.translation,privacyPolicy:{title:'سياسة الخصوصية وشروط الخدمة',intro:'مرحبًا بك في منصة TeachMeIslam. قبل التسجيل، يرجى قراءة والموافقة على سياسة الخصوصية وشروط الخدمة، والتي تتضمن تفاصيل حول معدلات العمولة وسياسات الحجز والإلغاء.',bookingPolicy:{title:'سياسة الحجز والإلغاء'},studentPolicy:{title:'سياسة الطالب',booking:{title:'حجز الدروس',description:'يمكن للطالب حجز درس في أي وقت متاح في جدول المدرس. يتم تأكيد الحجز فور إتمام الدفع عبر المنصة.'},cancellation:{title:'إلغاء الدروس',description:'يمكن للطالب إلغاء الدرس دون غرامة قبل 12 ساعة على الأقل من موعد الدرس. إذا تم الإلغاء خلال أقل من 12 ساعة، تُخصم قيمة الدرس كاملة. إذا تغيب الطالب دون إلغاء، يُعتبر الدرس مكتملًا ولا يحق له استرداد أي مبلغ.'},rescheduling:{title:'تعديل موعد الدرس',description:'يُسمح بتعديل موعد الدرس مرة واحدة فقط لكل درس، بشرط أن يكون ذلك قبل 12 ساعة من الموعد الأصلي.'},attendance:{title:'التأخير في الحضور',description:'إذا تأخر الطالب لأكثر من 15 دقيقة دون إشعار، يُعتبر غائبًا ويُحسب الدرس.'}},teacherPolicy:{title:'سياسة المدرس',availability:{title:'توفر المدرس للحجز',description:'يلتزم المدرس بتحديث جدول مواعيده بشكل منتظم، وتحديد الأوقات المتاحة بدقة.'},cancellation:{title:'إلغاء الدرس أو إعادة الجدولة',description:'يجب على المدرس إشعار الطالب عند إلغاء أو إعادة جدولة أي درس، وفي أقرب وقت ممكن. في حال تكرر إلغاء الدروس أو تغيب المدرس دون إشعار، قد يتم تعليق الحساب مؤقتًا أو اتخاذ إجراءات إدارية مناسبة.'},rescheduling:{title:'تعديل موعد الدرس',description:'يمكن للمدرس اقتراح تعديل الموعد باتفاق مسبق مع الطالب عبر المنصة.'},attendance:{title:'التأخير في الحضور',description:'يُمنح المدرس مهلة تأخير تصل إلى 15 دقيقة كحد أقصى. إذا لم يحضر المدرس بعد مرور 15 دقيقة، يُمنح الطالب حق إعادة جدولة الدرس أو استرداد قيمته كاملة.'}},generalNotes:{title:'ملاحظات عامة',timeZone:'يتم احتساب كل المدد الزمنية بحسب توقيت الطالب.',policyChanges:'تحتفظ المنصة بحقها في تعديل هذه السياسات بما يحقق مصلحة العملية التعليمية، مع إشعار المستخدمين بأي تغييرات.'},commissionPolicy:{title:'سياسة العمولة',description:'تطبق المنصة معدلات العمولة التالية على رسوم الدروس:',rates:{3:'للدروس بقيمة 3 دولار: عمولة 33.3%',4:'للدروس بقيمة 4 دولار: عمولة 25%',5:'للدروس بقيمة 5 دولار: عمولة 20%',6:'للدروس بقيمة 6 دولار: عمولة 16.7%',7:'للدروس بقيمة 7 دولار وما فوق: عمولة 15%'},explanation:'نسبة العمولة الحالية تم تحديدها لتكون عادلة ومحفزة، وقد تخضع للمراجعة الدورية بما يحقق التوازن بين جودة الخدمة وتكاليف التشغيل، على أن يُبلغ المدرسون بأي تعديل مسبقًا.'},agreement:{checkbox:'لقد قرأت وأوافق على سياسة الخصوصية وشروط الخدمة',required:'يجب أن توافق على سياسة الخصوصية وشروط الخدمة للتسجيل'}},teacher:{...resources.ar.translation.teacher,myLessons:'دروسي',studentName:'اسم الطالب',totalLessons:'إجمالي الدروس',noLessonsFound:'لا توجد دروس',submitComplaint:'تقديم شكوى',complaintReason:'سبب الشكوى',complaintType1:'الطالب حضر لكن العمولة لم تُحول',complaintType2:'الطالب لم يحضر نهائياً',complaintDetails:'تفاصيل الشكوى (اختياري)',complaintDetailsPlaceholder:'اكتب أي تفاصيل إضافية إذا لزم الأمر...',complaintStatus:'حالة الشكوى',complaintStatusValues:{pending:'في الانتظار',resolved:'تم الحل'},videoUpload:{title:'رفع فيديو تعريفي',description:'قم برفع فيديو قصير لتقديم نفسك للطلاب المحتملين. سيتم عرض هذا الفيديو على ملفك الشخصي.',requirements:'متطلبات الفيديو',formatRequirement:'الصيغ المسموح بها',sizeRequirement:'الحجم الأقصى',lengthRequirement:'المدة المقترحة',minutes:'دقائق',selectVideo:'اختر الفيديو',upload:'رفع الفيديو',uploading:'جاري الرفع...',success:'تم رفع الفيديو بنجاح!',videoReady:'فيديو التعريف جاهز. يمكنك الآن المتابعة إلى نموذج التقديم أو رفع فيديو مختلف.',continue:'متابعة إلى نموذج التقديم',delete:'حذف الفيديو',skipForNow:'تخطي الآن'},editVideoUpload:{title:'تعديل الفيديو التعريفي',description:'يمكنك تغيير الفيديو التعريفي الخاص بك هنا. الفيديو الجديد سيحل محل الفيديو الحالي.',requirements:'متطلبات الفيديو',formatRequirement:'الصيغة',sizeRequirement:'الحجم',lengthRequirement:'المدة',minutes:'دقائق',selectVideo:'اختر فيديو جديد',upload:'رفع الفيديو',uploading:'جاري الرفع...',success:'تم رفع الفيديو بنجاح!',videoReady:'الفيديو جاهز ومحفوظ',saveAndReturn:'حفظ والعودة',delete:'حذف الفيديو',invalidVideoFormat:'صيغة الفيديو غير صالحة. يرجى اختيار ملف MP4 أو WebM أو OGG.',videoTooSmall:'حجم الفيديو يجب أن يكون على الأقل 1 ميجابايت.',videoTooLarge:'حجم الفيديو يجب ألا يتجاوز 100 ميجابايت.',noVideoSelected:'يرجى اختيار ملف فيديو.',videoDeleteError:'خطأ في حذف الفيديو. يرجى المحاولة مرة أخرى.'},commitment:'التعهد',commitmentDescription:'يرجى قراءة والموافقة على التعهد التالي',commitmentProfileDescription:'عرض وإدارة حالة وتفاصيل التعهد الخاص بك',commitmentRequired:'يجب الموافقة على التعهد للمتابعة',commitmentStatus:{accepted:'تم قبول التعهد',pending:'في انتظار الموافقة على التعهد',rejected:'تم رفض التعهد'},commitmentTitle:'تعهد المعلمين',commitmentAccepted:'تم قبول التعهد',readCommitment:'قراءة التعهد والموافقة عليه',accept:'أوافق',reject:'أرفض',yourEarnings:'ما ستحصل عليه بعد خصم العمولة:',commitmentText:{intro:'أنا المدرّس/ـة المتقدّم/ـة للتدريس عبر منصة \"علّمني أون لاين بجميع اللغات\"، أُقرّ وأتعهد بما يلي:',point1:'التحلّي بالصدق والأمانة والإخلاص في أداء رسالتي التعليمية، والالتزام بالأخلاق الإسلامية الفاضلة في تعاملي مع الطلاب والإدارة.',point2:'الحرص على تعليم الإسلام والعربية وفق منهج أهل السنة والجماعة، ومراعاة ما اتفقت عليه كلمة أهل العلم المعتبرين.',point3:'الالتزام بمذهب السلف الصالح في العقيدة والمنهج والسلوك، وعدم الخروج عن ما جاء في الكتاب والسنة بفهم الصحابة رضي الله عنهم ومن تبعهم بإحسان.',point4:'الامتناع عن الإشادة أو الترويج لأي جماعات أو أفكار منحرفة أو متطرفة أو ذات طابع حزبي أو سياسي مخالف لمنهج السلف.',point5:'عدم استغلال المنصة في نشر أفكار أو توجهات مخالفة لهويتها الشرعية والعلمية، والاقتصار في التدريس على ما هو معتمد ومقرّر من قبل إدارة المنصة.',point6:'التعاون مع إدارة المنصة بكل شفافية واحترام، وتحديث البيانات وتوقيت العمل والرد على الطلاب في الوقت المحدد.',conclusion:'وأقرّ بأن مخالفة ما ورد في هذا التعهّد يخول إدارة المنصة اتخاذ ما تراه مناسبًا من إجراءات، بما في ذلك تعليق الحساب أو إنهاء التعاون.'}},admin:{...resources.ar.translation.admin,withdrawalManagement:{title:'إدارة طلبات السحب',statusFilter:'تصفية الحالة',all:'الكل',date:'التاريخ',teacher:'المعلم',amount:'المبلغ',paypalEmail:'بريد PayPal الإلكتروني',status:'الحالة',notes:'الملاحظات',actions:'الإجراءات',approve:'موافقة',reject:'رفض',approveWithdrawal:'الموافقة على السحب',rejectWithdrawal:'رفض السحب',notesOptional:'ملاحظات (اختيارية)',approveAndProcess:'موافقة ومعالجة',processingInfo:'سيتم معالجة السحب عبر PayPal Payouts API.',errorFetching:'خطأ في جلب طلبات السحب',errorProcessing:'خطأ في معالجة السحب'},earnings:{title:'أرباح المنصة',totalCommission:'إجمالي العمولة',totalLessons:'إجمالي الدروس',totalRevenue:'إجمالي الإيرادات',avgCommissionRate:'متوسط معدل العمولة',startDate:'تاريخ البداية',endDate:'تاريخ النهاية',filter:'تصفية',clearFilter:'مسح التصفية',date:'التاريخ',meeting:'الاجتماع',teacher:'المعلم',student:'الطالب',lessonAmount:'مبلغ الدرس',commissionRate:'معدل العمولة',commissionAmount:'مبلغ العمولة',teacherEarnings:'أرباح المعلم',errorFetching:'خطأ في جلب بيانات الأرباح'},dashboard:{title:'لوحة التحكم',welcomeMessage:'مرحبًا بك في لوحة تحكم المدير',overview:'إليك نظرة عامة على إحصائيات منصتك والأنشطة الحديثة.',totalTeachers:'إجمالي المعلمين',pendingApplications:'الطلبات المعلقة',totalStudents:'إجمالي الطلاب',totalRevenue:'إجمالي الإيرادات',totalCourseCategories:'إجمالي الفئات',totalLanguages:'إجمالي اللغات',totalBookings:'إجمالي الحجوزات',bookingStats:'إحصائيات الحجز',teachersByLanguage:'المعلمون حسب اللغة',studentsByCountry:'الطلاب حسب البلد',recentApplications:'الطلبات الحديثة',recentStudents:'الطلاب الجدد',viewAll:'عرض الكل',viewDetails:'عرض التفاصيل',noApplications:'لا توجد طلبات حديثة',noStudents:'لا يوجد طلاب جدد',noStudentsByCountry:'لا توجد بيانات طلاب حسب البلد',noTeachersByLanguage:'لا توجد بيانات معلمين حسب اللغة',noBookingStats:'لا توجد بيانات حجز',completed:'مكتمل',cancelled:'ملغي',pending:'قيد الانتظار',fetchError:'خطأ في جلب بيانات لوحة التحكم'},meetingSessions:{title:'تقارير وقت الاجتماعات',overview:'عرض تفصيلي لأوقات انضمام وخروج المعلمين والطلاب في الاجتماعات',totalMeetings:'إجمالي الاجتماعات',totalTime:'إجمالي الوقت',teacherTime:'وقت المعلمين',studentTime:'وقت الطلاب',filterResults:'فلترة النتائج',userType:'نوع المستخدم',all:'الكل',teacher:'معلم',student:'طالب',reset:'إعادة تعيين',sessions:'جلسات الاجتماعات',user:'المستخدم',type:'النوع',meeting:'الاجتماع',joinTime:'وقت الانضمام',leaveTime:'وقت الخروج',duration:'المدة',status:'الحالة',active:'نشط',ended:'منتهي'},meetingIssues:{title:'مشاكل الاجتماعات'},messages:{title:'الرسائل',from:'من',type:'النوع',subject:'الموضوع',date:'التاريخ',status:'الحالة',pending:'قيد الانتظار',answered:'تم الرد',all:'جميع الرسائل',view:'عرض',reply:'رد',delete:'حذف',search:'البحث في الرسائل...',noMessages:'لا توجد رسائل',fetchError:'خطأ في جلب الرسائل',replyTo:'رد على',originalMessage:'الرسالة الأصلية',yourReply:'ردك',sendReply:'إرسال الرد',replySent:'تم إرسال الرد بنجاح',replyError:'خطأ في إرسال الرد',confirmDelete:'تأكيد الحذف',deleteWarning:'هل أنت متأكد من رغبتك في حذف هذه الرسالة؟ لا يمكن التراجع عن هذا الإجراء.',deleteSuccess:'تم حذف الرسالة بنجاح',deleteError:'خطأ في حذف الرسالة'},emailTemplates:{title:'قوالب البريد الإلكتروني',description:'إدارة قوالب البريد الإلكتروني لإشعارات النظام المختلفة.',instructions:'قم بتحرير القوالب أدناه لتخصيص رسائل البريد الإلكتروني المرسلة للمستخدمين. يمكنك استخدام HTML والمتغيرات لتخصيص المحتوى.',approvalTemplate:'قالب الموافقة على الطلب',rejectionTemplate:'قالب رفض الطلب',save:'حفظ القالب',saveSuccess:'تم حفظ القالب بنجاح',saveError:'خطأ في حفظ القالب',fetchError:'خطأ في جلب القوالب',restoreDefault:'استعادة الافتراضي',preview:'معاينة',enterTemplate:'أدخل محتوى القالب هنا...',availableVariables:'المتغيرات المتاحة',variablesHelp:'سيتم استبدال هذه المتغيرات بالقيم الفعلية عند إرسال البريد الإلكتروني.',testEmail:'بريد إلكتروني تجريبي',testEmailPlaceholder:'أدخل عنوان البريد الإلكتروني للاختبار',sendTest:'إرسال بريد تجريبي',testEmailSuccess:'تم إرسال البريد التجريبي بنجاح',testEmailError:'خطأ في إرسال البريد التجريبي',variables:{teacherName:'الاسم الكامل للمعلم',teacherEmail:'عنوان البريد الإلكتروني للمعلم',dashboardUrl:'رابط لوحة تحكم المعلم',rejectionReason:'سبب رفض الطلب'}},earnings:{title:'أرباح المنصة',totalCommission:'إجمالي العمولة',totalLessons:'إجمالي الدروس',totalRevenue:'إجمالي الإيرادات',avgCommissionRate:'متوسط معدل العمولة',startDate:'تاريخ البداية',endDate:'تاريخ النهاية',filter:'تصفية',clearFilter:'مسح التصفية',date:'التاريخ',meeting:'الاجتماع',teacher:'المعلم',student:'الطالب',lessonAmount:'مبلغ الدرس',commissionRate:'معدل العمولة',commissionAmount:'مبلغ العمولة',teacherEarnings:'أرباح المعلم',errorFetching:'خطأ في جلب بيانات الأرباح'},passwordManagement:{title:'إدارة كلمات المرور',description:'إدارة طلبات إعادة تعيين كلمة المرور وكلمات مرور المستخدمين',searchPlaceholder:'البحث بالاسم أو البريد الإلكتروني',user:'المستخدم',email:'البريد الإلكتروني',role:'الدور',requestDate:'تاريخ الطلب',status:'الحالة',actions:'الإجراءات',resetPassword:'إعادة تعيين كلمة المرور',resetPasswordFor:'إعادة تعيين كلمة المرور لـ {name}',newPassword:'كلمة المرور الجديدة',generatePassword:'توليد كلمة مرور',resetSuccess:'تم إعادة تعيين كلمة المرور بنجاح لـ {email}',resetError:'خطأ في إعادة تعيين كلمة المرور',noRequests:'لا توجد طلبات إعادة تعيين كلمة المرور',statusPending:'قيد الانتظار',statusCompleted:'مكتمل',sendTestEmail:'إرسال بريد إلكتروني تجريبي',testEmailAddress:'عنوان البريد الإلكتروني التجريبي',testEmailDescription:'إرسال بريد إلكتروني تجريبي لإعادة تعيين كلمة المرور للتحقق من قالب البريد الإلكتروني والتسليم',sendEmail:'إرسال بريد إلكتروني',testEmailSent:'تم إرسال البريد الإلكتروني التجريبي بنجاح إلى {email}',testEmailError:'خطأ في إرسال البريد الإلكتروني التجريبي'}}}}};i18n.use(initReactI18next).init({resources:mergedResources,lng:localStorage.getItem('language')||'en',fallbackLng:'en',debug:false,interpolation:{escapeValue:false},react:{useSuspense:false},// Force reload translations\nload:'languageOnly',cleanCode:true});export default i18n;", "map": {"version": 3, "names": ["i18n", "initReactI18next", "meetingIssuesEn", "meetingIssuesAr", "resources", "en", "translation", "appName", "aboutUs", "wallet", "title", "balance", "currentBalance", "transactionHistory", "allTransactions", "noTransactions", "errorFetchingTransactions", "date", "description", "amount", "status", "debit", "credit", "pending", "completed", "failed", "cancelled", "payment", "lessonWith", "lessonFrom", "student", "teacher", "rowsPerPage", "addMoney", "payWithStripe", "pay", "depositSuccess", "errorProcessingPayment", "paypalDeposit", "stripeDeposit", "walletDeposit", "withdrawal", "availableBalance", "requestWithdrawal", "withdrawalHistory", "no<PERSON><PERSON><PERSON><PERSON><PERSON>", "paypalEmail", "actions", "cancel", "submit", "processing", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimumAmount", "insufficientBalance", "requestSubmitted", "requestCancelled", "errorSubmitting", "errorCancelling", "errorFetchingWithdrawals", "paypalEmailHelp", "processingTime", "minimumWithdrawal", "enterOTPTitle", "otpInstructions", "otpCode", "verify", "enterOTP", "errorVerifyingOTP", "requestCompleted", "withdrawalCancelled", "admin", "withdrawalManagement", "statusFilter", "all", "notes", "approve", "reject", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notesOptional", "approveAndProcess", "processingInfo", "errorFetching", "errorProcessing", "contactUs", "type", "typeQuestion", "typeProblem", "typeSuggestion", "typePayment", "typeOther", "subject", "message", "send", "messageSent", "sendError", "startConversation", "myMessages", "answered", "noMessages", "fetchError", "yourMessage", "adminReply", "awaitingReply", "booking", "bookLessonWith", "pricePer<PERSON><PERSON>on", "instructions", "instructionsText", "selectTimeSlot", "noAvailableSlots", "day", "time", "price", "confirmBooking", "confirmAndPay", "bookingSuccessTitle", "bookingSuccessMessage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentWeek", "weekOf", "previousWeek", "nextWeek", "weekNavigation", "viewMyBookings", "bookAgain", "bookingFailed", "selectDuration", "duration", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "meetingCreated", "meetingAccessInfo", "viewMyMeetings", "reviews", "writeReview", "myReviews", "teacherReviews", "rating", "comment", "update", "delete", "confirmDelete", "<PERSON><PERSON><PERSON><PERSON>", "noTeachers", "noCompletedLessons", "allTeachersR<PERSON>iewed", "allTeachersReviewedShort", "noReviews", "yourReview", "edit<PERSON><PERSON>ie<PERSON>", "reviewSuccess", "reviewUpdateSuccess", "reviewDeleteSuccess", "reviewError", "reviewRequired", "commentPlaceholder", "averageRating", "totalReviews", "oneReview", "reviewsBy", "reviewBy", "on", "stars", "star", "outOf5", "selectRating", "reviewsFor", "reviewsWritten", "reviewsReceived", "viewAll", "filterBy", "sortBy", "newest", "oldest", "highestRated", "lowestRated", "noComment", "noReviewsWithFilter", "teacher<PERSON><PERSON><PERSON>", "replyToReview", "editReply", "deleteReply", "replyPlaceholder", "sendReply", "updateReply", "replySuccess", "replyUpdateSuccess", "replyDeleteSuccess", "replyError", "confirmDeleteReply", "replyRequired", "sending", "about", "intro", "mission", "what<PERSON>eOffer", "services", "privateLessons", "conversationTraining", "culturalElements", "digitalPlatform", "targetAudience", "ourMission", "missionText", "contactText", "email", "app", "name", "copyright", "tagline", "brand", "common", "switchLanguage", "loading", "settings", "profile", "back", "continue", "confirm", "success", "error", "save", "saveAndReturn", "saving", "notProvided", "search", "close", "password", "fullName", "confirmPassword", "showMore", "showLess", "gender", "male", "female", "notSet", "edit", "view", "details", "footer", "currency", "updateSuccess", "passwordUpdateSuccess", "teachingInfoUpdateSuccess", "editInfo", "editTeachingInfo", "errors", "passwordUpdate", "passwordMismatch", "currentPassword", "updateFailed", "deleteRequest", "invalidCode", "codeRequired", "cancelDelete", "basicInfo", "teacherInfo", "changePassword", "newPassword", "editPersonalInfo", "togglePasswordVisibility", "uploadPhoto", "deleteAccount", "deleteAccountTitle", "deleteAccount<PERSON><PERSON>ning", "deleteAccountNote", "sendDeleteCode", "verifyDeleteCode", "deleteCodeSentMessage", "deleteCode", "deleteCodeSent", "deletePending", "deleteCancelled", "deletePending<PERSON><PERSON><PERSON>", "deletePendingMessage", "deleteScheduledFor", "updateStatus", "approved", "rejected", "requestDate", "reviewDate", "adminNotes", "pendingNote", "videoUpload", "requirements", "formatRequirement", "sizeRequirement", "lengthRequirement", "maximum", "minutes", "selectVideo", "upload", "uploading", "videoReady", "skipFor<PERSON>ow", "invalidVideoFormat", "videoTooSmall", "videoTooLarge", "noVideoSelected", "videoDeleteError", "uploadVideoNow", "deleteVideo", "availableHoursDescription", "availableHoursApplicationDescription", "availableHoursProfileDescription", "viewAvailableHours", "viewAvailableHoursDescription", "editAvailableHours", "noHoursForDay", "manageAvailableHours", "hoursSavedSuccess", "myLessons", "studentName", "totalLessons", "completedLessons", "scheduledLessons", "cancelledLessons", "noLessonsFound", "errorSavingHours", "errorLoadingHours", "errorParsingHours", "timeSlots", "noAvailableHours", "selectAll", "clearAll", "timeSlot", "available", "unavailable", "selectAllDays", "clearAllDays", "legend", "cellStatus", "changeProfilePicture", "personalInfo", "teachingInfo", "phone", "country", "residence", "nativeLanguage", "teachingLanguages", "Arabic", "English", "French", "Spanish", "Urdu", "Turkish", "Indonesian", "Malay", "Bengali", "Hindi", "Persian", "German", "Italian", "Portuguese", "Russian", "Chinese", "Japanese", "Korean", "Thai", "Vietnamese", "Swahili", "Hausa", "Somali", "select", "placeholder", "courseTypes", "qualifications", "teachingExperience", "availableHours", "timezone", "paymentMethod", "usd", "eur", "gbp", "experience", "beginner", "intermediate", "advanced", "expert", "genders", "nav", "home", "dashboard", "teachers", "students", "deletedUsers", "categories", "languages", "applications", "profileUpdates", "<PERSON><PERSON><PERSON><PERSON>", "myTeachers", "meetings", "chat", "login", "register", "logout", "language", "english", "arabic", "adminDashboard", "teacherDashboard", "studentDashboard", "totalDeleted", "deletedStudents", "deletedTeachers", "activeUsers", "newTeachers", "refresh", "user", "role", "deletionDate", "deletionReason", "deletedBy", "viewDetails", "restoreUser", "permanentDelete", "new<PERSON><PERSON><PERSON>", "notSpecified", "selfDeleted", "restoreConfirmTitle", "restoreConfirmMessage", "restoreConfirmNote", "restore", "restoring", "permanentDeleteTitle", "permanentDeleteW<PERSON>ning", "permanentDeleteMessage", "permanentDeleteNote", "permanent<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deleting", "displayedRows", "deletionInfo", "scheduledDeletion", "userInfo", "studentInfo", "joinDate", "totalStudents", "noRating", "totalEarnings", "totalBookings", "totalSpent", "platformName", "designedBy", "followUs", "facebook", "twitter", "instagram", "quickLinks", "contact", "privacy", "terms", "faq", "support", "hero", "subtitle", "startLearning", "become<PERSON><PERSON>er", "imageAlt", "subjects", "subjectsSubtitle", "expertTeachers", "expertTeachersDesc", "activeStudents", "courses", "coursesDesc", "studentsDesc", "whyChooseUs", "whyChooseUsSubtitle", "meetTeachers", "meetTeachersSubtitle", "features", "quality", "qualityDesc", "flexible", "flexibleDesc", "interactive", "interactiveDesc", "certified", "certifiedDesc", "testimonials", "testimonialsSubtitle", "reviewFor", "learnMore", "filters", "allSubjects", "allLanguages", "priceRange", "anyRating", "andAbove", "noTeachersFound", "yearsOfExperience", "<PERSON><PERSON><PERSON><PERSON>", "perHour", "searchButton", "clearFilters", "<PERSON><PERSON><PERSON>er", "watchIntro", "watchIntroVideo", "viewProfile", "contactAndBook", "clickToSelectSlot", "clickToBook", "fullHourAvailable", "halfHourOnly", "selectBookingOption", "bookingType", "regularHalf<PERSON>esson", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "secondHalfOnly", "fullLessonFromSecondHalf", "crossHourLesson", "<PERSON><PERSON><PERSON><PERSON>", "trialLessonEligible", "selectTimeRange", "startTime", "endTime", "lessonDuration", "from", "to", "availableSlot", "bookings", "weeklyTitle", "weeklyDescription", "bookingDetails", "noBookings", "timeRange", "statusValues", "scheduled", "issue_reported", "ongoing", "confirmCancel", "cancelWarning", "cancellationReason", "cancellationReasonPlaceholder", "confirmCancelButton", "cancelling", "cancelSuccess", "cancelError", "currentTime", "pastSlot", "takeBreak", "takeBreakTitle", "takeBreakMessage", "takeBreakNote", "breakTakenSuccess", "breakTakenError", "break", "clickToTakeBreak", "cancelBreakTitle", "cancelBreakMessage", "cancelBreakNote", "breakCancelledSuccess", "breakCancelledError", "reschedule", "rescheduleTitle", "rescheduleDescription", "currentBooking", "selectDay", "chooseDay", "selectTime", "chooseTime", "loadingDays", "loadingTimes", "noAvailableDays", "noAvailableTimes", "availableTimes", "rescheduleReason", "rescheduleReasonPlaceholder", "rescheduleSuccess", "rescheduleError", "fetchDaysError", "fetchTimesError", "selectNewDate", "selectNewTime", "selectDateFirst", "selectedTime", "confirmReschedule", "availableSlots", "backToSelectDay", "teacherDetails", "backToSearch", "teacherNotFound", "hideIntroVideo", "teachingSubjects", "studentReviews", "contactInfo", "location", "memberSince", "paymentInfo", "cv", "introVideo", "conversations", "messages", "typeMessage", "noChats", "startChat", "today", "yesterday", "online", "offline", "sent", "delivered", "read", "retry", "onlyStudents", "no_conversations", "type_message", "select_conversation", "last_seen", "typing", "deleteConfirmTitle", "deleteConfirmMessage", "conversationDeleted", "conversationDeletedByOther", "deleteConversationError", "messageDeleted", "messageEdited", "editError", "deleteError", "noConversations", "auth", "logoutConfirmTitle", "logoutConfirmMessage", "welcomeBack", "forgotPassword", "forgotPasswordInstructions", "sendResetCode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetCodeSent", "resetRequestFailed", "verifyCode", "verifyCodeInstructions", "verificationCode", "verifyAndContinue", "resendCode", "changeEmail", "verifyEmail", "verificationCodeSentTo", "verifying", "verificationCodeSent", "verificationFailed", "resendFailed", "resendCodeIn", "loginFailed", "invalidCredentials", "emailNotFound", "wrongPassword", "accountDeleted", "resetCodeResent", "resetPassword", "resetPasswordInstructions", "passwordResetSuccess", "passwordResetSuccessMessage", "redirectingToLogin", "resetPasswordFailed", "signIn", "or", "noAccount", "signUp", "continueWithGoogle", "googleSignInError", "signInWithGoogle", "loginError", "accountNotFound", "invalidRole", "chooseAccountType", "registerDescription", "register<PERSON><PERSON><PERSON>er", "teacherDescription", "registerAsStudent", "studentDescription", "alreadyHaveAccount", "getStarted", "studentRegistration", "teacherRegistration", "joinOurCommunity", "joinOurTeachers", "createAccount", "registrationError", "selectGender", "Islamic_Law", "<PERSON><PERSON><PERSON>", "Quran", "<PERSON><PERSON><PERSON>", "Islamic_History", "Arabic_Grammar", "Arabic_Speaking", "Arabic_Writing", "Islamic_Ethics", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "welcome", "unauthorized", "totalClasses", "recentBookings", "recentReviews", "learningProgress", "quickActions", "recommendedTeachers", "upcomingLessons", "noUpcomingLessons", "updateProfile", "browseCourses", "incompleteProfile", "completeProfileMessage", "completeProfileToAccess", "completeProfile", "completeProfileNow", "regions", "middle_east", "north_africa", "sub_saharan_africa", "south_asia", "southeast_asia", "central_asia", "europe", "north_america", "south_america", "east_asia", "oceania", "others", "phoneOptional", "application", "errorFetchingData", "errorFetchingCategories", "statusCardTitle", "editDescription", "warningTitle", "warningMessage", "warningDescription1", "warningDescription2", "warningConfirmation", "confirmEdit", "editApplication", "warning", "changeVideo", "statusMessage", "default", "applicationNextSteps", "steps", "qualificationsPlaceholder", "introVideoUrl", "introVideoUrlPlaceholder", "noIntroVideo", "videoLinkTab", "videoFileTab", "videoLink", "videoLinkHelp", "videoLinkNote", "videoUploadHelp", "videoUploadPlaceholder", "videoSelected", "videoUploadError", "formHasErrors", "allowedFormats", "maxFileSize", "recommendedPlatforms", "cvPlaceholder", "cvHelperText", "characters", "profilePicture", "profilePicturePlaceholder", "profilePictureRequired", "weeklySchedule", "scheduleDescription", "quickStats", "totalSlots", "hoursPerWeek", "activeDays", "pricePerLessonPlaceholder", "trialLessonPrice", "trialLessonPricePlaceholder", "trialPriceLessThanRegular", "phoneHelp", "selectedHours", "required", "yourEarnings", "invalidUrl", "invalidPhone", "invalidPrice", "videoFormats", "maxVideoSize", "videoRequired", "commitment", "commitmentDescription", "commitmentProfileDescription", "commitmentRequired", "commitmentStatus", "accepted", "commitmentTitle", "commitmentAccepted", "readCommitment", "accept", "weeklyBookings", "weeklyBookingsDescription", "fileTypes", "image", "document", "submitComplaint", "complaintReason", "complaintType1", "complaintType2", "complaintDetails", "complaintDetailsPlaceholder", "complaintStatus", "complaintStatusValues", "resolved", "validation", "min", "max", "match", "days", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday", "mondayShort", "tuesdayShort", "wednesdayShort", "thursdayShort", "fridayShort", "saturdayShort", "sundayShort", "hours", "hour1", "hour2", "hour3", "hour4", "hour5", "hour6", "hour7", "hour8", "hour9", "hour10", "hour11", "hour12", "hour13", "hour14", "hour15", "hour16", "hour17", "hour18", "hour19", "hour20", "hour21", "hour22", "hour23", "hour24", "complete", "languagePreferences", "learningPreferences", "preferredIslamicLanguage", "preferredArabicLanguage", "islamLearningLanguage", "arabicLearningLanguage", "age", "arabicLevel", "privateTutoring", "preferGroup", "preferPrivate", "levels", "noTeachersYet", "lessonsTaken", "welcomeMessage", "overview", "totalTeachers", "pendingApplications", "totalRevenue", "totalCourseCategories", "totalLanguages", "bookingStats", "teachersByLanguage", "studentsByCountry", "recentApplications", "recentStudents", "noApplications", "noStudents", "noStudentsByCountry", "noTeachersByLanguage", "noBookingStats", "meetingSessions", "totalMeetings", "totalTime", "teacherTime", "studentTime", "filterResults", "userType", "reset", "sessions", "meeting", "joinTime", "leaveTime", "active", "ended", "meetingIssues", "reply", "replyTo", "originalMessage", "yourReply", "replySent", "deleteWarning", "deleteSuccess", "emailTemplates", "approvalTemplate", "rejectionTemplate", "saveSuccess", "saveError", "restoreDefault", "preview", "enterTemplate", "availableVariables", "variablesHelp", "testEmail", "testEmailPlaceholder", "sendTest", "testEmailSuccess", "testEmailError", "variables", "<PERSON><PERSON><PERSON>", "teacherEmail", "dashboardUrl", "rejectionReason", "earnings", "totalCommission", "avgCommissionRate", "startDate", "endDate", "filter", "clearFilter", "lessonAmount", "commissionRate", "commissionAmount", "teacherEarnings", "passwordManagement", "searchPlaceholder", "resetPasswordFor", "generatePassword", "resetSuccess", "resetError", "noRequests", "statusPending", "statusCompleted", "sendTestEmail", "testEmail<PERSON><PERSON><PERSON>", "testEmailDescription", "sendEmail", "testEmailSent", "confirmNewPassword", "updateError", "passwordSuccess", "passwordError", "imageSuccess", "imageError", "teacher<PERSON><PERSON>", "platform_teacher", "new_teacher", "deleteConfirm", "studentDetails", "arabicProficiencyLevel", "profileCompleted", "proficiencyLevels", "addNew", "editTitle", "addTitle", "created<PERSON>y", "createSuccess", "invalidId", "nameRequired", "createError", "filterByStatus", "statuses", "allStatuses", "applicationDetails", "documents", "viewVideo", "viewCV", "teachingDetails", "schedule", "applicationDate", "pricing", "menu", "platformPolicy", "myMeetings", "noMeetings", "noMeetingsDescription", "with", "createNew", "meeting<PERSON>ame", "meetingDate", "create", "start", "join", "notStarted", "joinError", "copyLink", "linkCopied", "consecutiveSlots", "meetingCompleted", "errorCompletingMeeting", "participant", "you", "sessionTime", "remainingTime", "timeUp", "timeUpTitle", "timeUpMessage", "timeUpDescription", "backToBookings", "stopped", "dateRequired", "durationRequired", "allFieldsRequired", "dateTime", "section1", "item1", "item2", "item3", "section2", "item4", "item5", "section3", "section4", "content", "section5", "section6", "section7", "section8", "section9", "section10", "policies", "refund", "items", "bookingPayment", "points", "securePayments", "securePaymentsDesc", "multipleCurrencies", "multipleCurrenciesDesc", "instantReceipts", "instantReceiptsDesc", "instantConfirmation", "instantConfirmationDesc", "bookingCancellation", "studentPolicy", "cancellation", "rescheduling", "lateArrival", "tutorPolicy", "availability", "generalNotes", "summary", "forStudents", "forTutors", "freeCancellation", "<PERSON><PERSON><PERSON><PERSON>", "oneReschedule", "cancellationBefore", "delayAllowance", "immediateNotification", "importantNote", "mailingAddress", "company", "address", "note", "number", "website", "url", "platformPolicies", "ar", "fullHour", "mergedResources", "privacyPolicy", "bookingPolicy", "attendance", "teacherPolicy", "timeZone", "policyChanges", "commissionPolicy", "rates", "explanation", "agreement", "checkbox", "editVideoUpload", "commitmentText", "point1", "point2", "point3", "point4", "point5", "point6", "conclusion", "use", "init", "lng", "localStorage", "getItem", "fallbackLng", "debug", "interpolation", "escapeValue", "react", "useSuspense", "load", "cleanCode"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/i18n/i18n.js"], "sourcesContent": ["import i18n from 'i18next';\nimport { initReactI18next } from 'react-i18next';\nimport meetingIssuesEn from './adminMeetingIssues.en.json';\nimport meetingIssuesAr from './adminMeetingIssues.ar.json';\n\nconst resources = {\n  en: {\n    translation: {\n      appName: 'Allemnionline',\n      aboutUs: 'About Us',\n      wallet: {\n        title: 'Wallet',\n        balance: 'Balance',\n        currentBalance: 'Current Balance',\n        transactionHistory: 'Transaction History',\n        allTransactions: 'All Transactions',\n        noTransactions: 'No transactions found',\n        errorFetchingTransactions: 'Error fetching transactions. Please try again.',\n        date: 'Date',\n        description: 'Description',\n        amount: 'Amount',\n        status: 'Status',\n        debit: 'Debit',\n        credit: 'Credit',\n        pending: 'Pending',\n        completed: 'Completed',\n        failed: 'Failed',\n        cancelled: 'Cancelled',\n        payment: 'Payment',\n        lessonWith: 'Lesson with {{teacher}}',\n        lessonFrom: 'Lesson from {{student}}',\n        student: 'Student',\n        teacher: 'Teacher',\n        rowsPerPage: 'Rows per page',\n        addMoney: 'Add Money',\n        payWithStripe: 'Pay with Credit Card',\n        pay: 'Pay',\n        depositSuccess: 'Deposit successful! Your wallet has been updated.',\n        errorProcessingPayment: 'Error processing payment. Please try again.',\n        paypalDeposit: 'PayPal Deposit',\n        stripeDeposit: 'Stripe Deposit',\n        walletDeposit: 'Wallet Deposit'\n      },\n      withdrawal: {\n        title: 'Withdrawal',\n        availableBalance: 'Available Balance',\n        requestWithdrawal: 'Request Withdrawal',\n        withdrawalHistory: 'Withdrawal History',\n        noWithdrawals: 'No withdrawal requests found',\n        date: 'Date',\n        amount: 'Amount',\n        paypalEmail: 'PayPal Email',\n        status: 'Status',\n        actions: 'Actions',\n        cancel: 'Cancel',\n        submit: 'Submit',\n        pending: 'Pending',\n        processing: 'Processing',\n        completed: 'Completed',\n        failed: 'Failed',\n        cancelled: 'Cancelled',\n        fillAllFields: 'Please fill all required fields',\n        minimumAmount: 'Minimum withdrawal amount is ${{amount}}',\n        insufficientBalance: 'Insufficient balance',\n        requestSubmitted: 'Withdrawal request submitted successfully',\n        requestCancelled: 'Withdrawal request cancelled successfully',\n        errorSubmitting: 'Error submitting withdrawal request',\n        errorCancelling: 'Error cancelling withdrawal request',\n        errorFetchingWithdrawals: 'Error fetching withdrawal history',\n        paypalEmailHelp: 'Enter the PayPal email where you want to receive the payment',\n        processingTime: 'Processing time: {{days}} business days',\n        minimumWithdrawal: 'Minimum withdrawal',\n        enterOTPTitle: 'Enter Verification Code',\n        otpInstructions: 'Please enter the 6-digit verification code sent to your email.',\n        otpCode: 'Verification Code',\n        verify: 'Verify',\n        enterOTP: 'Please enter the verification code',\n        errorVerifyingOTP: 'Error verifying code',\n        requestCompleted: 'Withdrawal request completed successfully',\n        withdrawalCancelled: 'Withdrawal request has been cancelled due to multiple failed attempts',\n      },\n      admin: {\n        withdrawalManagement: {\n          title: 'Withdrawal Management',\n          statusFilter: 'Status Filter',\n          all: 'All',\n          date: 'Date',\n          teacher: 'Teacher',\n          amount: 'Amount',\n          paypalEmail: 'PayPal Email',\n          status: 'Status',\n          notes: 'Notes',\n          actions: 'Actions',\n          approve: 'Approve',\n          reject: 'Reject',\n          approveWithdrawal: 'Approve Withdrawal',\n          rejectWithdrawal: 'Reject Withdrawal',\n          notesOptional: 'Notes (Optional)',\n          approveAndProcess: 'Approve & Process',\n          processingInfo: 'This will process the withdrawal via PayPal Payouts API.',\n          errorFetching: 'Error fetching withdrawal requests',\n          errorProcessing: 'Error processing withdrawal'\n        }\n      },\n      contactUs: {\n        title: 'Contact Us',\n        type: 'Message Type',\n        typeQuestion: 'Question',\n        typeProblem: 'Problem',\n        typeSuggestion: 'Suggestion',\n        typePayment: 'Payment Issue',\n        typeOther: 'Other',\n        subject: 'Subject',\n        message: 'Message',\n        send: 'Send Message',\n        messageSent: 'Your message has been sent successfully. We will get back to you soon.',\n        fillAllFields: 'Please fill all required fields',\n        sendError: 'Error sending message. Please try again later.',\n        startConversation: 'Start a conversation with us'\n      },\n      myMessages: {\n        title: 'My Messages',\n        subject: 'Subject',\n        type: 'Type',\n        date: 'Date',\n        status: 'Status',\n        pending: 'Pending',\n        answered: 'Answered',\n        noMessages: 'No messages found',\n        fetchError: 'Error fetching messages',\n        yourMessage: 'Your Message',\n        adminReply: 'Admin Reply',\n        awaitingReply: 'Awaiting reply from admin...'\n      },\n      booking: {\n        title: 'Book a Lesson',\n        bookLessonWith: 'Book a Lesson with',\n        pricePerLesson: 'Price per lesson',\n        instructions: 'Booking Instructions',\n        instructionsText: 'Select a time slot from the available hours below. Once you confirm your booking, the amount will be deducted from your wallet balance.',\n        selectTimeSlot: 'Select a Time Slot',\n        noAvailableSlots: 'No available slots for this day',\n        teacher: 'Teacher',\n        day: 'Day',\n        date: 'Date',\n        time: 'Time',\n        price: 'Price',\n        confirmBooking: 'Confirm Booking',\n        confirmAndPay: 'Confirm & Pay',\n        bookingSuccessTitle: 'Booking Successful!',\n        bookingSuccessMessage: 'Your lesson has been booked successfully. You can view your bookings in the My Bookings section.',\n        backToTeacher: 'Back to Teacher',\n        currentWeek: 'Current Week',\n        weekOf: 'Week of',\n        previousWeek: 'Previous Week',\n        nextWeek: 'Next Week',\n        weekNavigation: 'Week Navigation',\n        viewMyBookings: 'View My Bookings',\n        bookAgain: 'Book Again',\n        bookingFailed: 'Booking failed. Please try again.',\n        insufficientBalance: 'Insufficient balance. Please add funds to your wallet.',\n        selectDuration: 'Select Lesson Duration',\n        duration: 'Duration',\n        fullLesson: 'Full Lesson ({{duration}} minutes)',\n        halfLesson: 'Half Lesson ({{duration}} minutes)',\n        meetingCreated: 'Meeting Created',\n        meetingAccessInfo: 'You can access this meeting from your Meetings page when it\\'s time for your lesson.',\n        viewMyMeetings: 'View My Meetings'\n      },\n      reviews: {\n        title: 'Reviews',\n        writeReview: 'Write a Review',\n        myReviews: 'My Reviews',\n        teacherReviews: 'Teacher Reviews',\n        rating: 'Rating',\n        comment: 'Comment',\n        submit: 'Submit Review',\n        update: 'Update Review',\n        delete: 'Delete Review',\n        confirmDelete: 'Are you sure you want to delete this review?',\n        selectTeacher: 'Select a Teacher',\n        noTeachers: 'We couldn\\'t find any teachers available for review. Please make sure you have completed at least one lesson before writing a review.',\n        noCompletedLessons: 'You haven\\'t had lessons with any teachers yet. Please book and complete a lesson first.',\n        allTeachersReviewed: 'You have already reviewed all teachers you\\'ve had lessons with. You can update your existing reviews below.',\n        allTeachersReviewedShort: 'You have reviewed all your teachers',\n        noReviews: 'No reviews yet',\n        yourReview: 'Your Review',\n        editReview: 'Edit Review',\n        reviewSuccess: 'Review submitted successfully',\n        reviewUpdateSuccess: 'Review updated successfully',\n        reviewDeleteSuccess: 'Review deleted successfully',\n        reviewError: 'Error submitting review',\n        reviewRequired: 'Please provide a rating',\n        commentPlaceholder: 'Share your experience with this teacher...',\n        averageRating: 'Average Rating',\n        totalReviews: '{{count}} reviews',\n        oneReview: '1 review',\n        reviewsBy: 'Reviews by Students',\n        reviewBy: 'Review by',\n        on: 'on',\n        stars: 'stars',\n        star: 'star',\n        outOf5: 'out of 5',\n        selectRating: 'Select Rating',\n        reviewsFor: 'Reviews for',\n        reviewsWritten: 'Reviews Written',\n        reviewsReceived: 'Reviews Received',\n        viewAll: 'View All',\n        filterBy: 'Filter by',\n        sortBy: 'Sort by',\n        newest: 'Newest',\n        oldest: 'Oldest',\n        highestRated: 'Highest Rated',\n        lowestRated: 'Lowest Rated',\n        noComment: 'No comment provided',\n        noReviewsWithFilter: 'No reviews match the selected filter',\n        teacherReply: 'Teacher Reply',\n        replyToReview: 'Reply to Review',\n        editReply: 'Edit Reply',\n        deleteReply: 'Delete Reply',\n        replyPlaceholder: 'Write your reply to this review...',\n        sendReply: 'Send Reply',\n        updateReply: 'Update Reply',\n        replySuccess: 'Reply sent successfully',\n        replyUpdateSuccess: 'Reply updated successfully',\n        replyDeleteSuccess: 'Reply deleted successfully',\n        replyError: 'Error sending reply',\n        confirmDeleteReply: 'Are you sure you want to delete this reply?',\n        replyRequired: 'Please write a reply',\n        sending: 'Sending...'\n      },\n      about: {\n        title: 'About Us',\n        intro: 'Allemnionline is an online educational platform specialized in providing Arabic language teaching services and knowledge related to Arabic culture to learners from around the world, through direct one-on-one lessons delivered by qualified and professional teachers.',\n        mission: 'The platform seeks to make learning Arabic accessible and effective, while considering individual differences and special needs of each learner, using the latest educational technological tools.',\n        whatWeOffer: 'What We Offer',\n        services: {\n          privateLessons: 'Private Arabic language lessons for all levels: from beginners to advanced.',\n          conversationTraining: 'Training in conversation, listening, reading, and writing in Arabic.',\n          culturalElements: 'Introduction to essential elements of Arabic culture that help understand the language in its natural context.',\n          digitalPlatform: 'An integrated digital platform that enables direct communication via video, lesson scheduling, and secure electronic payment.',\n          targetAudience: 'Our services are directed to children, adults, professionals, and anyone who wants to learn Arabic for academic, personal, or professional purposes.'\n        },\n        ourMission: 'Our Mission',\n        missionText: 'To provide distinguished education in Arabic language and related cultural knowledge, with high quality, through direct teaching and modern technologies, in a manner that respects the diversity of learners and their cultural particularities.',\n        contactUs: 'Contact Us',\n        contactText: 'For any inquiries or comments, please contact us via email:',\n        email: '<EMAIL>'\n      },\n      app: {\n        name: 'Allemnionline',\n        copyright: 'Allemnionline 2025',\n        tagline: 'Designed with love for Arabic education'\n      },\n      brand: {\n        name: 'Allemnionline'\n      },\n      common: {\n        switchLanguage: 'Switch Language',\n        loading: 'Loading...',\n        settings: 'Settings',\n        profile: 'Profile',\n        cancel: 'Cancel',\n        back: 'Back',\n        continue: 'Continue',\n        confirm: 'Confirm',\n        update: 'Update',\n        success: 'Success',\n        error: 'Error',\n        save: 'Save',\n        saveAndReturn: 'Save & Return',\n        saving: 'Saving...',\n        email: 'Email Address',\n        notProvided: 'Not provided',\n        search: 'Search',\n        close: 'Close',\n        password: 'Password',\n        fullName: 'Full Name',\n        name: 'Name',\n        confirmPassword: 'Confirm Password',\n        showMore: 'Show More',\n        showLess: 'Show Less',\n        gender: 'Gender',\n        male: 'Male',\n        female: 'Female',\n        submit: 'Submit',\n        notSet: 'Not Set',\n        actions: 'Actions',\n        edit: 'Edit',\n        delete: 'Delete',\n        view: 'View',\n        details: 'Details',\n        rowsPerPage: 'Rows per page',\n        footer: {\n          copyright: 'Allemnionline 2025',\n          tagline: 'Designed with love for Arabic education'\n        },\n        currency: 'USD',\n        details: 'Details'\n      },\n      profile: {\n        title: 'Profile',\n        fullName: 'Full Name',\n        edit: 'Edit Profile',\n        save: 'Save Changes',\n        cancel: 'Cancel',\n        success: 'Profile updated successfully',\n        error: 'Error updating profile',\n        updateSuccess: 'Profile updated successfully',\n        passwordUpdateSuccess: 'Password updated successfully',\n        teachingInfoUpdateSuccess: 'Teaching information updated successfully!',\n        editInfo: 'Edit Profile Information',\n        editTeachingInfo: 'Edit Teaching Info',\n        saving: 'Saving...',\n        errors: {\n          update: 'Failed to update profile',\n          passwordUpdate: 'Failed to update password',\n          passwordMismatch: 'Passwords do not match',\n          currentPassword: 'Current password is incorrect',\n          fetchError: 'Failed to fetch profile data',\n          updateFailed: 'Update failed. Please try again.',\n          deleteRequest: 'Failed to send delete request',\n          invalidCode: 'Invalid verification code',\n          codeRequired: 'Verification code is required',\n          cancelDelete: 'Failed to cancel account deletion'\n        },\n        basicInfo: 'Basic Information',\n        teacherInfo: 'Teacher Information',\n        editInfo: 'Edit Info',\n        changePassword: 'Change Password',\n        email: 'Email',\n        gender: 'Gender',\n        currentPassword: 'Current Password',\n        newPassword: 'New Password',\n        confirmPassword: 'Confirm Password',\n        editPersonalInfo: 'Edit Personal Information',\n        togglePasswordVisibility: 'Toggle password visibility',\n        uploadPhoto: 'Upload Photo',\n        deleteAccount: 'Delete Account',\n        deleteAccountTitle: 'Delete Account',\n        deleteAccountWarning: 'Are you sure you want to delete your account? This action cannot be undone.',\n        deleteAccountNote: 'A verification code will be sent to your email address.',\n        sendDeleteCode: 'Send Verification Code',\n        verifyDeleteCode: 'Verify Deletion Code',\n        deleteCodeSentMessage: 'A verification code has been sent to your email. Please enter it below:',\n        deleteCode: 'Verification Code',\n        confirmDelete: 'Confirm Deletion',\n        deleteCodeSent: 'Verification code sent to your email',\n        deletePending: 'Account deletion pending (10 days)',\n        cancelDelete: 'Cancel Deletion',\n        deleteCancelled: 'Account deletion cancelled successfully',\n        deletePendingAlert: '⚠️ Account Deletion Pending',\n        deletePendingMessage: 'Your account is scheduled for deletion. You can cancel this action at any time before the scheduled deletion time.',\n        deleteScheduledFor: 'Scheduled deletion time',\n        updateStatus: {\n          pending: 'Profile update request is under review',\n          approved: 'Profile update approved',\n          rejected: 'Profile update request rejected',\n          requestDate: 'Request Date',\n          reviewDate: 'Review Date',\n          adminNotes: 'Admin Notes',\n          pendingNote: 'Please wait for admin review. Your current data will remain active until the update is approved.'\n        },\n        teacher: {\n          videoUpload: {\n            title: 'Upload Introduction Video',\n            description: 'Upload a short video introducing yourself to potential students. This video will be shown on your profile.',\n            requirements: 'Video Requirements',\n            formatRequirement: 'Allowed formats',\n            sizeRequirement: 'Maximum size',\n            lengthRequirement: 'Recommended length',\n            maximum: 'maximum',\n            minutes: 'minutes',\n            selectVideo: 'Select Video',\n            upload: 'Upload Video',\n            uploading: 'Uploading...',\n            success: 'Video uploaded successfully!',\n            videoReady: 'Your video is ready to be included in your application.',\n            continue: 'Continue to Application',\n            delete: 'Delete Video',\n            skipForNow: 'Skip for now',\n            invalidVideoFormat: 'Invalid video format. Please select MP4, WebM, or OGG file.',\n            videoTooSmall: 'Video size must be at least 1MB.',\n            videoTooLarge: 'Video size must not exceed 100MB.',\n            noVideoSelected: 'Please select a video file.',\n            videoDeleteError: 'Error deleting video. Please try again.'\n          },\n          uploadVideoNow: 'Upload Video Now',\n          videoReady: 'Your video is ready to be included in your application',\n          deleteVideo: 'Delete Video',\n          availableHoursDescription: 'Select the hours you are available to teach. This will help students find suitable time slots for booking lessons with you.',\n          availableHoursApplicationDescription: 'Select the hours you will be available to teach if your application is approved. You can update these hours later from your profile.',\n          availableHoursProfileDescription: 'Manage your teaching availability to let students know when you are available for lessons.',\n          viewAvailableHours: 'View Available Hours',\n          viewAvailableHoursDescription: 'Here you can see all your available teaching hours organized by day.',\n          editAvailableHours: 'Edit Hours',\n          noHoursForDay: 'No available hours for this day',\n          manageAvailableHours: 'Manage Available Hours',\n          hoursSavedSuccess: 'Your available hours have been saved successfully!',\n          myLessons: 'My Lessons',\n          studentName: 'Student Name',\n          totalLessons: 'Total Lessons',\n          completedLessons: 'Completed Lessons',\n          scheduledLessons: 'Scheduled Lessons',\n          cancelledLessons: 'Cancelled Lessons',\n          noLessonsFound: 'No lessons found',\n          errorSavingHours: 'Error saving your available hours. Please try again.',\n          errorLoadingHours: 'Error loading your available hours. Please try again.',\n          errorParsingHours: 'Error parsing your available hours data. Please try again.',\n          saveAndReturn: 'Save & Return',\n          timeSlots: 'time slots',\n          noAvailableHours: 'No available hours selected. Please select your available hours.',\n          selectAll: 'Select All',\n          clearAll: 'Clear All',\n          timeSlot: 'Time Slot',\n          available: 'Available',\n          unavailable: 'Unavailable',\n          selectAllDays: 'Select this time for all days',\n          clearAllDays: 'Clear this time for all days',\n          legend: 'Legend',\n          cellStatus: 'Cell Status',\n          actions: 'Actions',\n\n          changeProfilePicture: 'Change profile picture',\n          profile: {\n            title: 'Teacher Profile',\n            personalInfo: 'Personal Information',\n            teachingInfo: 'Teaching Information',\n            phone: 'Phone Number',\n            country: 'Country',\n            residence: 'Place of Residence',\n            nativeLanguage: 'Native Language',\n            teachingLanguages: {\n              title: 'Teaching Languages',\n              Arabic: 'Arabic',\n              English: 'English',\n              French: 'French',\n              Spanish: 'Spanish',\n              Urdu: 'Urdu',\n              Turkish: 'Turkish',\n              Indonesian: 'Indonesian',\n              Malay: 'Malay',\n              Bengali: 'Bengali',\n              Hindi: 'Hindi',\n              Persian: 'Persian',\n              German: 'German',\n              Italian: 'Italian',\n              Portuguese: 'Portuguese',\n              Russian: 'Russian',\n              Chinese: 'Chinese',\n              Japanese: 'Japanese',\n              Korean: 'Korean',\n              Thai: 'Thai',\n              Vietnamese: 'Vietnamese',\n              Swahili: 'Swahili',\n              Hausa: 'Hausa',\n              Somali: 'Somali',\n              select: 'Select teaching languages',\n              placeholder: 'Select one or more languages'\n            },\n            courseTypes: 'Course Types',\n            qualifications: 'Qualifications',\n            teachingExperience: 'Teaching Experience',\n            availableHours: 'Available Hours',\n            pricePerLesson: 'Price per Lesson',\n            timezone: 'Time Zone',\n            paymentMethod: 'Payment Method',\n            currency: {\n              usd: 'USD',\n              eur: 'EUR',\n              gbp: 'GBP'\n            },\n            experience: {\n              beginner: 'Beginner (0-2 years)',\n              intermediate: 'Intermediate (2-5 years)',\n              advanced: 'Advanced (5-10 years)',\n              expert: 'Expert (10+ years)'\n            }\n          }\n        },\n        genders: {\n          male: 'Male',\n          female: 'Female'\n        }\n      },\n      nav: {\n        home: 'Home',\n        dashboard: 'Dashboard',\n        teachers: 'Teachers',\n        students: 'Students',\n        deletedUsers: 'Deleted Users',\n        categories: 'Categories',\n        languages: 'Languages',\n        applications: 'Applications',\n        profileUpdates: 'Profile Updates',\n        withdrawalManagement: 'Withdrawal Management',\n        findTeacher: 'Find Teacher',\n        myTeachers: 'My Teachers',\n        meetings: 'Meetings',\n        chat: 'Chat',\n        login: 'Login',\n        register: 'Register',\n        logout: 'Logout',\n        search: 'Search',\n        language: 'Language',\n        english: 'English',\n        arabic: 'Arabic',\n        adminDashboard: 'Admin Dashboard',\n        teacherDashboard: 'Teacher Dashboard',\n        studentDashboard: 'Student Dashboard'\n      },\n      deletedUsers: {\n        title: 'Deleted Users',\n        totalDeleted: 'Total Deleted',\n        deletedStudents: 'Deleted Students',\n        deletedTeachers: 'Deleted Teachers',\n        activeUsers: 'Active Users',\n        search: 'Search',\n        type: 'Type',\n        all: 'All',\n        students: 'Students',\n        teachers: 'Teachers',\n        newTeachers: 'New Teachers',\n        refresh: 'Refresh',\n        user: 'User',\n        role: 'Role',\n        deletionDate: 'Deletion Date',\n        deletionReason: 'Deletion Reason',\n        deletedBy: 'Deleted By',\n        actions: 'Actions',\n        viewDetails: 'View Details',\n        restoreUser: 'Restore User',\n        permanentDelete: 'Permanent Delete',\n        student: 'Student',\n        teacher: 'Teacher',\n        newTeacher: 'New Teacher',\n        notSpecified: 'Not Specified',\n        selfDeleted: 'Self Deleted',\n        restoreConfirmTitle: 'Restore User',\n        restoreConfirmMessage: 'Are you sure you want to restore user \"{name}\"?',\n        restoreConfirmNote: 'The user will be able to login and access the system again.',\n        cancel: 'Cancel',\n        restore: 'Restore',\n        restoring: 'Restoring...',\n        permanentDeleteTitle: 'Permanent Delete',\n        permanentDeleteWarning: 'Warning: This action cannot be undone!',\n        permanentDeleteMessage: 'Are you sure you want to permanently delete user \"{name}\"?',\n        permanentDeleteNote: 'All data associated with this user will be permanently deleted.',\n        permanentDeleteButton: 'Permanent Delete',\n        deleting: 'Deleting...',\n        rowsPerPage: 'Rows per page:',\n        displayedRows: '{from}-{to} of {count}',\n        deletionInfo: 'Deletion Information',\n        scheduledDeletion: 'Scheduled Deletion'\n      },\n      common: {\n        userInfo: 'User Information',\n        teacherInfo: 'Teacher Information',\n        studentInfo: 'Student Information',\n        gender: 'Gender',\n        male: 'Male',\n        female: 'Female',\n        joinDate: 'Join Date',\n        totalLessons: 'Total Lessons',\n        totalStudents: 'Total Students',\n        rating: 'Rating',\n        noRating: 'No Rating',\n        totalEarnings: 'Total Earnings',\n        currency: 'SAR',\n        totalBookings: 'Total Bookings',\n        completedLessons: 'Completed Lessons',\n        totalSpent: 'Total Spent',\n        timezone: 'Timezone',\n        notSet: 'Not Set',\n        close: 'Close'\n      },\n      footer: {\n        platformName: 'Allemnionline',\n        designedBy: 'Designed with love for Arabic education',\n        followUs: 'Follow Us',\n        facebook: 'Facebook',\n        twitter: 'Twitter',\n        instagram: 'Instagram',\n        quickLinks: 'Quick Links',\n        about: 'About Us',\n        contact: 'Contact Us',\n        privacy: 'Privacy Policy',\n        terms: 'Terms of Service',\n        faq: 'FAQ',\n        support: 'Support',\n        copyright: '© 2025 Allemnionline',\n        tagline: 'Designed with love for Arabic education'\n      },\n      hero: {\n        title: 'Arabic in All Languages',\n        subtitle: 'A unique educational journey that combines Arabic language and culture',\n        startLearning: 'Start Learning',\n        becomeTeacher: 'Become a Teacher',\n        imageAlt: 'The Holy Quran'\n      },\n      home: {\n        subjects: 'Our Courses',\n        subjectsSubtitle: 'Discover a diverse range of Islamic and Arabic subjects with our expert instructors',\n        teachers: 'teachers',\n        expertTeachers: 'Expert Teachers',\n        expertTeachersDesc: 'Learn from certified scholars and native speakers',\n        activeStudents: 'Active Students',\n        courses: 'Courses',\n        coursesDesc: 'Comprehensive courses in Islamic studies and Arabic language',\n        studentsDesc: 'Students from around the world',\n        whyChooseUs: 'Why Choose Us',\n        whyChooseUsSubtitle: 'We offer a unique learning experience combining modern technology with authentic Arabic education',\n        meetTeachers: 'Meet Our Teachers',\n        meetTeachersSubtitle: 'Learn from qualified and specialized instructors in Islamic and Arabic education',\n        features: {\n          quality: 'Quality Education',\n          qualityDesc: 'Expert instructors specialized in their fields',\n          flexible: 'Flexible Learning',\n          flexibleDesc: 'Learn anytime, anywhere at your pace',\n          interactive: 'Interactive Learning',\n          interactiveDesc: 'Interactive lessons and live discussions',\n          certified: 'Certified Courses',\n          certifiedDesc: 'Get certified in your field of study'\n        },\n        testimonials: 'What Our Students Say',\n        testimonialsSubtitle: 'Hear from our students about their learning experience with our expert teachers',\n        reviewFor: 'Review for',\n        learnMore: 'Learn More'\n      },\n      testimonials: {\n        student: 'Student',\n        reviewFor: 'Review for'\n      },\n      search: {\n        findTeacher: 'Find a Teacher',\n        filters: 'Filters',\n        subject: 'Subject',\n        allSubjects: 'All Subjects',\n        language: 'Language',\n        allLanguages: 'All Languages',\n        priceRange: 'Price Range',\n        rating: 'Rating',\n        anyRating: 'Any Rating',\n        andAbove: '& above',\n        noTeachersFound: 'No teachers found',\n        yearsOfExperience: '{{years}} years of experience',\n        bookLesson: 'Book a Lesson',\n        perHour: '/hour',\n        subjects: 'Subjects',\n        languages: 'Languages',\n        searchButton: 'Search',\n        clearFilters: 'Clear Filters',\n        noTeachersFound: 'No teachers found matching your criteria',\n        teachingLanguages: 'Teaching Languages',\n        verifiedTeacher: 'Verified Teacher',\n        watchIntro: 'Watch Intro',\n        watchIntroVideo: 'Watch Intro Video',\n        viewProfile: 'View Profile',\n        contactAndBook: 'Contact & Book'\n      },\n      booking: {\n        bookLessonWith: 'Book a Lesson with',\n        pricePerLesson: 'Price per lesson',\n        instructions: 'Booking Instructions',\n        instructionsText: 'Select a day and time slot from the calendar below. Once you confirm your booking, the amount will be deducted from your wallet balance.',\n        selectTimeSlot: 'Select a Time Slot',\n        clickToSelectSlot: 'Click on available time slots to book',\n        clickToBook: 'Click to book this slot',\n        fullHourAvailable: 'Full hour available',\n        halfHourOnly: 'Half hour only',\n        fullLesson: 'Full Lesson (50 min)',\n        halfLesson: 'Half Lesson (25 min)',\n        selectDuration: 'Select Lesson Duration',\n        selectBookingOption: 'Select Booking Option',\n        bookingType: 'Booking Type',\n        regularHalfLesson: 'Regular Half Lesson (25 min)',\n        regularFullLesson: 'Regular Full Lesson (50 min)',\n        secondHalfOnly: 'Second Half Only (30 min later)',\n        fullLessonFromSecondHalf: 'Full Lesson from Second Half (50 min)',\n        crossHourLesson: 'Cross-Hour Lesson (30+30 min)',\n        regularLesson: 'Regular Lesson',\n        duration: 'Duration',\n        noAvailableSlots: 'No available slots for this day',\n        confirmBooking: 'Confirm Booking',\n        teacher: 'Teacher',\n        day: 'Day',\n        date: 'Date',\n        time: 'Time',\n        price: 'Price',\n        confirmAndPay: 'Confirm & Pay',\n        bookingSuccessTitle: 'Booking Successful!',\n        bookingSuccessMessage: 'Your lesson has been booked successfully and the amount has been deducted from your wallet. You can view your bookings in the My Bookings section.',\n        trialLessonEligible: 'This is your first lesson with this teacher! Trial lesson price: ${{price}}',\n        backToTeacher: 'Back to Teacher',\n        viewMyBookings: 'View My Bookings',\n        bookAgain: 'Book Again',\n        bookingFailed: 'Booking failed. Please try again.',\n        insufficientBalance: 'Insufficient balance. Please add funds to your wallet before booking.',\n        currency: 'USD',\n        currentWeek: 'Current Week',\n        weekOf: 'Week of',\n        previousWeek: 'Previous Week',\n        nextWeek: 'Next Week',\n        weekNavigation: 'Week Navigation',\n        selectTimeRange: 'Select Time Range',\n        startTime: 'Start Time',\n        endTime: 'End Time',\n        lessonDuration: 'Lesson Duration',\n        from: 'From',\n        to: 'To',\n        availableSlot: 'Available Slot',\n        selectDuration: 'Select Duration'\n      },\n      bookings: {\n        title: 'My Bookings',\n        weeklyTitle: 'This Week\\'s Bookings',\n        description: 'View and manage your scheduled lessons with teachers.',\n        weeklyDescription: 'View your bookings for this week in a calendar format.',\n        bookingDetails: 'Booking Details',\n        noBookings: 'You have no bookings yet.',\n        fetchError: 'Error fetching bookings. Please try again later.',\n        teacher: 'Teacher',\n        date: 'Date',\n        time: 'Time',\n        timeRange: 'Time Range',\n        duration: 'Duration',\n        minutes: 'minutes',\n        status: 'Status',\n        statusValues: {\n          scheduled: 'Scheduled',\n          completed: 'Completed',\n          cancelled: 'Cancelled',\n          issue_reported: 'Issue Reported',\n          ongoing: 'Ongoing'\n        },\n        price: 'Price',\n        cancel: 'Cancel',\n        confirmCancel: 'Confirm Cancellation',\n        cancelWarning: 'Are you sure you want to cancel this booking? The amount will be refunded to your wallet.',\n        cancellationReason: 'Cancellation Reason (Optional)',\n        cancellationReasonPlaceholder: 'Please provide a reason for cancelling this booking...',\n        confirmCancelButton: 'Yes, Cancel Booking',\n        cancelling: 'Cancelling...',\n        cancelSuccess: 'Booking cancelled successfully and amount refunded to your wallet',\n        cancelError: 'Error cancelling booking. Please try again.',\n        availableSlot: 'Available Time Slot',\n        currentTime: 'Current Time',\n        pastSlot: 'Past Time',\n        takeBreak: 'Take Break',\n        takeBreakTitle: 'Take This Time as Break',\n        takeBreakMessage: 'Do you want to take this time as a break?',\n        takeBreakNote: 'This time will be hidden from students for this week only',\n        breakTakenSuccess: 'Break taken successfully',\n        breakTakenError: 'Error taking break',\n        break: 'Break',\n        clickToTakeBreak: 'Click to take break',\n        cancelBreakTitle: 'Cancel Break Time',\n        cancelBreakMessage: 'Do you want to cancel this break time?',\n        cancelBreakNote: 'This time will become available to students again',\n        breakCancelledSuccess: 'Break cancelled successfully',\n        breakCancelledError: 'Error cancelling break',\n        reschedule: 'Reschedule',\n        rescheduleTitle: 'Reschedule Booking',\n        rescheduleDescription: 'Select a new time slot for this booking from your available hours.',\n        currentBooking: 'Current Booking',\n        selectDay: 'Select Day',\n        chooseDay: 'Choose a day',\n        selectTime: 'Select Time',\n        chooseTime: 'Choose a time',\n        loadingDays: 'Loading available days...',\n        loadingTimes: 'Loading available times...',\n        noAvailableDays: 'No available days found for the next 30 days.',\n        noAvailableTimes: 'No available times for this day.',\n        availableTimes: 'available times',\n        rescheduleReason: 'Reschedule Reason (Optional)',\n        rescheduleReasonPlaceholder: 'Please provide a reason for rescheduling this booking...',\n        rescheduleSuccess: 'Booking rescheduled successfully',\n        rescheduleError: 'Error rescheduling booking. Please try again.',\n        fetchDaysError: 'Error fetching available days. Please try again.',\n        fetchTimesError: 'Error fetching available times. Please try again.',\n        currentBooking: 'Current Booking',\n        selectNewDate: 'Select New Date',\n        selectNewTime: 'Select New Time',\n        selectDateFirst: 'Please select a date from the calendar first',\n        selectedTime: 'Selected Time',\n        confirmReschedule: 'Confirm Reschedule',\n        availableSlots: 'slots available',\n        backToSelectDay: 'Back to Select Day'\n      },\n      teacherDetails: {\n        backToSearch: 'Back to Search',\n        teacherNotFound: 'Teacher not found',\n        errorFetching: 'Error fetching teacher details',\n        reviews: 'reviews',\n        watchIntroVideo: 'Watch Intro Video',\n        hideIntroVideo: 'Hide Intro Video',\n        bookLesson: 'Book a Lesson',\n        teachingSubjects: 'Teaching Subjects',\n        languages: 'Languages',\n        nativeLanguage: 'Native Language',\n        teachingLanguages: 'Teaching Languages',\n        qualifications: 'Qualifications',\n        yearsOfExperience: '{{years}} years of experience',\n        studentReviews: 'Student Reviews',\n        noReviews: 'No reviews yet',\n        contactInfo: 'Contact Information',\n        email: 'Email',\n        phone: 'Phone',\n        location: 'Location',\n        country: 'Country',\n        residence: 'City of Residence',\n        timezone: 'Timezone',\n        experience: 'Experience',\n        memberSince: 'Member since',\n        availableHours: 'Available Hours',\n        paymentInfo: 'Payment Information',\n        pricePerLesson: 'Price per Lesson',\n        paymentMethod: 'Payment Method',\n        cv: 'CV/Resume',\n        introVideo: 'Introduction Video',\n        courseTypes: 'Course Types'\n      },\n      chat: {\n        conversations: 'Conversations',\n        messages: 'Messages',\n        typeMessage: 'Type a message...',\n        send: 'Send',\n        noMessages: 'No messages',\n        noChats: 'No conversations',\n        startChat: 'Start new conversation',\n        loading: 'Loading...',\n        today: 'Today',\n        yesterday: 'Yesterday',\n        online: 'Online',\n        offline: 'Offline',\n        sent: 'Sent',\n        delivered: 'Delivered',\n        read: 'Read',\n        failed: 'Failed to send',\n        retry: 'Retry',\n        onlyStudents: 'Only students can start a chat with teachers',\n        no_conversations: 'No conversations',\n        type_message: 'Type your message here...',\n        select_conversation: 'Select a conversation to start',\n        last_seen: 'Last seen',\n        typing: 'typing...',\n        deleteConfirmTitle: 'Delete Conversation',\n        deleteConfirmMessage: 'Are you sure you want to delete the conversation with {{name}}?',\n        conversationDeleted: 'Conversation deleted successfully',\n        conversationDeletedByOther: 'This conversation has been deleted by the other participant',\n        deleteConversationError: 'Error deleting conversation',\n        messageDeleted: 'Message deleted successfully',\n        messageEdited: 'Message edited successfully',\n        editError: 'Error editing message',\n        deleteError: 'Error deleting message',\n        noConversations: 'No conversations found',\n         chat: 'Chat'\n      },\n      auth: {\n        login: 'Login',\n        logout: 'Logout',\n        logoutConfirmTitle: 'Confirm Logout',\n        logoutConfirmMessage: 'Are you sure you want to log out?',\n        welcomeBack: 'Welcome Back!',\n        email: 'Email Address',\n        forgotPassword: 'Forgot Password?',\n        forgotPasswordInstructions: 'Enter your email address and we\\'ll send you a verification code to reset your password.',\n        sendResetCode: 'Send Reset Code',\n        backToLogin: 'Back to Login',\n        resetCodeSent: 'Reset code sent to your email',\n        resetRequestFailed: 'Failed to send reset code',\n        verifyCode: 'Verify Code',\n        verifyCodeInstructions: 'Enter the 6-digit verification code sent to your email:',\n        verificationCode: 'Verification Code',\n        verifyAndContinue: 'Verify & Continue',\n        resendCode: 'Resend Code',\n        changeEmail: 'Change Email',\n        verifyEmail: 'Verify Email',\n        verificationCodeSentTo: 'We sent a verification code to',\n        verify: 'Verify',\n        verifying: 'Verifying...',\n        verificationCodeSent: 'Verification code sent successfully',\n        verificationFailed: 'Email verification failed',\n        resendFailed: 'Failed to resend verification code',\n        resendCodeIn: 'Resend code in',\n        sending: 'Sending...',\n        invalidCode: 'Please enter a valid 6-digit code',\n        fillAllFields: 'Please fill in all fields',\n        loginFailed: 'Login failed. Please check your credentials.',\n        invalidCredentials: 'Invalid email or password. Please try again.',\n        emailNotFound: 'This email address is not registered. Please check your email or sign up.',\n        wrongPassword: 'Incorrect password. Please try again.',\n        accountDeleted: 'This account has been deleted and cannot be used to login.',\n        invalidCode: 'Invalid verification code',\n        verificationFailed: 'Verification failed',\n        resetCodeResent: 'Reset code has been resent to your email',\n        resetPassword: 'Reset Password',\n        resetPasswordInstructions: 'Create a new password for your account',\n        newPassword: 'New Password',\n        confirmPassword: 'Confirm Password',\n        passwordResetSuccess: 'Password Reset Successful!',\n        passwordResetSuccessMessage: 'Your password has been reset successfully.',\n        redirectingToLogin: 'Redirecting to login page...',\n        resetPasswordFailed: 'Failed to reset password',\n        password: 'Password',\n        signIn: 'Sign In',\n        or: 'OR',\n        noAccount: \"Don't have an account?\",\n        signUp: 'Sign Up',\n        continueWithGoogle: 'Continue with Google',\n        googleSignInError: 'Google sign-in failed. Please try again.',\n        signInWithGoogle: 'Continue with Google',\n        loginError: 'Login failed. Please check your credentials.',\n        accountNotFound: 'Account not found',\n        invalidRole: 'Invalid user role',\n        chooseAccountType: 'Join Our Community',\n        registerDescription: 'Begin your journey in Arabic education. Choose your role and become part of our growing community of learners and educators.',\n        registerAsTeacher: 'Teach with Us',\n        teacherDescription: 'Share your knowledge and expertise in Islamic studies and Arabic language. Help others learn and grow while building your teaching career.',\n        registerAsStudent: 'Learn with Us',\n        studentDescription: 'Start your learning journey in Islamic studies and Arabic language with expert teachers from around the world.',\n        alreadyHaveAccount: 'Already have an account?',\n        getStarted: 'Get Started',\n        studentRegistration: 'Student Registration',\n        teacherRegistration: 'Teacher Registration',\n        joinOurCommunity: 'Join our community of learners',\n        joinOurTeachers: 'Share your knowledge with others',\n        createAccount: 'Create Account',\n        registrationError: 'Registration failed. Please try again.',\n        selectGender: 'Please select your gender',\n        gender: 'Gender',\n        male: 'Male',\n        female: 'Female'\n      },\n      courseTypes: {\n        Islamic_Law: 'Islamic Law',\n        Fiqh: 'Fiqh',\n        Quran: 'Quran',\n        Tajweed: 'Tajweed',\n        Islamic_History: 'Islamic History',\n        Arabic_Grammar: 'Arabic Grammar',\n        Arabic_Speaking: 'Arabic Speaking',\n        Arabic_Writing: 'Arabic Writing',\n        Islamic_Ethics: 'Islamic Ethics',\n        Hadith: 'Hadith',\n        Aqeedah: 'Islamic Creed'\n      },\n      languages: {\n        Arabic: 'Arabic',\n        English: 'English',\n        French: 'French',\n        Spanish: 'Spanish'\n      },\n      gender: {\n        male: 'Male',\n        female: 'Female'\n      },\n      dashboard: {\n        welcome: 'Welcome',\n        unauthorized: 'Unauthorized access to dashboard',\n        fetchError: 'Error fetching data',\n        totalStudents: 'Total Students',\n        totalClasses: 'Total Classes',\n        averageRating: 'Average Rating',\n        totalEarnings: 'Total Earnings',\n        categories: 'Teaching Categories',\n        recentBookings: 'Recent Bookings',\n        recentReviews: 'Recent Reviews',\n        learningProgress: 'Learning Progress',\n        quickActions: 'Quick Actions',\n        recommendedTeachers: 'Recommended Teachers',\n        upcomingLessons: 'Upcoming Lessons',\n        noUpcomingLessons: 'No upcoming lessons scheduled',\n        updateProfile: 'Update Profile',\n        findTeacher: 'Find a Teacher',\n        browseCourses: 'Browse Courses',\n        viewAll: 'View All',\n        incompleteProfile: 'Your Profile is Incomplete',\n        completeProfileMessage: 'Please complete your profile to access all features and find the perfect teacher for your learning journey.',\n        completeProfileToAccess: 'You need to complete your profile to access this page and its features.',\n        completeProfile: 'Complete Profile',\n        updateProfile: 'Update Profile',\n        completeProfileNow: 'Complete Profile Now'\n      },\n      regions: {\n        middle_east: 'Middle East',\n        north_africa: 'North Africa',\n        sub_saharan_africa: 'Sub-Saharan Africa',\n        south_asia: 'South Asia',\n        southeast_asia: 'Southeast Asia',\n        central_asia: 'Central Asia',\n        europe: 'Europe',\n        north_america: 'North America',\n        south_america: 'South America',\n        east_asia: 'East Asia',\n        oceania: 'Oceania',\n        others: 'others'\n      },\n      teacher: {\n        phoneOptional: 'Optional',\n        nativeLanguage: 'Native Language',\n        teachingLanguages: {\n          title: 'Teaching Languages',\n          Arabic: 'Arabic',\n          English: 'English',\n          French: 'French',\n          Spanish: 'Spanish',\n          Urdu: 'Urdu',\n          Turkish: 'Turkish',\n          Indonesian: 'Indonesian',\n          Malay: 'Malay',\n          Bengali: 'Bengali',\n          Hindi: 'Hindi',\n          Persian: 'Persian',\n          German: 'German',\n          Italian: 'Italian',\n          Portuguese: 'Portuguese',\n          Russian: 'Russian',\n          Chinese: 'Chinese',\n          Japanese: 'Japanese',\n          Korean: 'Korean',\n          Thai: 'Thai',\n          Vietnamese: 'Vietnamese',\n          Swahili: 'Swahili',\n          Hausa: 'Hausa',\n          Somali: 'Somali',\n          select: 'Select teaching languages',\n          placeholder: 'Select one or more languages'\n        },\n        qualifications: 'Qualifications',\n        experience: 'Experience',\n        subjects: 'Subjects',\n        profile: {\n          title: 'Teacher Profile',\n          personalInfo: 'Personal Information',\n          teachingInfo: 'Teaching Information',\n          phone: 'Phone Number',\n          country: 'Country',\n          residence: 'Place of Residence',\n          nativeLanguage: 'Native Language',\n          teachingLanguages: {\n            title: 'Teaching Languages',\n            Arabic: 'Arabic',\n            English: 'English',\n            French: 'French',\n            Spanish: 'Spanish',\n            Urdu: 'Urdu',\n            Turkish: 'Turkish',\n            Indonesian: 'Indonesian',\n            Malay: 'Malay',\n            Bengali: 'Bengali',\n            Hindi: 'Hindi',\n            Persian: 'Persian',\n            German: 'German',\n            Italian: 'Italian',\n            Portuguese: 'Portuguese',\n            Russian: 'Russian',\n            Chinese: 'Chinese',\n            Japanese: 'Japanese',\n            Korean: 'Korean',\n            Thai: 'Thai',\n            Vietnamese: 'Vietnamese',\n            Swahili: 'Swahili',\n            Hausa: 'Hausa',\n            Somali: 'Somali',\n            select: 'Select teaching languages',\n            placeholder: 'Select one or more languages'\n          },\n          courseTypes: 'Course Types',\n          qualifications: 'Qualifications',\n          teachingExperience: 'Teaching Experience',\n          availableHours: 'Available Hours',\n          pricePerLesson: 'Price per Lesson',\n          timezone: 'Time Zone',\n          paymentMethod: 'Payment Method',\n          currency: {\n            usd: 'USD',\n            eur: 'EUR',\n            gbp: 'GBP'\n          },\n          experience: {\n            beginner: 'Beginner (0-2 years)',\n            intermediate: 'Intermediate (2-5 years)',\n            advanced: 'Advanced (5-10 years)',\n            expert: 'Expert (10+ years)'\n          }\n        },\n        application: {\n          title: 'Teacher Application',\n          submit: 'Submit Application',\n          success: 'Application submitted successfully',\n          error: 'Error submitting application',\n          errorFetchingData: 'Error fetching data',\n          errorFetchingCategories: 'Error fetching categories',\n          statusCardTitle: 'Application Status',\n          edit: 'Edit Application',\n          editDescription: 'You can edit your application details, but you will need to be approved again by the admin.',\n          warningTitle: 'Warning: Application Edit',\n          warningMessage: 'Editing your application will send a profile update request that requires admin approval.',\n          warningDescription1: 'If you proceed, you will be redirected to the application form where you can edit your details.',\n          warningDescription2: 'You will continue teaching with your current data until the admin approves your update request.',\n          warningConfirmation: 'Are you sure you want to continue?',\n          confirmEdit: 'Yes, Edit Application',\n          editApplication: {\n            title: 'Edit Application',\n            description: 'Update your application details. Changes will be reviewed by admin before being applied.',\n            warning: 'Your current data will remain active until the admin approves your changes.'\n          },\n          changeVideo: 'Change Video',\n          status: {\n            pending: 'Pending Review',\n            approved: 'Approved',\n            rejected: 'Rejected'\n          },\n          statusMessage: {\n            pending: 'Your application is being reviewed',\n            approved: 'Congratulations! Your application has been approved',\n            rejected: 'Unfortunately, your application has been rejected',\n            default: 'Application status updated'\n          },\n          applicationNextSteps: {\n            approved: {\n              title: 'Next Steps',\n              steps: [\n                'Please log out from your account',\n                'Log in again to activate your full teacher privileges'\n              ]\n            }\n          }\n        },\n        country: 'Country',\n        residence: 'City of Residence',\n        nativeLanguage: 'Native Language',\n        teachingLanguages: 'Teaching Languages',\n        courseTypes: 'Course Types',\n        qualifications: 'Qualifications',\n        qualificationsPlaceholder: 'Enter your educational qualifications and certifications',\n        teachingExperience: 'Years of Teaching Experience',\n        introVideo: 'Introduction Video',\n        introVideoUrl: 'Introduction Video URL',\n        introVideoUrlPlaceholder: 'https://...',\n        noIntroVideo: 'No introduction video available',\n        videoLinkTab: 'Video Link',\n        videoFileTab: 'Upload Video',\n        videoLink: 'Link',\n        videoUpload: 'Upload',\n        videoLinkHelp: 'Enter a YouTube, Vimeo, or other video platform URL',\n        videoLinkNote: 'Share a link to your introduction video from a supported platform',\n        videoUploadHelp: 'Upload your introduction video directly to our platform',\n        videoUploadPlaceholder: 'Upload Video',\n        videoSelected: 'Selected video',\n        videoUploadError: 'Error uploading video',\n        formHasErrors: 'Please correct the errors in the form',\n        allowedFormats: 'Allowed formats',\n        maxFileSize: 'Maximum file size',\n        recommendedPlatforms: 'Recommended platforms',\n        cv: 'CV/Resume',\n        cvPlaceholder: 'write your CV',\n        cvHelperText: 'Write a brief summary of your qualifications, experience, and teaching approach (max 2000 characters)',\n        characters: 'characters',\n        profilePicture: 'Profile Picture',\n        profilePicturePlaceholder: 'Upload your profile picture',\n        profilePictureRequired: 'Profile picture is required',\n        availableHours: 'Available Teaching Hours',\n        availableHoursDescription: 'Select the available times for teaching on each day of the week',\n        availableHoursProfileDescription: 'Manage your teaching availability to let students know when you are available for lessons',\n        viewAvailableHours: 'View Available Hours',\n        viewAvailableHoursDescription: 'Review your current teaching schedule and availability',\n        noAvailableHours: 'No available hours have been set yet',\n        timeSlot: 'Time Slot',\n        timeSlots: 'time slots',\n        available: 'Available',\n        unavailable: 'Not Available',\n        editAvailableHours: 'Edit Available Hours',\n        weeklySchedule: 'Weekly Schedule',\n        scheduleDescription: 'Your teaching availability throughout the week',\n        time: 'Time',\n        legend: 'Legend',\n        quickStats: 'Quick Statistics',\n        totalSlots: 'Total Time Slots',\n        hoursPerWeek: 'Hours per Week',\n        activeDays: 'Active Days',\n        pricePerLesson: 'Price per Lesson',\n        pricePerLessonPlaceholder: 'Enter price in USD',\n        trialLessonPrice: 'Trial lesson price',\n        trialLessonPricePlaceholder: 'Enter trial lesson price in USD',\n        trialPriceLessThanRegular: 'Trial lesson price must be less than or equal to regular lesson price',\n        timezone: 'Timezone',\n        paymentMethod: 'Payment Method',\n        phone: 'Phone Number',\n        phoneHelp: 'Include country code (e.g., +1, +44, +966)',\n        selectedHours: 'hours selected',\n        selectAll: 'Select All',\n        required: 'Required field',\n        formHasErrors: 'Please correct the errors in the form',\n        priceRange: 'Price must be between $3 and $100',\n        yourEarnings: 'Your earnings after commission:',\n        invalidUrl: 'Please enter a valid URL',\n        invalidPhone: 'Please enter a valid phone number',\n        invalidPrice: 'Please enter a valid price',\n        uploadVideoNow: 'Upload Video Now',\n        allowedFormats: 'Allowed formats',\n        maxFileSize: 'Maximum file size',\n        videoFormats: 'MP4, WebM, OGG',\n        maxVideoSize: '100MB',\n        videoRequired: 'An introduction video is required for your application',\n        commitment: 'Teacher Commitment',\n        commitmentDescription: 'Please read and agree to the following commitment',\n        commitmentProfileDescription: 'View and manage your commitment status and details',\n        commitmentRequired: 'You must agree to the commitment to continue',\n        commitmentStatus: {\n          accepted: 'Commitment accepted',\n          pending: 'Commitment pending approval',\n          rejected: 'Commitment rejected'\n        },\n        commitmentTitle: 'Teacher Commitment',\n        commitmentAccepted: 'Commitment accepted',\n        readCommitment: 'Read and agree to the commitment',\n        accept: 'I Agree',\n        reject: 'I Decline',\n        manageAvailableHours: 'Manage Available Hours',\n        weeklyBookings: 'This Week\\'s Student Bookings',\n        weeklyBookingsDescription: 'View your student bookings for this week in a calendar format.',\n        fileTypes: {\n          image: 'Supported formats: JPG, PNG (max 5MB)',\n          document: 'Supported formats: PDF, DOC, DOCX (max 10MB)'\n        },\n        submitComplaint: 'Submit Complaint',\n        complaintReason: 'Reason for Complaint',\n        complaintType1: 'Student attended but commission not transferred',\n        complaintType2: 'Student did not attend at all',\n        complaintDetails: 'Complaint Details (optional)',\n        complaintDetailsPlaceholder: 'Write any additional details if needed...',\n        complaintStatus: 'Complaint Status',\n        complaintStatusValues: {\n          pending: 'Pending',\n          resolved: 'Resolved'\n        },\n      },\n      role: {\n        admin: 'Admin',\n        teacher: 'Teacher',\n        student: 'Student'\n      },\n      validation: {\n        required: 'This field is required',\n        email: 'Please enter a valid email address',\n        password: {\n          min: 'Password must be at least {{min}} characters',\n          max: 'Password must be at most {{max}} characters',\n          match: 'Passwords do not match'\n        }\n      },\n      days: {\n        monday: 'Monday',\n        tuesday: 'Tuesday',\n        wednesday: 'Wednesday',\n        thursday: 'Thursday',\n        friday: 'Friday',\n        saturday: 'Saturday',\n        sunday: 'Sunday',\n        mondayShort: 'Mon',\n        tuesdayShort: 'Tue',\n        wednesdayShort: 'Wed',\n        thursdayShort: 'Thu',\n        fridayShort: 'Fri',\n        saturdayShort: 'Sat',\n        sundayShort: 'Sun'\n      },\n      hours: {\n        hour1: '1:00 AM',\n        hour2: '2:00 AM',\n        hour3: '3:00 AM',\n        hour4: '4:00 AM',\n        hour5: '5:00 AM',\n        hour6: '6:00 AM',\n        hour7: '7:00 AM',\n        hour8: '8:00 AM',\n        hour9: '9:00 AM',\n        hour10: '10:00 AM',\n        hour11: '11:00 AM',\n        hour12: '12:00 PM',\n        hour13: '1:00 PM',\n        hour14: '2:00 PM',\n        hour15: '3:00 PM',\n        hour16: '4:00 PM',\n        hour17: '5:00 PM',\n        hour18: '6:00 PM',\n        hour19: '7:00 PM',\n        hour20: '8:00 PM',\n        hour21: '9:00 PM',\n        hour22: '10:00 PM',\n        hour23: '11:00 PM',\n        hour24: '12:00 AM'\n      },\n      student: {\n        profile: {\n          title: 'Student Profile',\n          complete: 'Complete Your Profile',\n          languagePreferences: 'Language Preferences',\n          personalInfo: 'Personal Information',\n          learningPreferences: 'Learning Preferences',\n          nativeLanguage: 'Native Language',\n          preferredIslamicLanguage: 'Preferred Language for Learning Islam',\n          preferredArabicLanguage: 'Preferred Language for Learning Arabic',\n          islamLearningLanguage: 'Language for Learning Islam',\n          arabicLearningLanguage: 'Language for Learning Arabic',\n          age: 'Age',\n          country: 'Country',\n          timezone: 'Timezone',\n          arabicLevel: 'Arabic Proficiency Level',\n          privateTutoring: 'I am interested in private tutoring',\n          preferGroup: 'NO',\n          preferPrivate: 'YES',\n          updateSuccess: 'Profile updated successfully',\n          editInfo: 'Edit Profile Information',\n          levels: {\n            beginner: 'Beginner',\n            intermediate: 'Intermediate',\n            advanced: 'Advanced'\n          },\n          success: 'Profile updated successfully! Redirecting to dashboard...'\n        },\n        myTeachers: 'My Teachers',\n        noTeachersYet: 'You have no teachers yet.',\n        lessonsTaken: 'Lessons Taken'\n      },\n      admin: {\n        dashboard: {\n          title: 'Dashboard',\n          welcomeMessage: 'Welcome to Admin Dashboard',\n          overview: 'Here\\'s an overview of your platform statistics and recent activities.',\n          totalTeachers: 'Total Teachers',\n          pendingApplications: 'Pending Applications',\n          totalStudents: 'Total Students',\n          totalRevenue: 'Total Revenue',\n          totalCourseCategories: 'Total Categories',\n          totalLanguages: 'Total Languages',\n          totalBookings: 'Total Bookings',\n          bookingStats: 'Booking Statistics',\n          teachersByLanguage: 'Teachers by Language',\n          studentsByCountry: 'Students by Country',\n          recentApplications: 'Recent Applications',\n          recentStudents: 'Recent Students',\n          viewAll: 'View All',\n          viewDetails: 'View Details',\n          noApplications: 'No recent applications',\n          noStudents: 'No recent students',\n          noStudentsByCountry: 'No student country data available',\n          noTeachersByLanguage: 'No teacher language data available',\n          noBookingStats: 'No booking data available',\n          completed: 'Completed',\n          cancelled: 'Cancelled',\n          pending: 'Pending',\n          fetchError: 'Error fetching dashboard data'\n        },\n        meetingSessions: {\n          title: 'Meeting Time Reports',\n          overview: 'Detailed view of teachers and students join/leave times in meetings',\n          totalMeetings: 'Total Meetings',\n          totalTime: 'Total Time',\n          teacherTime: 'Teacher Time',\n          studentTime: 'Student Time',\n          filterResults: 'Filter Results',\n          userType: 'User Type',\n          all: 'All',\n          teacher: 'Teacher',\n          student: 'Student',\n          reset: 'Reset',\n          sessions: 'Meeting Sessions',\n          user: 'User',\n          type: 'Type',\n          meeting: 'Meeting',\n          joinTime: 'Join Time',\n          leaveTime: 'Leave Time',\n          duration: 'Duration',\n          status: 'Status',\n          active: 'Active',\n          ended: 'Ended'\n        },\n        meetingIssues: {\n          title: 'Meeting Issues'\n        },\n        messages: {\n          title: 'Messages',\n          from: 'From',\n          type: 'Type',\n          subject: 'Subject',\n          date: 'Date',\n          status: 'Status',\n          pending: 'Pending',\n          answered: 'Answered',\n          all: 'All Messages',\n          view: 'View',\n          reply: 'Reply',\n          delete: 'Delete',\n          search: 'Search messages...',\n          noMessages: 'No messages found',\n          fetchError: 'Error fetching messages',\n          replyTo: 'Reply to',\n          originalMessage: 'Original Message',\n          yourReply: 'Your Reply',\n          sendReply: 'Send Reply',\n          replySent: 'Reply sent successfully',\n          replyError: 'Error sending reply',\n          confirmDelete: 'Confirm Delete',\n          deleteWarning: 'Are you sure you want to delete this message? This action cannot be undone.',\n          deleteSuccess: 'Message deleted successfully',\n          deleteError: 'Error deleting message'\n        },\n        emailTemplates: {\n          title: 'Email Templates',\n          description: 'Manage email templates for various system notifications.',\n          instructions: 'Edit the templates below to customize the emails sent to users. You can use HTML and variables to personalize the content.',\n          approvalTemplate: 'Application Approval Template',\n          rejectionTemplate: 'Application Rejection Template',\n          save: 'Save Template',\n          saveSuccess: 'Template saved successfully',\n          saveError: 'Error saving template',\n          fetchError: 'Error fetching templates',\n          restoreDefault: 'Restore Default',\n          preview: 'Preview',\n          enterTemplate: 'Enter template content here...',\n          availableVariables: 'Available Variables',\n          variablesHelp: 'These variables will be replaced with actual values when the email is sent.',\n          testEmail: 'Test Email',\n          testEmailPlaceholder: 'Enter email address to test',\n          sendTest: 'Send Test Email',\n          testEmailSuccess: 'Test email sent successfully',\n          testEmailError: 'Error sending test email',\n          variables: {\n            teacherName: 'Teacher\\'s full name',\n            teacherEmail: 'Teacher\\'s email address',\n            dashboardUrl: 'URL to the teacher dashboard',\n            rejectionReason: 'Reason for application rejection'\n          }\n        },\n        earnings: {\n          title: 'Platform Earnings',\n          totalCommission: 'Total Commission',\n          totalLessons: 'Total Lessons',\n          totalRevenue: 'Total Revenue',\n          avgCommissionRate: 'Avg Commission Rate',\n          startDate: 'Start Date',\n          endDate: 'End Date',\n          filter: 'Filter',\n          clearFilter: 'Clear Filter',\n          date: 'Date',\n          meeting: 'Meeting',\n          teacher: 'Teacher',\n          student: 'Student',\n          lessonAmount: 'Lesson Amount',\n          commissionRate: 'Commission Rate',\n          commissionAmount: 'Commission Amount',\n          teacherEarnings: 'Teacher Earnings',\n          errorFetching: 'Error fetching earnings data'\n        },\n        passwordManagement: {\n          title: 'Password Management',\n          description: 'Manage password reset requests and user passwords',\n          searchPlaceholder: 'Search by name or email',\n          user: 'User',\n          email: 'Email',\n          role: 'Role',\n          requestDate: 'Request Date',\n          status: 'Status',\n          actions: 'Actions',\n          resetPassword: 'Reset Password',\n          resetPasswordFor: 'Reset Password for {name}',\n          newPassword: 'New Password',\n          generatePassword: 'Generate Password',\n          resetSuccess: 'Password reset successfully for {email}',\n          resetError: 'Error resetting password',\n          noRequests: 'No password reset requests found',\n          statusPending: 'Pending',\n          statusCompleted: 'Completed',\n          sendTestEmail: 'Send Test Email',\n          testEmailAddress: 'Test Email Address',\n          testEmailDescription: 'Send a test password reset email to verify the email template and delivery',\n          sendEmail: 'Send Email',\n          testEmailSent: 'Test email sent successfully to {email}',\n          testEmailError: 'Error sending test email'\n        },\n        profile: {\n          personalInfo: 'Personal Information',\n          changePassword: 'Change Password',\n          currentPassword: 'Current Password',\n          newPassword: 'New Password',\n          confirmNewPassword: 'Confirm New Password',\n          updateSuccess: 'Profile updated successfully',\n          updateError: 'Error updating profile',\n          passwordSuccess: 'Password changed successfully',\n          passwordError: 'Error changing password',\n          imageSuccess: 'Profile image updated successfully',\n          imageError: 'Error updating profile image'\n        },\n        teacherRole: {\n          platform_teacher: 'Platform Teacher',\n          new_teacher: 'New Teacher'\n        },\n        teachers: {\n          title: 'Teachers',\n          searchPlaceholder: 'Search teachers...',\n          name: 'Name',\n          email: 'Email',\n          gender: 'Gender',\n          role: 'Role',\n          actions: 'Actions',\n          viewDetails: 'View Details',\n          teacherDetails: 'Teacher Details',\n          personalInfo: 'Personal Information',\n          deleteConfirm: 'Are you sure you want to delete this teacher?',\n          deleteSuccess: 'Teacher deleted successfully',\n          deleteError: 'Error deleting teacher',\n          fetchError: 'Error fetching teachers'\n        },\n        students: {\n          title: 'Students',\n          searchPlaceholder: 'Search students...',\n          name: 'Name',\n          email: 'Email',\n          gender: 'Gender',\n          country: 'Country',\n          age: 'Age',\n          timezone: 'Timezone',\n          actions: 'Actions',\n          viewDetails: 'View Details',\n          studentDetails: 'Student Details',\n          personalInfo: 'Personal Information',\n          learningPreferences: 'Learning Preferences',\n          nativeLanguage: 'Native Language',\n          islamLearningLanguage: 'Islam Learning Language',\n          arabicLearningLanguage: 'Arabic Learning Language',\n          arabicProficiencyLevel: 'Arabic Proficiency Level',\n          privateTutoring: 'Private Tutoring',\n          profileCompleted: 'Profile Completed',\n          deleteConfirm: 'Are you sure you want to delete this student?',\n          deleteSuccess: 'Student deleted successfully',\n          deleteError: 'Error deleting student',\n          fetchError: 'Error fetching students',\n          noStudents: 'No students found',\n          delete: 'Delete',\n          proficiencyLevels: {\n            beginner: 'Beginner',\n            intermediate: 'Intermediate',\n            advanced: 'Advanced'\n          }\n        },\n        categories: {\n          title: 'Categories',\n          addNew: 'Add New Category',\n          editTitle: 'Edit Category',\n          addTitle: 'Add Category',\n          name: 'Name',\n          description: 'Description',\n          createdBy: 'Created By',\n          updateSuccess: 'Category updated successfully',\n          createSuccess: 'Category created successfully',\n          deleteSuccess: 'Category deleted successfully',\n          deleteConfirm: 'Are you sure you want to delete this category?',\n          fetchError: 'Error fetching categories',\n          saveError: 'Error saving category',\n          deleteError: 'Error deleting category',\n          unauthorized: 'Unauthorized access',\n          invalidId: 'Invalid category ID'\n        },\n        languages: {\n          title: 'Languages',\n          addNew: 'Add New Language',\n          editTitle: 'Edit Language',\n          addTitle: 'Add Language',\n          name: 'Language Name',\n          nameRequired: 'Language name is required',\n          unauthorized: 'You are not authorized to perform this action',\n          fetchError: 'Error fetching languages',\n          createSuccess: 'Language created successfully',\n          createError: 'Error creating language',\n          updateSuccess: 'Language updated successfully',\n          updateError: 'Error updating language',\n          deleteSuccess: 'Language deleted successfully',\n          deleteError: 'Error deleting language',\n          deleteConfirm: 'Are you sure you want to delete this language?',\n          invalidId: 'Invalid language ID',\n          saveError: 'Error saving language'\n        },\n        applications: {\n          searchPlaceholder: 'Search applications...',\n          filterByStatus: 'Filter by status',\n          name: 'Name',\n          email: 'Email',\n          phone: 'Phone',\n          country: 'Country',\n          languages: 'Languages',\n          status: 'Status',\n          actions: 'Actions',\n          statuses: {\n            pending: 'Pending',\n            approved: 'Approved',\n            rejected: 'Rejected'\n          },\n          title: 'Teacher Applications',\n          allStatuses: 'All Statuses',\n          viewDetails: 'View Details',\n          approve: 'Approve',\n          reject: 'Reject',\n          applicationDetails: 'Application Details',\n          personalInfo: 'Personal Information',\n          teachingInfo: 'Teaching Information',\n          documents: 'Documents',\n          nativeLanguage: 'Native Language',\n          teachingLanguages: 'Teaching Languages',\n          qualifications: 'Qualifications',\n          experience: 'Experience',\n          pricePerLesson: 'Price per Lesson',\n          viewVideo: 'View Introduction Video',\n          viewCV: 'View CV',\n          basicInfo: 'Basic Info',\n          teachingDetails: 'Teaching Details',\n          schedule: 'Schedule',\n          location: 'Location',\n          applicationDate: 'Application Date',\n          courseTypes: 'Course Types',\n          pricing: 'Pricing',\n          paymentMethod: 'Payment Method',\n          introVideo: 'Introduction Video',\n          cv: 'CV/Resume',\n          availableHours: 'Available Hours',\n          yearsOfExperience: '{{years}} years of experience'\n        }\n      },\n      menu: {\n        dashboard: 'Dashboard',\n        meetings: 'Meetings',\n        profile: 'Profile',\n        chat: 'Chat',\n        platformPolicy: 'Platform Policy'\n      },\n      meetings: {\n        title: 'Meetings',\n        myMeetings: 'My Meetings',\n        description: 'View and manage your scheduled meetings with teachers',\n        noMeetings: 'No meetings scheduled',\n        noMeetingsDescription: 'You don\\'t have any meetings scheduled yet. Book a lesson to get started!',\n        teacher: 'Teacher',\n        date: 'Date',\n        time: 'Time',\n        fetchError: 'Error fetching meetings',\n        with: 'with',\n        createNew: 'Create New Meeting',\n        meetingName: 'Meeting Name',\n        meetingDate: 'Meeting Date',\n        duration: 'Duration',\n        minutes: 'Minutes',\n        create: 'Create Meeting',\n        start: 'Start Meeting',\n        join: 'Join Meeting',\n        notStarted: 'Meeting not started yet',\n        ended: 'Meeting ended',\n        joinError: 'Error joining meeting',\n        cancel: 'Cancel Meeting',\n        copyLink: 'Copy Room Code',\n        linkCopied: 'Room code copied to clipboard',\n        cancelSuccess: 'Meeting cancelled successfully',\n        cancelError: 'Error cancelling meeting',\n        createSuccess: 'Meeting created successfully',\n        createError: 'Error creating meeting',\n        halfLesson: 'Half Lesson',\n        fullLesson: 'Full Lesson',\n        consecutiveSlots: 'Consecutive Slots',\n        currency: 'USD',\n        status: {\n          pending: 'Pending',\n          ongoing: 'Ongoing',\n          completed: 'Completed',\n          cancelled: 'Cancelled',\n          scheduled: 'Scheduled'\n        },\n        meetingCompleted: 'Meeting completed successfully',\n        errorCompletingMeeting: 'Error completing the meeting',\n        participant: 'Participant',\n        you: 'You',\n        sessionTime: 'Session Time',\n        remainingTime: 'Time Remaining',\n        timeUp: 'Time Up',\n        timeUpTitle: 'Meeting Time Ended',\n        timeUpMessage: 'The scheduled meeting time has ended. The meeting will now be closed automatically.',\n        timeUpDescription: 'You will be redirected back to your bookings page.',\n        backToBookings: 'Back to Bookings',\n        active: 'Active',\n        stopped: 'Stopped',\n        validation: {\n          nameRequired: 'Meeting name is required',\n          dateRequired: 'Meeting date is required',\n          durationRequired: 'Duration is required',\n          allFieldsRequired: 'All fields are required'\n        },\n        student: 'Student',\n        dateTime: 'Date & Time',\n        amount: 'Amount',\n        actions: 'Actions'\n      },\n      privacy: {\n        title: 'Privacy Policy',\n        intro: 'This Privacy Policy explains how Allemnionline (\"we\", \"our\", or \"us\") collects, uses, and protects the personal information of users (\"you\") who visit our website or use our educational services.',\n        section1: {\n          title: '1. Information We Collect',\n          subtitle: 'We may collect the following types of personal information:',\n          item1: 'Name, email address, and phone number',\n          item2: 'Payment and billing details',\n          item3: 'User account information (e.g., language level, lesson history)'\n        },\n        section2: {\n          title: '2. How We Use Your Information',\n          subtitle: 'We use your information to:',\n          item1: 'Provide and improve our educational services',\n          item2: 'Manage scheduling and payments',\n          item3: 'Communicate with you regarding your lessons and account',\n          item4: 'Ensure platform security and prevent fraud',\n          item5: 'Comply with legal and regulatory obligations'\n        },\n        section3: {\n          title: '3. How We Share Your Information',\n          subtitle: 'We do not sell your personal information.',\n          description: 'We may share it with:',\n          item1: 'Payment processors (e.g., Stripe)',\n          item2: 'Legal authorities if required by law',\n          item3: 'Trusted service providers under confidentiality agreements'\n        },\n        section4: {\n          title: '4. Data Security',\n          content: 'We implement appropriate technical and organizational measures to protect your data from unauthorized access, alteration, or disclosure.'\n        },\n        section5: {\n          title: '5. Your Rights',\n          subtitle: 'You have the right to:',\n          item1: 'Access your personal data',\n          item2: 'Request correction or deletion',\n          item3: 'Withdraw consent for data processing',\n          item4: 'File a complaint with a data protection authority (where applicable)',\n          contact: 'To exercise your rights, contact us at: <EMAIL>'\n        },\n        section6: {\n          title: '6. Cookies and Tracking',\n          content: 'Our website may use cookies to improve your browsing experience. You can adjust cookie settings in your browser preferences.'\n        },\n        section7: {\n          title: '7. Children\\'s Privacy',\n          content: 'We do not knowingly collect data from children under 13 without parental consent. If you believe we have collected such data, please contact us immediately.'\n        },\n        section8: {\n          title: '8. Third-Party Links',\n          content: 'Our site may contain links to third-party services. We are not responsible for the privacy practices of those websites.'\n        },\n        section9: {\n          title: '9. Changes to This Policy',\n          content: 'We may update this policy from time to time. We encourage you to review it periodically. Any changes will be posted on this page with an updated effective date.'\n        },\n        section10: {\n          title: '10. Contact Us',\n          content: 'For questions or concerns regarding this policy, please contact:',\n          email: '<EMAIL>'\n        }\n      },\n      policies: {\n        refund: {\n          title: 'Refund Policy',\n          section1: {\n            title: 'First: Cases where students are granted lesson refunds',\n            description: 'Students are entitled to have their lesson refunded to their platform balance for rescheduling in the following cases:',\n            items: [\n              'If the teacher cancels the lesson or is absent at the scheduled time.',\n              'If the lesson cannot be held due to a technical failure on the part of the teacher or platform.'\n            ]\n          },\n          section2: {\n            title: 'Second: Cases where lesson refunds or rescheduling are not granted',\n            description: 'Students are not entitled to request a lesson refund or rescheduling in the following cases:',\n            items: [\n              'Student cancels the lesson within less than 12 hours of its scheduled time.',\n              'Student is absent from attending the lesson without prior notice.',\n              'Loss of connection due to weak internet or malfunction in the student\\'s device.',\n              'Forgetting login credentials (username or password).',\n              'Balance expiration due to not using the platform for more than 180 days.',\n              'Student voluntarily deletes their account.',\n              'Account suspension due to violation of terms of use.'\n            ]\n          },\n          section3: {\n            title: 'Third: Refund policy updates',\n            description: 'The platform reserves the right to modify this policy at any time. Continued use of the platform after policy updates constitutes implicit agreement to what is stated therein.'\n          },\n          contact: {\n            title: 'Contact Us',\n            email: '📧 <EMAIL>'\n          }\n        },\n        bookingPayment: {\n          title: 'Booking and Payment Policy',\n          subtitle: 'This policy explains how lessons are scheduled, confirmed, and paid for through the Allemnionline platform.',\n          section1: {\n            title: '1. Lesson Booking',\n            points: [\n              'Students can book one-on-one lessons with available tutors directly through the platform.',\n              'Lesson times are displayed according to the user\\'s local time zone.',\n              'Bookings must be made in advance and are subject to tutor availability.',\n              'Once the booking and payment are completed, a confirmation email will be sent to both the student and the tutor.'\n            ]\n          },\n          section2: {\n            title: '2. Payments',\n            points: [\n              'All lessons must be paid for in advance to confirm the booking.',\n              'Payments are securely processed through trusted payment providers (e.g., Stripe, PayPal, or Wise).',\n              'Students are responsible for any transaction fees or commissions charged by their bank or payment provider.',\n              'Lesson prices are clearly displayed before completing the payment.'\n            ]\n          },\n          section3: {\n            title: '3. Currency and Exchange Rates',\n            points: [\n              'Payments are made in USD by default.',\n              'Prices shown in other currencies are for reference only.',\n              'If your payment method uses a different currency, conversion fees and exchange rate differences may apply.',\n              'Allemnionline is not responsible for currency fluctuations or additional banking fees.'\n            ]\n          },\n          section4: {\n            title: '4. Taxes and Fees',\n            points: [\n              'Prices may include applicable local taxes (such as VAT or service tax), depending on the student\\'s location.',\n              'All applicable fees are displayed transparently before payment is finalized.'\n            ]\n          },\n          section5: {\n            title: '5. Payment Confirmation and Receipts',\n            points: [\n              'Once the payment is successfully processed, a digital receipt will be emailed to the student.',\n              'A lesson is considered confirmed only after payment is completed.'\n            ]\n          },\n          section6: {\n            title: '6. Failed or Delayed Payments',\n            points: [\n              'If the payment fails or is delayed, the booking will be marked as \"pending\" and not confirmed.',\n              'Students must resolve any payment issues promptly to maintain their booking.'\n            ]\n          },\n          section7: {\n            title: '7. Automatic Payments or Subscriptions (if applicable)',\n            points: [\n              'If a student chooses to activate subscriptions or auto-refill, charges will be automatically deducted from their saved payment method.',\n              'Subscriptions can be canceled or modified anytime from the account settings.'\n            ]\n          },\n          section8: {\n            title: '8. Support and Contact',\n            description: 'For any questions or issues related to payment, please contact our support team:',\n            email: '<EMAIL>'\n          },\n          features: {\n            securePayments: 'Secure Payments',\n            securePaymentsDesc: 'All transactions are protected with advanced encryption technologies to ensure the security of your financial data.',\n            multipleCurrencies: 'Multiple Currencies',\n            multipleCurrenciesDesc: 'We support a wide range of currencies and payment methods to make the payment process convenient for you.',\n            instantReceipts: 'Instant Receipts',\n            instantReceiptsDesc: 'You receive an instant digital receipt after every successful payment transaction.',\n            instantConfirmation: 'Instant Confirmation',\n            instantConfirmationDesc: 'Bookings are confirmed immediately after successful payment completion.'\n          }\n        },\n        bookingCancellation: {\n          title: 'Booking, Cancellation, and Rescheduling Policy',\n          subtitle: 'This policy outlines the rules and procedures for scheduling, canceling, and modifying one-on-one lessons by both students and tutors, in order to ensure a professional and effective learning experience.',\n          studentPolicy: {\n            title: '1. Student Policy',\n            booking: {\n              title: '1.1 Booking Lessons',\n              points: [\n                'Students may book any available time slot from the tutor\\'s schedule.',\n                'Bookings are confirmed immediately upon successful payment through the platform.'\n              ]\n            },\n            cancellation: {\n              title: '1.2 Lesson Cancellation',\n              points: [\n                'Students may cancel a lesson free of charge if the cancellation is made at least 12 hours before the scheduled time.',\n                'If the cancellation occurs less than 12 hours before the lesson, the full lesson fee will be charged.',\n                'If the student fails to attend the lesson without prior cancellation, the lesson will be considered completed and non-refundable.'\n              ]\n            },\n            rescheduling: {\n              title: '1.3 Rescheduling a Lesson',\n              points: [\n                'Each lesson can be rescheduled only once, and the request must be made at least 12 hours before the original lesson time.',\n                'Students may also contact the tutor directly to request rescheduling even less than 12 hours in advance, but the decision will be at the tutor\\'s discretion.'\n              ]\n            },\n            lateArrival: {\n              title: '1.4 Late Arrival',\n              points: [\n                'Students have a 15-minute grace period after the scheduled start time.',\n                'If the student does not show up within 15 minutes without prior notice, the lesson will be marked as completed and the fee will not be refunded.'\n              ]\n            }\n          },\n          tutorPolicy: {\n            title: '2. Tutor Policy',\n            availability: {\n              title: '2.1 Availability',\n              points: [\n                'Tutors must regularly update their schedules and clearly define their available time slots for lessons.'\n              ]\n            },\n            cancellation: {\n              title: '2.2 Lesson Cancellation or Rescheduling',\n              points: [\n                'Tutors must notify the student as early as possible if they need to cancel or reschedule a lesson.',\n                'Repeated cancellations or no-shows by the tutor may lead to temporary account suspension or other administrative actions.'\n              ]\n            },\n            rescheduling: {\n              title: '2.3 Modifying Lesson Times',\n              points: [\n                'Tutors may cancel or modify a lesson time only if the change is made more than 12 hours in advance.',\n                'Any rescheduling must be communicated to the student through the platform along with a clear explanation.'\n              ]\n            },\n            lateArrival: {\n              title: '2.4 Late Arrival',\n              points: [\n                'Tutors are allowed a maximum delay of 15 minutes.',\n                'If the tutor fails to attend the lesson within that time, the student will be entitled to either a full refund or a free rescheduling.'\n              ]\n            }\n          },\n          generalNotes: {\n            title: 'General Notes',\n            points: [\n              'All time calculations are based on the user\\'s local time zone.',\n              'The platform reserves the right to modify this policy in a way that best serves the educational process. Users will be notified of any official changes.'\n            ]\n          },\n          summary: {\n            forStudents: 'For Students',\n            forTutors: 'For Tutors',\n            freeCancellation: 'Free cancellation 12+ hours before',\n            gracePeriod: '15-minute grace period',\n            oneReschedule: 'One reschedule per booking',\n            cancellationBefore: 'Cancellation 12+ hours before',\n            delayAllowance: '15-minute delay allowance',\n            immediateNotification: 'Immediate student notification',\n            importantNote: 'Important Note: All times are calculated based on the user\\'s local time zone'\n          }\n        }\n      },\n      contact: {\n        title: 'Contact Us',\n        subtitle: 'We are happy to hear from you through the following channels for any inquiries related to our educational services, lesson schedules, or technical support:',\n        mailingAddress: {\n          title: '📍 Mailing Address',\n          company: 'Allemnionline',\n          address: '30 N Gould St, Ste R, Sheridan, WY 82801'\n        },\n        email: {\n          title: '📧 Official Email',\n          address: '<EMAIL>',\n          note: 'We welcome all inquiries and aim to respond within 24 hours.'\n        },\n        phone: {\n          title: '☎️ Phone / WhatsApp',\n          number: '0019179937166',\n          note: 'Available during official working hours, from 9:00 AM to 5:00 PM (Mecca Time).'\n        },\n        website: {\n          title: '🌐 Website',\n          url: 'www.allemnionline.com'\n        }\n      },\n      platformPolicies: 'Platform Policies',\n      about: {\n        title: 'About Us',\n        intro: 'Allemnionline is an online educational platform specialized in providing Arabic language teaching services and knowledge related to Arabic culture to learners from around the world, through direct one-on-one lessons delivered by qualified and professional teachers.',\n        mission: 'The platform seeks to make learning Arabic accessible and effective, while considering individual differences and special needs of each learner, using the latest educational technological tools.',\n        whatWeOffer: 'What We Offer',\n        services: {\n          privateLessons: 'Private Arabic language lessons for all levels: from beginners to advanced.',\n          conversationTraining: 'Training in conversation, listening, reading, and writing in Arabic.',\n          culturalElements: 'Introduction to essential elements of Arabic culture that help understand the language in its natural context.',\n          digitalPlatform: 'An integrated digital platform that enables direct communication via video, lesson scheduling, and secure electronic payment.',\n          targetAudience: 'Our services are directed to children, adults, professionals, and anyone who wants to learn Arabic for academic, personal, or professional purposes.'\n        },\n        ourMission: 'Our Mission',\n        missionText: 'To provide distinguished education in Arabic language and related cultural knowledge, with high quality, through direct teaching and modern technologies, in a manner that respects the diversity of learners and their cultural particularities.',\n        contactUs: 'Contact Us',\n        contactText: 'For any inquiries or comments, please contact us via email:',\n        email: '<EMAIL>'\n      }\n    }\n  },\n  ar: {\n    translation: {\n      appName: 'علّمني أون لاين',\n      aboutUs: 'نبذة عنا',\n      wallet: {\n        title: 'المحفظة',\n        balance: 'الرصيد',\n        currentBalance: 'الرصيد الحالي',\n        transactionHistory: 'سجل المعاملات',\n        allTransactions: 'جميع المعاملات',\n        noTransactions: 'لم يتم العثور على معاملات',\n        errorFetchingTransactions: 'خطأ في جلب المعاملات. يرجى المحاولة مرة أخرى.',\n        date: 'التاريخ',\n        description: 'الوصف',\n        amount: 'المبلغ',\n        status: 'الحالة',\n        debit: 'خصم',\n        credit: 'إضافة',\n        pending: 'قيد الانتظار',\n        completed: 'مكتمل',\n        failed: 'فاشل',\n        cancelled: 'ملغي',\n        payment: 'دفع',\n        lessonWith: 'درس مع {{teacher}}',\n        lessonFrom: 'درس من {{student}}',\n        student: 'الطالب',\n        teacher: 'المعلم',\n        rowsPerPage: 'عدد الصفوف في الصفحة',\n        addMoney: 'إضافة أموال',\n        payWithStripe: 'الدفع بالبطاقة الائتمانية',\n        pay: 'دفع',\n        depositSuccess: 'تم الإيداع بنجاح! تم تحديث محفظتك.',\n        errorProcessingPayment: 'خطأ في معالجة الدفع. يرجى المحاولة مرة أخرى.',\n        paypalDeposit: 'إيداع PayPal',\n        stripeDeposit: 'إيداع Stripe',\n        walletDeposit: 'إيداع محفظة',\n        enterOTPTitle: 'أدخل رمز التحقق',\n        otpInstructions: 'الرجاء إدخال رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني.',\n        otpCode: 'رمز التحقق',\n        verify: 'تحقق',\n        enterOTP: 'الرجاء إدخال رمز التحقق',\n        errorVerifyingOTP: 'خطأ في التحقق من الرمز',\n      },\n      withdrawal: {\n        title: 'سحب الأموال',\n        availableBalance: 'الرصيد المتاح',\n        requestWithdrawal: 'طلب سحب',\n        withdrawalHistory: 'تاريخ السحب',\n        noWithdrawals: 'لا توجد طلبات سحب',\n        date: 'التاريخ',\n        amount: 'المبلغ',\n        paypalEmail: 'بريد PayPal الإلكتروني',\n        status: 'الحالة',\n        actions: 'الإجراءات',\n        cancel: 'إلغاء',\n        submit: 'إرسال',\n        pending: 'قيد الانتظار',\n        processing: 'قيد المعالجة',\n        completed: 'مكتمل',\n        failed: 'فشل',\n        cancelled: 'ملغي',\n        fillAllFields: 'يرجى ملء جميع الحقول المطلوبة',\n        minimumAmount: 'الحد الأدنى للسحب هو ${{amount}}',\n        insufficientBalance: 'رصيد غير كافي',\n        requestSubmitted: 'تم إرسال طلب السحب بنجاح',\n        requestCancelled: 'تم إلغاء طلب السحب بنجاح',\n        errorSubmitting: 'خطأ في إرسال طلب السحب',\n        errorCancelling: 'خطأ في إلغاء طلب السحب',\n        errorFetchingWithdrawals: 'خطأ في جلب تاريخ السحب',\n        paypalEmailHelp: 'أدخل بريد PayPal الإلكتروني حيث تريد استلام الدفعة',\n        processingTime: 'وقت المعالجة: {{days}} أيام عمل',\n        minimumWithdrawal: 'الحد الأدنى للسحب',\n        enterOTPTitle: 'أدخل رمز التحقق',\n        otpInstructions: 'الرجاء إدخال رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني.',\n        otpCode: 'رمز التحقق',\n        verify: 'تحقق',\n        enterOTP: 'الرجاء إدخال رمز التحقق',\n        errorVerifyingOTP: 'خطأ في التحقق من الرمز',\n        requestCompleted: 'تم اكتمال طلب السحب بنجاح',\n        withdrawalCancelled: 'تم إلغاء طلب السحب بسبب عدة محاولات فاشلة',\n        errorCancelling: 'خطأ في إلغاء طلب السحب',\n      },\n      admin: {\n        withdrawalManagement: {\n          title: 'إدارة طلبات السحب',\n          statusFilter: 'تصفية الحالة',\n          all: 'الكل',\n          date: 'التاريخ',\n          teacher: 'المعلم',\n          amount: 'المبلغ',\n          paypalEmail: 'بريد PayPal الإلكتروني',\n          status: 'الحالة',\n          notes: 'الملاحظات',\n          actions: 'الإجراءات',\n          approve: 'موافقة',\n          reject: 'رفض',\n          approveWithdrawal: 'الموافقة على السحب',\n          rejectWithdrawal: 'رفض السحب',\n          notesOptional: 'ملاحظات (اختيارية)',\n          approveAndProcess: 'موافقة ومعالجة',\n          processingInfo: 'سيتم معالجة السحب عبر PayPal Payouts API.',\n          errorFetching: 'خطأ في جلب طلبات السحب',\n          errorProcessing: 'خطأ في معالجة السحب'\n        }\n      },\n      contactUs: {\n        title: 'تواصل معنا',\n        type: 'نوع الرسالة',\n        typeQuestion: 'سؤال',\n        typeProblem: 'مشكلة',\n        typeSuggestion: 'اقتراح',\n        typePayment: 'مشكلة في الدفع',\n        typeOther: 'أخرى',\n        subject: 'الموضوع',\n        message: 'الرسالة',\n        send: 'إرسال الرسالة',\n        messageSent: 'تم إرسال رسالتك بنجاح. سنرد عليك قريبًا.',\n        fillAllFields: 'يرجى ملء جميع الحقول المطلوبة',\n        sendError: 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى لاحقًا.',\n        startConversation: 'ابدأ محادثة معنا'\n      },\n      myMessages: {\n        title: 'رسائلي',\n        subject: 'الموضوع',\n        type: 'النوع',\n        date: 'التاريخ',\n        status: 'الحالة',\n        pending: 'قيد الانتظار',\n        answered: 'تم الرد',\n        noMessages: 'لا توجد رسائل',\n        fetchError: 'خطأ في جلب الرسائل',\n        yourMessage: 'رسالتك',\n        adminReply: 'رد الإدارة',\n        awaitingReply: 'في انتظار الرد من الإدارة...'\n      },\n      booking: {\n        title: 'حجز درس',\n        bookLessonWith: 'حجز درس مع',\n        pricePerLesson: 'سعر الدرس',\n        instructions: 'تعليمات الحجز',\n        instructionsText: 'اختر وقتًا من الأوقات المتاحة أدناه. بمجرد تأكيد الحجز، سيتم خصم المبلغ من رصيد محفظتك.',\n        selectTimeSlot: 'اختر وقتًا',\n        noAvailableSlots: 'لا توجد أوقات متاحة لهذا اليوم',\n        teacher: 'المعلم',\n        day: 'اليوم',\n        date: 'التاريخ',\n        time: 'الوقت',\n        price: 'السعر',\n        confirmBooking: 'تأكيد الحجز',\n        confirmAndPay: 'تأكيد والدفع',\n        bookingSuccessTitle: 'تم الحجز بنجاح!',\n        bookingSuccessMessage: 'تم حجز درسك بنجاح. يمكنك عرض حجوزاتك في قسم حجوزاتي.',\n        backToTeacher: 'العودة إلى المعلم',\n        viewMyBookings: 'عرض حجوزاتي',\n        bookAgain: 'احجز مرة أخرى',\n        bookingFailed: 'فشل الحجز. يرجى المحاولة مرة أخرى.',\n        insufficientBalance: 'رصيد غير كافٍ. يرجى إضافة أموال إلى محفظتك.',\n        currentWeek: 'الأسبوع الحالي',\n        weekOf: 'أسبوع',\n        previousWeek: 'الأسبوع السابق',\n        nextWeek: 'الأسبوع التالي',\n        weekNavigation: 'التنقل بين الأسابيع',\n        selectTimeRange: 'اختر المدة الزمنية',\n        startTime: 'وقت البداية',\n        endTime: 'وقت النهاية',\n        lessonDuration: 'مدة الدرس',\n        from: 'من',\n        to: 'إلى',\n        availableSlot: 'خانة متاحة',\n        selectDuration: 'اختر المدة',\n        selectDuration: 'اختر مدة الدرس',\n        duration: 'المدة',\n        fullLesson: 'درس كامل ({{duration}} دقيقة)',\n        halfLesson: 'نصف درس ({{duration}} دقيقة)',\n        meetingCreated: 'تم إنشاء الاجتماع',\n        meetingAccessInfo: 'يمكنك الوصول إلى هذا الاجتماع من صفحة الاجتماعات عندما يحين وقت الدرس.',\n        viewMyMeetings: 'عرض اجتماعاتي'\n      },\n      reviews: {\n        title: 'التقييمات',\n        writeReview: 'كتابة تقييم',\n        myReviews: 'تقييماتي',\n        teacherReviews: 'تقييمات المعلم',\n        rating: 'التقييم',\n        comment: 'التعليق',\n        submit: 'إرسال التقييم',\n        update: 'تحديث التقييم',\n        delete: 'حذف التقييم',\n        confirmDelete: 'هل أنت متأكد من رغبتك في حذف هذا التقييم؟',\n        selectTeacher: 'اختر معلمًا',\n        noTeachers: 'لم نتمكن من العثور على معلمين متاحين لكتابة مراجعات لهم. يرجى التأكد من إكمال درس واحد على الأقل قبل كتابة مراجعة.',\n        noCompletedLessons: 'لم تأخذ دروسًا مع أي معلمين بعد. يرجى حجز درس وإكماله أولاً.',\n        allTeachersReviewed: 'لقد قمت بتقييم جميع المعلمين الذين أخذت دروسًا معهم. يمكنك تحديث تقييماتك الحالية أدناه.',\n        allTeachersReviewedShort: 'لقد قمت بتقييم جميع المعلمين',\n        noReviews: 'لا توجد تقييمات حتى الآن',\n        yourReview: 'تقييمك',\n        editReview: 'تعديل التقييم',\n        reviewSuccess: 'تم إرسال التقييم بنجاح',\n        reviewUpdateSuccess: 'تم تحديث التقييم بنجاح',\n        reviewDeleteSuccess: 'تم حذف التقييم بنجاح',\n        reviewError: 'خطأ في إرسال التقييم',\n        reviewRequired: 'يرجى تقديم تقييم',\n        commentPlaceholder: 'شارك تجربتك مع هذا المعلم...',\n        averageRating: 'متوسط التقييم',\n        totalReviews: '{{count}} تقييمات',\n        oneReview: 'تقييم واحد',\n        reviewsBy: 'تقييمات الطلاب',\n        reviewBy: 'تقييم بواسطة',\n        on: 'في',\n        stars: 'نجوم',\n        star: 'نجمة',\n        outOf5: 'من 5',\n        selectRating: 'اختر التقييم',\n        reviewsFor: 'تقييمات لـ',\n        reviewsWritten: 'التقييمات المكتوبة',\n        reviewsReceived: 'التقييمات المستلمة',\n        viewAll: 'عرض الكل',\n        filterBy: 'تصفية حسب',\n        sortBy: 'ترتيب حسب',\n        newest: 'الأحدث',\n        oldest: 'الأقدم',\n        highestRated: 'الأعلى تقييمًا',\n        lowestRated: 'الأقل تقييمًا',\n        noComment: 'لم يتم تقديم تعليق',\n        noReviewsWithFilter: 'لا توجد تقييمات تطابق التصفية المحددة',\n        teacherReply: 'رد المعلم',\n        replyToReview: 'رد على المراجعة',\n        editReply: 'تعديل الرد',\n        deleteReply: 'حذف الرد',\n        replyPlaceholder: 'اكتب ردك على هذه المراجعة...',\n        sendReply: 'إرسال الرد',\n        updateReply: 'تحديث الرد',\n        replySuccess: 'تم إرسال الرد بنجاح',\n        replyUpdateSuccess: 'تم تحديث الرد بنجاح',\n        replyDeleteSuccess: 'تم حذف الرد بنجاح',\n        replyError: 'خطأ في إرسال الرد',\n        confirmDeleteReply: 'هل أنت متأكد من حذف هذا الرد؟',\n        replyRequired: 'يرجى كتابة رد',\n        sending: 'جاري الإرسال...'\n      },\n      about: {\n        title: 'من نحن',\n        intro: 'منصة Allemnionline هي منصة تعليمية عبر الإنترنت متخصصة في تقديم خدمات تعليم اللغة العربية والمعرفة ذات الصلة بالثقافة العربية للمتعلمين من جميع أنحاء العالم، من خلال دروس مباشرة (واحد لواحد) يقدمها معلمون مؤهلون ومحترفون.',\n        mission: 'تسعى المنصة إلى جعل تعلم اللغة العربية ميسّرًا وفعّالًا، مع مراعاة الفروق الفردية والاحتياجات الخاصة لكل متعلم، وذلك باستخدام أحدث الوسائل التقنية التعليمية.',\n        whatWeOffer: 'ماذا نقدم؟',\n        services: {\n          privateLessons: 'دروس خصوصية في اللغة العربية لجميع المستويات: من المبتدئين إلى المتقدمين.',\n          conversationTraining: 'تدريب على المحادثة، الاستماع، القراءة، والكتابة باللغة العربية.',\n          culturalElements: 'تعريف بالعناصر الأساسية من الثقافة العربية التي تساعد على فهم اللغة في سياقها الطبيعي.',\n          digitalPlatform: 'منصة رقمية متكاملة تتيح التواصل المباشر عبر الفيديو، وجدولة الدروس، والدفع الإلكتروني الآمن.',\n          targetAudience: 'خدماتنا موجهة للأطفال، والبالغين، والمهنيين، ولكل من يرغب في تعلم العربية لأغراض أكاديمية أو شخصية أو مهنية.'\n        },\n        ourMission: 'رسالتنا',\n        missionText: 'أن نوفّر تعليمًا متميزًا للغة العربية والمعرفة الثقافية المرتبطة بها، بجودة عالية، ومن خلال التعليم المباشر والتقنيات الحديثة، وبأسلوب يحترم تنوع المتعلمين وخصوصياتهم الثقافية.',\n        contactUs: 'للتواصل معنا',\n        contactText: 'لأي استفسارات أو ملاحظات، يُرجى التواصل عبر البريد الإلكتروني:',\n        email: '<EMAIL>'\n      },\n      app: {\n        name: 'علّمني أون لاين',\n        copyright: 'علّمني أون لاين ٢٠٢٥',\n        tagline: 'صُمم بحب للتعليم العربي'\n      },\n      brand: {\n        name: 'علّمني أون لاين'\n      },\n      common: {\n        switchLanguage: 'تغيير اللغة',\n        loading: 'جاري التحميل...',\n        settings: 'الإعدادات',\n        profile: 'الملف الشخصي',\n        cancel: 'إلغاء',\n        back: 'رجوع',\n        continue: 'متابعة',\n        confirm: 'تأكيد',\n        update: 'تحديث',\n        success: 'نجاح',\n        error: 'خطأ',\n        save: 'حفظ',\n        saveAndReturn: 'حفظ والعودة',\n        saving: 'جاري الحفظ...',\n        email: 'البريد الإلكتروني',\n        notProvided: 'غير متوفر',\n        search: 'بحث',\n        close: 'إغلاق',\n        password: 'كلمة المرور',\n        fullName: 'الاسم الكامل',\n        name: 'الاسم',\n        confirmPassword: 'تأكيد كلمة المرور',\n        showMore: 'عرض المزيد',\n        showLess: 'عرض أقل',\n        gender: 'الجنس',\n        male: 'ذكر',\n        female: 'أنثى',\n        submit: 'إرسال',\n        notSet: 'غير محدد',\n        actions: 'الإجراءات',\n        edit: 'تعديل',\n        delete: 'حذف',\n        view: 'عرض',\n        details: 'التفاصيل',\n        rowsPerPage: 'عدد الصفوف في الصفحة',\n        footer: {\n          copyright: 'علّمني أون لاين ٢٠٢٥',\n          tagline: 'صُمم بحب للتعليم العربي'\n        },\n        currency: 'دولار',\n        details: 'التفاصيل'\n      },\n      profile: {\n        title: 'الملف الشخصي',\n        fullName: 'الاسم الكامل',\n        edit: 'تعديل الملف الشخصي',\n        save: 'حفظ التغييرات',\n        cancel: 'إلغاء',\n        success: 'تم تحديث الملف الشخصي بنجاح',\n        error: 'حدث خطأ أثناء تحديث الملف الشخصي',\n        updateSuccess: 'تم تحديث الملف الشخصي بنجاح',\n        passwordUpdateSuccess: 'تم تحديث كلمة المرور بنجاح',\n        teachingInfoUpdateSuccess: 'تم تحديث معلومات التدريس بنجاح!',\n        editTeachingInfo: 'تعديل معلومات التدريس',\n        errors: {\n          update: 'فشل تحديث الملف الشخصي',\n          passwordUpdate: 'فشل تحديث كلمة المرور',\n          passwordMismatch: 'كلمات المرور الجديدة غير متطابقة',\n          currentPassword: 'كلمة المرور الحالية غير صحيحة',\n          fetchError: 'فشل في جلب بيانات الملف الشخصي',\n          updateFailed: 'فشل التحديث. يرجى المحاولة مرة أخرى.',\n          deleteRequest: 'فشل في إرسال طلب الحذف',\n          invalidCode: 'رمز التحقق غير صحيح',\n          codeRequired: 'رمز التحقق مطلوب',\n          cancelDelete: 'فشل في إلغاء حذف الحساب'\n        },\n        basicInfo: 'المعلومات الأساسية',\n        teacherInfo: 'معلومات المعلم',\n        editInfo: 'تعديل المعلومات',\n        changePassword: 'تغيير كلمة المرور',\n        email: 'البريد الإلكتروني',\n        gender: 'الجنس',\n        currentPassword: 'كلمة المرور الحالية',\n        newPassword: 'كلمة المرور الجديدة',\n        confirmPassword: 'تأكيد كلمة المرور',\n        editPersonalInfo: 'تعديل المعلومات الشخصية',\n        togglePasswordVisibility: 'إظهار/إخفاء كلمة المرور',\n        uploadPhoto: 'رفع صورة',\n        deleteAccount: 'حذف الحساب',\n        deleteAccountTitle: 'حذف الحساب',\n        deleteAccountWarning: 'هل أنت متأكد من رغبتك في حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه.',\n        deleteAccountNote: 'سيتم إرسال رمز تحقق إلى عنوان بريدك الإلكتروني.',\n        sendDeleteCode: 'إرسال رمز التحقق',\n        verifyDeleteCode: 'تحقق من رمز الحذف',\n        deleteCodeSentMessage: 'تم إرسال رمز التحقق إلى بريدك الإلكتروني. يرجى إدخاله أدناه:',\n        deleteCode: 'رمز التحقق',\n        confirmDelete: 'تأكيد الحذف',\n        deleteCodeSent: 'تم إرسال رمز التحقق إلى بريدك الإلكتروني',\n        deletePending: 'حذف الحساب قيد الانتظار (10 أيام)',\n        cancelDelete: 'إلغاء الحذف',\n        deleteCancelled: 'تم إلغاء حذف الحساب بنجاح',\n        deletePendingAlert: '⚠️ حذف الحساب قيد الانتظار',\n        deletePendingMessage: 'حسابك مجدول للحذف. يمكنك إلغاء هذا الإجراء في أي وقت قبل موعد الحذف المحدد.',\n        deleteScheduledFor: 'موعد الحذف المجدول',\n        updateStatus: {\n          pending: 'طلب تعديل البيانات قيد المراجعة',\n          approved: 'تم الموافقة على تعديل البيانات',\n          rejected: 'تم رفض طلب تعديل البيانات',\n          requestDate: 'تاريخ الطلب',\n          reviewDate: 'تاريخ المراجعة',\n          adminNotes: 'ملاحظات الإدارة',\n          pendingNote: 'يرجى انتظار مراجعة الإدارة لطلب التعديل. ستعمل بالبيانات الحالية حتى الموافقة على التعديل.'\n        },\n        teacher: {\n          videoUpload: {\n            title: 'رفع فيديو تعريفي',\n            description: 'قم برفع فيديو قصير لتقديم نفسك للطلاب المحتملين. سيتم عرض هذا الفيديو على ملفك الشخصي.',\n            requirements: 'متطلبات الفيديو',\n            formatRequirement: 'الصيغ المسموح بها',\n            sizeRequirement: 'الحجم الأقصى',\n            lengthRequirement: 'المدة المقترحة',\n            maximum: 'كحد أقصى',\n            minutes: 'دقائق',\n            selectVideo: 'اختر الفيديو',\n            upload: 'رفع الفيديو',\n            uploading: 'جاري الرفع...',\n            success: 'تم رفع الفيديو بنجاح!',\n            videoReady: 'فيديو التعريف جاهز للإضافة إلى طلبك.',\n            continue: 'متابعة إلى نموذج التقديم',\n            delete: 'حذف الفيديو',\n            skipForNow: 'تخطي الآن',\n            invalidVideoFormat: 'صيغة الفيديو غير صالحة. يرجى اختيار ملف MP4 أو WebM أو OGG.',\n            videoTooSmall: 'حجم الفيديو يجب أن يكون على الأقل 1 ميجابايت.',\n            videoTooLarge: 'حجم الفيديو يجب ألا يتجاوز 100 ميجابايت.',\n            noVideoSelected: 'يرجى اختيار ملف فيديو.',\n            videoDeleteError: 'Error deleting video. Please try again.'\n          },\n          uploadVideoNow: 'رفع الفيديو الآن',\n          videoReady: 'فيديو التعريف جاهز للإضافة إلى طلبك',\n          deleteVideo: 'حذف الفيديو',\n          availableHoursDescription: 'حدد الساعات التي تكون متاحًا فيها للتدريس. سيساعد ذلك الطلاب في العثور على أوقات مناسبة لحجز الدروس معك.',\n          availableHoursApplicationDescription: 'حدد الساعات التي ستكون متاحًا فيها للتدريس إذا تمت الموافقة على طلبك. يمكنك تحديث هذه الساعات لاحقًا من ملفك الشخصي.',\n          availableHoursProfileDescription: 'إدارة أوقات تواجدك للتدريس لإعلام الطلاب بالأوقات المتاحة للدروس.',\n          viewAvailableHours: 'عرض الساعات المتاحة',\n          viewAvailableHoursDescription: 'هنا يمكنك رؤية جميع ساعات التدريس المتاحة منظمة حسب اليوم.',\n          editAvailableHours: 'تعديل الساعات',\n          noHoursForDay: 'لا توجد ساعات متاحة لهذا اليوم',\n          manageAvailableHours: 'إدارة الساعات المتاحة',\n          hoursSavedSuccess: 'تم حفظ ساعاتك المتاحة بنجاح!',\n          myLessons: 'دروسي',\n          studentName: 'اسم الطالب',\n          totalLessons: 'إجمالي الدروس',\n          completedLessons: 'الدروس المكتملة',\n          scheduledLessons: 'الدروس المجدولة',\n          cancelledLessons: 'الدروس الملغاة',\n          noLessonsFound: 'لا توجد دروس',\n          errorSavingHours: 'خطأ في حفظ ساعاتك المتاحة. يرجى المحاولة مرة أخرى.',\n          errorLoadingHours: 'خطأ في تحميل ساعاتك المتاحة. يرجى المحاولة مرة أخرى.',\n          errorParsingHours: 'خطأ في تحليل بيانات ساعاتك المتاحة. يرجى المحاولة مرة أخرى.',\n          saveAndReturn: 'حفظ والعودة',\n          timeSlots: 'فترة زمنية',\n          noAvailableHours: 'لم يتم تحديد ساعات متاحة. يرجى تحديد ساعاتك المتاحة.',\n          selectAll: 'تحديد الكل',\n          clearAll: 'مسح الكل',\n          timeSlot: 'الفترة الزمنية',\n          available: 'متاح',\n          unavailable: 'غير متاح',\n          selectAllDays: 'تحديد هذا الوقت لجميع الأيام',\n          clearAllDays: 'مسح هذا الوقت لجميع الأيام',\n          legend: 'مفتاح الرموز',\n          cellStatus: 'حالة الخلية',\n          actions: 'الإجراءات',\n\n          changeProfilePicture: 'تغيير الصورة الشخصية',\n          profile: {\n            title: 'ملف المعلم',\n            personalInfo: 'المعلومات الشخصية',\n            teachingInfo: 'معلومات التدريس',\n            phone: 'رقم الهاتف',\n            country: 'الدولة',\n            residence: 'مكان الإقامة',\n            nativeLanguage: 'اللغة الأم',\n            teachingLanguages: {\n              title: 'لغات التدريس',\n              Arabic: 'العربية',\n              English: 'الإنجليزية',\n              French: 'الفرنسية',\n              Spanish: 'الإسبانية',\n              Urdu: 'الأوردية',\n              Turkish: 'التركية',\n              Indonesian: 'الإندونيسية',\n              Malay: 'الماليزية',\n              Bengali: 'البنغالية',\n              Hindi: 'الهندية',\n              Persian: 'الفارسية',\n              German: 'الألمانية',\n              Italian: 'الإيطالية',\n              Portuguese: 'البرتغالية',\n              Russian: 'الروسية',\n              Chinese: 'الصينية',\n              Japanese: 'اليابانية',\n              Korean: 'الكورية',\n              Thai: 'التايلاندية',\n              Vietnamese: 'الفيتنامية',\n              Swahili: 'السواحيلية',\n              Hausa: 'الهوسا',\n              Somali: 'الصومالية',\n              select: 'اختر لغات التدريس',\n              placeholder: 'اختر لغة واحدة أو أكثر'\n            },\n            courseTypes: 'أنواع الدورات',\n            qualifications: 'المؤهلات',\n            teachingExperience: 'الخبرة في التدريس',\n            availableHours: 'الساعات المتاحة',\n            pricePerLesson: 'السعر لكل درس',\n            timezone: 'المنطقة الزمنية',\n            paymentMethod: 'طريقة الدفع',\n            currency: {\n              usd: 'دولار أمريكي',\n              eur: 'يورو',\n              gbp: 'جنيه إسترليني'\n            },\n            experience: {\n              beginner: 'مبتدئ (0-2 سنوات)',\n              intermediate: 'متوسط (2-5 سنوات)',\n              advanced: 'متقدم (5-10 سنوات)',\n              expert: 'خبير (أكثر من 10 سنوات)'\n            }\n          }\n        },\n        genders: {\n          male: 'ذكر',\n          female: 'أنثى'\n        }\n      },\n      nav: {\n        home: 'الرئيسية',\n        dashboard: 'لوحة التحكم',\n        teachers: 'المعلمون',\n        students: 'الطلاب',\n        deletedUsers: 'المستخدمون المحذوفون',\n        categories: 'التصنيفات',\n        languages: 'اللغات',\n        applications: 'الطلبات',\n        profileUpdates: 'تعديلات البيانات',\n        withdrawalManagement: 'إدارة طلبات السحب',\n        findTeacher: 'ابحث عن معلم',\n        myTeachers: 'أساتذتي',\n        meetings: 'الاجتماعات',\n        chat: 'المحادثات',\n        login: 'تسجيل الدخول',\n        register: 'إنشاء حساب',\n        logout: 'تسجيل الخروج',\n        search: 'بحث',\n        language: 'اللغة',\n        english: 'الإنجليزية',\n        arabic: 'العربية',\n        adminDashboard: 'لوحة تحكم المدير',\n        teacherDashboard: 'لوحة تحكم المعلم',\n        studentDashboard: 'لوحة تحكم الطالب'\n      },\n      deletedUsers: {\n        title: 'المستخدمون المحذوفون',\n        totalDeleted: 'إجمالي المحذوفين',\n        deletedStudents: 'الطلاب المحذوفين',\n        deletedTeachers: 'المعلمين المحذوفين',\n        activeUsers: 'المستخدمين النشطين',\n        search: 'البحث',\n        type: 'النوع',\n        all: 'الكل',\n        students: 'طلاب',\n        teachers: 'معلمين',\n        newTeachers: 'معلمين جدد',\n        refresh: 'تحديث',\n        user: 'المستخدم',\n        role: 'النوع',\n        deletionDate: 'تاريخ الحذف',\n        deletionReason: 'سبب الحذف',\n        deletedBy: 'حُذف بواسطة',\n        actions: 'الإجراءات',\n        viewDetails: 'عرض التفاصيل',\n        restoreUser: 'استرداد المستخدم',\n        permanentDelete: 'حذف نهائي',\n        student: 'طالب',\n        teacher: 'معلم',\n        newTeacher: 'معلم جديد',\n        notSpecified: 'غير محدد',\n        selfDeleted: 'حذف ذاتي',\n        restoreConfirmTitle: 'استرداد المستخدم',\n        restoreConfirmMessage: 'هل أنت متأكد من استرداد المستخدم \"{name}\"؟',\n        restoreConfirmNote: 'سيتمكن المستخدم من تسجيل الدخول والوصول للنظام مرة أخرى.',\n        cancel: 'إلغاء',\n        restore: 'استرداد',\n        restoring: 'جاري الاسترداد...',\n        permanentDeleteTitle: 'حذف نهائي',\n        permanentDeleteWarning: 'تحذير: هذا الإجراء لا يمكن التراجع عنه!',\n        permanentDeleteMessage: 'هل أنت متأكد من الحذف النهائي للمستخدم \"{name}\"؟',\n        permanentDeleteNote: 'سيتم حذف جميع البيانات المرتبطة بهذا المستخدم نهائياً.',\n        permanentDeleteButton: 'حذف نهائي',\n        deleting: 'جاري الحذف...',\n        rowsPerPage: 'عدد الصفوف:',\n        displayedRows: '{from}-{to} من {count}',\n        deletionInfo: 'معلومات الحذف',\n        scheduledDeletion: 'الحذف المجدول'\n      },\n      common: {\n        userInfo: 'معلومات المستخدم',\n        teacherInfo: 'معلومات المعلم',\n        studentInfo: 'معلومات الطالب',\n        gender: 'الجنس',\n        male: 'ذكر',\n        female: 'أنثى',\n        joinDate: 'تاريخ الانضمام',\n        totalLessons: 'إجمالي الدروس',\n        totalStudents: 'إجمالي الطلاب',\n        rating: 'التقييم',\n        noRating: 'لا يوجد تقييم',\n        totalEarnings: 'إجمالي الأرباح',\n        currency: 'ريال',\n        totalBookings: 'إجمالي الحجوزات',\n        completedLessons: 'الدروس المكتملة',\n        totalSpent: 'إجمالي المصروف',\n        timezone: 'المنطقة الزمنية',\n        notSet: 'غير محدد',\n        close: 'إغلاق'\n      },\n      footer: {\n        platformName: 'علّمني أون لاين',\n        designedBy: 'صُمم بحب للتعليم العربي',\n        followUs: 'تابعنا',\n        facebook: 'فيسبوك',\n        twitter: 'تويتر',\n        instagram: 'انستغرام',\n        quickLinks: 'روابط سريعة',\n        about: 'من نحن',\n        contact: 'اتصل بنا',\n        privacy: 'سياسة الخصوصية',\n        terms: 'شروط الخدمة',\n        faq: 'الأسئلة الشائعة',\n        support: 'الدعم الفني',\n        copyright: '© ٢٠٢٥ علّمني أون لاين',\n        tagline: 'صُمم بحب للتعليم العربي'\n      },\n      hero: {\n        title: 'العربية بجميع اللغات',\n        subtitle: 'رحلة تعليمية فريدة تجمع بين اللغة والثقافة العربية',\n        startLearning: 'ابدأ التعلم',\n        becomeTeacher: 'كن مدرساً',\n        imageAlt: 'القرآن الكريم'\n      },\n      home: {\n        subjects: 'موادنا الدراسية',\n        subjectsSubtitle: 'اكتشف مجموعة متنوعة من المواد الإسلامية والعربية مع نخبة من المعلمين المتخصصين',\n        teachers: 'معلم',\n        expertTeachers: 'معلمون متخصصون',\n        expertTeachersDesc: 'تعلم على يد علماء معتمدين ومتحدثين أصليين',\n        activeStudents: 'طلاب نشطون',\n        courses: 'دورات',\n        coursesDesc: 'دورات شاملة في الدراسات الإسلامية واللغة العربية',\n        studentsDesc: 'طلاب من جميع أنحاء العالم',\n        whyChooseUs: 'لماذا تختارنا',\n        whyChooseUsSubtitle: 'نقدم تجربة تعليمية فريدة تجمع بين التكنولوجيا الحديثة والتعليم الإسلامي الأصيل',\n        meetTeachers: 'تعرف على معلمينا',\n        meetTeachersSubtitle: 'نخبة من المعلمين المؤهلين والمتخصصين في تعليم الإسلام واللغة العربية',\n        features: {\n          quality: 'تعليم عالي الجودة',\n          qualityDesc: 'معلمون متخصصون وخبراء في مجالاتهم',\n          flexible: 'مرونة في التعلم',\n          flexibleDesc: 'تعلم في أي وقت ومن أي مكان',\n          interactive: 'تعلم تفاعلي',\n          interactiveDesc: 'دروس تفاعلية ومناقشات مباشرة',\n          certified: 'شهادات معتمدة',\n          certifiedDesc: 'احصل على شهادات معتمدة في مجال دراستك'\n        },\n        testimonials: 'ماذا يقول طلابنا',\n        testimonialsSubtitle: 'استمع إلى طلابنا حول تجربتهم التعليمية مع معلمينا المتخصصين',\n        reviewFor: 'تقييم لـ',\n        learnMore: 'اكتشف المزيد'\n      },\n      testimonials: {\n        student: 'طالب',\n        reviewFor: 'تقييم لـ'\n      },\n      search: {\n        findTeacher: 'ابحث عن معلم',\n        filters: 'التصفية',\n        subject: 'المادة',\n        allSubjects: 'جميع المواد',\n        language: 'اللغة',\n        allLanguages: 'جميع اللغات',\n        priceRange: 'نطاق السعر',\n        rating: 'التقييم',\n        anyRating: 'أي تقييم',\n        andAbove: 'وما فوق',\n        noTeachersFound: 'لم يتم العثور على معلمين',\n        yearsOfExperience: '{{years}} سنوات من الخبرة',\n        bookLesson: 'احجز درساً',\n        perHour: '/ساعة',\n        subjects: 'المواد',\n        languages: 'اللغات',\n        searchButton: 'بحث',\n        clearFilters: 'مسح التصفية',\n        noTeachersFound: 'لم يتم العثور على معلمين مطابقين لمعاييرك',\n        teachingLanguages: 'لغات التدريس',\n        verifiedTeacher: 'معلم موثق',\n        watchIntro: 'مشاهدة المقدمة',\n        watchIntroVideo: 'مشاهدة فيديو التعريف',\n        viewProfile: 'عرض الملف الشخصي',\n        contactAndBook: 'تواصل واحجز'\n      },\n      booking: {\n        bookLessonWith: 'احجز درساً مع',\n        pricePerLesson: 'سعر الدرس',\n        instructions: 'تعليمات الحجز',\n        instructionsText: 'اختر يوماً وموعداً من التقويم أدناه. بعد تأكيد الحجز، سيتم خصم المبلغ من رصيد محفظتك.',\n        selectTimeSlot: 'اختر موعداً',\n        clickToSelectSlot: 'اضغط على المواعيد المتاحة للحجز',\n        clickToBook: 'اضغط لحجز هذا الموعد',\n        fullHour: 'ساعة كاملة',\n        fullHourAvailable: 'ساعة كاملة متاحة',\n        halfHourOnly: 'نصف ساعة فقط',\n        fullLesson: 'درس كامل (50 دقيقة)',\n        halfLesson: 'نصف درس (25 دقيقة)',\n        consecutiveSlots: 'فترات متتالية',\n        selectDuration: 'اختر مدة الدرس',\n        selectBookingOption: 'اختر نوع الحجز',\n        bookingType: 'نوع الحجز',\n        regularHalfLesson: 'نصف درس عادي (25 دقيقة)',\n        regularFullLesson: 'درس كامل عادي (50 دقيقة)',\n        secondHalfOnly: 'النصف الثاني فقط (بعد 30 دقيقة)',\n        fullLessonFromSecondHalf: 'درس كامل من النصف الثاني (50 دقيقة)',\n        crossHourLesson: 'درس عبر الساعات (30+30 دقيقة)',\n        regularLesson: 'درس عادي',\n        duration: 'المدة',\n        noAvailableSlots: 'لا توجد مواعيد متاحة لهذا اليوم',\n        confirmBooking: 'تأكيد الحجز',\n        teacher: 'المعلم',\n        day: 'اليوم',\n        date: 'التاريخ',\n        time: 'الوقت',\n        price: 'السعر',\n        confirmAndPay: 'تأكيد والدفع',\n        bookingSuccessTitle: 'تم الحجز بنجاح!',\n        bookingSuccessMessage: 'تم حجز درسك بنجاح وتم خصم المبلغ من محفظتك. يمكنك عرض حجوزاتك في قسم حجوزاتي.',\n        backToTeacher: 'العودة إلى المعلم',\n        viewMyBookings: 'عرض حجوزاتي',\n        bookAgain: 'احجز مرة أخرى',\n        bookingFailed: 'فشل الحجز. يرجى المحاولة مرة أخرى.',\n        insufficientBalance: 'رصيدك غير كافٍ. يرجى إضافة رصيد إلى محفظتك قبل الحجز.',\n        currency: 'دولار',\n        currentWeek: 'الأسبوع الحالي',\n        weekOf: 'أسبوع',\n        previousWeek: 'الأسبوع السابق',\n        nextWeek: 'الأسبوع التالي',\n        weekNavigation: 'التنقل بين الأسابيع',\n        selectTimeRange: 'اختر المدة الزمنية',\n        startTime: 'وقت البداية',\n        endTime: 'وقت النهاية',\n        lessonDuration: 'مدة الدرس',\n        from: 'من',\n        to: 'إلى',\n        availableSlot: 'خانة متاحة',\n        selectDuration: 'اختر المدة'\n      },\n      bookings: {\n        title: 'حجوزاتي',\n        weeklyTitle: 'حجوزات هذا الأسبوع',\n        description: 'عرض وإدارة دروسك المجدولة مع المعلمين.',\n        weeklyDescription: 'عرض حجوزاتك لهذا الأسبوع في شكل تقويم.',\n        bookingDetails: 'تفاصيل الحجز',\n        noBookings: 'ليس لديك حجوزات حتى الآن.',\n        fetchError: 'خطأ في جلب الحجوزات. يرجى المحاولة مرة أخرى.',\n        teacher: 'المعلم',\n        date: 'التاريخ',\n        time: 'الوقت',\n        timeRange: 'نطاق الوقت',\n        duration: 'المدة',\n        minutes: 'دقيقة',\n        status: 'الحالة',\n        statusValues: {\n          scheduled: 'مجدول',\n          completed: 'مكتمل',\n          cancelled: 'ملغي',\n          issue_reported: 'تم الإبلاغ عن مشكلة',\n          ongoing: 'جاري'\n        },\n        price: 'السعر',\n        cancel: 'إلغاء',\n        confirmCancel: 'تأكيد الإلغاء',\n        cancelWarning: 'هل أنت متأكد من رغبتك في إلغاء هذا الحجز؟ سيتم إعادة المبلغ إلى محفظتك.',\n        cancellationReason: 'سبب الإلغاء (اختياري)',\n        cancellationReasonPlaceholder: 'يرجى كتابة سبب إلغاء هذا الحجز...',\n        confirmCancelButton: 'نعم، إلغاء الحجز',\n        cancelling: 'جاري الإلغاء...',\n        cancelSuccess: 'تم إلغاء الحجز بنجاح وتمت إعادة المبلغ إلى محفظتك',\n        cancelError: 'خطأ في إلغاء الحجز. يرجى المحاولة مرة أخرى.',\n        availableSlot: 'وقت متاح',\n        currentTime: 'الوقت الحالي',\n        pastSlot: 'وقت فائت',\n        takeBreak: 'أخذ راحة',\n        takeBreakTitle: 'أخذ هذا الوقت راحة',\n        takeBreakMessage: 'هل تريد أخذ هذا الوقت راحة؟',\n        takeBreakNote: 'سيتم إخفاء هذا الوقت من الطلاب لهذا الأسبوع فقط',\n        breakTakenSuccess: 'تم أخذ الراحة بنجاح',\n        breakTakenError: 'خطأ في أخذ الراحة',\n        break: 'راحة',\n        clickToTakeBreak: 'اضغط لأخذ راحة',\n        cancelBreakTitle: 'إلغاء وقت الراحة',\n        cancelBreakMessage: 'هل تريد إلغاء وقت الراحة هذا؟',\n        cancelBreakNote: 'سيصبح هذا الوقت متاحاً للطلاب مرة أخرى',\n        breakCancelledSuccess: 'تم إلغاء الراحة بنجاح',\n        breakCancelledError: 'خطأ في إلغاء الراحة',\n        reschedule: 'إعادة جدولة',\n        rescheduleTitle: 'إعادة جدولة الحجز',\n        rescheduleDescription: 'اختر موعداً جديداً لهذا الحجز من أوقاتك المتاحة.',\n        currentBooking: 'الحجز الحالي',\n        selectDay: 'اختر اليوم',\n        chooseDay: 'اختر يوماً',\n        selectTime: 'اختر الوقت',\n        chooseTime: 'اختر وقتاً',\n        loadingDays: 'جاري تحميل الأيام المتاحة...',\n        loadingTimes: 'جاري تحميل الأوقات المتاحة...',\n        noAvailableDays: 'لا توجد أيام متاحة خلال الـ 30 يوماً القادمة.',\n        noAvailableTimes: 'لا توجد أوقات متاحة في هذا اليوم.',\n        availableTimes: 'أوقات متاحة',\n        rescheduleReason: 'سبب إعادة الجدولة (اختياري)',\n        rescheduleReasonPlaceholder: 'يرجى كتابة سبب إعادة جدولة هذا الحجز...',\n        rescheduleSuccess: 'تم إعادة جدولة الحجز بنجاح',\n        rescheduleError: 'خطأ في إعادة جدولة الحجز. يرجى المحاولة مرة أخرى.',\n        fetchDaysError: 'خطأ في جلب الأيام المتاحة. يرجى المحاولة مرة أخرى.',\n        fetchTimesError: 'خطأ في جلب الأوقات المتاحة. يرجى المحاولة مرة أخرى.',\n        currentBooking: 'الحجز الحالي',\n        selectNewDate: 'اختر التاريخ الجديد',\n        selectNewTime: 'اختر الوقت الجديد',\n        selectDateFirst: 'اختر تاريخاً من التقويم أولاً',\n        selectedTime: 'الوقت المختار',\n        confirmReschedule: 'تأكيد إعادة الجدولة',\n        availableSlots: 'فترة متاحة',\n        backToSelectDay: 'العودة لاختيار اليوم'\n      },\n      teacherDetails: {\n        backToSearch: 'العودة إلى البحث',\n        teacherNotFound: 'لم يتم العثور على المعلم',\n        errorFetching: 'خطأ في جلب تفاصيل المعلم',\n        reviews: 'تقييمات',\n        watchIntroVideo: 'مشاهدة الفيديو التعريفي',\n        hideIntroVideo: 'إخفاء الفيديو التعريفي',\n        bookLesson: 'حجز درس',\n        teachingSubjects: 'المواد التعليمية',\n        languages: 'اللغات',\n        nativeLanguage: 'اللغة الأم',\n        teachingLanguages: 'لغات التدريس',\n        qualifications: 'المؤهلات',\n        yearsOfExperience: '{{years}} سنوات من الخبرة',\n        studentReviews: 'تقييمات الطلاب',\n        noReviews: 'لا توجد تقييمات حتى الآن',\n        contactInfo: 'معلومات الاتصال',\n        email: 'البريد الإلكتروني',\n        phone: 'رقم الهاتف',\n        location: 'الموقع',\n        country: 'الدولة',\n        residence: 'مدينة الإقامة',\n        timezone: 'المنطقة الزمنية',\n        experience: 'الخبرة',\n        memberSince: 'عضو منذ',\n        availableHours: 'الساعات المتاحة',\n        paymentInfo: 'معلومات الدفع',\n        pricePerLesson: 'سعر الدرس',\n        paymentMethod: 'طريقة الدفع',\n        cv: 'السيرة الذاتية',\n        introVideo: 'الفيديو التعريفي',\n        courseTypes: 'أنواع الدورات'\n      },\n      chat: {\n        conversations: 'المحادثات',\n        messages: 'الرسائل',\n        typeMessage: 'اكتب رسالة...',\n        send: 'إرسال',\n        noMessages: 'لا توجد رسائل',\n        noChats: 'لا توجد محادثات',\n        startChat: 'ابدأ محادثة جديدة',\n        loading: 'جاري التحميل...',\n        today: 'اليوم',\n        yesterday: 'أمس',\n        online: 'متصل',\n        offline: 'غير متصل',\n        sent: 'تم الإرسال',\n        delivered: 'تم التوصيل',\n        read: 'تمت القراءة',\n        failed: 'فشل الإرسال',\n        retry: 'إعادة المحاولة',\n        onlyStudents: 'يمكن للطلاب فقط بدء محادثة مع المعلمين',\n        no_conversations: 'لا توجد محادثات',\n        type_message: 'اكتب رسالتك هنا...',\n        select_conversation: 'اختر محادثة للبدء',\n        last_seen: 'آخر ظهور',\n        typing: 'يكتب...',\n        deleteConfirmTitle: 'حذف المحادثة',\n        deleteConfirmMessage: 'هل أنت متأكد من رغبتك في حذف المحادثة مع {{name}}؟',\n        conversationDeleted: 'تم حذف المحادثة بنجاح',\n        conversationDeletedByOther: 'تم حذف هذه المحادثة من قبل المشارك الآخر',\n        deleteConversationError: 'حدث خطأ أثناء حذف المحادثة',\n        messageDeleted: 'تم حذف الرسالة بنجاح',\n        messageEdited: 'تم تعديل الرسالة بنجاح',\n        editError: 'حدث خطأ أثناء تعديل الرسالة',\n        deleteError: 'حدث خطأ أثناء حذف الرسالة',\n        noConversations: 'لا توجد محادثات',\n         chat: 'دردشة'\n      },\n      auth: {\n        login: 'تسجيل الدخول',\n        logout: 'تسجيل الخروج',\n        logoutConfirmTitle: 'تأكيد تسجيل الخروج',\n        logoutConfirmMessage: 'هل أنت متأكد أنك تريد تسجيل الخروج؟',\n        welcomeBack: 'مرحباً بعودتك!',\n        email: 'البريد الإلكتروني',\n        password: 'كلمة المرور',\n        signIn: 'تسجيل الدخول',\n        or: 'أو',\n        noAccount: 'ليس لديك حساب؟',\n        forgotPassword: 'نسيت كلمة المرور؟',\n        forgotPasswordInstructions: 'أدخل بريدك الإلكتروني وسنرسل لك رمز التحقق لإعادة تعيين كلمة المرور.',\n        sendResetCode: 'إرسال رمز التحقق',\n        backToLogin: 'العودة إلى تسجيل الدخول',\n        resetCodeSent: 'تم إرسال رمز التحقق إلى بريدك الإلكتروني',\n        resetRequestFailed: 'فشل إرسال رمز التحقق',\n        verifyCode: 'التحقق من الرمز',\n        verifyCodeInstructions: 'أدخل رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني:',\n        verifyEmail: 'تحقق من البريد الإلكتروني',\n        verificationCodeSentTo: 'أرسلنا رمز التحقق إلى',\n        verify: 'تحقق',\n        verifying: 'جاري التحقق...',\n        verificationCodeSent: 'تم إرسال رمز التحقق بنجاح',\n        verificationFailed: 'فشل التحقق من البريد الإلكتروني',\n        resendFailed: 'فشل إعادة إرسال رمز التحقق',\n        resendCodeIn: 'إعادة إرسال الرمز خلال',\n        sending: 'جاري الإرسال...',\n        invalidCode: 'يرجى إدخال رمز صحيح مكون من 6 أرقام',\n        fillAllFields: 'يرجى ملء جميع الحقول',\n        loginFailed: 'فشل تسجيل الدخول. يرجى التحقق من بياناتك.',\n        invalidCredentials: 'البريد الإلكتروني أو كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.',\n        emailNotFound: 'هذا البريد الإلكتروني غير مسجل. يرجى التحقق من البريد الإلكتروني أو إنشاء حساب جديد.',\n        wrongPassword: 'كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.',\n        accountDeleted: 'تم حذف هذا الحساب ولا يمكن تسجيل الدخول به.',\n        verificationCode: 'رمز التحقق',\n        verifyAndContinue: 'التحقق والمتابعة',\n        resendCode: 'إعادة إرسال الرمز',\n        changeEmail: 'تغيير البريد الإلكتروني',\n        invalidCode: 'رمز التحقق غير صالح',\n        verificationFailed: 'فشل التحقق',\n        resetCodeResent: 'تم إعادة إرسال رمز التحقق إلى بريدك الإلكتروني',\n        resetPassword: 'إعادة تعيين كلمة المرور',\n        resetPasswordInstructions: 'أنشئ كلمة مرور جديدة لحسابك',\n        newPassword: 'كلمة المرور الجديدة',\n        confirmPassword: 'تأكيد كلمة المرور',\n        passwordResetSuccess: 'تم إعادة تعيين كلمة المرور بنجاح!',\n        passwordResetSuccessMessage: 'تم إعادة تعيين كلمة المرور الخاصة بك بنجاح.',\n        redirectingToLogin: 'جاري إعادة التوجيه إلى صفحة تسجيل الدخول...',\n        resetPasswordFailed: 'فشل إعادة تعيين كلمة المرور',\n        signUp: 'إنشاء حساب',\n        continueWithGoogle: 'المتابعة باستخدام جوجل',\n        googleSignInError: 'فشل تسجيل الدخول بجوجل. يرجى المحاولة مرة أخرى.',\n        signInWithGoogle: 'المتابعة باستخدام جوجل',\n        loginError: 'فشل تسجيل الدخول. يرجى التحقق من بيانات الاعتماد الخاصة بك.',\n        accountNotFound: 'الحساب غير موجود',\n        invalidRole: 'دور المستخدم غير صالح',\n        chooseAccountType: 'انضم إلى مجتمعنا',\n        registerDescription: 'ابدأ رحلتك في التعليم الإسلامي. اختر دورك وكن جزءاً من مجتمعنا المتنامي من المتعلمين والمعلمين.',\n        registerAsTeacher: 'علّم معنا',\n        teacherDescription: 'شارك معرفتك وخبرتك في الدراسات الإسلامية واللغة العربية. ساعد الآخرين على التعلم والنمو مع تطوير مسيرتك في التعليم.',\n        registerAsStudent: 'تعلّم معنا',\n        studentDescription: 'ابدأ رحلة تعلمك في الدراسات الإسلامية واللغة العربية مع معلمين خبراء من جميع أنحاء العالم.',\n        alreadyHaveAccount: 'لديك حساب بالفعل؟',\n        getStarted: 'ابدأ الآن',\n        studentRegistration: 'تسجيل طالب جديد',\n        teacherRegistration: 'تسجيل معلم جديد',\n        joinOurCommunity: 'انضم إلى مجتمع المتعلمين',\n        joinOurTeachers: 'شارك معرفتك مع الآخرين',\n        createAccount: 'إنشاء حساب',\n        registrationError: 'فشل التسجيل. يرجى المحاولة مرة أخرى.',\n        selectGender: 'الرجاء اختيار الجنس',\n        gender: 'الجنس',\n        male: 'ذكر',\n        female: 'أنثى'\n      },\n      courseTypes: {\n        Islamic_Law: 'الشريعة الإسلامية',\n        Fiqh: 'الفقه',\n        Quran: 'القرآن الكريم',\n        Tajweed: 'التجويد',\n        Islamic_History: 'التاريخ الإسلامي',\n        Arabic_Grammar: 'قواعد اللغة العربية',\n        Arabic_Speaking: 'المحادثة العربية',\n        Arabic_Writing: 'الكتابة العربية',\n        Islamic_Ethics: 'الأخلاق الإسلامية',\n        Hadith: 'الحديث الشريف',\n        Aqeedah: 'العقيدة'\n      },\n      languages: {\n        Arabic: 'العربية',\n        English: 'الإنجليزية',\n        French: 'الفرنسية',\n        Spanish: 'الإسبانية'\n      },\n      gender: {\n        male: 'ذكر',\n        female: 'أنثى'\n      },\n      dashboard: {\n        welcome: 'مرحباً',\n        unauthorized: 'غير مصرح لك بالوصول إلى لوحة التحكم',\n        fetchError: 'حدث خطأ أثناء جلب البيانات',\n        totalStudents: 'إجمالي الطلاب',\n        totalClasses: 'إجمالي الدروس',\n        averageRating: 'متوسط التقييم',\n        totalEarnings: 'إجمالي الأرباح',\n        categories: 'فئات التدريس',\n        recentBookings: 'آخر الحجوزات',\n        recentReviews: 'آخر التقييمات',\n        learningProgress: 'تقدم التعلم',\n        quickActions: 'إجراءات سريعة',\n        recommendedTeachers: 'معلمون موصى بهم',\n        upcomingLessons: 'الدروس القادمة',\n        noUpcomingLessons: 'لا توجد دروس قادمة',\n        updateProfile: 'تحديث الملف الشخصي',\n        findTeacher: 'ابحث عن معلم',\n        browseCourses: 'تصفح الدورات',\n        viewAll: 'عرض الكل',\n        incompleteProfile: 'ملفك الشخصي غير مكتمل',\n        completeProfileMessage: 'يرجى إكمال ملفك الشخصي للوصول إلى جميع الميزات والعثور على المعلم المناسب لرحلتك التعليمية.',\n        completeProfileToAccess: 'يجب عليك إكمال ملفك الشخصي للوصول إلى هذه الصفحة وميزاتها.',\n        completeProfile: 'إكمال الملف الشخصي',\n        updateProfile: 'تحديث الملف الشخصي',\n        completeProfileNow: 'أكمل ملفك الشخصي الآن'\n      },\n      regions: {\n        middle_east: 'الشرق الأوسط',\n        north_africa: 'شمال أفريقيا',\n        sub_saharan_africa: 'أفريقيا جنوب الصحراء',\n        south_asia: 'جنوب آسيا',\n        southeast_asia: 'جنوب شرق آسيا',\n        central_asia: 'آسيا الوسطى',\n        europe: 'أوروبا',\n        north_america: 'أمريكا الشمالية',\n        south_america: 'أمريكا الجنوبية',\n        east_asia: 'شرق آسيا',\n        oceania: 'أوقيانوسيا',\n        others: 'أخرى'\n      },\n      teacher: {\n        phoneOptional: 'اختياري',\n        nativeLanguage: 'اللغة الأم',\n        teachingLanguages: {\n          title: 'لغات التدريس',\n          Arabic: 'العربية',\n          English: 'الإنجليزية',\n          French: 'الفرنسية',\n          Spanish: 'الإسبانية',\n          Urdu: 'الأوردية',\n          Turkish: 'التركية',\n          Indonesian: 'الإندونيسية',\n          Malay: 'الماليزية',\n          Bengali: 'البنغالية',\n          Hindi: 'الهندية',\n          Persian: 'الفارسية',\n          German: 'الألمانية',\n          Italian: 'الإيطالية',\n          Portuguese: 'البرتغالية',\n          Russian: 'الروسية',\n          Chinese: 'الصينية',\n          Japanese: 'اليابانية',\n          Korean: 'الكورية',\n          Thai: 'التايلاندية',\n          Vietnamese: 'الفيتنامية',\n          Swahili: 'السواحيلية',\n          Hausa: 'الهوسا',\n          Somali: 'الصومالية',\n          select: 'اختر لغات التدريس',\n          placeholder: 'اختر لغة واحدة أو أكثر'\n        },\n        qualifications: 'المؤهلات',\n        experience: 'الخبرة',\n        subjects: 'المواد',\n        profile: {\n          title: 'ملف المعلم',\n          personalInfo: 'المعلومات الشخصية',\n          teachingInfo: 'معلومات التدريس',\n          phone: 'رقم الهاتف',\n          country: 'الدولة',\n          residence: 'مكان الإقامة',\n          nativeLanguage: 'اللغة الأم',\n          teachingLanguages: {\n            title: 'لغات التدريس',\n            Arabic: 'العربية',\n            English: 'الإنجليزية',\n            French: 'الفرنسية',\n            Spanish: 'الإسبانية',\n            Urdu: 'الأوردية',\n            Turkish: 'التركية',\n            Indonesian: 'الإندونيسية',\n            Malay: 'الماليزية',\n            Bengali: 'البنغالية',\n            Hindi: 'الهندية',\n            Persian: 'الفارسية',\n            German: 'الألمانية',\n            Italian: 'الإيطالية',\n            Portuguese: 'البرتغالية',\n            Russian: 'الروسية',\n            Chinese: 'الصينية',\n            Japanese: 'اليابانية',\n            Korean: 'الكورية',\n            Thai: 'التايلاندية',\n            Vietnamese: 'الفيتنامية',\n            Swahili: 'السواحيلية',\n            Hausa: 'الهوسا',\n            Somali: 'الصومالية',\n            select: 'اختر لغات التدريس',\n            placeholder: 'اختر لغة واحدة أو أكثر'\n          },\n          courseTypes: 'أنواع الدورات',\n          qualifications: 'المؤهلات',\n          teachingExperience: 'الخبرة في التدريس',\n          availableHours: 'الساعات المتاحة',\n          pricePerLesson: 'السعر لكل درس',\n          timezone: 'المنطقة الزمنية',\n          paymentMethod: 'طريقة الدفع',\n          currency: {\n            usd: 'دولار أمريكي',\n            eur: 'يورو',\n            gbp: 'جنيه إسترليني'\n          },\n          experience: {\n            beginner: 'مبتدئ (0-2 سنوات)',\n            intermediate: 'متوسط (2-5 سنوات)',\n            advanced: 'متقدم (5-10 سنوات)',\n            expert: 'خبير (أكثر من 10 سنوات)'\n          }\n        },\n        application: {\n          title: 'طلب التقديم للتدريس',\n          submit: 'تقديم الطلب',\n          success: 'تم تقديم الطلب بنجاح',\n          error: 'حدث خطأ أثناء تقديم الطلب',\n          errorFetchingData: 'حدث خطأ أثناء جلب البيانات',\n          errorFetchingCategories: 'حدث خطأ أثناء جلب التصنيفات',\n          statusCardTitle: 'حالة طلب التقديم',\n          edit: 'تعديل الطلب',\n          editDescription: 'يمكنك تعديل بيانات طلبك، ولكن ستحتاج إلى موافقة المشرف مرة أخرى.',\n          warningTitle: 'تحذير: تعديل الطلب',\n          warningMessage: 'تعديل بياناتك سيرسل طلب تحديث يتطلب موافقة الإدارة.',\n          warningDescription1: 'إذا تابعت، سيتم توجيهك إلى نموذج التعديل حيث يمكنك تحديث بياناتك.',\n          warningDescription2: 'ستستمر في التدريس بالبيانات الحالية حتى توافق الإدارة على طلب التحديث.',\n          warningConfirmation: 'هل أنت متأكد من أنك تريد المتابعة؟',\n          confirmEdit: 'نعم، تعديل الطلب',\n          editApplication: {\n            title: 'تعديل الطلب',\n            description: 'تحديث بيانات طلبك. ستتم مراجعة التغييرات من قبل الإدارة قبل تطبيقها.',\n            warning: 'ستبقى بياناتك الحالية نشطة حتى توافق الإدارة على التغييرات.'\n          },\n          changeVideo: 'تغيير الفيديو',\n          status: {\n            pending: 'قيد المراجعة',\n            approved: 'تم القبول',\n            rejected: 'تم الرفض'\n          },\n          statusMessage: {\n            pending: 'طلبك قيد المراجعة',\n            approved: 'تهانينا! تم قبول طلبك كمدرس',\n            rejected: 'عذراً، تم رفض طلبك',\n            default: 'تم تحديث حالة الطلب'\n          },\n          applicationNextSteps: {\n            approved: {\n              title: 'الخطوات التالية',\n              steps: [\n                'الرجاء تسجيل الخروج من حسابك',\n                'تسجيل الدخول مرة أخرى لتفعيل صلاحياتك الكاملة كمدرس'\n              ]\n            }\n          }\n        },\n        country: 'الدولة',\n        residence: 'مدينة الإقامة',\n        nativeLanguage: 'اللغة الأم',\n        teachingLanguages: 'لغات التدريس',\n        courseTypes: 'أنواع الدورات',\n        qualifications: 'المؤهلات',\n        qualificationsPlaceholder: 'أدخل مؤهلاتك التعليمية والشهادات',\n        teachingExperience: 'سنوات الخبرة في التدريس',\n        introVideo: 'فيديو التعريف',\n        introVideoUrl: 'رابط فيديو التعريف',\n        introVideoUrlPlaceholder: 'https://...',\n        noIntroVideo: 'لا يوجد فيديو تعريفي متاح',\n        videoLinkTab: 'رابط الفيديو',\n        videoFileTab: 'رفع فيديو',\n        videoLink: 'رابط',\n        videoUpload: 'رفع',\n        videoLinkHelp: 'أدخل رابط من يوتيوب أو منصة فيديو أخرى',\n        videoLinkNote: 'شارك رابط فيديو التعريف الخاص بك من منصة مدعومة',\n        videoUploadHelp: 'قم برفع فيديو التعريف الخاص بك مباشرة إلى منصتنا',\n        videoUploadPlaceholder: 'رفع فيديو',\n        videoSelected: 'الفيديو المحدد',\n        videoUploadError: 'خطأ في رفع الفيديو',\n        allowedFormats: 'الصيغ المسموحة',\n        maxFileSize: 'الحجم الأقصى للملف',\n        recommendedPlatforms: 'المنصات الموصى بها',\n        cv: 'السيرة الذاتية',\n        cvPlaceholder: 'اكتب سيرتك الذاتية',\n        cvHelperText: 'اكتب ملخصًا موجزًا لمؤهلاتك وخبراتك ونهجك في التدريس (الحد الأقصى 2000 حرف)',\n        characters: 'حرف',\n        profilePicture: 'الصورة الشخصية',\n        profilePicturePlaceholder: 'ارفع صورتك الشخصية',\n        profilePictureRequired: 'الصورة الشخصية مطلوبة',\n        availableHours: 'ساعات التدريس المتاحة',\n        availableHoursDescription: 'حدد الأوقات المتاحة للتدريس في كل يوم من أيام الأسبوع',\n        availableHoursProfileDescription: 'إدارة أوقات تواجدك للتدريس لإعلام الطلاب بالأوقات المتاحة للدروس',\n        viewAvailableHours: 'عرض الساعات المتاحة',\n        viewAvailableHoursDescription: 'مراجعة جدولك الحالي للتدريس والأوقات المتاحة',\n        noAvailableHours: 'لم يتم تحديد ساعات متاحة بعد',\n        timeSlot: 'الفترة الزمنية',\n        timeSlots: 'فترات زمنية',\n        available: 'متاح',\n        unavailable: 'غير متاح',\n        editAvailableHours: 'تعديل الساعات المتاحة',\n        weeklySchedule: 'الجدول الأسبوعي',\n        scheduleDescription: 'أوقات تواجدك للتدريس خلال الأسبوع',\n        time: 'الوقت',\n        legend: 'مفتاح الرموز',\n        quickStats: 'إحصائيات سريعة',\n        totalSlots: 'إجمالي الفترات الزمنية',\n        hoursPerWeek: 'ساعات في الأسبوع',\n        activeDays: 'الأيام النشطة',\n        pricePerLesson: 'السعر لكل درس',\n        pricePerLessonPlaceholder: 'أدخل السعر بالدولار الأمريكي',\n        trialLessonPrice: 'سعر الدرس التجريبي',\n        trialLessonPricePlaceholder: 'أدخل سعر الدرس التجريبي بالدولار الأمريكي',\n        trialPriceLessThanRegular: 'يجب أن يكون سعر الدرس التجريبي أقل من أو يساوي سعر الدرس العادي',\n        timezone: 'المنطقة الزمنية',\n        paymentMethod: 'طريقة الدفع',\n        phone: 'رقم الهاتف',\n        phoneHelp: 'متضمناً رمز الدولة (مثال: 966+, 44+, 1+)',\n        commitment: 'التعهد',\n        commitmentDescription: 'يرجى قراءة والموافقة على التعهد التالي',\n        commitmentProfileDescription: 'عرض وإدارة حالة وتفاصيل التعهد الخاص بك',\n        commitmentRequired: 'يجب الموافقة على التعهد للمتابعة',\n        commitmentStatus: {\n          accepted: 'تم قبول التعهد',\n          pending: 'في انتظار الموافقة على التعهد',\n          rejected: 'تم رفض التعهد'\n        },\n        formHasErrors: 'يرجى تصحيح الأخطاء في النموذج قبل المتابعة',\n        selectedHours: 'ساعات مختارة',\n        selectAll: 'اختيار الكل',\n        required: 'هذا الحقل مطلوب',\n        formHasErrors: 'يرجى تصحيح الأخطاء في النموذج',\n        priceRange: 'السعر يجب أن يكون بين $3 و $100',\n        yourEarnings: 'ما ستحصل عليه بعد خصم العمولة:',\n        invalidUrl: 'الرجاء إدخال رابط صحيح',\n        invalidPhone: 'الرجاء إدخال رقم هاتف صحيح',\n        invalidPrice: 'الرجاء إدخال سعر صحيح',\n        uploadVideoNow: 'رفع الفيديو الآن',\n        videoReady: 'الفيديو جاهز',\n        deleteVideo: 'حذف الفيديو',\n        manageAvailableHours: 'إدارة الساعات المتاحة',\n        weeklyBookings: 'حجوزات الطلاب لهذا الأسبوع',\n        weeklyBookingsDescription: 'عرض حجوزات طلابك لهذا الأسبوع في شكل تقويم.',\n\n        commitmentTitle: 'تعهد المعلمين',\n        commitmentAccepted: 'تم قبول التعهد',\n        readCommitment: 'قراءة التعهد والموافقة عليه',\n        accept: 'أوافق',\n        reject: 'أرفض',\n        allowedFormats: 'الصيغ المسموحة',\n        maxFileSize: 'الحد الأقصى لحجم الملف',\n        videoFormats: 'MP4, WebM, OGG',\n        maxVideoSize: '100 ميجابايت',\n        videoRequired: 'فيديو تعريفي مطلوب لطلب التقديم الخاص بك',\n        fileTypes: {\n          image: 'الصيغ المدعومة: JPG, PNG (الحد الأقصى 5 ميجابايت)',\n          document: 'الصيغ المدعومة: PDF, DOC, DOCX (الحد الأقصى 10 ميجابايت)'\n        },\n        submitComplaint: 'تقديم شكوى',\n        complaintReason: 'سبب الشكوى',\n        complaintType1: 'الطالب حضر لكن العمولة لم تُحول',\n        complaintType2: 'الطالب لم يحضر نهائياً',\n        complaintDetails: 'تفاصيل الشكوى (اختياري)',\n        complaintDetailsPlaceholder: 'اكتب أي تفاصيل إضافية إذا لزم الأمر...',\n        complaintStatus: 'حالة الشكوى',\n        complaintStatusValues: {\n          pending: 'في الانتظار',\n          resolved: 'تم الحل'\n        }\n      },\n      role: {\n        admin: 'مدير',\n        teacher: 'معلم',\n        student: 'طالب'\n      },\n      validation: {\n        required: 'هذا الحقل مطلوب',\n        email: 'يرجى إدخال بريد إلكتروني صحيح',\n        password: {\n          min: 'يجب أن تكون كلمة المرور {{min}} أحرف على الأقل',\n          max: 'يجب أن تكون كلمة المرور {{max}} حرفاً على الأكثر',\n          match: 'كلمات المرور غير متطابقة'\n        }\n      },\n      days: {\n        monday: 'الإثنين',\n        tuesday: 'الثلاثاء',\n        wednesday: 'الأربعاء',\n        thursday: 'الخميس',\n        friday: 'الجمعة',\n        saturday: 'السبت',\n        sunday: 'الأحد',\n        mondayShort: 'اثنين',\n        tuesdayShort: 'ثلاثاء',\n        wednesdayShort: 'أربعاء',\n        thursdayShort: 'خميس',\n        fridayShort: 'جمعة',\n        saturdayShort: 'سبت',\n        sundayShort: 'أحد'\n      },\n      hours: {\n        hour1: '1:00 ص',\n        hour2: '2:00 ص',\n        hour3: '3:00 ص',\n        hour4: '4:00 ص',\n        hour5: '5:00 ص',\n        hour6: '6:00 ص',\n        hour7: '7:00 ص',\n        hour8: '8:00 ص',\n        hour9: '9:00 ص',\n        hour10: '10:00 ص',\n        hour11: '11:00 ص',\n        hour12: '12:00 م',\n        hour13: '1:00 م',\n        hour14: '2:00 م',\n        hour15: '3:00 م',\n        hour16: '4:00 م',\n        hour17: '5:00 م',\n        hour18: '6:00 م',\n        hour19: '7:00 م',\n        hour20: '8:00 م',\n        hour21: '9:00 م',\n        hour22: '10:00 م',\n        hour23: '11:00 م',\n        hour24: '12:00 ص'\n      },\n      student: {\n        profile: {\n          title: 'الملف الشخصي للطالب',\n          complete: 'أكمل ملفك الشخصي',\n          languagePreferences: 'تفضيلات اللغة',\n          personalInfo: 'المعلومات الشخصية',\n          learningPreferences: 'تفضيلات التعلم',\n          nativeLanguage: 'اللغة الأم',\n          preferredIslamicLanguage: 'اللغة المفضلة لتعلم الإسلام',\n          preferredArabicLanguage: 'اللغة المفضلة لتعلم العربية',\n          islamLearningLanguage: 'لغة تعلم الإسلام',\n          arabicLearningLanguage: 'لغة تعلم العربية',\n          age: 'العمر',\n          country: 'البلد',\n          timezone: 'المنطقة الزمنية',\n          arabicLevel: 'مستوى اللغة العربية',\n          privateTutoring: 'أنا مهتم بالدروس الخصوصية',\n          preferGroup: 'لا',\n          preferPrivate: 'نعم',\n          updateSuccess: 'تم تحديث الملف الشخصي بنجاح',\n          editInfo: 'تعديل معلومات الملف الشخصي',\n          levels: {\n            beginner: 'مبتدئ',\n            intermediate: 'متوسط',\n            advanced: 'متقدم'\n          },\n          success: 'تم تحديث الملف الشخصي بنجاح! جاري التوجيه إلى لوحة التحكم...'\n        },\n        myTeachers: 'أساتذتي',\n        noTeachersYet: 'لم تقم بحجز أي درس بعد.',\n        lessonsTaken: 'عدد الدروس'\n      },\n      admin: {\n        profile: {\n          personalInfo: 'المعلومات الشخصية',\n          changePassword: 'تغيير كلمة المرور',\n          currentPassword: 'كلمة المرور الحالية',\n          newPassword: 'كلمة المرور الجديدة',\n          confirmNewPassword: 'تأكيد كلمة المرور الجديدة',\n          updateSuccess: 'تم تحديث الملف الشخصي بنجاح',\n          updateError: 'خطأ في تحديث الملف الشخصي',\n          passwordSuccess: 'تم تغيير كلمة المرور بنجاح',\n          passwordError: 'خطأ في تغيير كلمة المرور',\n          imageSuccess: 'تم تحديث صورة الملف الشخصي بنجاح',\n          imageError: 'خطأ في تحديث صورة الملف الشخصي'\n        },\n        teacherRole: {\n          platform_teacher: 'معلم معتمد',\n          new_teacher: 'معلم جديد'\n        },\n        teachers: {\n          title: 'المعلمون',\n          searchPlaceholder: 'البحث عن معلمين...',\n          name: 'الاسم',\n          email: 'البريد الإلكتروني',\n          gender: 'الجنس',\n          role: 'الدور',\n          actions: 'الإجراءات',\n          viewDetails: 'عرض التفاصيل',\n          teacherDetails: 'تفاصيل المعلم',\n          personalInfo: 'المعلومات الشخصية',\n          deleteConfirm: 'هل أنت متأكد من حذف هذا المعلم؟',\n          deleteSuccess: 'تم حذف المعلم بنجاح',\n          deleteError: 'خطأ في حذف المعلم',\n          fetchError: 'خطأ في جلب المعلمين'\n        },\n        students: {\n          title: 'الطلاب',\n          searchPlaceholder: 'البحث عن طلاب...',\n          name: 'الاسم',\n          email: 'البريد الإلكتروني',\n          gender: 'الجنس',\n          country: 'الدولة',\n          age: 'العمر',\n          timezone: 'المنطقة الزمنية',\n          actions: 'الإجراءات',\n          viewDetails: 'عرض التفاصيل',\n          studentDetails: 'تفاصيل الطالب',\n          personalInfo: 'المعلومات الشخصية',\n          learningPreferences: 'تفضيلات التعلم',\n          nativeLanguage: 'اللغة الأم',\n          islamLearningLanguage: 'لغة تعلم الإسلام',\n          arabicLearningLanguage: 'لغة تعلم العربية',\n          arabicProficiencyLevel: 'مستوى إتقان اللغة العربية',\n          privateTutoring: 'التدريس الخصوصي',\n          profileCompleted: 'اكتمال الملف الشخصي',\n          deleteConfirm: 'هل أنت متأكد من حذف هذا الطالب؟',\n          deleteSuccess: 'تم حذف الطالب بنجاح',\n          deleteError: 'خطأ في حذف الطالب',\n          fetchError: 'خطأ في جلب الطلاب',\n          noStudents: 'لم يتم العثور على طلاب',\n          delete: 'حذف',\n          proficiencyLevels: {\n            beginner: 'مبتدئ',\n            intermediate: 'متوسط',\n            advanced: 'متقدم'\n          }\n        },\n        categories: {\n          title: 'التصنيفات',\n          addNew: 'إضافة تصنيف جديد',\n          editTitle: 'تعديل التصنيف',\n          addTitle: 'إضافة تصنيف',\n          name: 'الاسم',\n          description: 'الوصف',\n          createdBy: 'تم الإنشاء بواسطة',\n          updateSuccess: 'تم تحديث التصنيف بنجاح',\n          createSuccess: 'تم إنشاء التصنيف بنجاح',\n          deleteSuccess: 'تم حذف التصنيف بنجاح',\n          deleteConfirm: 'هل أنت متأكد من حذف هذا التصنيف؟',\n          fetchError: 'خطأ في جلب التصنيفات',\n          saveError: 'خطأ في حفظ التصنيف',\n          deleteError: 'خطأ في حذف التصنيف',\n          unauthorized: 'غير مصرح به',\n          invalidId: 'معرف التصنيف غير صالح'\n        },\n        languages: {\n          title: 'اللغات',\n          addNew: 'إضافة لغة جديدة',\n          editTitle: 'تعديل اللغة',\n          addTitle: 'إضافة لغة',\n          name: 'اسم اللغة',\n          nameRequired: 'اسم اللغة مطلوب',\n          unauthorized: 'غير مصرح لك بتنفيذ هذا الإجراء',\n          fetchError: 'خطأ في جلب اللغات',\n          createSuccess: 'تم إنشاء اللغة بنجاح',\n          createError: 'خطأ في إنشاء اللغة',\n          updateSuccess: 'تم تحديث اللغة بنجاح',\n          updateError: 'خطأ في تحديث اللغة',\n          deleteSuccess: 'تم حذف اللغة بنجاح',\n          deleteError: 'خطأ في حذف اللغة',\n          deleteConfirm: 'هل أنت متأكد أنك تريد حذف هذه اللغة؟',\n          invalidId: 'معرف اللغة غير صالح',\n          saveError: 'خطأ في حفظ اللغة'\n        },\n        applications: {\n          searchPlaceholder: 'البحث في الطلبات...',\n          filterByStatus: 'تصفية حسب الحالة',\n          name: 'الاسم',\n          email: 'البريد الإلكتروني',\n          phone: 'الهاتف',\n          country: 'الدولة',\n          languages: 'اللغات',\n          status: 'الحالة',\n          actions: 'الإجراءات',\n          statuses: {\n            pending: 'قيد المراجعة',\n            approved: 'مقبول',\n            rejected: 'مرفوض'\n          },\n          title: 'طلبات المعلمين',\n          allStatuses: 'جميع الحالات',\n          viewDetails: 'عرض التفاصيل',\n          approve: 'موافقة',\n          reject: 'رفض',\n          applicationDetails: 'تفاصيل الطلب',\n          personalInfo: 'المعلومات الشخصية',\n          teachingInfo: 'معلومات التدريس',\n          documents: 'المستندات',\n          nativeLanguage: 'اللغة الأم',\n          teachingLanguages: 'لغات التدريس',\n          qualifications: 'المؤهلات',\n          experience: 'الخبرة',\n          pricePerLesson: 'سعر الدرس',\n          viewVideo: 'عرض الفيديو التعريفي',\n          viewCV: 'عرض السيرة الذاتية',\n          basicInfo: 'المعلومات الأساسية',\n          teachingDetails: 'تفاصيل التدريس',\n          schedule: 'الجدول الزمني',\n          location: 'الموقع',\n          applicationDate: 'تاريخ التقديم',\n          courseTypes: 'أنواع الدورات',\n          pricing: 'التسعير',\n          paymentMethod: 'طريقة الدفع',\n          introVideo: 'الفيديو التعريفي',\n          cv: 'السيرة الذاتية',\n          availableHours: 'الساعات المتاحة',\n          yearsOfExperience: '{{years}} سنوات من الخبرة'\n        }\n      },\n      menu: {\n        dashboard: 'لوحة التحكم',\n        meetings: 'الاجتماعات',\n        profile: 'الملف الشخصي',\n        chat: 'المحادثات',\n        platformPolicy: 'سياسة المنصة'\n      },\n      meetings: {\n        title: 'الاجتماعات',\n        myMeetings: 'اجتماعاتي',\n        description: 'عرض وإدارة اجتماعاتك المجدولة مع المعلمين',\n        noMeetings: 'لا توجد اجتماعات مجدولة',\n        noMeetingsDescription: 'ليس لديك أي اجتماعات مجدولة حتى الآن. احجز درسًا للبدء!',\n        teacher: 'المعلم',\n        date: 'التاريخ',\n        time: 'الوقت',\n        fetchError: 'خطأ في جلب الاجتماعات',\n        with: 'مع',\n        createNew: 'إنشاء اجتماع جديد',\n        meetingName: 'اسم الاجتماع',\n        meetingDate: 'تاريخ الاجتماع',\n        duration: 'المدة',\n        minutes: 'دقيقة',\n        create: 'إنشاء الاجتماع',\n        start: 'بدء الاجتماع',\n        join: 'انضمام للاجتماع',\n        notStarted: 'لم يبدأ الاجتماع بعد',\n        ended: 'انتهى الاجتماع',\n        joinError: 'خطأ في الانضمام للاجتماع',\n        cancel: 'إلغاء الاجتماع',\n        copyLink: 'نسخ رمز الغرفة',\n        linkCopied: 'تم نسخ رمز الغرفة',\n        cancelSuccess: 'تم إلغاء الاجتماع بنجاح',\n        cancelError: 'حدث خطأ أثناء إلغاء الاجتماع',\n        createSuccess: 'تم إنشاء الاجتماع بنجاح',\n        createError: 'حدث خطأ أثناء إنشاء الاجتماع',\n        halfLesson: 'نصف حصة',\n        fullLesson: 'حصة كاملة',\n        currency: 'دولار',\n        status: {\n          pending: 'قيد الانتظار',\n          ongoing: 'جاري',\n          completed: 'مكتمل',\n          cancelled: 'ملغي',\n          scheduled: 'مجدول'\n        },\n        meetingCompleted: 'تم إنهاء الاجتماع بنجاح',\n        errorCompletingMeeting: 'حدث خطأ أثناء إنهاء الاجتماع',\n        participant: 'مشارك',\n        you: 'أنت',\n        sessionTime: 'وقت الجلسة',\n        remainingTime: 'الوقت المتبقي',\n        timeUp: 'انتهى الوقت',\n        timeUpTitle: 'انتهى وقت الاجتماع',\n        timeUpMessage: 'انتهى الوقت المحدد للاجتماع. سيتم إغلاق الاجتماع تلقائياً الآن.',\n        timeUpDescription: 'سيتم توجيهك إلى صفحة الحجوزات.',\n        backToBookings: 'العودة للحجوزات',\n        active: 'نشط',\n        stopped: 'متوقف',\n        validation: {\n          nameRequired: 'اسم الاجتماع مطلوب',\n          dateRequired: 'تاريخ الاجتماع مطلوب',\n          durationRequired: 'مدة الاجتماع مطلوبة',\n          allFieldsRequired: 'جميع الحقول مطلوبة'\n        },\n        student: 'الطالب',\n        dateTime: 'التاريخ والوقت',\n        amount: 'المبلغ',\n        actions: 'الإجراءات'\n      },\n      privacy: {\n        title: 'سياسة الخصوصية',\n        intro: 'توضح سياسة الخصوصية هذه كيف تقوم منصة Allemnionline (\"نحن\" أو \"المنصة\") بجمع المعلومات الشخصية لمستخدميها (\"أنت\") الذين يزورون موقعنا الإلكتروني أو يستخدمون خدماتنا التعليمية، وكيفية استخدام هذه المعلومات وحمايتها.',\n        section1: {\n          title: '1. المعلومات التي نجمعها',\n          subtitle: 'قد نقوم بجمع المعلومات التالية:',\n          item1: 'الاسم، وعنوان البريد الإلكتروني، ورقم الهاتف',\n          item2: 'بيانات الدفع والفوترة',\n          item3: 'معلومات الحساب (مثل المستوى اللغوي، وسجل الدروس)'\n        },\n        section2: {\n          title: '2. كيفية استخدام المعلومات',\n          subtitle: 'نستخدم معلوماتك من أجل:',\n          item1: 'تقديم خدماتنا التعليمية وتحسينها',\n          item2: 'إدارة جدولة الدروس وعمليات الدفع',\n          item3: 'التواصل معك بخصوص دروسك وحسابك',\n          item4: 'ضمان أمان المنصة ومنع الاحتيال',\n          item5: 'الامتثال للالتزامات القانونية والتنظيمية'\n        },\n        section3: {\n          title: '3. مشاركة المعلومات',\n          subtitle: 'نحن لا نبيع معلوماتك الشخصية لأي طرف ثالث.',\n          description: 'وقد نشاركها مع:',\n          item1: 'مزودي خدمات الدفع (مثل Stripe)',\n          item2: 'الجهات القانونية إذا طُلب منا ذلك بموجب القانون',\n          item3: 'مزودي الخدمات الموثوقين المرتبطين باتفاقيات سرية'\n        },\n        section4: {\n          title: '4. أمن البيانات',\n          content: 'نطبق إجراءات تقنية وتنظيمية مناسبة لحماية بياناتك من الوصول أو التغيير أو الكشف غير المصرح به.'\n        },\n        section5: {\n          title: '5. حقوقك',\n          subtitle: 'يحق لك:',\n          item1: 'الوصول إلى بياناتك الشخصية',\n          item2: 'طلب تصحيحها أو حذفها',\n          item3: 'سحب موافقتك على معالجتها',\n          item4: 'تقديم شكوى لجهة حماية البيانات (إن وُجدت)',\n          contact: 'لممارسة حقوقك، يُرجى التواصل معنا على البريد التالي: <EMAIL>'\n        },\n        section6: {\n          title: '6. ملفات تعريف الارتباط (Cookies)',\n          content: 'قد يستخدم موقعنا ملفات تعريف الارتباط لتحسين تجربة التصفح. يمكنك تعديل إعدادات المتصفح للتحكم في ملفات تعريف الارتباط.'\n        },\n        section7: {\n          title: '7. خصوصية الأطفال',\n          content: 'لا نقوم بجمع بيانات من الأطفال دون سن 13 عامًا بدون موافقة الوالدين. إذا كنت تعتقد أننا جمعنا بيانات من طفل دون إذن، يُرجى التواصل معنا فورًا.'\n        },\n        section8: {\n          title: '8. الروابط الخارجية',\n          content: 'قد يحتوي موقعنا على روابط إلى مواقع خارجية. لسنا مسؤولين عن ممارسات الخصوصية الخاصة بتلك المواقع.'\n        },\n        section9: {\n          title: '9. التعديلات على هذه السياسة',\n          content: 'قد نقوم بتحديث هذه السياسة من وقت لآخر. ونشجعك على مراجعتها بشكل دوري. سيتم نشر أي تغييرات على هذه الصفحة مع تاريخ نفاذ محدث.'\n        },\n        section10: {\n          title: '10. للتواصل معنا',\n          content: 'إذا كانت لديك أي أسئلة أو استفسارات بخصوص هذه السياسة، يُرجى التواصل عبر:',\n          email: '<EMAIL>'\n        }\n      },\n      policies: {\n        refund: {\n          title: 'سياسة الاسترداد',\n          section1: {\n            title: 'أولًا: حالات يُمنح فيها الطالب استرداد الدرس',\n            description: 'يحق للطالب استرداد الدرس إلى رصيده داخل المنصة لإعادة جدولته، في الحالات التالية:',\n            items: [\n              'إذا ألغى المعلم الدرس أو تغيب عنه في الموعد المحدد.',\n              'إذا تعذر عقد الدرس بسبب خلل تقني من طرف المعلم أو المنصة.'\n            ]\n          },\n          section2: {\n            title: 'ثانيًا: حالات لا يُمنح فيها استرداد الدرس أو إعادة الجدولة',\n            description: 'لا يحق للطالب طلب استرداد الدرس أو إعادة جدولته في الحالات الآتية:',\n            items: [\n              'إلغاء الطالب للدرس خلال أقل من 12 ساعة من موعده.',\n              'تغيّب الطالب عن حضور الدرس دون إشعار مسبق.',\n              'فقدان الاتصال بسبب ضعف الإنترنت أو عطل في جهاز الطالب.',\n              'نسيان بيانات الدخول (اسم المستخدم أو كلمة المرور).',\n              'انتهاء صلاحية الرصيد نتيجة عدم استخدام المنصة لمدة تتجاوز 180 يومًا.',\n              'حذف الطالب لحسابه بشكل اختياري.',\n              'إيقاف الحساب نتيجة مخالفة شروط الاستخدام.'\n            ]\n          },\n          section3: {\n            title: 'ثالثًا: تحديث سياسة الاسترداد',\n            description: 'تحتفظ المنصة بحق تعديل هذه السياسة في أي وقت. ويُعد استمرار استخدام المنصة بعد تحديث السياسة موافقة ضمنية على ما ورد فيها.'\n          },\n          contact: {\n            title: 'للتواصل معنا',\n            email: '📧 <EMAIL>'\n          }\n        },\n        bookingPayment: {\n          title: 'سياسة الحجز والدفع',\n          subtitle: 'توضح هذه السياسة كيفية جدولة الدروس وتأكيدها وتسديد رسومها عبر منصة Allemnionline.',\n          section1: {\n            title: '1. حجز الدروس',\n            points: [\n              'يمكن للطلاب حجز دروس فردية (واحد لواحد) مع المعلمين المتاحين مباشرة عبر المنصة.',\n              'يتم عرض أوقات الدروس حسب التوقيت المحلي للمستخدم.',\n              'يجب أن يتم الحجز مسبقًا ويخضع لتوفر المعلم.',\n              'بعد إتمام الحجز والدفع، يتم إرسال رسالة تأكيد تلقائيًا إلى الطالب والمعلم.'\n            ]\n          },\n          section2: {\n            title: '2. الدفع',\n            points: [\n              'يجب دفع رسوم الدرس مسبقًا لتأكيد الحجز.',\n              'تتم عمليات الدفع بأمان عبر مزودي خدمات الدفع المعتمدين (مثل Stripe أو PayPal أو Wise).',\n              'يتحمل الطالب أي رسوم تحويل أو عمولات تفرضها بنوكه أو مزودو الدفع.',\n              'تُعرض أسعار الدروس بوضوح قبل إتمام الدفع.'\n            ]\n          },\n          section3: {\n            title: '3. العملة وأسعار الصرف',\n            points: [\n              'تتم عمليات الدفع افتراضيًا بالدولار الأمريكي (USD).',\n              'تُعرض الأسعار بعملات أخرى لأغراض الإرشاد فقط.',\n              'إذا كانت وسيلة الدفع الخاصة بك تستخدم عملة مختلفة، فقد تُطبق رسوم تحويل عملة أو فروقات في سعر الصرف.',\n              'لا تتحمل Allemnionline أي مسؤولية عن فروقات الأسعار الناتجة عن تقلبات العملات أو الرسوم البنكية.'\n            ]\n          },\n          section4: {\n            title: '4. الضرائب والرسوم',\n            points: [\n              'قد تشمل الأسعار ضرائب محلية (مثل ضريبة القيمة المضافة أو ضريبة الخدمات) حسب موقع المستخدم.',\n              'يتم عرض جميع الرسوم المطبقة بشكل شفاف قبل إتمام الدفع.'\n            ]\n          },\n          section5: {\n            title: '5. تأكيد الدفع والإيصالات',\n            points: [\n              'بعد إتمام الدفع بنجاح، يتم إرسال إيصال إلكتروني إلى الطالب عبر البريد الإلكتروني.',\n              'يتم تأكيد الحجز فقط بعد معالجة الدفع بنجاح.'\n            ]\n          },\n          section6: {\n            title: '6. فشل أو تأخير الدفع',\n            points: [\n              'في حال فشل الدفع أو تأخره، يُعتبر الحجز \"معلّقًا\" ولا يُعد مؤكدًا.',\n              'يجب على الطالب معالجة المشكلة فورًا لضمان الحفاظ على الحجز.'\n            ]\n          },\n          section7: {\n            title: '7. الدفع التلقائي أو الاشتراكات (إن وجدت)',\n            points: [\n              'إذا اختار الطالب الاشتراك أو إعادة التعبئة التلقائية، سيتم خصم الرسوم تلقائيًا من وسيلة الدفع المحفوظة.',\n              'يمكن إلغاء الاشتراك أو تعديله في أي وقت من خلال إعدادات الحساب.'\n            ]\n          },\n          section8: {\n            title: '8. الدعم الفني والتواصل',\n            description: 'للاستفسارات أو المشكلات المتعلقة بالدفع، يرجى التواصل مع فريق الدعم عبر:',\n            email: '<EMAIL>'\n          },\n          features: {\n            securePayments: 'الدفع الآمن',\n            securePaymentsDesc: 'جميع المعاملات محمية بتقنيات التشفير المتقدمة لضمان أمان بياناتك المالية.',\n            multipleCurrencies: 'عملات متعددة',\n            multipleCurrenciesDesc: 'ندعم مجموعة واسعة من العملات ووسائل الدفع لتسهيل عملية الدفع عليك.',\n            instantReceipts: 'إيصالات فورية',\n            instantReceiptsDesc: 'تحصل على إيصال إلكتروني فوري بعد كل عملية دفع ناجحة.',\n            instantConfirmation: 'تأكيد فوري',\n            instantConfirmationDesc: 'يتم تأكيد الحجز فوراً بعد إتمام الدفع بنجاح.'\n          }\n        },\n        bookingCancellation: {\n          title: 'سياسة الحجز والإلغاء وإعادة الجدولة',\n          subtitle: 'توضح هذه السياسة الضوابط والإجراءات المتعلقة بحجز الدروس الفردية (واحد لواحد)، وإلغائها أو تعديلها، من قبل كل من الطالب والمدرس، وذلك بهدف ضمان الانضباط وتحقيق أفضل تجربة تعليمية.',\n          studentPolicy: {\n            title: 'أولًا: سياسة الطالب',\n            booking: {\n              title: '1. حجز الدروس',\n              points: [\n                'يمكن للطالب حجز أي وقت متاح في جدول المعلم.',\n                'يتم تأكيد الحجز فور إتمام الدفع عبر المنصة.'\n              ]\n            },\n            cancellation: {\n              title: '2. إلغاء الدرس',\n              points: [\n                'يمكن للطالب إلغاء الدرس دون أي خصم بشرط أن يتم الإلغاء قبل 12 ساعة على الأقل من موعد الدرس.',\n                'في حال الإلغاء خلال أقل من 12 ساعة، يتم خصم قيمة الدرس كاملة.',\n                'إذا تغيب الطالب عن حضور الدرس دون إلغاء مسبق، يُعتبر الدرس مكتملًا ولا يحق له استرداد أي مبلغ.'\n              ]\n            },\n            rescheduling: {\n              title: '3. تعديل موعد الدرس',\n              points: [\n                'يحق للطالب تعديل موعد الدرس مرة واحدة فقط لكل حجز، بشرط أن يتم التعديل قبل 12 ساعة على الأقل من الموعد الأصلي.',\n                'يمكن للطالب التواصل المباشر مع المعلم وطلب إعادة الجدولة بشكل ودي حتى قبل أقل من 12 ساعة، ويعود القرار في هذه الحالة إلى المعلم.'\n              ]\n            },\n            lateArrival: {\n              title: '4. التأخر في الحضور',\n              points: [\n                'يُمنح الطالب فترة سماح مدتها 15 دقيقة بعد وقت بدء الدرس.',\n                'إذا لم يحضر الطالب خلال هذه الفترة ودون إشعار مسبق، يُعتبر الدرس لاغيًا ويُحسب كاملًا.'\n              ]\n            }\n          },\n          tutorPolicy: {\n            title: 'ثانيًا: سياسة المعلم',\n            availability: {\n              title: '1. إتاحة المواعيد للحجز',\n              points: [\n                'يجب على المعلم تحديث جدوله بانتظام، وتحديد أوقات الدروس المتاحة بدقة وشفافية.'\n              ]\n            },\n            cancellation: {\n              title: '2. إلغاء أو إعادة جدولة الدروس',\n              points: [\n                'يلتزم المعلم بإشعار الطالب فورًا في حال قرر إلغاء الدرس أو إعادة جدولته.',\n                'في حال تكرار إلغاء الدروس من طرف المعلم أو تغيب دون إشعار، تحتفظ المنصة بحق تعليق الحساب مؤقتًا أو اتخاذ ما يلزم من الإجراءات الإدارية.'\n              ]\n            },\n            rescheduling: {\n              title: '3. تعديل مواعيد الدروس',\n              points: [\n                'يحق للمعلم إلغاء الدرس أو تعديله بشرط أن يتم ذلك قبل أكثر من 12 ساعة من الموعد المحدد.',\n                'يمكنه إعادة جدولة الدرس بشرط تقديم سبب واضح عبر المنصة وإبلاغ الطالب بذلك.'\n              ]\n            },\n            lateArrival: {\n              title: '4. التأخر في الحضور',\n              points: [\n                'يُسمح للمعلم بفترة تأخير لا تتجاوز 15 دقيقة.',\n                'إذا لم يحضر المعلم بعد انقضاء هذه المدة، يُمنح الطالب خيار إعادة جدولة الدرس أو استرداد كامل المبلغ.'\n              ]\n            }\n          },\n          generalNotes: {\n            title: 'ملاحظات عامة',\n            points: [\n              'يتم احتساب جميع المدد الزمنية حسب توقيت الطالب المحلي.',\n              'تحتفظ المنصة بحق تعديل هذه السياسة بما يحقق مصلحة العملية التعليمية، وسيتم إشعار جميع المستخدمين بأي تحديثات رسمية.'\n            ]\n          },\n          summary: {\n            forStudents: 'للطالب',\n            forTutors: 'للمعلم',\n            freeCancellation: 'إلغاء مجاني قبل 12 ساعة',\n            gracePeriod: 'فترة سماح 15 دقيقة',\n            oneReschedule: 'تعديل مرة واحدة لكل حجز',\n            cancellationBefore: 'إلغاء قبل 12+ ساعة',\n            delayAllowance: 'فترة تأخير 15 دقيقة',\n            immediateNotification: 'إشعار فوري للطالب',\n            importantNote: 'ملاحظة مهمة: جميع الأوقات تُحسب حسب التوقيت المحلي للمستخدم'\n          }\n        }\n      },\n      platformPolicies: 'سياسات المنصة',\n      about: {\n        title: 'من نحن',\n        intro: 'منصة Allemnionline هي منصة تعليمية عبر الإنترنت متخصصة في تقديم خدمات تعليم اللغة العربية والمعرفة ذات الصلة بالثقافة العربية للمتعلمين من جميع أنحاء العالم، من خلال دروس مباشرة (واحد لواحد) يقدمها معلمون مؤهلون ومحترفون.',\n        mission: 'تسعى المنصة إلى جعل تعلم اللغة العربية ميسّرًا وفعّالًا، مع مراعاة الفروق الفردية والاحتياجات الخاصة لكل متعلم، وذلك باستخدام أحدث الوسائل التقنية التعليمية.',\n        whatWeOffer: 'ماذا نقدم؟',\n        services: {\n          privateLessons: 'دروس خصوصية في اللغة العربية لجميع المستويات: من المبتدئين إلى المتقدمين.',\n          conversationTraining: 'تدريب على المحادثة، الاستماع، القراءة، والكتابة باللغة العربية.',\n          culturalElements: 'تعريف بالعناصر الأساسية من الثقافة العربية التي تساعد على فهم اللغة في سياقها الطبيعي.',\n          digitalPlatform: 'منصة رقمية متكاملة تتيح التواصل المباشر عبر الفيديو، وجدولة الدروس، والدفع الإلكتروني الآمن.',\n          targetAudience: 'خدماتنا موجهة للأطفال، والبالغين، والمهنيين، ولكل من يرغب في تعلم العربية لأغراض أكاديمية أو شخصية أو مهنية.'\n        },\n        ourMission: 'رسالتنا',\n        missionText: 'أن نوفّر تعليمًا متميزًا للغة العربية والمعرفة الثقافية المرتبطة بها، بجودة عالية، ومن خلال التعليم المباشر والتقنيات الحديثة، وبأسلوب يحترم تنوع المتعلمين وخصوصياتهم الثقافية.',\n        contactUs: 'للتواصل معنا',\n        contactText: 'لأي استفسارات أو ملاحظات، يُرجى التواصل عبر البريد الإلكتروني:',\n        email: '<EMAIL>'\n      }\n    }\n  }\n};\n\n// Define the complete merged resources\nconst mergedResources = {\n  en: {\n    translation: {\n      ...resources.en.translation,\n      privacyPolicy: {\n        title: 'Privacy Policy & Terms of Service',\n        intro: 'Welcome to TeachMeIslam platform. Before registering, please read and agree to our privacy policy and terms of service, which include details about commission rates, booking and cancellation policies.',\n        bookingPolicy: {\n          title: 'Booking and Cancellation Policy',\n        },\n        studentPolicy: {\n          title: 'Student Policy',\n          booking: {\n            title: 'Booking Lessons',\n            description: 'Students can book a lesson at any available time in the teacher\\'s schedule. Booking is confirmed immediately upon payment through the platform.'\n          },\n          cancellation: {\n            title: 'Cancelling Lessons',\n            description: 'Students can cancel a lesson without penalty at least 12 hours before the scheduled time. If cancelled less than 12 hours before, the full lesson fee will be charged. If a student is absent without cancellation, the lesson is considered completed and no refund will be issued.'\n          },\n          rescheduling: {\n            title: 'Rescheduling Lessons',\n            description: 'Lessons can be rescheduled once per lesson, provided the change is made at least 12 hours before the original scheduled time.'\n          },\n          attendance: {\n            title: 'Attendance',\n            description: 'If a student is more than 15 minutes late without notice, they will be considered absent and the lesson will be counted as completed.'\n          }\n        },\n        teacherPolicy: {\n          title: 'Teacher Policy',\n          availability: {\n            title: 'Availability',\n            description: 'Teachers must regularly update their schedule and accurately indicate available times.'\n          },\n          cancellation: {\n            title: 'Cancelling or Rescheduling',\n            description: 'Teachers must notify students when cancelling or rescheduling any lesson as soon as possible. Repeated cancellations or absences without notice may result in temporary account suspension or appropriate administrative action.'\n          },\n          rescheduling: {\n            title: 'Rescheduling Lessons',\n            description: 'Teachers may suggest rescheduling with prior agreement from the student through the platform.'\n          },\n          attendance: {\n            title: 'Attendance',\n            description: 'Teachers are allowed a maximum delay of 15 minutes. If a teacher does not attend after 15 minutes, the student has the right to reschedule the lesson or receive a full refund.'\n          }\n        },\n        generalNotes: {\n          title: 'General Notes',\n          timeZone: 'All time periods are calculated according to the student\\'s time zone.',\n          policyChanges: 'The platform reserves the right to modify these policies to benefit the educational process, with notification to users of any changes.'\n        },\n        commissionPolicy: {\n          title: 'Commission Policy',\n          description: 'The platform applies the following commission rates on lesson fees:',\n          rates: {\n            3: 'For $3 lessons: 33.3% commission',\n            4: 'For $4 lessons: 25% commission',\n            5: 'For $5 lessons: 20% commission',\n            6: 'For $6 lessons: 16.7% commission',\n            7: 'For $7 and above lessons: 15% commission'\n          },\n          explanation: 'The current commission rates have been set to be fair and motivating, and may be subject to periodic review to achieve a balance between service quality and operating costs, with teachers being notified of any changes in advance.'\n        },\n\n        agreement: {\n          checkbox: 'I have read and agree to the Privacy Policy and Terms of Service',\n          required: 'You must agree to the Privacy Policy and Terms of Service to register'\n        }\n      },\n      admin: {\n        ...resources.en.translation.admin\n      },\n      teacher: {\n        ...resources.en.translation.teacher,\n        myLessons: 'My Lessons',\n        studentName: 'Student Name',\n        totalLessons: 'Total Lessons',\n        noLessonsFound: 'No lessons found',\n        submitComplaint: 'Submit Complaint',\n        complaintReason: 'Reason for Complaint',\n        complaintType1: 'Student attended but commission not transferred',\n        complaintType2: 'Student did not attend at all',\n        complaintDetails: 'Complaint Details (optional)',\n        complaintDetailsPlaceholder: 'Write any additional details if needed...',\n        complaintStatus: 'Complaint Status',\n        complaintStatusValues: {\n          pending: 'Pending',\n          resolved: 'Resolved'\n        },\n        videoUpload: {\n          title: 'Upload Introduction Video',\n          description: 'Upload a short video introducing yourself to potential students. This video will be shown on your profile.',\n          requirements: 'Video Requirements',\n          formatRequirement: 'Allowed formats',\n          sizeRequirement: 'Maximum size',\n          lengthRequirement: 'Recommended length',\n          minutes: 'minutes',\n          selectVideo: 'Select Video',\n          upload: 'Upload Video',\n          uploading: 'Uploading...',\n          success: 'Video uploaded successfully!',\n          videoReady: 'Your video is ready. You can now continue to the application form or upload a different video.',\n          continue: 'Continue to Application',\n          delete: 'Delete Video',\n          skipForNow: 'Skip for now',\n        },\n        editVideoUpload: {\n          title: 'Edit Introduction Video',\n          description: 'You can change your introduction video here. The new video will replace the current one.',\n          requirements: 'Video Requirements',\n          formatRequirement: 'Format',\n          sizeRequirement: 'Size',\n          lengthRequirement: 'Duration',\n          minutes: 'minutes',\n          selectVideo: 'Select New Video',\n          upload: 'Upload Video',\n          uploading: 'Uploading...',\n          success: 'Video uploaded successfully!',\n          videoReady: 'Video is ready and saved',\n          saveAndReturn: 'Save and Return',\n          delete: 'Delete Video',\n          invalidVideoFormat: 'Invalid video format. Please select MP4, WebM, or OGG file.',\n          videoTooSmall: 'Video size must be at least 1MB.',\n          videoTooLarge: 'Video size must not exceed 100MB.',\n          noVideoSelected: 'Please select a video file.',\n          videoDeleteError: 'Error deleting video. Please try again.'\n        },\n        commitment: 'Teacher Commitment',\n        commitmentDescription: 'Please read and agree to the following commitment',\n        commitmentProfileDescription: 'View and manage your commitment status and details',\n        commitmentRequired: 'You must agree to the commitment to continue',\n        commitmentStatus: {\n          accepted: 'Commitment accepted',\n          pending: 'Commitment pending approval',\n          rejected: 'Commitment rejected'\n        },\n        commitmentTitle: 'Teacher Commitment',\n        commitmentAccepted: 'Commitment accepted',\n        readCommitment: 'Read and agree to the commitment',\n        accept: 'I Agree',\n        reject: 'I Decline',\n        yourEarnings: 'Your earnings after commission:',\n\n        commitmentText: {\n          intro: 'I, the teacher applying to teach through the \"Allemnionline in All Languages\" platform, hereby acknowledge and pledge the following:',\n          point1: 'To uphold honesty, integrity, and sincerity in fulfilling my educational mission, and to adhere to noble Islamic ethics in my dealings with students and the administration.',\n          point2: 'To ensure that the teaching of Islam and Arabic is in accordance with the methodology of Ahl al-Sunnah wa al-Jama\\'ah, and to respect the consensus of recognized scholars.',\n          point3: 'To adhere to the methodology of the righteous predecessors (Salaf) in creed, methodology, and conduct, and not to deviate from what is found in the Quran and Sunnah as understood by the Companions (may Allah be pleased with them) and those who followed them in righteousness.',\n          point4: 'To refrain from praising or promoting any deviant, extremist groups or ideas, or those with partisan or political orientations that contradict the methodology of the Salaf.',\n          point5: 'Not to exploit the platform to spread ideas or orientations that contradict its religious and scholarly identity, and to limit teaching to what is approved and prescribed by the platform administration.',\n          point6: 'To cooperate with the platform administration with complete transparency and respect, and to update data, work schedules, and respond to students in a timely manner.',\n          conclusion: 'I acknowledge that violating what is stated in this commitment authorizes the platform administration to take appropriate measures as it deems fit, including suspending the account or terminating cooperation.'\n        }\n      }\n    }\n  },\n  ar: {\n    translation: {\n      ...resources.ar.translation,\n      privacyPolicy: {\n        title: 'سياسة الخصوصية وشروط الخدمة',\n        intro: 'مرحبًا بك في منصة TeachMeIslam. قبل التسجيل، يرجى قراءة والموافقة على سياسة الخصوصية وشروط الخدمة، والتي تتضمن تفاصيل حول معدلات العمولة وسياسات الحجز والإلغاء.',\n        bookingPolicy: {\n          title: 'سياسة الحجز والإلغاء',\n        },\n        studentPolicy: {\n          title: 'سياسة الطالب',\n          booking: {\n            title: 'حجز الدروس',\n            description: 'يمكن للطالب حجز درس في أي وقت متاح في جدول المدرس. يتم تأكيد الحجز فور إتمام الدفع عبر المنصة.'\n          },\n          cancellation: {\n            title: 'إلغاء الدروس',\n            description: 'يمكن للطالب إلغاء الدرس دون غرامة قبل 12 ساعة على الأقل من موعد الدرس. إذا تم الإلغاء خلال أقل من 12 ساعة، تُخصم قيمة الدرس كاملة. إذا تغيب الطالب دون إلغاء، يُعتبر الدرس مكتملًا ولا يحق له استرداد أي مبلغ.'\n          },\n          rescheduling: {\n            title: 'تعديل موعد الدرس',\n            description: 'يُسمح بتعديل موعد الدرس مرة واحدة فقط لكل درس، بشرط أن يكون ذلك قبل 12 ساعة من الموعد الأصلي.'\n          },\n          attendance: {\n            title: 'التأخير في الحضور',\n            description: 'إذا تأخر الطالب لأكثر من 15 دقيقة دون إشعار، يُعتبر غائبًا ويُحسب الدرس.'\n          }\n        },\n        teacherPolicy: {\n          title: 'سياسة المدرس',\n          availability: {\n            title: 'توفر المدرس للحجز',\n            description: 'يلتزم المدرس بتحديث جدول مواعيده بشكل منتظم، وتحديد الأوقات المتاحة بدقة.'\n          },\n          cancellation: {\n            title: 'إلغاء الدرس أو إعادة الجدولة',\n            description: 'يجب على المدرس إشعار الطالب عند إلغاء أو إعادة جدولة أي درس، وفي أقرب وقت ممكن. في حال تكرر إلغاء الدروس أو تغيب المدرس دون إشعار، قد يتم تعليق الحساب مؤقتًا أو اتخاذ إجراءات إدارية مناسبة.'\n          },\n          rescheduling: {\n            title: 'تعديل موعد الدرس',\n            description: 'يمكن للمدرس اقتراح تعديل الموعد باتفاق مسبق مع الطالب عبر المنصة.'\n          },\n          attendance: {\n            title: 'التأخير في الحضور',\n            description: 'يُمنح المدرس مهلة تأخير تصل إلى 15 دقيقة كحد أقصى. إذا لم يحضر المدرس بعد مرور 15 دقيقة، يُمنح الطالب حق إعادة جدولة الدرس أو استرداد قيمته كاملة.'\n          }\n        },\n        generalNotes: {\n          title: 'ملاحظات عامة',\n          timeZone: 'يتم احتساب كل المدد الزمنية بحسب توقيت الطالب.',\n          policyChanges: 'تحتفظ المنصة بحقها في تعديل هذه السياسات بما يحقق مصلحة العملية التعليمية، مع إشعار المستخدمين بأي تغييرات.'\n        },\n        commissionPolicy: {\n          title: 'سياسة العمولة',\n          description: 'تطبق المنصة معدلات العمولة التالية على رسوم الدروس:',\n          rates: {\n            3: 'للدروس بقيمة 3 دولار: عمولة 33.3%',\n            4: 'للدروس بقيمة 4 دولار: عمولة 25%',\n            5: 'للدروس بقيمة 5 دولار: عمولة 20%',\n            6: 'للدروس بقيمة 6 دولار: عمولة 16.7%',\n            7: 'للدروس بقيمة 7 دولار وما فوق: عمولة 15%'\n          },\n          explanation: 'نسبة العمولة الحالية تم تحديدها لتكون عادلة ومحفزة، وقد تخضع للمراجعة الدورية بما يحقق التوازن بين جودة الخدمة وتكاليف التشغيل، على أن يُبلغ المدرسون بأي تعديل مسبقًا.'\n        },\n\n        agreement: {\n          checkbox: 'لقد قرأت وأوافق على سياسة الخصوصية وشروط الخدمة',\n          required: 'يجب أن توافق على سياسة الخصوصية وشروط الخدمة للتسجيل'\n        }\n      },\n      teacher: {\n        ...resources.ar.translation.teacher,\n        myLessons: 'دروسي',\n        studentName: 'اسم الطالب',\n        totalLessons: 'إجمالي الدروس',\n        noLessonsFound: 'لا توجد دروس',\n        submitComplaint: 'تقديم شكوى',\n        complaintReason: 'سبب الشكوى',\n        complaintType1: 'الطالب حضر لكن العمولة لم تُحول',\n        complaintType2: 'الطالب لم يحضر نهائياً',\n        complaintDetails: 'تفاصيل الشكوى (اختياري)',\n        complaintDetailsPlaceholder: 'اكتب أي تفاصيل إضافية إذا لزم الأمر...',\n        complaintStatus: 'حالة الشكوى',\n        complaintStatusValues: {\n          pending: 'في الانتظار',\n          resolved: 'تم الحل'\n        },\n        videoUpload: {\n          title: 'رفع فيديو تعريفي',\n          description: 'قم برفع فيديو قصير لتقديم نفسك للطلاب المحتملين. سيتم عرض هذا الفيديو على ملفك الشخصي.',\n          requirements: 'متطلبات الفيديو',\n          formatRequirement: 'الصيغ المسموح بها',\n          sizeRequirement: 'الحجم الأقصى',\n          lengthRequirement: 'المدة المقترحة',\n          minutes: 'دقائق',\n          selectVideo: 'اختر الفيديو',\n          upload: 'رفع الفيديو',\n          uploading: 'جاري الرفع...',\n          success: 'تم رفع الفيديو بنجاح!',\n          videoReady: 'فيديو التعريف جاهز. يمكنك الآن المتابعة إلى نموذج التقديم أو رفع فيديو مختلف.',\n          continue: 'متابعة إلى نموذج التقديم',\n          delete: 'حذف الفيديو',\n          skipForNow: 'تخطي الآن',\n        },\n        editVideoUpload: {\n          title: 'تعديل الفيديو التعريفي',\n          description: 'يمكنك تغيير الفيديو التعريفي الخاص بك هنا. الفيديو الجديد سيحل محل الفيديو الحالي.',\n          requirements: 'متطلبات الفيديو',\n          formatRequirement: 'الصيغة',\n          sizeRequirement: 'الحجم',\n          lengthRequirement: 'المدة',\n          minutes: 'دقائق',\n          selectVideo: 'اختر فيديو جديد',\n          upload: 'رفع الفيديو',\n          uploading: 'جاري الرفع...',\n          success: 'تم رفع الفيديو بنجاح!',\n          videoReady: 'الفيديو جاهز ومحفوظ',\n          saveAndReturn: 'حفظ والعودة',\n          delete: 'حذف الفيديو',\n          invalidVideoFormat: 'صيغة الفيديو غير صالحة. يرجى اختيار ملف MP4 أو WebM أو OGG.',\n          videoTooSmall: 'حجم الفيديو يجب أن يكون على الأقل 1 ميجابايت.',\n          videoTooLarge: 'حجم الفيديو يجب ألا يتجاوز 100 ميجابايت.',\n          noVideoSelected: 'يرجى اختيار ملف فيديو.',\n          videoDeleteError: 'خطأ في حذف الفيديو. يرجى المحاولة مرة أخرى.'\n        },\n        commitment: 'التعهد',\n        commitmentDescription: 'يرجى قراءة والموافقة على التعهد التالي',\n        commitmentProfileDescription: 'عرض وإدارة حالة وتفاصيل التعهد الخاص بك',\n        commitmentRequired: 'يجب الموافقة على التعهد للمتابعة',\n        commitmentStatus: {\n          accepted: 'تم قبول التعهد',\n          pending: 'في انتظار الموافقة على التعهد',\n          rejected: 'تم رفض التعهد'\n        },\n        commitmentTitle: 'تعهد المعلمين',\n        commitmentAccepted: 'تم قبول التعهد',\n        readCommitment: 'قراءة التعهد والموافقة عليه',\n        accept: 'أوافق',\n        reject: 'أرفض',\n        yourEarnings: 'ما ستحصل عليه بعد خصم العمولة:',\n\n        commitmentText: {\n          intro: 'أنا المدرّس/ـة المتقدّم/ـة للتدريس عبر منصة \"علّمني أون لاين بجميع اللغات\"، أُقرّ وأتعهد بما يلي:',\n          point1: 'التحلّي بالصدق والأمانة والإخلاص في أداء رسالتي التعليمية، والالتزام بالأخلاق الإسلامية الفاضلة في تعاملي مع الطلاب والإدارة.',\n          point2: 'الحرص على تعليم الإسلام والعربية وفق منهج أهل السنة والجماعة، ومراعاة ما اتفقت عليه كلمة أهل العلم المعتبرين.',\n          point3: 'الالتزام بمذهب السلف الصالح في العقيدة والمنهج والسلوك، وعدم الخروج عن ما جاء في الكتاب والسنة بفهم الصحابة رضي الله عنهم ومن تبعهم بإحسان.',\n          point4: 'الامتناع عن الإشادة أو الترويج لأي جماعات أو أفكار منحرفة أو متطرفة أو ذات طابع حزبي أو سياسي مخالف لمنهج السلف.',\n          point5: 'عدم استغلال المنصة في نشر أفكار أو توجهات مخالفة لهويتها الشرعية والعلمية، والاقتصار في التدريس على ما هو معتمد ومقرّر من قبل إدارة المنصة.',\n          point6: 'التعاون مع إدارة المنصة بكل شفافية واحترام، وتحديث البيانات وتوقيت العمل والرد على الطلاب في الوقت المحدد.',\n          conclusion: 'وأقرّ بأن مخالفة ما ورد في هذا التعهّد يخول إدارة المنصة اتخاذ ما تراه مناسبًا من إجراءات، بما في ذلك تعليق الحساب أو إنهاء التعاون.'\n        }\n      },\n      admin: {\n        ...resources.ar.translation.admin,\n        withdrawalManagement: {\n          title: 'إدارة طلبات السحب',\n          statusFilter: 'تصفية الحالة',\n          all: 'الكل',\n          date: 'التاريخ',\n          teacher: 'المعلم',\n          amount: 'المبلغ',\n          paypalEmail: 'بريد PayPal الإلكتروني',\n          status: 'الحالة',\n          notes: 'الملاحظات',\n          actions: 'الإجراءات',\n          approve: 'موافقة',\n          reject: 'رفض',\n          approveWithdrawal: 'الموافقة على السحب',\n          rejectWithdrawal: 'رفض السحب',\n          notesOptional: 'ملاحظات (اختيارية)',\n          approveAndProcess: 'موافقة ومعالجة',\n          processingInfo: 'سيتم معالجة السحب عبر PayPal Payouts API.',\n          errorFetching: 'خطأ في جلب طلبات السحب',\n          errorProcessing: 'خطأ في معالجة السحب'\n        },\n        earnings: {\n          title: 'أرباح المنصة',\n          totalCommission: 'إجمالي العمولة',\n          totalLessons: 'إجمالي الدروس',\n          totalRevenue: 'إجمالي الإيرادات',\n          avgCommissionRate: 'متوسط معدل العمولة',\n          startDate: 'تاريخ البداية',\n          endDate: 'تاريخ النهاية',\n          filter: 'تصفية',\n          clearFilter: 'مسح التصفية',\n          date: 'التاريخ',\n          meeting: 'الاجتماع',\n          teacher: 'المعلم',\n          student: 'الطالب',\n          lessonAmount: 'مبلغ الدرس',\n          commissionRate: 'معدل العمولة',\n          commissionAmount: 'مبلغ العمولة',\n          teacherEarnings: 'أرباح المعلم',\n          errorFetching: 'خطأ في جلب بيانات الأرباح'\n        },\n        dashboard: {\n          title: 'لوحة التحكم',\n          welcomeMessage: 'مرحبًا بك في لوحة تحكم المدير',\n          overview: 'إليك نظرة عامة على إحصائيات منصتك والأنشطة الحديثة.',\n          totalTeachers: 'إجمالي المعلمين',\n          pendingApplications: 'الطلبات المعلقة',\n          totalStudents: 'إجمالي الطلاب',\n          totalRevenue: 'إجمالي الإيرادات',\n          totalCourseCategories: 'إجمالي الفئات',\n          totalLanguages: 'إجمالي اللغات',\n          totalBookings: 'إجمالي الحجوزات',\n          bookingStats: 'إحصائيات الحجز',\n          teachersByLanguage: 'المعلمون حسب اللغة',\n          studentsByCountry: 'الطلاب حسب البلد',\n          recentApplications: 'الطلبات الحديثة',\n          recentStudents: 'الطلاب الجدد',\n          viewAll: 'عرض الكل',\n          viewDetails: 'عرض التفاصيل',\n          noApplications: 'لا توجد طلبات حديثة',\n          noStudents: 'لا يوجد طلاب جدد',\n          noStudentsByCountry: 'لا توجد بيانات طلاب حسب البلد',\n          noTeachersByLanguage: 'لا توجد بيانات معلمين حسب اللغة',\n          noBookingStats: 'لا توجد بيانات حجز',\n          completed: 'مكتمل',\n          cancelled: 'ملغي',\n          pending: 'قيد الانتظار',\n          fetchError: 'خطأ في جلب بيانات لوحة التحكم'\n        },\n        meetingSessions: {\n          title: 'تقارير وقت الاجتماعات',\n          overview: 'عرض تفصيلي لأوقات انضمام وخروج المعلمين والطلاب في الاجتماعات',\n          totalMeetings: 'إجمالي الاجتماعات',\n          totalTime: 'إجمالي الوقت',\n          teacherTime: 'وقت المعلمين',\n          studentTime: 'وقت الطلاب',\n          filterResults: 'فلترة النتائج',\n          userType: 'نوع المستخدم',\n          all: 'الكل',\n          teacher: 'معلم',\n          student: 'طالب',\n          reset: 'إعادة تعيين',\n          sessions: 'جلسات الاجتماعات',\n          user: 'المستخدم',\n          type: 'النوع',\n          meeting: 'الاجتماع',\n          joinTime: 'وقت الانضمام',\n          leaveTime: 'وقت الخروج',\n          duration: 'المدة',\n          status: 'الحالة',\n          active: 'نشط',\n          ended: 'منتهي'\n        },\n        meetingIssues: {\n          title: 'مشاكل الاجتماعات'\n        },\n        messages: {\n          title: 'الرسائل',\n          from: 'من',\n          type: 'النوع',\n          subject: 'الموضوع',\n          date: 'التاريخ',\n          status: 'الحالة',\n          pending: 'قيد الانتظار',\n          answered: 'تم الرد',\n          all: 'جميع الرسائل',\n          view: 'عرض',\n          reply: 'رد',\n          delete: 'حذف',\n          search: 'البحث في الرسائل...',\n          noMessages: 'لا توجد رسائل',\n          fetchError: 'خطأ في جلب الرسائل',\n          replyTo: 'رد على',\n          originalMessage: 'الرسالة الأصلية',\n          yourReply: 'ردك',\n          sendReply: 'إرسال الرد',\n          replySent: 'تم إرسال الرد بنجاح',\n          replyError: 'خطأ في إرسال الرد',\n          confirmDelete: 'تأكيد الحذف',\n          deleteWarning: 'هل أنت متأكد من رغبتك في حذف هذه الرسالة؟ لا يمكن التراجع عن هذا الإجراء.',\n          deleteSuccess: 'تم حذف الرسالة بنجاح',\n          deleteError: 'خطأ في حذف الرسالة'\n        },\n        emailTemplates: {\n          title: 'قوالب البريد الإلكتروني',\n          description: 'إدارة قوالب البريد الإلكتروني لإشعارات النظام المختلفة.',\n          instructions: 'قم بتحرير القوالب أدناه لتخصيص رسائل البريد الإلكتروني المرسلة للمستخدمين. يمكنك استخدام HTML والمتغيرات لتخصيص المحتوى.',\n          approvalTemplate: 'قالب الموافقة على الطلب',\n          rejectionTemplate: 'قالب رفض الطلب',\n          save: 'حفظ القالب',\n          saveSuccess: 'تم حفظ القالب بنجاح',\n          saveError: 'خطأ في حفظ القالب',\n          fetchError: 'خطأ في جلب القوالب',\n          restoreDefault: 'استعادة الافتراضي',\n          preview: 'معاينة',\n          enterTemplate: 'أدخل محتوى القالب هنا...',\n          availableVariables: 'المتغيرات المتاحة',\n          variablesHelp: 'سيتم استبدال هذه المتغيرات بالقيم الفعلية عند إرسال البريد الإلكتروني.',\n          testEmail: 'بريد إلكتروني تجريبي',\n          testEmailPlaceholder: 'أدخل عنوان البريد الإلكتروني للاختبار',\n          sendTest: 'إرسال بريد تجريبي',\n          testEmailSuccess: 'تم إرسال البريد التجريبي بنجاح',\n          testEmailError: 'خطأ في إرسال البريد التجريبي',\n          variables: {\n            teacherName: 'الاسم الكامل للمعلم',\n            teacherEmail: 'عنوان البريد الإلكتروني للمعلم',\n            dashboardUrl: 'رابط لوحة تحكم المعلم',\n            rejectionReason: 'سبب رفض الطلب'\n          }\n        },\n        earnings: {\n          title: 'أرباح المنصة',\n          totalCommission: 'إجمالي العمولة',\n          totalLessons: 'إجمالي الدروس',\n          totalRevenue: 'إجمالي الإيرادات',\n          avgCommissionRate: 'متوسط معدل العمولة',\n          startDate: 'تاريخ البداية',\n          endDate: 'تاريخ النهاية',\n          filter: 'تصفية',\n          clearFilter: 'مسح التصفية',\n          date: 'التاريخ',\n          meeting: 'الاجتماع',\n          teacher: 'المعلم',\n          student: 'الطالب',\n          lessonAmount: 'مبلغ الدرس',\n          commissionRate: 'معدل العمولة',\n          commissionAmount: 'مبلغ العمولة',\n          teacherEarnings: 'أرباح المعلم',\n          errorFetching: 'خطأ في جلب بيانات الأرباح'\n        },\n        passwordManagement: {\n          title: 'إدارة كلمات المرور',\n          description: 'إدارة طلبات إعادة تعيين كلمة المرور وكلمات مرور المستخدمين',\n          searchPlaceholder: 'البحث بالاسم أو البريد الإلكتروني',\n          user: 'المستخدم',\n          email: 'البريد الإلكتروني',\n          role: 'الدور',\n          requestDate: 'تاريخ الطلب',\n          status: 'الحالة',\n          actions: 'الإجراءات',\n          resetPassword: 'إعادة تعيين كلمة المرور',\n          resetPasswordFor: 'إعادة تعيين كلمة المرور لـ {name}',\n          newPassword: 'كلمة المرور الجديدة',\n          generatePassword: 'توليد كلمة مرور',\n          resetSuccess: 'تم إعادة تعيين كلمة المرور بنجاح لـ {email}',\n          resetError: 'خطأ في إعادة تعيين كلمة المرور',\n          noRequests: 'لا توجد طلبات إعادة تعيين كلمة المرور',\n          statusPending: 'قيد الانتظار',\n          statusCompleted: 'مكتمل',\n          sendTestEmail: 'إرسال بريد إلكتروني تجريبي',\n          testEmailAddress: 'عنوان البريد الإلكتروني التجريبي',\n          testEmailDescription: 'إرسال بريد إلكتروني تجريبي لإعادة تعيين كلمة المرور للتحقق من قالب البريد الإلكتروني والتسليم',\n          sendEmail: 'إرسال بريد إلكتروني',\n          testEmailSent: 'تم إرسال البريد الإلكتروني التجريبي بنجاح إلى {email}',\n          testEmailError: 'خطأ في إرسال البريد الإلكتروني التجريبي'\n        }\n      }\n    }\n  }\n};\n\ni18n\n  .use(initReactI18next)\n  .init({\n    resources: mergedResources,\n    lng: localStorage.getItem('language') || 'en',\n    fallbackLng: 'en',\n    debug: false,\n    interpolation: {\n      escapeValue: false\n    },\n    react: {\n      useSuspense: false\n    },\n    // Force reload translations\n    load: 'languageOnly',\n    cleanCode: true\n  });\n\nexport default i18n;\n"], "mappings": "AAAA,MAAO,CAAAA,IAAI,KAAM,SAAS,CAC1B,OAASC,gBAAgB,KAAQ,eAAe,CAChD,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAC1D,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAE1D,KAAM,CAAAC,SAAS,CAAG,CAChBC,EAAE,CAAE,CACFC,WAAW,CAAE,CACXC,OAAO,CAAE,eAAe,CACxBC,OAAO,CAAE,UAAU,CACnBC,MAAM,CAAE,CACNC,KAAK,CAAE,QAAQ,CACfC,OAAO,CAAE,SAAS,CAClBC,cAAc,CAAE,iBAAiB,CACjCC,kBAAkB,CAAE,qBAAqB,CACzCC,eAAe,CAAE,kBAAkB,CACnCC,cAAc,CAAE,uBAAuB,CACvCC,yBAAyB,CAAE,gDAAgD,CAC3EC,IAAI,CAAE,MAAM,CACZC,WAAW,CAAE,aAAa,CAC1BC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,WAAW,CACtBC,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,WAAW,CACtBC,OAAO,CAAE,SAAS,CAClBC,UAAU,CAAE,yBAAyB,CACrCC,UAAU,CAAE,yBAAyB,CACrCC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,WAAW,CACrBC,aAAa,CAAE,sBAAsB,CACrCC,GAAG,CAAE,KAAK,CACVC,cAAc,CAAE,mDAAmD,CACnEC,sBAAsB,CAAE,6CAA6C,CACrEC,aAAa,CAAE,gBAAgB,CAC/BC,aAAa,CAAE,gBAAgB,CAC/BC,aAAa,CAAE,gBACjB,CAAC,CACDC,UAAU,CAAE,CACV/B,KAAK,CAAE,YAAY,CACnBgC,gBAAgB,CAAE,mBAAmB,CACrCC,iBAAiB,CAAE,oBAAoB,CACvCC,iBAAiB,CAAE,oBAAoB,CACvCC,aAAa,CAAE,8BAA8B,CAC7C5B,IAAI,CAAE,MAAM,CACZE,MAAM,CAAE,QAAQ,CAChB2B,WAAW,CAAE,cAAc,CAC3B1B,MAAM,CAAE,QAAQ,CAChB2B,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChB1B,OAAO,CAAE,SAAS,CAClB2B,UAAU,CAAE,YAAY,CACxB1B,SAAS,CAAE,WAAW,CACtBC,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,WAAW,CACtByB,aAAa,CAAE,iCAAiC,CAChDC,aAAa,CAAE,0CAA0C,CACzDC,mBAAmB,CAAE,sBAAsB,CAC3CC,gBAAgB,CAAE,2CAA2C,CAC7DC,gBAAgB,CAAE,2CAA2C,CAC7DC,eAAe,CAAE,qCAAqC,CACtDC,eAAe,CAAE,qCAAqC,CACtDC,wBAAwB,CAAE,mCAAmC,CAC7DC,eAAe,CAAE,8DAA8D,CAC/EC,cAAc,CAAE,yCAAyC,CACzDC,iBAAiB,CAAE,oBAAoB,CACvCC,aAAa,CAAE,yBAAyB,CACxCC,eAAe,CAAE,gEAAgE,CACjFC,OAAO,CAAE,mBAAmB,CAC5BC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,oCAAoC,CAC9CC,iBAAiB,CAAE,sBAAsB,CACzCC,gBAAgB,CAAE,2CAA2C,CAC7DC,mBAAmB,CAAE,uEACvB,CAAC,CACDC,KAAK,CAAE,CACLC,oBAAoB,CAAE,CACpB7D,KAAK,CAAE,uBAAuB,CAC9B8D,YAAY,CAAE,eAAe,CAC7BC,GAAG,CAAE,KAAK,CACVxD,IAAI,CAAE,MAAM,CACZc,OAAO,CAAE,SAAS,CAClBZ,MAAM,CAAE,QAAQ,CAChB2B,WAAW,CAAE,cAAc,CAC3B1B,MAAM,CAAE,QAAQ,CAChBsD,KAAK,CAAE,OAAO,CACd3B,OAAO,CAAE,SAAS,CAClB4B,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBC,iBAAiB,CAAE,oBAAoB,CACvCC,gBAAgB,CAAE,mBAAmB,CACrCC,aAAa,CAAE,kBAAkB,CACjCC,iBAAiB,CAAE,mBAAmB,CACtCC,cAAc,CAAE,0DAA0D,CAC1EC,aAAa,CAAE,oCAAoC,CACnDC,eAAe,CAAE,6BACnB,CACF,CAAC,CACDC,SAAS,CAAE,CACT1E,KAAK,CAAE,YAAY,CACnB2E,IAAI,CAAE,cAAc,CACpBC,YAAY,CAAE,UAAU,CACxBC,WAAW,CAAE,SAAS,CACtBC,cAAc,CAAE,YAAY,CAC5BC,WAAW,CAAE,eAAe,CAC5BC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,cAAc,CACpBC,WAAW,CAAE,wEAAwE,CACrF3C,aAAa,CAAE,iCAAiC,CAChD4C,SAAS,CAAE,gDAAgD,CAC3DC,iBAAiB,CAAE,8BACrB,CAAC,CACDC,UAAU,CAAE,CACVvF,KAAK,CAAE,aAAa,CACpBiF,OAAO,CAAE,SAAS,CAClBN,IAAI,CAAE,MAAM,CACZpE,IAAI,CAAE,MAAM,CACZG,MAAM,CAAE,QAAQ,CAChBG,OAAO,CAAE,SAAS,CAClB2E,QAAQ,CAAE,UAAU,CACpBC,UAAU,CAAE,mBAAmB,CAC/BC,UAAU,CAAE,yBAAyB,CACrCC,WAAW,CAAE,cAAc,CAC3BC,UAAU,CAAE,aAAa,CACzBC,aAAa,CAAE,8BACjB,CAAC,CACDC,OAAO,CAAE,CACP9F,KAAK,CAAE,eAAe,CACtB+F,cAAc,CAAE,oBAAoB,CACpCC,cAAc,CAAE,kBAAkB,CAClCC,YAAY,CAAE,sBAAsB,CACpCC,gBAAgB,CAAE,yIAAyI,CAC3JC,cAAc,CAAE,oBAAoB,CACpCC,gBAAgB,CAAE,iCAAiC,CACnD/E,OAAO,CAAE,SAAS,CAClBgF,GAAG,CAAE,KAAK,CACV9F,IAAI,CAAE,MAAM,CACZ+F,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,OAAO,CACdC,cAAc,CAAE,iBAAiB,CACjCC,aAAa,CAAE,eAAe,CAC9BC,mBAAmB,CAAE,qBAAqB,CAC1CC,qBAAqB,CAAE,kGAAkG,CACzHC,aAAa,CAAE,iBAAiB,CAChCC,WAAW,CAAE,cAAc,CAC3BC,MAAM,CAAE,SAAS,CACjBC,YAAY,CAAE,eAAe,CAC7BC,QAAQ,CAAE,WAAW,CACrBC,cAAc,CAAE,iBAAiB,CACjCC,cAAc,CAAE,kBAAkB,CAClCC,SAAS,CAAE,YAAY,CACvBC,aAAa,CAAE,mCAAmC,CAClDzE,mBAAmB,CAAE,wDAAwD,CAC7E0E,cAAc,CAAE,wBAAwB,CACxCC,QAAQ,CAAE,UAAU,CACpBC,UAAU,CAAE,oCAAoC,CAChDC,UAAU,CAAE,oCAAoC,CAChDC,cAAc,CAAE,iBAAiB,CACjCC,iBAAiB,CAAE,sFAAsF,CACzGC,cAAc,CAAE,kBAClB,CAAC,CACDC,OAAO,CAAE,CACP5H,KAAK,CAAE,SAAS,CAChB6H,WAAW,CAAE,gBAAgB,CAC7BC,SAAS,CAAE,YAAY,CACvBC,cAAc,CAAE,iBAAiB,CACjCC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClB1F,MAAM,CAAE,eAAe,CACvB2F,MAAM,CAAE,eAAe,CACvBC,MAAM,CAAE,eAAe,CACvBC,aAAa,CAAE,8CAA8C,CAC7DC,aAAa,CAAE,kBAAkB,CACjCC,UAAU,CAAE,uIAAuI,CACnJC,kBAAkB,CAAE,0FAA0F,CAC9GC,mBAAmB,CAAE,8GAA8G,CACnIC,wBAAwB,CAAE,qCAAqC,CAC/DC,SAAS,CAAE,gBAAgB,CAC3BC,UAAU,CAAE,aAAa,CACzBC,UAAU,CAAE,aAAa,CACzBC,aAAa,CAAE,+BAA+B,CAC9CC,mBAAmB,CAAE,6BAA6B,CAClDC,mBAAmB,CAAE,6BAA6B,CAClDC,WAAW,CAAE,yBAAyB,CACtCC,cAAc,CAAE,yBAAyB,CACzCC,kBAAkB,CAAE,4CAA4C,CAChEC,aAAa,CAAE,gBAAgB,CAC/BC,YAAY,CAAE,mBAAmB,CACjCC,SAAS,CAAE,UAAU,CACrBC,SAAS,CAAE,qBAAqB,CAChCC,QAAQ,CAAE,WAAW,CACrBC,EAAE,CAAE,IAAI,CACRC,KAAK,CAAE,OAAO,CACdC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,UAAU,CAClBC,YAAY,CAAE,eAAe,CAC7BC,UAAU,CAAE,aAAa,CACzBC,cAAc,CAAE,iBAAiB,CACjCC,eAAe,CAAE,kBAAkB,CACnCC,OAAO,CAAE,UAAU,CACnBC,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,YAAY,CAAE,eAAe,CAC7BC,WAAW,CAAE,cAAc,CAC3BC,SAAS,CAAE,qBAAqB,CAChCC,mBAAmB,CAAE,sCAAsC,CAC3DC,YAAY,CAAE,eAAe,CAC7BC,aAAa,CAAE,iBAAiB,CAChCC,SAAS,CAAE,YAAY,CACvBC,WAAW,CAAE,cAAc,CAC3BC,gBAAgB,CAAE,oCAAoC,CACtDC,SAAS,CAAE,YAAY,CACvBC,WAAW,CAAE,cAAc,CAC3BC,YAAY,CAAE,yBAAyB,CACvCC,kBAAkB,CAAE,4BAA4B,CAChDC,kBAAkB,CAAE,4BAA4B,CAChDC,UAAU,CAAE,qBAAqB,CACjCC,kBAAkB,CAAE,6CAA6C,CACjEC,aAAa,CAAE,sBAAsB,CACrCC,OAAO,CAAE,YACX,CAAC,CACDC,KAAK,CAAE,CACLvL,KAAK,CAAE,UAAU,CACjBwL,KAAK,CAAE,2QAA2Q,CAClRC,OAAO,CAAE,oMAAoM,CAC7MC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,CACRC,cAAc,CAAE,6EAA6E,CAC7FC,oBAAoB,CAAE,sEAAsE,CAC5FC,gBAAgB,CAAE,gHAAgH,CAClIC,eAAe,CAAE,+HAA+H,CAChJC,cAAc,CAAE,sJAClB,CAAC,CACDC,UAAU,CAAE,aAAa,CACzBC,WAAW,CAAE,mPAAmP,CAChQxH,SAAS,CAAE,YAAY,CACvByH,WAAW,CAAE,6DAA6D,CAC1EC,KAAK,CAAE,2BACT,CAAC,CACDC,GAAG,CAAE,CACHC,IAAI,CAAE,eAAe,CACrBC,SAAS,CAAE,oBAAoB,CAC/BC,OAAO,CAAE,yCACX,CAAC,CACDC,KAAK,CAAE,CACLH,IAAI,CAAE,eACR,CAAC,CACDI,MAAM,CAAE,CACNC,cAAc,CAAE,iBAAiB,CACjCC,OAAO,CAAE,YAAY,CACrBC,QAAQ,CAAE,UAAU,CACpBC,OAAO,CAAE,SAAS,CAClBxK,MAAM,CAAE,QAAQ,CAChByK,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,UAAU,CACpBC,OAAO,CAAE,SAAS,CAClB/E,MAAM,CAAE,QAAQ,CAChBgF,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,OAAO,CACdC,IAAI,CAAE,MAAM,CACZC,aAAa,CAAE,eAAe,CAC9BC,MAAM,CAAE,WAAW,CACnBlB,KAAK,CAAE,eAAe,CACtBmB,WAAW,CAAE,cAAc,CAC3BC,MAAM,CAAE,QAAQ,CAChBC,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,WAAW,CACrBrB,IAAI,CAAE,MAAM,CACZsB,eAAe,CAAE,kBAAkB,CACnCC,QAAQ,CAAE,WAAW,CACrBC,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QAAQ,CAChB1L,MAAM,CAAE,QAAQ,CAChB2L,MAAM,CAAE,SAAS,CACjB7L,OAAO,CAAE,SAAS,CAClB8L,IAAI,CAAE,MAAM,CACZhG,MAAM,CAAE,QAAQ,CAChBiG,IAAI,CAAE,MAAM,CACZC,OAAO,CAAE,SAAS,CAClB/M,WAAW,CAAE,eAAe,CAC5BgN,MAAM,CAAE,CACN/B,SAAS,CAAE,oBAAoB,CAC/BC,OAAO,CAAE,yCACX,CAAC,CACD+B,QAAQ,CAAE,KAAK,CACfF,OAAO,CAAE,SACX,CAAC,CACDvB,OAAO,CAAE,CACP9M,KAAK,CAAE,SAAS,CAChB2N,QAAQ,CAAE,WAAW,CACrBQ,IAAI,CAAE,cAAc,CACpBf,IAAI,CAAE,cAAc,CACpB9K,MAAM,CAAE,QAAQ,CAChB4K,OAAO,CAAE,8BAA8B,CACvCC,KAAK,CAAE,wBAAwB,CAC/BqB,aAAa,CAAE,8BAA8B,CAC7CC,qBAAqB,CAAE,+BAA+B,CACtDC,yBAAyB,CAAE,4CAA4C,CACvEC,QAAQ,CAAE,0BAA0B,CACpCC,gBAAgB,CAAE,oBAAoB,CACtCtB,MAAM,CAAE,WAAW,CACnBuB,MAAM,CAAE,CACN3G,MAAM,CAAE,0BAA0B,CAClC4G,cAAc,CAAE,2BAA2B,CAC3CC,gBAAgB,CAAE,wBAAwB,CAC1CC,eAAe,CAAE,+BAA+B,CAChDtJ,UAAU,CAAE,8BAA8B,CAC1CuJ,YAAY,CAAE,kCAAkC,CAChDC,aAAa,CAAE,+BAA+B,CAC9CC,WAAW,CAAE,2BAA2B,CACxCC,YAAY,CAAE,+BAA+B,CAC7CC,YAAY,CAAE,mCAChB,CAAC,CACDC,SAAS,CAAE,mBAAmB,CAC9BC,WAAW,CAAE,qBAAqB,CAClCZ,QAAQ,CAAE,WAAW,CACrBa,cAAc,CAAE,iBAAiB,CACjCpD,KAAK,CAAE,OAAO,CACd2B,MAAM,CAAE,QAAQ,CAChBiB,eAAe,CAAE,kBAAkB,CACnCS,WAAW,CAAE,cAAc,CAC3B7B,eAAe,CAAE,kBAAkB,CACnC8B,gBAAgB,CAAE,2BAA2B,CAC7CC,wBAAwB,CAAE,4BAA4B,CACtDC,WAAW,CAAE,cAAc,CAC3BC,aAAa,CAAE,gBAAgB,CAC/BC,kBAAkB,CAAE,gBAAgB,CACpCC,oBAAoB,CAAE,6EAA6E,CACnGC,iBAAiB,CAAE,yDAAyD,CAC5EC,cAAc,CAAE,wBAAwB,CACxCC,gBAAgB,CAAE,sBAAsB,CACxCC,qBAAqB,CAAE,yEAAyE,CAChGC,UAAU,CAAE,mBAAmB,CAC/BhI,aAAa,CAAE,kBAAkB,CACjCiI,cAAc,CAAE,sCAAsC,CACtDC,aAAa,CAAE,oCAAoC,CACnDjB,YAAY,CAAE,iBAAiB,CAC/BkB,eAAe,CAAE,yCAAyC,CAC1DC,kBAAkB,CAAE,6BAA6B,CACjDC,oBAAoB,CAAE,oHAAoH,CAC1IC,kBAAkB,CAAE,yBAAyB,CAC7CC,YAAY,CAAE,CACZ9P,OAAO,CAAE,wCAAwC,CACjD+P,QAAQ,CAAE,yBAAyB,CACnCC,QAAQ,CAAE,iCAAiC,CAC3CC,WAAW,CAAE,cAAc,CAC3BC,UAAU,CAAE,aAAa,CACzBC,UAAU,CAAE,aAAa,CACzBC,WAAW,CAAE,kGACf,CAAC,CACD5P,OAAO,CAAE,CACP6P,WAAW,CAAE,CACXlR,KAAK,CAAE,2BAA2B,CAClCQ,WAAW,CAAE,4GAA4G,CACzH2Q,YAAY,CAAE,oBAAoB,CAClCC,iBAAiB,CAAE,iBAAiB,CACpCC,eAAe,CAAE,cAAc,CAC/BC,iBAAiB,CAAE,oBAAoB,CACvCC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,WAAW,CAAE,cAAc,CAC3BC,MAAM,CAAE,cAAc,CACtBC,SAAS,CAAE,cAAc,CACzBzE,OAAO,CAAE,8BAA8B,CACvC0E,UAAU,CAAE,yDAAyD,CACrE5E,QAAQ,CAAE,yBAAyB,CACnC7E,MAAM,CAAE,cAAc,CACtB0J,UAAU,CAAE,cAAc,CAC1BC,kBAAkB,CAAE,6DAA6D,CACjFC,aAAa,CAAE,kCAAkC,CACjDC,aAAa,CAAE,mCAAmC,CAClDC,eAAe,CAAE,6BAA6B,CAC9CC,gBAAgB,CAAE,yCACpB,CAAC,CACDC,cAAc,CAAE,kBAAkB,CAClCP,UAAU,CAAE,wDAAwD,CACpEQ,WAAW,CAAE,cAAc,CAC3BC,yBAAyB,CAAE,6HAA6H,CACxJC,oCAAoC,CAAE,sIAAsI,CAC5KC,gCAAgC,CAAE,4FAA4F,CAC9HC,kBAAkB,CAAE,sBAAsB,CAC1CC,6BAA6B,CAAE,sEAAsE,CACrGC,kBAAkB,CAAE,YAAY,CAChCC,aAAa,CAAE,iCAAiC,CAChDC,oBAAoB,CAAE,wBAAwB,CAC9CC,iBAAiB,CAAE,oDAAoD,CACvEC,SAAS,CAAE,YAAY,CACvBC,WAAW,CAAE,cAAc,CAC3BC,YAAY,CAAE,eAAe,CAC7BC,gBAAgB,CAAE,mBAAmB,CACrCC,gBAAgB,CAAE,mBAAmB,CACrCC,gBAAgB,CAAE,mBAAmB,CACrCC,cAAc,CAAE,kBAAkB,CAClCC,gBAAgB,CAAE,sDAAsD,CACxEC,iBAAiB,CAAE,uDAAuD,CAC1EC,iBAAiB,CAAE,4DAA4D,CAC/ElG,aAAa,CAAE,eAAe,CAC9BmG,SAAS,CAAE,YAAY,CACvBC,gBAAgB,CAAE,kEAAkE,CACpFC,SAAS,CAAE,YAAY,CACvBC,QAAQ,CAAE,WAAW,CACrBC,QAAQ,CAAE,WAAW,CACrBC,SAAS,CAAE,WAAW,CACtBC,WAAW,CAAE,aAAa,CAC1BC,aAAa,CAAE,+BAA+B,CAC9CC,YAAY,CAAE,8BAA8B,CAC5CC,MAAM,CAAE,QAAQ,CAChBC,UAAU,CAAE,aAAa,CACzB7R,OAAO,CAAE,SAAS,CAElB8R,oBAAoB,CAAE,wBAAwB,CAC9CrH,OAAO,CAAE,CACP9M,KAAK,CAAE,iBAAiB,CACxBoU,YAAY,CAAE,sBAAsB,CACpCC,YAAY,CAAE,sBAAsB,CACpCC,KAAK,CAAE,cAAc,CACrBC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,oBAAoB,CAC/BC,cAAc,CAAE,iBAAiB,CACjCC,iBAAiB,CAAE,CACjB1U,KAAK,CAAE,oBAAoB,CAC3B2U,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,MAAM,CACZC,OAAO,CAAE,SAAS,CAClBC,UAAU,CAAE,YAAY,CACxBC,KAAK,CAAE,OAAO,CACdC,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,OAAO,CACdC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,MAAM,CACZC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,2BAA2B,CACnCC,WAAW,CAAE,8BACf,CAAC,CACDC,WAAW,CAAE,cAAc,CAC3BC,cAAc,CAAE,gBAAgB,CAChCC,kBAAkB,CAAE,qBAAqB,CACzCC,cAAc,CAAE,iBAAiB,CACjCvQ,cAAc,CAAE,kBAAkB,CAClCwQ,QAAQ,CAAE,WAAW,CACrBC,aAAa,CAAE,gBAAgB,CAC/BlI,QAAQ,CAAE,CACRmI,GAAG,CAAE,KAAK,CACVC,GAAG,CAAE,KAAK,CACVC,GAAG,CAAE,KACP,CAAC,CACDC,UAAU,CAAE,CACVC,QAAQ,CAAE,sBAAsB,CAChCC,YAAY,CAAE,0BAA0B,CACxCC,QAAQ,CAAE,uBAAuB,CACjCC,MAAM,CAAE,oBACV,CACF,CACF,CAAC,CACDC,OAAO,CAAE,CACPlJ,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QACV,CACF,CAAC,CACDkJ,GAAG,CAAE,CACHC,IAAI,CAAE,MAAM,CACZC,SAAS,CAAE,WAAW,CACtBC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,UAAU,CACpBC,YAAY,CAAE,eAAe,CAC7BC,UAAU,CAAE,YAAY,CACxBC,SAAS,CAAE,WAAW,CACtBC,YAAY,CAAE,cAAc,CAC5BC,cAAc,CAAE,iBAAiB,CACjC/T,oBAAoB,CAAE,uBAAuB,CAC7CgU,WAAW,CAAE,cAAc,CAC3BC,UAAU,CAAE,aAAa,CACzBC,QAAQ,CAAE,UAAU,CACpBC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,QAAQ,CAChB3K,MAAM,CAAE,QAAQ,CAChB4K,QAAQ,CAAE,UAAU,CACpBC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBC,cAAc,CAAE,iBAAiB,CACjCC,gBAAgB,CAAE,mBAAmB,CACrCC,gBAAgB,CAAE,mBACpB,CAAC,CACDjB,YAAY,CAAE,CACZxX,KAAK,CAAE,eAAe,CACtB0Y,YAAY,CAAE,eAAe,CAC7BC,eAAe,CAAE,kBAAkB,CACnCC,eAAe,CAAE,kBAAkB,CACnCC,WAAW,CAAE,cAAc,CAC3BrL,MAAM,CAAE,QAAQ,CAChB7I,IAAI,CAAE,MAAM,CACZZ,GAAG,CAAE,KAAK,CACVwT,QAAQ,CAAE,UAAU,CACpBD,QAAQ,CAAE,UAAU,CACpBwB,WAAW,CAAE,cAAc,CAC3BC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,MAAM,CACZC,YAAY,CAAE,eAAe,CAC7BC,cAAc,CAAE,iBAAiB,CACjCC,SAAS,CAAE,YAAY,CACvB/W,OAAO,CAAE,SAAS,CAClBgX,WAAW,CAAE,cAAc,CAC3BC,WAAW,CAAE,cAAc,CAC3BC,eAAe,CAAE,kBAAkB,CACnCnY,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBmY,UAAU,CAAE,aAAa,CACzBC,YAAY,CAAE,eAAe,CAC7BC,WAAW,CAAE,cAAc,CAC3BC,mBAAmB,CAAE,cAAc,CACnCC,qBAAqB,CAAE,iDAAiD,CACxEC,kBAAkB,CAAE,6DAA6D,CACjFvX,MAAM,CAAE,QAAQ,CAChBwX,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,cAAc,CACzBC,oBAAoB,CAAE,kBAAkB,CACxCC,sBAAsB,CAAE,wCAAwC,CAChEC,sBAAsB,CAAE,4DAA4D,CACpFC,mBAAmB,CAAE,iEAAiE,CACtFC,qBAAqB,CAAE,kBAAkB,CACzCC,QAAQ,CAAE,aAAa,CACvB/Y,WAAW,CAAE,gBAAgB,CAC7BgZ,aAAa,CAAE,wBAAwB,CACvCC,YAAY,CAAE,sBAAsB,CACpCC,iBAAiB,CAAE,oBACrB,CAAC,CACD9N,MAAM,CAAE,CACN+N,QAAQ,CAAE,kBAAkB,CAC5BlL,WAAW,CAAE,qBAAqB,CAClCmL,WAAW,CAAE,qBAAqB,CAClC3M,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QAAQ,CAChB0M,QAAQ,CAAE,WAAW,CACrB3H,YAAY,CAAE,eAAe,CAC7B4H,aAAa,CAAE,gBAAgB,CAC/B5S,MAAM,CAAE,QAAQ,CAChB6S,QAAQ,CAAE,WAAW,CACrBC,aAAa,CAAE,gBAAgB,CAC/BvM,QAAQ,CAAE,KAAK,CACfwM,aAAa,CAAE,gBAAgB,CAC/B9H,gBAAgB,CAAE,mBAAmB,CACrC+H,UAAU,CAAE,aAAa,CACzBxE,QAAQ,CAAE,UAAU,CACpBtI,MAAM,CAAE,SAAS,CACjBT,KAAK,CAAE,OACT,CAAC,CACDa,MAAM,CAAE,CACN2M,YAAY,CAAE,eAAe,CAC7BC,UAAU,CAAE,yCAAyC,CACrDC,QAAQ,CAAE,WAAW,CACrBC,QAAQ,CAAE,UAAU,CACpBC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,WAAW,CACtBC,UAAU,CAAE,aAAa,CACzBhQ,KAAK,CAAE,UAAU,CACjBiQ,OAAO,CAAE,YAAY,CACrBC,OAAO,CAAE,gBAAgB,CACzBC,KAAK,CAAE,kBAAkB,CACzBC,GAAG,CAAE,KAAK,CACVC,OAAO,CAAE,SAAS,CAClBrP,SAAS,CAAE,sBAAsB,CACjCC,OAAO,CAAE,yCACX,CAAC,CACDqP,IAAI,CAAE,CACJ7b,KAAK,CAAE,yBAAyB,CAChC8b,QAAQ,CAAE,wEAAwE,CAClFC,aAAa,CAAE,gBAAgB,CAC/BC,aAAa,CAAE,kBAAkB,CACjCC,QAAQ,CAAE,gBACZ,CAAC,CACD7E,IAAI,CAAE,CACJ8E,QAAQ,CAAE,aAAa,CACvBC,gBAAgB,CAAE,qFAAqF,CACvG7E,QAAQ,CAAE,UAAU,CACpB8E,cAAc,CAAE,iBAAiB,CACjCC,kBAAkB,CAAE,mDAAmD,CACvEC,cAAc,CAAE,iBAAiB,CACjCC,OAAO,CAAE,SAAS,CAClBC,WAAW,CAAE,8DAA8D,CAC3EC,YAAY,CAAE,gCAAgC,CAC9CC,WAAW,CAAE,eAAe,CAC5BC,mBAAmB,CAAE,mGAAmG,CACxHC,YAAY,CAAE,mBAAmB,CACjCC,oBAAoB,CAAE,kFAAkF,CACxGC,QAAQ,CAAE,CACRC,OAAO,CAAE,mBAAmB,CAC5BC,WAAW,CAAE,gDAAgD,CAC7DC,QAAQ,CAAE,mBAAmB,CAC7BC,YAAY,CAAE,sCAAsC,CACpDC,WAAW,CAAE,sBAAsB,CACnCC,eAAe,CAAE,0CAA0C,CAC3DC,SAAS,CAAE,mBAAmB,CAC9BC,aAAa,CAAE,sCACjB,CAAC,CACDC,YAAY,CAAE,uBAAuB,CACrCC,oBAAoB,CAAE,iFAAiF,CACvGC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YACb,CAAC,CACDH,YAAY,CAAE,CACZnc,OAAO,CAAE,SAAS,CAClBqc,SAAS,CAAE,YACb,CAAC,CACDjQ,MAAM,CAAE,CACNqK,WAAW,CAAE,gBAAgB,CAC7B8F,OAAO,CAAE,SAAS,CAClB1Y,OAAO,CAAE,SAAS,CAClB2Y,WAAW,CAAE,cAAc,CAC3BxF,QAAQ,CAAE,UAAU,CACpByF,YAAY,CAAE,eAAe,CAC7BC,UAAU,CAAE,aAAa,CACzB9V,MAAM,CAAE,QAAQ,CAChB+V,SAAS,CAAE,YAAY,CACvBC,QAAQ,CAAE,SAAS,CACnBC,eAAe,CAAE,mBAAmB,CACpCC,iBAAiB,CAAE,+BAA+B,CAClDC,UAAU,CAAE,eAAe,CAC3BC,OAAO,CAAE,OAAO,CAChBlC,QAAQ,CAAE,UAAU,CACpBxE,SAAS,CAAE,WAAW,CACtB2G,YAAY,CAAE,QAAQ,CACtBC,YAAY,CAAE,eAAe,CAC7BL,eAAe,CAAE,0CAA0C,CAC3DvJ,iBAAiB,CAAE,oBAAoB,CACvC6J,eAAe,CAAE,kBAAkB,CACnCC,UAAU,CAAE,aAAa,CACzBC,eAAe,CAAE,mBAAmB,CACpCC,WAAW,CAAE,cAAc,CAC3BC,cAAc,CAAE,gBAClB,CAAC,CACD7Y,OAAO,CAAE,CACPC,cAAc,CAAE,oBAAoB,CACpCC,cAAc,CAAE,kBAAkB,CAClCC,YAAY,CAAE,sBAAsB,CACpCC,gBAAgB,CAAE,0IAA0I,CAC5JC,cAAc,CAAE,oBAAoB,CACpCyY,iBAAiB,CAAE,uCAAuC,CAC1DC,WAAW,CAAE,yBAAyB,CACtCC,iBAAiB,CAAE,qBAAqB,CACxCC,YAAY,CAAE,gBAAgB,CAC9BxX,UAAU,CAAE,sBAAsB,CAClCC,UAAU,CAAE,sBAAsB,CAClCH,cAAc,CAAE,wBAAwB,CACxC2X,mBAAmB,CAAE,uBAAuB,CAC5CC,WAAW,CAAE,cAAc,CAC3BC,iBAAiB,CAAE,8BAA8B,CACjDC,iBAAiB,CAAE,8BAA8B,CACjDC,cAAc,CAAE,iCAAiC,CACjDC,wBAAwB,CAAE,uCAAuC,CACjEC,eAAe,CAAE,+BAA+B,CAChDC,aAAa,CAAE,gBAAgB,CAC/BjY,QAAQ,CAAE,UAAU,CACpBlB,gBAAgB,CAAE,iCAAiC,CACnDI,cAAc,CAAE,iBAAiB,CACjCnF,OAAO,CAAE,SAAS,CAClBgF,GAAG,CAAE,KAAK,CACV9F,IAAI,CAAE,MAAM,CACZ+F,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,OAAO,CACdE,aAAa,CAAE,eAAe,CAC9BC,mBAAmB,CAAE,qBAAqB,CAC1CC,qBAAqB,CAAE,oJAAoJ,CAC3K6Y,mBAAmB,CAAE,6EAA6E,CAClG5Y,aAAa,CAAE,iBAAiB,CAChCM,cAAc,CAAE,kBAAkB,CAClCC,SAAS,CAAE,YAAY,CACvBC,aAAa,CAAE,mCAAmC,CAClDzE,mBAAmB,CAAE,uEAAuE,CAC5F4L,QAAQ,CAAE,KAAK,CACf1H,WAAW,CAAE,cAAc,CAC3BC,MAAM,CAAE,SAAS,CACjBC,YAAY,CAAE,eAAe,CAC7BC,QAAQ,CAAE,WAAW,CACrBC,cAAc,CAAE,iBAAiB,CACjCwY,eAAe,CAAE,mBAAmB,CACpCC,SAAS,CAAE,YAAY,CACvBC,OAAO,CAAE,UAAU,CACnBC,cAAc,CAAE,iBAAiB,CACjCC,IAAI,CAAE,MAAM,CACZC,EAAE,CAAE,IAAI,CACRC,aAAa,CAAE,gBAAgB,CAC/B1Y,cAAc,CAAE,iBAClB,CAAC,CACD2Y,QAAQ,CAAE,CACRhgB,KAAK,CAAE,aAAa,CACpBigB,WAAW,CAAE,uBAAuB,CACpCzf,WAAW,CAAE,uDAAuD,CACpE0f,iBAAiB,CAAE,wDAAwD,CAC3EC,cAAc,CAAE,iBAAiB,CACjCC,UAAU,CAAE,2BAA2B,CACvC1a,UAAU,CAAE,kDAAkD,CAC9DrE,OAAO,CAAE,SAAS,CAClBd,IAAI,CAAE,MAAM,CACZ+F,IAAI,CAAE,MAAM,CACZ+Z,SAAS,CAAE,YAAY,CACvB/Y,QAAQ,CAAE,UAAU,CACpBkK,OAAO,CAAE,SAAS,CAClB9Q,MAAM,CAAE,QAAQ,CAChB4f,YAAY,CAAE,CACZC,SAAS,CAAE,WAAW,CACtBzf,SAAS,CAAE,WAAW,CACtBE,SAAS,CAAE,WAAW,CACtBwf,cAAc,CAAE,gBAAgB,CAChCC,OAAO,CAAE,SACX,CAAC,CACDla,KAAK,CAAE,OAAO,CACdjE,MAAM,CAAE,QAAQ,CAChBoe,aAAa,CAAE,sBAAsB,CACrCC,aAAa,CAAE,2FAA2F,CAC1GC,kBAAkB,CAAE,gCAAgC,CACpDC,6BAA6B,CAAE,wDAAwD,CACvFC,mBAAmB,CAAE,qBAAqB,CAC1CC,UAAU,CAAE,eAAe,CAC3BC,aAAa,CAAE,mEAAmE,CAClFC,WAAW,CAAE,6CAA6C,CAC1DlB,aAAa,CAAE,qBAAqB,CACpCmB,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,WAAW,CACrBC,SAAS,CAAE,YAAY,CACvBC,cAAc,CAAE,yBAAyB,CACzCC,gBAAgB,CAAE,2CAA2C,CAC7DC,aAAa,CAAE,2DAA2D,CAC1EC,iBAAiB,CAAE,0BAA0B,CAC7CC,eAAe,CAAE,oBAAoB,CACrCC,KAAK,CAAE,OAAO,CACdC,gBAAgB,CAAE,qBAAqB,CACvCC,gBAAgB,CAAE,mBAAmB,CACrCC,kBAAkB,CAAE,wCAAwC,CAC5DC,eAAe,CAAE,mDAAmD,CACpEC,qBAAqB,CAAE,8BAA8B,CACrDC,mBAAmB,CAAE,wBAAwB,CAC7CC,UAAU,CAAE,YAAY,CACxBC,eAAe,CAAE,oBAAoB,CACrCC,qBAAqB,CAAE,oEAAoE,CAC3FC,cAAc,CAAE,iBAAiB,CACjCC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,cAAc,CACzBC,UAAU,CAAE,aAAa,CACzBC,UAAU,CAAE,eAAe,CAC3BC,WAAW,CAAE,2BAA2B,CACxCC,YAAY,CAAE,4BAA4B,CAC1CC,eAAe,CAAE,+CAA+C,CAChEC,gBAAgB,CAAE,kCAAkC,CACpDC,cAAc,CAAE,iBAAiB,CACjCC,gBAAgB,CAAE,8BAA8B,CAChDC,2BAA2B,CAAE,0DAA0D,CACvFC,iBAAiB,CAAE,kCAAkC,CACrDC,eAAe,CAAE,+CAA+C,CAChEC,cAAc,CAAE,kDAAkD,CAClEC,eAAe,CAAE,mDAAmD,CACpEf,cAAc,CAAE,iBAAiB,CACjCgB,aAAa,CAAE,iBAAiB,CAChCC,aAAa,CAAE,iBAAiB,CAChCC,eAAe,CAAE,8CAA8C,CAC/DC,YAAY,CAAE,eAAe,CAC7BC,iBAAiB,CAAE,oBAAoB,CACvCC,cAAc,CAAE,iBAAiB,CACjCC,eAAe,CAAE,oBACnB,CAAC,CACDC,cAAc,CAAE,CACdC,YAAY,CAAE,gBAAgB,CAC9BC,eAAe,CAAE,mBAAmB,CACpCrf,aAAa,CAAE,gCAAgC,CAC/CoD,OAAO,CAAE,SAAS,CAClB6W,eAAe,CAAE,mBAAmB,CACpCqF,cAAc,CAAE,kBAAkB,CAClC3F,UAAU,CAAE,eAAe,CAC3B4F,gBAAgB,CAAE,mBAAmB,CACrCrM,SAAS,CAAE,WAAW,CACtBjD,cAAc,CAAE,iBAAiB,CACjCC,iBAAiB,CAAE,oBAAoB,CACvC2B,cAAc,CAAE,gBAAgB,CAChC6H,iBAAiB,CAAE,+BAA+B,CAClD8F,cAAc,CAAE,iBAAiB,CACjCtb,SAAS,CAAE,gBAAgB,CAC3Bub,WAAW,CAAE,qBAAqB,CAClC7X,KAAK,CAAE,OAAO,CACdkI,KAAK,CAAE,OAAO,CACd4P,QAAQ,CAAE,UAAU,CACpB3P,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,mBAAmB,CAC9BgC,QAAQ,CAAE,UAAU,CACpBK,UAAU,CAAE,YAAY,CACxBsN,WAAW,CAAE,cAAc,CAC3B5N,cAAc,CAAE,iBAAiB,CACjC6N,WAAW,CAAE,qBAAqB,CAClCpe,cAAc,CAAE,kBAAkB,CAClCyQ,aAAa,CAAE,gBAAgB,CAC/B4N,EAAE,CAAE,WAAW,CACfC,UAAU,CAAE,oBAAoB,CAChClO,WAAW,CAAE,cACf,CAAC,CACD4B,IAAI,CAAE,CACJuM,aAAa,CAAE,eAAe,CAC9BC,QAAQ,CAAE,UAAU,CACpBC,WAAW,CAAE,mBAAmB,CAChCtf,IAAI,CAAE,MAAM,CACZM,UAAU,CAAE,aAAa,CACzBif,OAAO,CAAE,kBAAkB,CAC3BC,SAAS,CAAE,wBAAwB,CACnC/X,OAAO,CAAE,YAAY,CACrBgY,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,WAAW,CACtBC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,MAAM,CACZC,SAAS,CAAE,WAAW,CACtBC,IAAI,CAAE,MAAM,CACZnkB,MAAM,CAAE,gBAAgB,CACxBokB,KAAK,CAAE,OAAO,CACdC,YAAY,CAAE,8CAA8C,CAC5DC,gBAAgB,CAAE,kBAAkB,CACpCC,YAAY,CAAE,2BAA2B,CACzCC,mBAAmB,CAAE,gCAAgC,CACrDC,SAAS,CAAE,WAAW,CACtBC,MAAM,CAAE,WAAW,CACnBC,kBAAkB,CAAE,qBAAqB,CACzCC,oBAAoB,CAAE,iEAAiE,CACvFC,mBAAmB,CAAE,mCAAmC,CACxDC,0BAA0B,CAAE,6DAA6D,CACzFC,uBAAuB,CAAE,6BAA6B,CACtDC,cAAc,CAAE,8BAA8B,CAC9CC,aAAa,CAAE,6BAA6B,CAC5CC,SAAS,CAAE,uBAAuB,CAClCC,WAAW,CAAE,wBAAwB,CACrCC,eAAe,CAAE,wBAAwB,CACxCnO,IAAI,CAAE,MACT,CAAC,CACDoO,IAAI,CAAE,CACJnO,KAAK,CAAE,OAAO,CACdE,MAAM,CAAE,QAAQ,CAChBkO,kBAAkB,CAAE,gBAAgB,CACpCC,oBAAoB,CAAE,mCAAmC,CACzDC,WAAW,CAAE,eAAe,CAC5Bna,KAAK,CAAE,eAAe,CACtBoa,cAAc,CAAE,kBAAkB,CAClCC,0BAA0B,CAAE,0FAA0F,CACtHC,aAAa,CAAE,iBAAiB,CAChCC,WAAW,CAAE,eAAe,CAC5BC,aAAa,CAAE,+BAA+B,CAC9CC,kBAAkB,CAAE,2BAA2B,CAC/CC,UAAU,CAAE,aAAa,CACzBC,sBAAsB,CAAE,yDAAyD,CACjFC,gBAAgB,CAAE,mBAAmB,CACrCC,iBAAiB,CAAE,mBAAmB,CACtCC,UAAU,CAAE,aAAa,CACzBC,WAAW,CAAE,cAAc,CAC3BC,WAAW,CAAE,cAAc,CAC3BC,sBAAsB,CAAE,gCAAgC,CACxD9jB,MAAM,CAAE,QAAQ,CAChB+jB,SAAS,CAAE,cAAc,CACzBC,oBAAoB,CAAE,qCAAqC,CAC3DC,kBAAkB,CAAE,2BAA2B,CAC/CC,YAAY,CAAE,oCAAoC,CAClDC,YAAY,CAAE,gBAAgB,CAC9Bpc,OAAO,CAAE,YAAY,CACrB6D,WAAW,CAAE,mCAAmC,CAChD1M,aAAa,CAAE,2BAA2B,CAC1CklB,WAAW,CAAE,8CAA8C,CAC3DC,kBAAkB,CAAE,8CAA8C,CAClEC,aAAa,CAAE,2EAA2E,CAC1FC,aAAa,CAAE,uCAAuC,CACtDC,cAAc,CAAE,4DAA4D,CAC5E5Y,WAAW,CAAE,2BAA2B,CACxCqY,kBAAkB,CAAE,qBAAqB,CACzCQ,eAAe,CAAE,0CAA0C,CAC3DC,aAAa,CAAE,gBAAgB,CAC/BC,yBAAyB,CAAE,wCAAwC,CACnEzY,WAAW,CAAE,cAAc,CAC3B7B,eAAe,CAAE,kBAAkB,CACnCua,oBAAoB,CAAE,4BAA4B,CAClDC,2BAA2B,CAAE,4CAA4C,CACzEC,kBAAkB,CAAE,8BAA8B,CAClDC,mBAAmB,CAAE,0BAA0B,CAC/C5a,QAAQ,CAAE,UAAU,CACpB6a,MAAM,CAAE,SAAS,CACjBC,EAAE,CAAE,IAAI,CACRC,SAAS,CAAE,wBAAwB,CACnCC,MAAM,CAAE,SAAS,CACjBC,kBAAkB,CAAE,sBAAsB,CAC1CC,iBAAiB,CAAE,0CAA0C,CAC7DC,gBAAgB,CAAE,sBAAsB,CACxCC,UAAU,CAAE,8CAA8C,CAC1DC,eAAe,CAAE,mBAAmB,CACpCC,WAAW,CAAE,mBAAmB,CAChCC,iBAAiB,CAAE,oBAAoB,CACvCC,mBAAmB,CAAE,8HAA8H,CACnJC,iBAAiB,CAAE,eAAe,CAClCC,kBAAkB,CAAE,4IAA4I,CAChKC,iBAAiB,CAAE,eAAe,CAClCC,kBAAkB,CAAE,gHAAgH,CACpIC,kBAAkB,CAAE,0BAA0B,CAC9CC,UAAU,CAAE,aAAa,CACzBC,mBAAmB,CAAE,sBAAsB,CAC3CC,mBAAmB,CAAE,sBAAsB,CAC3CC,gBAAgB,CAAE,gCAAgC,CAClDC,eAAe,CAAE,kCAAkC,CACnDC,aAAa,CAAE,gBAAgB,CAC/BC,iBAAiB,CAAE,wCAAwC,CAC3DC,YAAY,CAAE,2BAA2B,CACzChc,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QACV,CAAC,CACDmI,WAAW,CAAE,CACX4T,WAAW,CAAE,aAAa,CAC1BC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,OAAO,CACdC,OAAO,CAAE,SAAS,CAClBC,eAAe,CAAE,iBAAiB,CAClCC,cAAc,CAAE,gBAAgB,CAChCC,eAAe,CAAE,iBAAiB,CAClCC,cAAc,CAAE,gBAAgB,CAChCC,cAAc,CAAE,gBAAgB,CAChCC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,eACX,CAAC,CACDhT,SAAS,CAAE,CACT/C,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SACX,CAAC,CACD/G,MAAM,CAAE,CACNC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QACV,CAAC,CACDoJ,SAAS,CAAE,CACTsT,OAAO,CAAE,SAAS,CAClBC,YAAY,CAAE,kCAAkC,CAChDllB,UAAU,CAAE,qBAAqB,CACjCkV,aAAa,CAAE,gBAAgB,CAC/BiQ,YAAY,CAAE,eAAe,CAC7B1hB,aAAa,CAAE,gBAAgB,CAC/B2R,aAAa,CAAE,gBAAgB,CAC/BrD,UAAU,CAAE,qBAAqB,CACjCqT,cAAc,CAAE,iBAAiB,CACjCC,aAAa,CAAE,gBAAgB,CAC/BC,gBAAgB,CAAE,mBAAmB,CACrCC,YAAY,CAAE,eAAe,CAC7BC,mBAAmB,CAAE,sBAAsB,CAC3CC,eAAe,CAAE,kBAAkB,CACnCC,iBAAiB,CAAE,+BAA+B,CAClDC,aAAa,CAAE,gBAAgB,CAC/BxT,WAAW,CAAE,gBAAgB,CAC7ByT,aAAa,CAAE,gBAAgB,CAC/BthB,OAAO,CAAE,UAAU,CACnBuhB,iBAAiB,CAAE,4BAA4B,CAC/CC,sBAAsB,CAAE,6GAA6G,CACrIC,uBAAuB,CAAE,yEAAyE,CAClGC,eAAe,CAAE,kBAAkB,CACnCL,aAAa,CAAE,gBAAgB,CAC/BM,kBAAkB,CAAE,sBACtB,CAAC,CACDC,OAAO,CAAE,CACPC,WAAW,CAAE,aAAa,CAC1BC,YAAY,CAAE,cAAc,CAC5BC,kBAAkB,CAAE,oBAAoB,CACxCC,UAAU,CAAE,YAAY,CACxBC,cAAc,CAAE,gBAAgB,CAChCC,YAAY,CAAE,cAAc,CAC5BC,MAAM,CAAE,QAAQ,CAChBC,aAAa,CAAE,eAAe,CAC9BC,aAAa,CAAE,eAAe,CAC9BC,SAAS,CAAE,WAAW,CACtBC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QACV,CAAC,CACDnrB,OAAO,CAAE,CACPorB,aAAa,CAAE,UAAU,CACzBhY,cAAc,CAAE,iBAAiB,CACjCC,iBAAiB,CAAE,CACjB1U,KAAK,CAAE,oBAAoB,CAC3B2U,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,MAAM,CACZC,OAAO,CAAE,SAAS,CAClBC,UAAU,CAAE,YAAY,CACxBC,KAAK,CAAE,OAAO,CACdC,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,OAAO,CACdC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,MAAM,CACZC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,2BAA2B,CACnCC,WAAW,CAAE,8BACf,CAAC,CACDE,cAAc,CAAE,gBAAgB,CAChCQ,UAAU,CAAE,YAAY,CACxBqF,QAAQ,CAAE,UAAU,CACpBpP,OAAO,CAAE,CACP9M,KAAK,CAAE,iBAAiB,CACxBoU,YAAY,CAAE,sBAAsB,CACpCC,YAAY,CAAE,sBAAsB,CACpCC,KAAK,CAAE,cAAc,CACrBC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,oBAAoB,CAC/BC,cAAc,CAAE,iBAAiB,CACjCC,iBAAiB,CAAE,CACjB1U,KAAK,CAAE,oBAAoB,CAC3B2U,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,MAAM,CACZC,OAAO,CAAE,SAAS,CAClBC,UAAU,CAAE,YAAY,CACxBC,KAAK,CAAE,OAAO,CACdC,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,OAAO,CACdC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,MAAM,CACZC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,2BAA2B,CACnCC,WAAW,CAAE,8BACf,CAAC,CACDC,WAAW,CAAE,cAAc,CAC3BC,cAAc,CAAE,gBAAgB,CAChCC,kBAAkB,CAAE,qBAAqB,CACzCC,cAAc,CAAE,iBAAiB,CACjCvQ,cAAc,CAAE,kBAAkB,CAClCwQ,QAAQ,CAAE,WAAW,CACrBC,aAAa,CAAE,gBAAgB,CAC/BlI,QAAQ,CAAE,CACRmI,GAAG,CAAE,KAAK,CACVC,GAAG,CAAE,KAAK,CACVC,GAAG,CAAE,KACP,CAAC,CACDC,UAAU,CAAE,CACVC,QAAQ,CAAE,sBAAsB,CAChCC,YAAY,CAAE,0BAA0B,CACxCC,QAAQ,CAAE,uBAAuB,CACjCC,MAAM,CAAE,oBACV,CACF,CAAC,CACDyV,WAAW,CAAE,CACX1sB,KAAK,CAAE,qBAAqB,CAC5BuC,MAAM,CAAE,oBAAoB,CAC5B2K,OAAO,CAAE,oCAAoC,CAC7CC,KAAK,CAAE,8BAA8B,CACrCwf,iBAAiB,CAAE,qBAAqB,CACxCC,uBAAuB,CAAE,2BAA2B,CACpDC,eAAe,CAAE,oBAAoB,CACrC1e,IAAI,CAAE,kBAAkB,CACxB2e,eAAe,CAAE,6FAA6F,CAC9GC,YAAY,CAAE,2BAA2B,CACzCC,cAAc,CAAE,2FAA2F,CAC3GC,mBAAmB,CAAE,iGAAiG,CACtHC,mBAAmB,CAAE,iGAAiG,CACtHC,mBAAmB,CAAE,oCAAoC,CACzDC,WAAW,CAAE,uBAAuB,CACpCC,eAAe,CAAE,CACfrtB,KAAK,CAAE,kBAAkB,CACzBQ,WAAW,CAAE,0FAA0F,CACvG8sB,OAAO,CAAE,6EACX,CAAC,CACDC,WAAW,CAAE,cAAc,CAC3B7sB,MAAM,CAAE,CACNG,OAAO,CAAE,gBAAgB,CACzB+P,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,UACZ,CAAC,CACD2c,aAAa,CAAE,CACb3sB,OAAO,CAAE,oCAAoC,CAC7C+P,QAAQ,CAAE,qDAAqD,CAC/DC,QAAQ,CAAE,mDAAmD,CAC7D4c,OAAO,CAAE,4BACX,CAAC,CACDC,oBAAoB,CAAE,CACpB9c,QAAQ,CAAE,CACR5Q,KAAK,CAAE,YAAY,CACnB2tB,KAAK,CAAE,CACL,kCAAkC,CAClC,uDAAuD,CAE3D,CACF,CACF,CAAC,CACDpZ,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,mBAAmB,CAC9BC,cAAc,CAAE,iBAAiB,CACjCC,iBAAiB,CAAE,oBAAoB,CACvC0B,WAAW,CAAE,cAAc,CAC3BC,cAAc,CAAE,gBAAgB,CAChCuX,yBAAyB,CAAE,0DAA0D,CACrFtX,kBAAkB,CAAE,8BAA8B,CAClDgO,UAAU,CAAE,oBAAoB,CAChCuJ,aAAa,CAAE,wBAAwB,CACvCC,wBAAwB,CAAE,aAAa,CACvCC,YAAY,CAAE,iCAAiC,CAC/CC,YAAY,CAAE,YAAY,CAC1BC,YAAY,CAAE,cAAc,CAC5BC,SAAS,CAAE,MAAM,CACjBhd,WAAW,CAAE,QAAQ,CACrBid,aAAa,CAAE,qDAAqD,CACpEC,aAAa,CAAE,mEAAmE,CAClFC,eAAe,CAAE,yDAAyD,CAC1EC,sBAAsB,CAAE,cAAc,CACtCC,aAAa,CAAE,gBAAgB,CAC/BC,gBAAgB,CAAE,uBAAuB,CACzCC,aAAa,CAAE,uCAAuC,CACtDC,cAAc,CAAE,iBAAiB,CACjCC,WAAW,CAAE,mBAAmB,CAChCC,oBAAoB,CAAE,uBAAuB,CAC7CvK,EAAE,CAAE,WAAW,CACfwK,aAAa,CAAE,eAAe,CAC9BC,YAAY,CAAE,uGAAuG,CACrHC,UAAU,CAAE,YAAY,CACxBC,cAAc,CAAE,iBAAiB,CACjCC,yBAAyB,CAAE,6BAA6B,CACxDC,sBAAsB,CAAE,6BAA6B,CACrD3Y,cAAc,CAAE,0BAA0B,CAC1ClE,yBAAyB,CAAE,iEAAiE,CAC5FE,gCAAgC,CAAE,2FAA2F,CAC7HC,kBAAkB,CAAE,sBAAsB,CAC1CC,6BAA6B,CAAE,wDAAwD,CACvFgB,gBAAgB,CAAE,sCAAsC,CACxDG,QAAQ,CAAE,WAAW,CACrBJ,SAAS,CAAE,YAAY,CACvBK,SAAS,CAAE,WAAW,CACtBC,WAAW,CAAE,eAAe,CAC5BpB,kBAAkB,CAAE,sBAAsB,CAC1Cyc,cAAc,CAAE,iBAAiB,CACjCC,mBAAmB,CAAE,gDAAgD,CACrE9oB,IAAI,CAAE,MAAM,CACZ2N,MAAM,CAAE,QAAQ,CAChBob,UAAU,CAAE,kBAAkB,CAC9BC,UAAU,CAAE,kBAAkB,CAC9BC,YAAY,CAAE,gBAAgB,CAC9BC,UAAU,CAAE,aAAa,CACzBxpB,cAAc,CAAE,kBAAkB,CAClCypB,yBAAyB,CAAE,oBAAoB,CAC/CC,gBAAgB,CAAE,oBAAoB,CACtCC,2BAA2B,CAAE,iCAAiC,CAC9DC,yBAAyB,CAAE,uEAAuE,CAClGpZ,QAAQ,CAAE,UAAU,CACpBC,aAAa,CAAE,gBAAgB,CAC/BnC,KAAK,CAAE,cAAc,CACrBub,SAAS,CAAE,4CAA4C,CACvDC,aAAa,CAAE,gBAAgB,CAC/Bpc,SAAS,CAAE,YAAY,CACvBqc,QAAQ,CAAE,gBAAgB,CAC1BtB,aAAa,CAAE,uCAAuC,CACtD3Q,UAAU,CAAE,mCAAmC,CAC/CkS,YAAY,CAAE,iCAAiC,CAC/CC,UAAU,CAAE,0BAA0B,CACtCC,YAAY,CAAE,mCAAmC,CACjDC,YAAY,CAAE,4BAA4B,CAC1Che,cAAc,CAAE,kBAAkB,CAClCuc,cAAc,CAAE,iBAAiB,CACjCC,WAAW,CAAE,mBAAmB,CAChCyB,YAAY,CAAE,gBAAgB,CAC9BC,YAAY,CAAE,OAAO,CACrBC,aAAa,CAAE,wDAAwD,CACvEC,UAAU,CAAE,oBAAoB,CAChCC,qBAAqB,CAAE,mDAAmD,CAC1EC,4BAA4B,CAAE,oDAAoD,CAClFC,kBAAkB,CAAE,8CAA8C,CAClEC,gBAAgB,CAAE,CAChBC,QAAQ,CAAE,qBAAqB,CAC/B/vB,OAAO,CAAE,6BAA6B,CACtCgQ,QAAQ,CAAE,qBACZ,CAAC,CACDggB,eAAe,CAAE,oBAAoB,CACrCC,kBAAkB,CAAE,qBAAqB,CACzCC,cAAc,CAAE,kCAAkC,CAClDC,MAAM,CAAE,SAAS,CACjB9sB,MAAM,CAAE,WAAW,CACnB0O,oBAAoB,CAAE,wBAAwB,CAC9Cqe,cAAc,CAAE,+BAA+B,CAC/CC,yBAAyB,CAAE,gEAAgE,CAC3FC,SAAS,CAAE,CACTC,KAAK,CAAE,uCAAuC,CAC9CC,QAAQ,CAAE,8CACZ,CAAC,CACDC,eAAe,CAAE,kBAAkB,CACnCC,eAAe,CAAE,sBAAsB,CACvCC,cAAc,CAAE,iDAAiD,CACjEC,cAAc,CAAE,+BAA+B,CAC/CC,gBAAgB,CAAE,8BAA8B,CAChDC,2BAA2B,CAAE,2CAA2C,CACxEC,eAAe,CAAE,kBAAkB,CACnCC,qBAAqB,CAAE,CACrBhxB,OAAO,CAAE,SAAS,CAClBixB,QAAQ,CAAE,UACZ,CACF,CAAC,CACD7Y,IAAI,CAAE,CACJrV,KAAK,CAAE,OAAO,CACdvC,OAAO,CAAE,SAAS,CAClBD,OAAO,CAAE,SACX,CAAC,CACD2wB,UAAU,CAAE,CACVhC,QAAQ,CAAE,wBAAwB,CAClC3jB,KAAK,CAAE,oCAAoC,CAC3CsB,QAAQ,CAAE,CACRskB,GAAG,CAAE,8CAA8C,CACnDC,GAAG,CAAE,6CAA6C,CAClDC,KAAK,CAAE,wBACT,CACF,CAAC,CACDC,IAAI,CAAE,CACJC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,WAAW,CACtBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,KAAK,CAClBC,YAAY,CAAE,KAAK,CACnBC,cAAc,CAAE,KAAK,CACrBC,aAAa,CAAE,KAAK,CACpBC,WAAW,CAAE,KAAK,CAClBC,aAAa,CAAE,KAAK,CACpBC,WAAW,CAAE,KACf,CAAC,CACDC,KAAK,CAAE,CACLC,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAE,UAAU,CAClBC,MAAM,CAAE,UAAU,CAClBC,MAAM,CAAE,UAAU,CAClBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,UAAU,CAClBC,MAAM,CAAE,UAAU,CAClBC,MAAM,CAAE,UACV,CAAC,CACDtzB,OAAO,CAAE,CACP0L,OAAO,CAAE,CACP9M,KAAK,CAAE,iBAAiB,CACxB20B,QAAQ,CAAE,uBAAuB,CACjCC,mBAAmB,CAAE,sBAAsB,CAC3CxgB,YAAY,CAAE,sBAAsB,CACpCygB,mBAAmB,CAAE,sBAAsB,CAC3CpgB,cAAc,CAAE,iBAAiB,CACjCqgB,wBAAwB,CAAE,uCAAuC,CACjEC,uBAAuB,CAAE,wCAAwC,CACjEC,qBAAqB,CAAE,6BAA6B,CACpDC,sBAAsB,CAAE,8BAA8B,CACtDC,GAAG,CAAE,KAAK,CACV3gB,OAAO,CAAE,SAAS,CAClBiC,QAAQ,CAAE,UAAU,CACpB2e,WAAW,CAAE,0BAA0B,CACvCC,eAAe,CAAE,qCAAqC,CACtDC,WAAW,CAAE,IAAI,CACjBC,aAAa,CAAE,KAAK,CACpB9mB,aAAa,CAAE,8BAA8B,CAC7CG,QAAQ,CAAE,0BAA0B,CACpC4mB,MAAM,CAAE,CACNze,QAAQ,CAAE,UAAU,CACpBC,YAAY,CAAE,cAAc,CAC5BC,QAAQ,CAAE,UACZ,CAAC,CACD9J,OAAO,CAAE,2DACX,CAAC,CACD4K,UAAU,CAAE,aAAa,CACzB0d,aAAa,CAAE,2BAA2B,CAC1CC,YAAY,CAAE,eAChB,CAAC,CACD7xB,KAAK,CAAE,CACLyT,SAAS,CAAE,CACTrX,KAAK,CAAE,WAAW,CAClB01B,cAAc,CAAE,4BAA4B,CAC5CC,QAAQ,CAAE,wEAAwE,CAClFC,aAAa,CAAE,gBAAgB,CAC/BC,mBAAmB,CAAE,sBAAsB,CAC3Cjb,aAAa,CAAE,gBAAgB,CAC/Bkb,YAAY,CAAE,eAAe,CAC7BC,qBAAqB,CAAE,kBAAkB,CACzCC,cAAc,CAAE,iBAAiB,CACjCjb,aAAa,CAAE,gBAAgB,CAC/Bkb,YAAY,CAAE,oBAAoB,CAClCC,kBAAkB,CAAE,sBAAsB,CAC1CC,iBAAiB,CAAE,qBAAqB,CACxCC,kBAAkB,CAAE,qBAAqB,CACzCC,cAAc,CAAE,iBAAiB,CACjCrsB,OAAO,CAAE,UAAU,CACnBqP,WAAW,CAAE,cAAc,CAC3Bid,cAAc,CAAE,wBAAwB,CACxCC,UAAU,CAAE,oBAAoB,CAChCC,mBAAmB,CAAE,mCAAmC,CACxDC,oBAAoB,CAAE,oCAAoC,CAC1DC,cAAc,CAAE,2BAA2B,CAC3C51B,SAAS,CAAE,WAAW,CACtBE,SAAS,CAAE,WAAW,CACtBH,OAAO,CAAE,SAAS,CAClB6E,UAAU,CAAE,+BACd,CAAC,CACDixB,eAAe,CAAE,CACf32B,KAAK,CAAE,sBAAsB,CAC7B21B,QAAQ,CAAE,qEAAqE,CAC/EiB,aAAa,CAAE,gBAAgB,CAC/BC,SAAS,CAAE,YAAY,CACvBC,WAAW,CAAE,cAAc,CAC3BC,WAAW,CAAE,cAAc,CAC3BC,aAAa,CAAE,gBAAgB,CAC/BC,QAAQ,CAAE,WAAW,CACrBlzB,GAAG,CAAE,KAAK,CACV1C,OAAO,CAAE,SAAS,CAClBD,OAAO,CAAE,SAAS,CAClB81B,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,kBAAkB,CAC5Bne,IAAI,CAAE,MAAM,CACZrU,IAAI,CAAE,MAAM,CACZyyB,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,WAAW,CACrBC,SAAS,CAAE,YAAY,CACvBhwB,QAAQ,CAAE,UAAU,CACpB5G,MAAM,CAAE,QAAQ,CAChB62B,MAAM,CAAE,QAAQ,CAChBC,KAAK,CAAE,OACT,CAAC,CACDC,aAAa,CAAE,CACbz3B,KAAK,CAAE,gBACT,CAAC,CACDwkB,QAAQ,CAAE,CACRxkB,KAAK,CAAE,UAAU,CACjB6f,IAAI,CAAE,MAAM,CACZlb,IAAI,CAAE,MAAM,CACZM,OAAO,CAAE,SAAS,CAClB1E,IAAI,CAAE,MAAM,CACZG,MAAM,CAAE,QAAQ,CAChBG,OAAO,CAAE,SAAS,CAClB2E,QAAQ,CAAE,UAAU,CACpBzB,GAAG,CAAE,cAAc,CACnBqK,IAAI,CAAE,MAAM,CACZspB,KAAK,CAAE,OAAO,CACdvvB,MAAM,CAAE,QAAQ,CAChBqF,MAAM,CAAE,oBAAoB,CAC5B/H,UAAU,CAAE,mBAAmB,CAC/BC,UAAU,CAAE,yBAAyB,CACrCiyB,OAAO,CAAE,UAAU,CACnBC,eAAe,CAAE,kBAAkB,CACnCC,SAAS,CAAE,YAAY,CACvB/sB,SAAS,CAAE,YAAY,CACvBgtB,SAAS,CAAE,yBAAyB,CACpC3sB,UAAU,CAAE,qBAAqB,CACjC/C,aAAa,CAAE,gBAAgB,CAC/B2vB,aAAa,CAAE,6EAA6E,CAC5FC,aAAa,CAAE,8BAA8B,CAC7C9R,WAAW,CAAE,wBACf,CAAC,CACD+R,cAAc,CAAE,CACdj4B,KAAK,CAAE,iBAAiB,CACxBQ,WAAW,CAAE,0DAA0D,CACvEyF,YAAY,CAAE,4HAA4H,CAC1IiyB,gBAAgB,CAAE,+BAA+B,CACjDC,iBAAiB,CAAE,gCAAgC,CACnD/qB,IAAI,CAAE,eAAe,CACrBgrB,WAAW,CAAE,6BAA6B,CAC1CC,SAAS,CAAE,uBAAuB,CAClC3yB,UAAU,CAAE,0BAA0B,CACtC4yB,cAAc,CAAE,iBAAiB,CACjCC,OAAO,CAAE,SAAS,CAClBC,aAAa,CAAE,gCAAgC,CAC/CC,kBAAkB,CAAE,qBAAqB,CACzCC,aAAa,CAAE,6EAA6E,CAC5FC,SAAS,CAAE,YAAY,CACvBC,oBAAoB,CAAE,6BAA6B,CACnDC,QAAQ,CAAE,iBAAiB,CAC3BC,gBAAgB,CAAE,8BAA8B,CAChDC,cAAc,CAAE,0BAA0B,CAC1CC,SAAS,CAAE,CACTC,WAAW,CAAE,sBAAsB,CACnCC,YAAY,CAAE,0BAA0B,CACxCC,YAAY,CAAE,8BAA8B,CAC5CC,eAAe,CAAE,kCACnB,CACF,CAAC,CACDC,QAAQ,CAAE,CACRr5B,KAAK,CAAE,mBAAmB,CAC1Bs5B,eAAe,CAAE,kBAAkB,CACnCtmB,YAAY,CAAE,eAAe,CAC7B8iB,YAAY,CAAE,eAAe,CAC7ByD,iBAAiB,CAAE,qBAAqB,CACxCC,SAAS,CAAE,YAAY,CACvBC,OAAO,CAAE,UAAU,CACnBC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,cAAc,CAC3Bp5B,IAAI,CAAE,MAAM,CACZ62B,OAAO,CAAE,SAAS,CAClB/1B,OAAO,CAAE,SAAS,CAClBD,OAAO,CAAE,SAAS,CAClBw4B,YAAY,CAAE,eAAe,CAC7BC,cAAc,CAAE,iBAAiB,CACjCC,gBAAgB,CAAE,mBAAmB,CACrCC,eAAe,CAAE,kBAAkB,CACnCv1B,aAAa,CAAE,8BACjB,CAAC,CACDw1B,kBAAkB,CAAE,CAClBh6B,KAAK,CAAE,qBAAqB,CAC5BQ,WAAW,CAAE,mDAAmD,CAChEy5B,iBAAiB,CAAE,yBAAyB,CAC5CjhB,IAAI,CAAE,MAAM,CACZ5M,KAAK,CAAE,OAAO,CACd6M,IAAI,CAAE,MAAM,CACZnI,WAAW,CAAE,cAAc,CAC3BpQ,MAAM,CAAE,QAAQ,CAChB2B,OAAO,CAAE,SAAS,CAClB4lB,aAAa,CAAE,gBAAgB,CAC/BiS,gBAAgB,CAAE,2BAA2B,CAC7CzqB,WAAW,CAAE,cAAc,CAC3B0qB,gBAAgB,CAAE,mBAAmB,CACrCC,YAAY,CAAE,yCAAyC,CACvDC,UAAU,CAAE,0BAA0B,CACtCC,UAAU,CAAE,kCAAkC,CAC9CC,aAAa,CAAE,SAAS,CACxBC,eAAe,CAAE,WAAW,CAC5BC,aAAa,CAAE,iBAAiB,CAChCC,gBAAgB,CAAE,oBAAoB,CACtCC,oBAAoB,CAAE,4EAA4E,CAClGC,SAAS,CAAE,YAAY,CACvBC,aAAa,CAAE,yCAAyC,CACxD9B,cAAc,CAAE,0BAClB,CAAC,CACDjsB,OAAO,CAAE,CACPsH,YAAY,CAAE,sBAAsB,CACpC5E,cAAc,CAAE,iBAAiB,CACjCR,eAAe,CAAE,kBAAkB,CACnCS,WAAW,CAAE,cAAc,CAC3BqrB,kBAAkB,CAAE,sBAAsB,CAC1CtsB,aAAa,CAAE,8BAA8B,CAC7CusB,WAAW,CAAE,wBAAwB,CACrCC,eAAe,CAAE,+BAA+B,CAChDC,aAAa,CAAE,yBAAyB,CACxCC,YAAY,CAAE,oCAAoC,CAClDC,UAAU,CAAE,8BACd,CAAC,CACDC,WAAW,CAAE,CACXC,gBAAgB,CAAE,kBAAkB,CACpCC,WAAW,CAAE,aACf,CAAC,CACDhkB,QAAQ,CAAE,CACRtX,KAAK,CAAE,UAAU,CACjBi6B,iBAAiB,CAAE,oBAAoB,CACvC3tB,IAAI,CAAE,MAAM,CACZF,KAAK,CAAE,OAAO,CACd2B,MAAM,CAAE,QAAQ,CAChBkL,IAAI,CAAE,MAAM,CACZ5W,OAAO,CAAE,SAAS,CAClBgX,WAAW,CAAE,cAAc,CAC3BsK,cAAc,CAAE,iBAAiB,CACjCvP,YAAY,CAAE,sBAAsB,CACpCmnB,aAAa,CAAE,+CAA+C,CAC9DvD,aAAa,CAAE,8BAA8B,CAC7C9R,WAAW,CAAE,wBAAwB,CACrCxgB,UAAU,CAAE,yBACd,CAAC,CACD6R,QAAQ,CAAE,CACRvX,KAAK,CAAE,UAAU,CACjBi6B,iBAAiB,CAAE,oBAAoB,CACvC3tB,IAAI,CAAE,MAAM,CACZF,KAAK,CAAE,OAAO,CACd2B,MAAM,CAAE,QAAQ,CAChBwG,OAAO,CAAE,SAAS,CAClB2gB,GAAG,CAAE,KAAK,CACV1e,QAAQ,CAAE,UAAU,CACpBnU,OAAO,CAAE,SAAS,CAClBgX,WAAW,CAAE,cAAc,CAC3BmiB,cAAc,CAAE,iBAAiB,CACjCpnB,YAAY,CAAE,sBAAsB,CACpCygB,mBAAmB,CAAE,sBAAsB,CAC3CpgB,cAAc,CAAE,iBAAiB,CACjCugB,qBAAqB,CAAE,yBAAyB,CAChDC,sBAAsB,CAAE,0BAA0B,CAClDwG,sBAAsB,CAAE,0BAA0B,CAClDrG,eAAe,CAAE,kBAAkB,CACnCsG,gBAAgB,CAAE,mBAAmB,CACrCH,aAAa,CAAE,+CAA+C,CAC9DvD,aAAa,CAAE,8BAA8B,CAC7C9R,WAAW,CAAE,wBAAwB,CACrCxgB,UAAU,CAAE,yBAAyB,CACrC6wB,UAAU,CAAE,mBAAmB,CAC/BpuB,MAAM,CAAE,QAAQ,CAChBwzB,iBAAiB,CAAE,CACjB7kB,QAAQ,CAAE,UAAU,CACpBC,YAAY,CAAE,cAAc,CAC5BC,QAAQ,CAAE,UACZ,CACF,CAAC,CACDS,UAAU,CAAE,CACVzX,KAAK,CAAE,YAAY,CACnB47B,MAAM,CAAE,kBAAkB,CAC1BC,SAAS,CAAE,eAAe,CAC1BC,QAAQ,CAAE,cAAc,CACxBxvB,IAAI,CAAE,MAAM,CACZ9L,WAAW,CAAE,aAAa,CAC1Bu7B,SAAS,CAAE,YAAY,CACvBvtB,aAAa,CAAE,+BAA+B,CAC9CwtB,aAAa,CAAE,+BAA+B,CAC9ChE,aAAa,CAAE,+BAA+B,CAC9CuD,aAAa,CAAE,gDAAgD,CAC/D71B,UAAU,CAAE,2BAA2B,CACvC2yB,SAAS,CAAE,uBAAuB,CAClCnS,WAAW,CAAE,yBAAyB,CACtC0E,YAAY,CAAE,qBAAqB,CACnCqR,SAAS,CAAE,qBACb,CAAC,CACDvkB,SAAS,CAAE,CACT1X,KAAK,CAAE,WAAW,CAClB47B,MAAM,CAAE,kBAAkB,CAC1BC,SAAS,CAAE,eAAe,CAC1BC,QAAQ,CAAE,cAAc,CACxBxvB,IAAI,CAAE,eAAe,CACrB4vB,YAAY,CAAE,2BAA2B,CACzCtR,YAAY,CAAE,+CAA+C,CAC7DllB,UAAU,CAAE,0BAA0B,CACtCs2B,aAAa,CAAE,+BAA+B,CAC9CG,WAAW,CAAE,yBAAyB,CACtC3tB,aAAa,CAAE,+BAA+B,CAC9CusB,WAAW,CAAE,yBAAyB,CACtC/C,aAAa,CAAE,+BAA+B,CAC9C9R,WAAW,CAAE,yBAAyB,CACtCqV,aAAa,CAAE,gDAAgD,CAC/DU,SAAS,CAAE,qBAAqB,CAChC5D,SAAS,CAAE,uBACb,CAAC,CACD1gB,YAAY,CAAE,CACZsiB,iBAAiB,CAAE,wBAAwB,CAC3CmC,cAAc,CAAE,kBAAkB,CAClC9vB,IAAI,CAAE,MAAM,CACZF,KAAK,CAAE,OAAO,CACdkI,KAAK,CAAE,OAAO,CACdC,OAAO,CAAE,SAAS,CAClBmD,SAAS,CAAE,WAAW,CACtBhX,MAAM,CAAE,QAAQ,CAChB2B,OAAO,CAAE,SAAS,CAClBg6B,QAAQ,CAAE,CACRx7B,OAAO,CAAE,SAAS,CAClB+P,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,UACZ,CAAC,CACD7Q,KAAK,CAAE,sBAAsB,CAC7Bs8B,WAAW,CAAE,cAAc,CAC3BjjB,WAAW,CAAE,cAAc,CAC3BpV,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBq4B,kBAAkB,CAAE,qBAAqB,CACzCnoB,YAAY,CAAE,sBAAsB,CACpCC,YAAY,CAAE,sBAAsB,CACpCmoB,SAAS,CAAE,WAAW,CACtB/nB,cAAc,CAAE,iBAAiB,CACjCC,iBAAiB,CAAE,oBAAoB,CACvC2B,cAAc,CAAE,gBAAgB,CAChCQ,UAAU,CAAE,YAAY,CACxB7Q,cAAc,CAAE,kBAAkB,CAClCy2B,SAAS,CAAE,yBAAyB,CACpCC,MAAM,CAAE,SAAS,CACjBptB,SAAS,CAAE,YAAY,CACvBqtB,eAAe,CAAE,kBAAkB,CACnCC,QAAQ,CAAE,UAAU,CACpB1Y,QAAQ,CAAE,UAAU,CACpB2Y,eAAe,CAAE,kBAAkB,CACnCzmB,WAAW,CAAE,cAAc,CAC3B0mB,OAAO,CAAE,SAAS,CAClBrmB,aAAa,CAAE,gBAAgB,CAC/B6N,UAAU,CAAE,oBAAoB,CAChCD,EAAE,CAAE,WAAW,CACf9N,cAAc,CAAE,iBAAiB,CACjC2H,iBAAiB,CAAE,+BACrB,CACF,CAAC,CACD6e,IAAI,CAAE,CACJ1lB,SAAS,CAAE,WAAW,CACtBU,QAAQ,CAAE,UAAU,CACpBjL,OAAO,CAAE,SAAS,CAClBkL,IAAI,CAAE,MAAM,CACZglB,cAAc,CAAE,iBAClB,CAAC,CACDjlB,QAAQ,CAAE,CACR/X,KAAK,CAAE,UAAU,CACjBi9B,UAAU,CAAE,aAAa,CACzBz8B,WAAW,CAAE,uDAAuD,CACpE08B,UAAU,CAAE,uBAAuB,CACnCC,qBAAqB,CAAE,2EAA2E,CAClG97B,OAAO,CAAE,SAAS,CAClBd,IAAI,CAAE,MAAM,CACZ+F,IAAI,CAAE,MAAM,CACZZ,UAAU,CAAE,yBAAyB,CACrC03B,IAAI,CAAE,MAAM,CACZC,SAAS,CAAE,oBAAoB,CAC/BC,WAAW,CAAE,cAAc,CAC3BC,WAAW,CAAE,cAAc,CAC3Bj2B,QAAQ,CAAE,UAAU,CACpBkK,OAAO,CAAE,SAAS,CAClBgsB,MAAM,CAAE,gBAAgB,CACxBC,KAAK,CAAE,eAAe,CACtBC,IAAI,CAAE,cAAc,CACpBC,UAAU,CAAE,yBAAyB,CACrCnG,KAAK,CAAE,eAAe,CACtBoG,SAAS,CAAE,uBAAuB,CAClCt7B,MAAM,CAAE,gBAAgB,CACxBu7B,QAAQ,CAAE,gBAAgB,CAC1BC,UAAU,CAAE,+BAA+B,CAC3C9c,aAAa,CAAE,gCAAgC,CAC/CC,WAAW,CAAE,0BAA0B,CACvC+a,aAAa,CAAE,8BAA8B,CAC7CG,WAAW,CAAE,wBAAwB,CACrC30B,UAAU,CAAE,aAAa,CACzBD,UAAU,CAAE,aAAa,CACzBw2B,gBAAgB,CAAE,mBAAmB,CACrCxvB,QAAQ,CAAE,KAAK,CACf7N,MAAM,CAAE,CACNG,OAAO,CAAE,SAAS,CAClB4f,OAAO,CAAE,SAAS,CAClB3f,SAAS,CAAE,WAAW,CACtBE,SAAS,CAAE,WAAW,CACtBuf,SAAS,CAAE,WACb,CAAC,CACDyd,gBAAgB,CAAE,gCAAgC,CAClDC,sBAAsB,CAAE,8BAA8B,CACtDC,WAAW,CAAE,aAAa,CAC1BC,GAAG,CAAE,KAAK,CACVC,WAAW,CAAE,cAAc,CAC3BC,aAAa,CAAE,gBAAgB,CAC/BC,MAAM,CAAE,SAAS,CACjBC,WAAW,CAAE,oBAAoB,CACjCC,aAAa,CAAE,qFAAqF,CACpGC,iBAAiB,CAAE,oDAAoD,CACvEC,cAAc,CAAE,kBAAkB,CAClCnH,MAAM,CAAE,QAAQ,CAChBoH,OAAO,CAAE,SAAS,CAClB5M,UAAU,CAAE,CACVmK,YAAY,CAAE,0BAA0B,CACxC0C,YAAY,CAAE,0BAA0B,CACxCC,gBAAgB,CAAE,sBAAsB,CACxCC,iBAAiB,CAAE,yBACrB,CAAC,CACD19B,OAAO,CAAE,SAAS,CAClB29B,QAAQ,CAAE,aAAa,CACvBt+B,MAAM,CAAE,QAAQ,CAChB4B,OAAO,CAAE,SACX,CAAC,CACDoZ,OAAO,CAAE,CACPzb,KAAK,CAAE,gBAAgB,CACvBwL,KAAK,CAAE,qMAAqM,CAC5MwzB,QAAQ,CAAE,CACRh/B,KAAK,CAAE,2BAA2B,CAClC8b,QAAQ,CAAE,6DAA6D,CACvEmjB,KAAK,CAAE,uCAAuC,CAC9CC,KAAK,CAAE,6BAA6B,CACpCC,KAAK,CAAE,iEACT,CAAC,CACDC,QAAQ,CAAE,CACRp/B,KAAK,CAAE,gCAAgC,CACvC8b,QAAQ,CAAE,6BAA6B,CACvCmjB,KAAK,CAAE,8CAA8C,CACrDC,KAAK,CAAE,gCAAgC,CACvCC,KAAK,CAAE,yDAAyD,CAChEE,KAAK,CAAE,4CAA4C,CACnDC,KAAK,CAAE,8CACT,CAAC,CACDC,QAAQ,CAAE,CACRv/B,KAAK,CAAE,kCAAkC,CACzC8b,QAAQ,CAAE,2CAA2C,CACrDtb,WAAW,CAAE,uBAAuB,CACpCy+B,KAAK,CAAE,mCAAmC,CAC1CC,KAAK,CAAE,sCAAsC,CAC7CC,KAAK,CAAE,4DACT,CAAC,CACDK,QAAQ,CAAE,CACRx/B,KAAK,CAAE,kBAAkB,CACzBy/B,OAAO,CAAE,0IACX,CAAC,CACDC,QAAQ,CAAE,CACR1/B,KAAK,CAAE,gBAAgB,CACvB8b,QAAQ,CAAE,wBAAwB,CAClCmjB,KAAK,CAAE,2BAA2B,CAClCC,KAAK,CAAE,gCAAgC,CACvCC,KAAK,CAAE,sCAAsC,CAC7CE,KAAK,CAAE,sEAAsE,CAC7E7jB,OAAO,CAAE,mEACX,CAAC,CACDmkB,QAAQ,CAAE,CACR3/B,KAAK,CAAE,yBAAyB,CAChCy/B,OAAO,CAAE,8HACX,CAAC,CACDG,QAAQ,CAAE,CACR5/B,KAAK,CAAE,wBAAwB,CAC/By/B,OAAO,CAAE,8JACX,CAAC,CACDI,QAAQ,CAAE,CACR7/B,KAAK,CAAE,sBAAsB,CAC7By/B,OAAO,CAAE,yHACX,CAAC,CACDK,QAAQ,CAAE,CACR9/B,KAAK,CAAE,2BAA2B,CAClCy/B,OAAO,CAAE,kKACX,CAAC,CACDM,SAAS,CAAE,CACT//B,KAAK,CAAE,gBAAgB,CACvBy/B,OAAO,CAAE,kEAAkE,CAC3ErzB,KAAK,CAAE,2BACT,CACF,CAAC,CACD4zB,QAAQ,CAAE,CACRC,MAAM,CAAE,CACNjgC,KAAK,CAAE,eAAe,CACtBg/B,QAAQ,CAAE,CACRh/B,KAAK,CAAE,wDAAwD,CAC/DQ,WAAW,CAAE,wHAAwH,CACrI0/B,KAAK,CAAE,CACL,uEAAuE,CACvE,iGAAiG,CAErG,CAAC,CACDd,QAAQ,CAAE,CACRp/B,KAAK,CAAE,oEAAoE,CAC3EQ,WAAW,CAAE,8FAA8F,CAC3G0/B,KAAK,CAAE,CACL,6EAA6E,CAC7E,mEAAmE,CACnE,kFAAkF,CAClF,sDAAsD,CACtD,0EAA0E,CAC1E,4CAA4C,CAC5C,sDAAsD,CAE1D,CAAC,CACDX,QAAQ,CAAE,CACRv/B,KAAK,CAAE,8BAA8B,CACrCQ,WAAW,CAAE,iLACf,CAAC,CACDgb,OAAO,CAAE,CACPxb,KAAK,CAAE,YAAY,CACnBoM,KAAK,CAAE,8BACT,CACF,CAAC,CACD+zB,cAAc,CAAE,CACdngC,KAAK,CAAE,4BAA4B,CACnC8b,QAAQ,CAAE,6GAA6G,CACvHkjB,QAAQ,CAAE,CACRh/B,KAAK,CAAE,mBAAmB,CAC1BogC,MAAM,CAAE,CACN,2FAA2F,CAC3F,sEAAsE,CACtE,yEAAyE,CACzE,kHAAkH,CAEtH,CAAC,CACDhB,QAAQ,CAAE,CACRp/B,KAAK,CAAE,aAAa,CACpBogC,MAAM,CAAE,CACN,iEAAiE,CACjE,oGAAoG,CACpG,6GAA6G,CAC7G,oEAAoE,CAExE,CAAC,CACDb,QAAQ,CAAE,CACRv/B,KAAK,CAAE,gCAAgC,CACvCogC,MAAM,CAAE,CACN,sCAAsC,CACtC,0DAA0D,CAC1D,4GAA4G,CAC5G,wFAAwF,CAE5F,CAAC,CACDZ,QAAQ,CAAE,CACRx/B,KAAK,CAAE,mBAAmB,CAC1BogC,MAAM,CAAE,CACN,+GAA+G,CAC/G,8EAA8E,CAElF,CAAC,CACDV,QAAQ,CAAE,CACR1/B,KAAK,CAAE,sCAAsC,CAC7CogC,MAAM,CAAE,CACN,+FAA+F,CAC/F,mEAAmE,CAEvE,CAAC,CACDT,QAAQ,CAAE,CACR3/B,KAAK,CAAE,+BAA+B,CACtCogC,MAAM,CAAE,CACN,gGAAgG,CAChG,8EAA8E,CAElF,CAAC,CACDR,QAAQ,CAAE,CACR5/B,KAAK,CAAE,wDAAwD,CAC/DogC,MAAM,CAAE,CACN,wIAAwI,CACxI,8EAA8E,CAElF,CAAC,CACDP,QAAQ,CAAE,CACR7/B,KAAK,CAAE,wBAAwB,CAC/BQ,WAAW,CAAE,kFAAkF,CAC/F4L,KAAK,CAAE,2BACT,CAAC,CACD0Q,QAAQ,CAAE,CACRujB,cAAc,CAAE,iBAAiB,CACjCC,kBAAkB,CAAE,qHAAqH,CACzIC,kBAAkB,CAAE,qBAAqB,CACzCC,sBAAsB,CAAE,2GAA2G,CACnIC,eAAe,CAAE,kBAAkB,CACnCC,mBAAmB,CAAE,oFAAoF,CACzGC,mBAAmB,CAAE,sBAAsB,CAC3CC,uBAAuB,CAAE,yEAC3B,CACF,CAAC,CACDC,mBAAmB,CAAE,CACnB7gC,KAAK,CAAE,gDAAgD,CACvD8b,QAAQ,CAAE,6MAA6M,CACvNglB,aAAa,CAAE,CACb9gC,KAAK,CAAE,mBAAmB,CAC1B8F,OAAO,CAAE,CACP9F,KAAK,CAAE,qBAAqB,CAC5BogC,MAAM,CAAE,CACN,uEAAuE,CACvE,kFAAkF,CAEtF,CAAC,CACDW,YAAY,CAAE,CACZ/gC,KAAK,CAAE,yBAAyB,CAChCogC,MAAM,CAAE,CACN,sHAAsH,CACtH,uGAAuG,CACvG,mIAAmI,CAEvI,CAAC,CACDY,YAAY,CAAE,CACZhhC,KAAK,CAAE,2BAA2B,CAClCogC,MAAM,CAAE,CACN,2HAA2H,CAC3H,+JAA+J,CAEnK,CAAC,CACDa,WAAW,CAAE,CACXjhC,KAAK,CAAE,kBAAkB,CACzBogC,MAAM,CAAE,CACN,wEAAwE,CACxE,kJAAkJ,CAEtJ,CACF,CAAC,CACDc,WAAW,CAAE,CACXlhC,KAAK,CAAE,iBAAiB,CACxBmhC,YAAY,CAAE,CACZnhC,KAAK,CAAE,kBAAkB,CACzBogC,MAAM,CAAE,CACN,yGAAyG,CAE7G,CAAC,CACDW,YAAY,CAAE,CACZ/gC,KAAK,CAAE,yCAAyC,CAChDogC,MAAM,CAAE,CACN,oGAAoG,CACpG,2HAA2H,CAE/H,CAAC,CACDY,YAAY,CAAE,CACZhhC,KAAK,CAAE,4BAA4B,CACnCogC,MAAM,CAAE,CACN,qGAAqG,CACrG,2GAA2G,CAE/G,CAAC,CACDa,WAAW,CAAE,CACXjhC,KAAK,CAAE,kBAAkB,CACzBogC,MAAM,CAAE,CACN,mDAAmD,CACnD,wIAAwI,CAE5I,CACF,CAAC,CACDgB,YAAY,CAAE,CACZphC,KAAK,CAAE,eAAe,CACtBogC,MAAM,CAAE,CACN,iEAAiE,CACjE,0JAA0J,CAE9J,CAAC,CACDiB,OAAO,CAAE,CACPC,WAAW,CAAE,cAAc,CAC3BC,SAAS,CAAE,YAAY,CACvBC,gBAAgB,CAAE,oCAAoC,CACtDC,WAAW,CAAE,wBAAwB,CACrCC,aAAa,CAAE,4BAA4B,CAC3CC,kBAAkB,CAAE,+BAA+B,CACnDC,cAAc,CAAE,2BAA2B,CAC3CC,qBAAqB,CAAE,gCAAgC,CACvDC,aAAa,CAAE,+EACjB,CACF,CACF,CAAC,CACDtmB,OAAO,CAAE,CACPxb,KAAK,CAAE,YAAY,CACnB8b,QAAQ,CAAE,6JAA6J,CACvKimB,cAAc,CAAE,CACd/hC,KAAK,CAAE,oBAAoB,CAC3BgiC,OAAO,CAAE,eAAe,CACxBC,OAAO,CAAE,0CACX,CAAC,CACD71B,KAAK,CAAE,CACLpM,KAAK,CAAE,mBAAmB,CAC1BiiC,OAAO,CAAE,2BAA2B,CACpCC,IAAI,CAAE,8DACR,CAAC,CACD5tB,KAAK,CAAE,CACLtU,KAAK,CAAE,qBAAqB,CAC5BmiC,MAAM,CAAE,eAAe,CACvBD,IAAI,CAAE,gFACR,CAAC,CACDE,OAAO,CAAE,CACPpiC,KAAK,CAAE,YAAY,CACnBqiC,GAAG,CAAE,uBACP,CACF,CAAC,CACDC,gBAAgB,CAAE,mBAAmB,CACrC/2B,KAAK,CAAE,CACLvL,KAAK,CAAE,UAAU,CACjBwL,KAAK,CAAE,2QAA2Q,CAClRC,OAAO,CAAE,oMAAoM,CAC7MC,WAAW,CAAE,eAAe,CAC5BC,QAAQ,CAAE,CACRC,cAAc,CAAE,6EAA6E,CAC7FC,oBAAoB,CAAE,sEAAsE,CAC5FC,gBAAgB,CAAE,gHAAgH,CAClIC,eAAe,CAAE,+HAA+H,CAChJC,cAAc,CAAE,sJAClB,CAAC,CACDC,UAAU,CAAE,aAAa,CACzBC,WAAW,CAAE,mPAAmP,CAChQxH,SAAS,CAAE,YAAY,CACvByH,WAAW,CAAE,6DAA6D,CAC1EC,KAAK,CAAE,2BACT,CACF,CACF,CAAC,CACDm2B,EAAE,CAAE,CACF3iC,WAAW,CAAE,CACXC,OAAO,CAAE,iBAAiB,CAC1BC,OAAO,CAAE,UAAU,CACnBC,MAAM,CAAE,CACNC,KAAK,CAAE,SAAS,CAChBC,OAAO,CAAE,QAAQ,CACjBC,cAAc,CAAE,eAAe,CAC/BC,kBAAkB,CAAE,eAAe,CACnCC,eAAe,CAAE,gBAAgB,CACjCC,cAAc,CAAE,2BAA2B,CAC3CC,yBAAyB,CAAE,+CAA+C,CAC1EC,IAAI,CAAE,SAAS,CACfC,WAAW,CAAE,OAAO,CACpBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,OAAO,CACfC,OAAO,CAAE,cAAc,CACvBC,SAAS,CAAE,OAAO,CAClBC,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,MAAM,CACjBC,OAAO,CAAE,KAAK,CACdC,UAAU,CAAE,oBAAoB,CAChCC,UAAU,CAAE,oBAAoB,CAChCC,OAAO,CAAE,QAAQ,CACjBC,OAAO,CAAE,QAAQ,CACjBC,WAAW,CAAE,sBAAsB,CACnCC,QAAQ,CAAE,aAAa,CACvBC,aAAa,CAAE,2BAA2B,CAC1CC,GAAG,CAAE,KAAK,CACVC,cAAc,CAAE,oCAAoC,CACpDC,sBAAsB,CAAE,8CAA8C,CACtEC,aAAa,CAAE,cAAc,CAC7BC,aAAa,CAAE,cAAc,CAC7BC,aAAa,CAAE,aAAa,CAC5BsB,aAAa,CAAE,iBAAiB,CAChCC,eAAe,CAAE,wEAAwE,CACzFC,OAAO,CAAE,YAAY,CACrBC,MAAM,CAAE,MAAM,CACdC,QAAQ,CAAE,yBAAyB,CACnCC,iBAAiB,CAAE,wBACrB,CAAC,CACD1B,UAAU,CAAE,CACV/B,KAAK,CAAE,aAAa,CACpBgC,gBAAgB,CAAE,eAAe,CACjCC,iBAAiB,CAAE,SAAS,CAC5BC,iBAAiB,CAAE,aAAa,CAChCC,aAAa,CAAE,mBAAmB,CAClC5B,IAAI,CAAE,SAAS,CACfE,MAAM,CAAE,QAAQ,CAChB2B,WAAW,CAAE,wBAAwB,CACrC1B,MAAM,CAAE,QAAQ,CAChB2B,OAAO,CAAE,WAAW,CACpBC,MAAM,CAAE,OAAO,CACfC,MAAM,CAAE,OAAO,CACf1B,OAAO,CAAE,cAAc,CACvB2B,UAAU,CAAE,cAAc,CAC1B1B,SAAS,CAAE,OAAO,CAClBC,MAAM,CAAE,KAAK,CACbC,SAAS,CAAE,MAAM,CACjByB,aAAa,CAAE,+BAA+B,CAC9CC,aAAa,CAAE,kCAAkC,CACjDC,mBAAmB,CAAE,eAAe,CACpCC,gBAAgB,CAAE,0BAA0B,CAC5CC,gBAAgB,CAAE,0BAA0B,CAC5CC,eAAe,CAAE,wBAAwB,CACzCC,eAAe,CAAE,wBAAwB,CACzCC,wBAAwB,CAAE,wBAAwB,CAClDC,eAAe,CAAE,oDAAoD,CACrEC,cAAc,CAAE,iCAAiC,CACjDC,iBAAiB,CAAE,mBAAmB,CACtCC,aAAa,CAAE,iBAAiB,CAChCC,eAAe,CAAE,wEAAwE,CACzFC,OAAO,CAAE,YAAY,CACrBC,MAAM,CAAE,MAAM,CACdC,QAAQ,CAAE,yBAAyB,CACnCC,iBAAiB,CAAE,wBAAwB,CAC3CC,gBAAgB,CAAE,2BAA2B,CAC7CC,mBAAmB,CAAE,2CAA2C,CAChEZ,eAAe,CAAE,wBACnB,CAAC,CACDa,KAAK,CAAE,CACLC,oBAAoB,CAAE,CACpB7D,KAAK,CAAE,mBAAmB,CAC1B8D,YAAY,CAAE,cAAc,CAC5BC,GAAG,CAAE,MAAM,CACXxD,IAAI,CAAE,SAAS,CACfc,OAAO,CAAE,QAAQ,CACjBZ,MAAM,CAAE,QAAQ,CAChB2B,WAAW,CAAE,wBAAwB,CACrC1B,MAAM,CAAE,QAAQ,CAChBsD,KAAK,CAAE,WAAW,CAClB3B,OAAO,CAAE,WAAW,CACpB4B,OAAO,CAAE,QAAQ,CACjBC,MAAM,CAAE,KAAK,CACbC,iBAAiB,CAAE,oBAAoB,CACvCC,gBAAgB,CAAE,WAAW,CAC7BC,aAAa,CAAE,oBAAoB,CACnCC,iBAAiB,CAAE,gBAAgB,CACnCC,cAAc,CAAE,2CAA2C,CAC3DC,aAAa,CAAE,wBAAwB,CACvCC,eAAe,CAAE,qBACnB,CACF,CAAC,CACDC,SAAS,CAAE,CACT1E,KAAK,CAAE,YAAY,CACnB2E,IAAI,CAAE,aAAa,CACnBC,YAAY,CAAE,MAAM,CACpBC,WAAW,CAAE,OAAO,CACpBC,cAAc,CAAE,QAAQ,CACxBC,WAAW,CAAE,gBAAgB,CAC7BC,SAAS,CAAE,MAAM,CACjBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,eAAe,CACrBC,WAAW,CAAE,0CAA0C,CACvD3C,aAAa,CAAE,+BAA+B,CAC9C4C,SAAS,CAAE,6DAA6D,CACxEC,iBAAiB,CAAE,kBACrB,CAAC,CACDC,UAAU,CAAE,CACVvF,KAAK,CAAE,QAAQ,CACfiF,OAAO,CAAE,SAAS,CAClBN,IAAI,CAAE,OAAO,CACbpE,IAAI,CAAE,SAAS,CACfG,MAAM,CAAE,QAAQ,CAChBG,OAAO,CAAE,cAAc,CACvB2E,QAAQ,CAAE,SAAS,CACnBC,UAAU,CAAE,eAAe,CAC3BC,UAAU,CAAE,oBAAoB,CAChCC,WAAW,CAAE,QAAQ,CACrBC,UAAU,CAAE,YAAY,CACxBC,aAAa,CAAE,8BACjB,CAAC,CACDC,OAAO,CAAE,CACP9F,KAAK,CAAE,SAAS,CAChB+F,cAAc,CAAE,YAAY,CAC5BC,cAAc,CAAE,WAAW,CAC3BC,YAAY,CAAE,eAAe,CAC7BC,gBAAgB,CAAE,yFAAyF,CAC3GC,cAAc,CAAE,YAAY,CAC5BC,gBAAgB,CAAE,gCAAgC,CAClD/E,OAAO,CAAE,QAAQ,CACjBgF,GAAG,CAAE,OAAO,CACZ9F,IAAI,CAAE,SAAS,CACf+F,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,OAAO,CACdC,cAAc,CAAE,aAAa,CAC7BC,aAAa,CAAE,cAAc,CAC7BC,mBAAmB,CAAE,iBAAiB,CACtCC,qBAAqB,CAAE,sDAAsD,CAC7EC,aAAa,CAAE,mBAAmB,CAClCM,cAAc,CAAE,aAAa,CAC7BC,SAAS,CAAE,eAAe,CAC1BC,aAAa,CAAE,oCAAoC,CACnDzE,mBAAmB,CAAE,6CAA6C,CAClEkE,WAAW,CAAE,gBAAgB,CAC7BC,MAAM,CAAE,OAAO,CACfC,YAAY,CAAE,gBAAgB,CAC9BC,QAAQ,CAAE,gBAAgB,CAC1BC,cAAc,CAAE,qBAAqB,CACrCwY,eAAe,CAAE,oBAAoB,CACrCC,SAAS,CAAE,aAAa,CACxBC,OAAO,CAAE,aAAa,CACtBC,cAAc,CAAE,WAAW,CAC3BC,IAAI,CAAE,IAAI,CACVC,EAAE,CAAE,KAAK,CACTC,aAAa,CAAE,YAAY,CAC3B1Y,cAAc,CAAE,YAAY,CAC5BA,cAAc,CAAE,gBAAgB,CAChCC,QAAQ,CAAE,OAAO,CACjBC,UAAU,CAAE,+BAA+B,CAC3CC,UAAU,CAAE,8BAA8B,CAC1CC,cAAc,CAAE,mBAAmB,CACnCC,iBAAiB,CAAE,wEAAwE,CAC3FC,cAAc,CAAE,eAClB,CAAC,CACDC,OAAO,CAAE,CACP5H,KAAK,CAAE,WAAW,CAClB6H,WAAW,CAAE,aAAa,CAC1BC,SAAS,CAAE,UAAU,CACrBC,cAAc,CAAE,gBAAgB,CAChCC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,SAAS,CAClB1F,MAAM,CAAE,eAAe,CACvB2F,MAAM,CAAE,eAAe,CACvBC,MAAM,CAAE,aAAa,CACrBC,aAAa,CAAE,2CAA2C,CAC1DC,aAAa,CAAE,aAAa,CAC5BC,UAAU,CAAE,oHAAoH,CAChIC,kBAAkB,CAAE,8DAA8D,CAClFC,mBAAmB,CAAE,0FAA0F,CAC/GC,wBAAwB,CAAE,8BAA8B,CACxDC,SAAS,CAAE,0BAA0B,CACrCC,UAAU,CAAE,QAAQ,CACpBC,UAAU,CAAE,eAAe,CAC3BC,aAAa,CAAE,wBAAwB,CACvCC,mBAAmB,CAAE,wBAAwB,CAC7CC,mBAAmB,CAAE,sBAAsB,CAC3CC,WAAW,CAAE,sBAAsB,CACnCC,cAAc,CAAE,kBAAkB,CAClCC,kBAAkB,CAAE,8BAA8B,CAClDC,aAAa,CAAE,eAAe,CAC9BC,YAAY,CAAE,mBAAmB,CACjCC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,gBAAgB,CAC3BC,QAAQ,CAAE,cAAc,CACxBC,EAAE,CAAE,IAAI,CACRC,KAAK,CAAE,MAAM,CACbC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,cAAc,CAC5BC,UAAU,CAAE,YAAY,CACxBC,cAAc,CAAE,oBAAoB,CACpCC,eAAe,CAAE,oBAAoB,CACrCC,OAAO,CAAE,UAAU,CACnBC,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,WAAW,CACnBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,YAAY,CAAE,gBAAgB,CAC9BC,WAAW,CAAE,eAAe,CAC5BC,SAAS,CAAE,oBAAoB,CAC/BC,mBAAmB,CAAE,uCAAuC,CAC5DC,YAAY,CAAE,WAAW,CACzBC,aAAa,CAAE,iBAAiB,CAChCC,SAAS,CAAE,YAAY,CACvBC,WAAW,CAAE,UAAU,CACvBC,gBAAgB,CAAE,8BAA8B,CAChDC,SAAS,CAAE,YAAY,CACvBC,WAAW,CAAE,YAAY,CACzBC,YAAY,CAAE,qBAAqB,CACnCC,kBAAkB,CAAE,qBAAqB,CACzCC,kBAAkB,CAAE,mBAAmB,CACvCC,UAAU,CAAE,mBAAmB,CAC/BC,kBAAkB,CAAE,+BAA+B,CACnDC,aAAa,CAAE,eAAe,CAC9BC,OAAO,CAAE,iBACX,CAAC,CACDC,KAAK,CAAE,CACLvL,KAAK,CAAE,QAAQ,CACfwL,KAAK,CAAE,+NAA+N,CACtOC,OAAO,CAAE,+JAA+J,CACxKC,WAAW,CAAE,YAAY,CACzBC,QAAQ,CAAE,CACRC,cAAc,CAAE,2EAA2E,CAC3FC,oBAAoB,CAAE,iEAAiE,CACvFC,gBAAgB,CAAE,wFAAwF,CAC1GC,eAAe,CAAE,8FAA8F,CAC/GC,cAAc,CAAE,8GAClB,CAAC,CACDC,UAAU,CAAE,SAAS,CACrBC,WAAW,CAAE,kLAAkL,CAC/LxH,SAAS,CAAE,cAAc,CACzByH,WAAW,CAAE,gEAAgE,CAC7EC,KAAK,CAAE,2BACT,CAAC,CACDC,GAAG,CAAE,CACHC,IAAI,CAAE,iBAAiB,CACvBC,SAAS,CAAE,sBAAsB,CACjCC,OAAO,CAAE,yBACX,CAAC,CACDC,KAAK,CAAE,CACLH,IAAI,CAAE,iBACR,CAAC,CACDI,MAAM,CAAE,CACNC,cAAc,CAAE,aAAa,CAC7BC,OAAO,CAAE,iBAAiB,CAC1BC,QAAQ,CAAE,WAAW,CACrBC,OAAO,CAAE,cAAc,CACvBxK,MAAM,CAAE,OAAO,CACfyK,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,QAAQ,CAClBC,OAAO,CAAE,OAAO,CAChB/E,MAAM,CAAE,OAAO,CACfgF,OAAO,CAAE,MAAM,CACfC,KAAK,CAAE,KAAK,CACZC,IAAI,CAAE,KAAK,CACXC,aAAa,CAAE,aAAa,CAC5BC,MAAM,CAAE,eAAe,CACvBlB,KAAK,CAAE,mBAAmB,CAC1BmB,WAAW,CAAE,WAAW,CACxBC,MAAM,CAAE,KAAK,CACbC,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,aAAa,CACvBC,QAAQ,CAAE,cAAc,CACxBrB,IAAI,CAAE,OAAO,CACbsB,eAAe,CAAE,mBAAmB,CACpCC,QAAQ,CAAE,YAAY,CACtBC,QAAQ,CAAE,SAAS,CACnBC,MAAM,CAAE,OAAO,CACfC,IAAI,CAAE,KAAK,CACXC,MAAM,CAAE,MAAM,CACd1L,MAAM,CAAE,OAAO,CACf2L,MAAM,CAAE,UAAU,CAClB7L,OAAO,CAAE,WAAW,CACpB8L,IAAI,CAAE,OAAO,CACbhG,MAAM,CAAE,KAAK,CACbiG,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,UAAU,CACnB/M,WAAW,CAAE,sBAAsB,CACnCgN,MAAM,CAAE,CACN/B,SAAS,CAAE,sBAAsB,CACjCC,OAAO,CAAE,yBACX,CAAC,CACD+B,QAAQ,CAAE,OAAO,CACjBF,OAAO,CAAE,UACX,CAAC,CACDvB,OAAO,CAAE,CACP9M,KAAK,CAAE,cAAc,CACrB2N,QAAQ,CAAE,cAAc,CACxBQ,IAAI,CAAE,oBAAoB,CAC1Bf,IAAI,CAAE,eAAe,CACrB9K,MAAM,CAAE,OAAO,CACf4K,OAAO,CAAE,6BAA6B,CACtCC,KAAK,CAAE,kCAAkC,CACzCqB,aAAa,CAAE,6BAA6B,CAC5CC,qBAAqB,CAAE,4BAA4B,CACnDC,yBAAyB,CAAE,iCAAiC,CAC5DE,gBAAgB,CAAE,uBAAuB,CACzCC,MAAM,CAAE,CACN3G,MAAM,CAAE,wBAAwB,CAChC4G,cAAc,CAAE,uBAAuB,CACvCC,gBAAgB,CAAE,kCAAkC,CACpDC,eAAe,CAAE,+BAA+B,CAChDtJ,UAAU,CAAE,gCAAgC,CAC5CuJ,YAAY,CAAE,sCAAsC,CACpDC,aAAa,CAAE,wBAAwB,CACvCC,WAAW,CAAE,qBAAqB,CAClCC,YAAY,CAAE,kBAAkB,CAChCC,YAAY,CAAE,yBAChB,CAAC,CACDC,SAAS,CAAE,oBAAoB,CAC/BC,WAAW,CAAE,gBAAgB,CAC7BZ,QAAQ,CAAE,iBAAiB,CAC3Ba,cAAc,CAAE,mBAAmB,CACnCpD,KAAK,CAAE,mBAAmB,CAC1B2B,MAAM,CAAE,OAAO,CACfiB,eAAe,CAAE,qBAAqB,CACtCS,WAAW,CAAE,qBAAqB,CAClC7B,eAAe,CAAE,mBAAmB,CACpC8B,gBAAgB,CAAE,yBAAyB,CAC3CC,wBAAwB,CAAE,yBAAyB,CACnDC,WAAW,CAAE,UAAU,CACvBC,aAAa,CAAE,YAAY,CAC3BC,kBAAkB,CAAE,YAAY,CAChCC,oBAAoB,CAAE,sEAAsE,CAC5FC,iBAAiB,CAAE,iDAAiD,CACpEC,cAAc,CAAE,kBAAkB,CAClCC,gBAAgB,CAAE,mBAAmB,CACrCC,qBAAqB,CAAE,8DAA8D,CACrFC,UAAU,CAAE,YAAY,CACxBhI,aAAa,CAAE,aAAa,CAC5BiI,cAAc,CAAE,0CAA0C,CAC1DC,aAAa,CAAE,mCAAmC,CAClDjB,YAAY,CAAE,aAAa,CAC3BkB,eAAe,CAAE,2BAA2B,CAC5CC,kBAAkB,CAAE,4BAA4B,CAChDC,oBAAoB,CAAE,6EAA6E,CACnGC,kBAAkB,CAAE,oBAAoB,CACxCC,YAAY,CAAE,CACZ9P,OAAO,CAAE,iCAAiC,CAC1C+P,QAAQ,CAAE,gCAAgC,CAC1CC,QAAQ,CAAE,2BAA2B,CACrCC,WAAW,CAAE,aAAa,CAC1BC,UAAU,CAAE,gBAAgB,CAC5BC,UAAU,CAAE,iBAAiB,CAC7BC,WAAW,CAAE,4FACf,CAAC,CACD5P,OAAO,CAAE,CACP6P,WAAW,CAAE,CACXlR,KAAK,CAAE,kBAAkB,CACzBQ,WAAW,CAAE,wFAAwF,CACrG2Q,YAAY,CAAE,iBAAiB,CAC/BC,iBAAiB,CAAE,mBAAmB,CACtCC,eAAe,CAAE,cAAc,CAC/BC,iBAAiB,CAAE,gBAAgB,CACnCC,OAAO,CAAE,UAAU,CACnBC,OAAO,CAAE,OAAO,CAChBC,WAAW,CAAE,cAAc,CAC3BC,MAAM,CAAE,aAAa,CACrBC,SAAS,CAAE,eAAe,CAC1BzE,OAAO,CAAE,uBAAuB,CAChC0E,UAAU,CAAE,sCAAsC,CAClD5E,QAAQ,CAAE,0BAA0B,CACpC7E,MAAM,CAAE,aAAa,CACrB0J,UAAU,CAAE,WAAW,CACvBC,kBAAkB,CAAE,6DAA6D,CACjFC,aAAa,CAAE,+CAA+C,CAC9DC,aAAa,CAAE,0CAA0C,CACzDC,eAAe,CAAE,wBAAwB,CACzCC,gBAAgB,CAAE,yCACpB,CAAC,CACDC,cAAc,CAAE,kBAAkB,CAClCP,UAAU,CAAE,qCAAqC,CACjDQ,WAAW,CAAE,aAAa,CAC1BC,yBAAyB,CAAE,0GAA0G,CACrIC,oCAAoC,CAAE,sHAAsH,CAC5JC,gCAAgC,CAAE,mEAAmE,CACrGC,kBAAkB,CAAE,qBAAqB,CACzCC,6BAA6B,CAAE,4DAA4D,CAC3FC,kBAAkB,CAAE,eAAe,CACnCC,aAAa,CAAE,gCAAgC,CAC/CC,oBAAoB,CAAE,uBAAuB,CAC7CC,iBAAiB,CAAE,8BAA8B,CACjDC,SAAS,CAAE,OAAO,CAClBC,WAAW,CAAE,YAAY,CACzBC,YAAY,CAAE,eAAe,CAC7BC,gBAAgB,CAAE,iBAAiB,CACnCC,gBAAgB,CAAE,iBAAiB,CACnCC,gBAAgB,CAAE,gBAAgB,CAClCC,cAAc,CAAE,cAAc,CAC9BC,gBAAgB,CAAE,oDAAoD,CACtEC,iBAAiB,CAAE,sDAAsD,CACzEC,iBAAiB,CAAE,6DAA6D,CAChFlG,aAAa,CAAE,aAAa,CAC5BmG,SAAS,CAAE,YAAY,CACvBC,gBAAgB,CAAE,sDAAsD,CACxEC,SAAS,CAAE,YAAY,CACvBC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,gBAAgB,CAC1BC,SAAS,CAAE,MAAM,CACjBC,WAAW,CAAE,UAAU,CACvBC,aAAa,CAAE,8BAA8B,CAC7CC,YAAY,CAAE,4BAA4B,CAC1CC,MAAM,CAAE,cAAc,CACtBC,UAAU,CAAE,aAAa,CACzB7R,OAAO,CAAE,WAAW,CAEpB8R,oBAAoB,CAAE,sBAAsB,CAC5CrH,OAAO,CAAE,CACP9M,KAAK,CAAE,YAAY,CACnBoU,YAAY,CAAE,mBAAmB,CACjCC,YAAY,CAAE,iBAAiB,CAC/BC,KAAK,CAAE,YAAY,CACnBC,OAAO,CAAE,QAAQ,CACjBC,SAAS,CAAE,cAAc,CACzBC,cAAc,CAAE,YAAY,CAC5BC,iBAAiB,CAAE,CACjB1U,KAAK,CAAE,cAAc,CACrB2U,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,YAAY,CACrBC,MAAM,CAAE,UAAU,CAClBC,OAAO,CAAE,WAAW,CACpBC,IAAI,CAAE,UAAU,CAChBC,OAAO,CAAE,SAAS,CAClBC,UAAU,CAAE,aAAa,CACzBC,KAAK,CAAE,WAAW,CAClBC,OAAO,CAAE,WAAW,CACpBC,KAAK,CAAE,SAAS,CAChBC,OAAO,CAAE,UAAU,CACnBC,MAAM,CAAE,WAAW,CACnBC,OAAO,CAAE,WAAW,CACpBC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,SAAS,CACjBC,IAAI,CAAE,aAAa,CACnBC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,YAAY,CACrBC,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,WAAW,CACnBC,MAAM,CAAE,mBAAmB,CAC3BC,WAAW,CAAE,wBACf,CAAC,CACDC,WAAW,CAAE,eAAe,CAC5BC,cAAc,CAAE,UAAU,CAC1BC,kBAAkB,CAAE,mBAAmB,CACvCC,cAAc,CAAE,iBAAiB,CACjCvQ,cAAc,CAAE,eAAe,CAC/BwQ,QAAQ,CAAE,iBAAiB,CAC3BC,aAAa,CAAE,aAAa,CAC5BlI,QAAQ,CAAE,CACRmI,GAAG,CAAE,cAAc,CACnBC,GAAG,CAAE,MAAM,CACXC,GAAG,CAAE,eACP,CAAC,CACDC,UAAU,CAAE,CACVC,QAAQ,CAAE,mBAAmB,CAC7BC,YAAY,CAAE,mBAAmB,CACjCC,QAAQ,CAAE,oBAAoB,CAC9BC,MAAM,CAAE,yBACV,CACF,CACF,CAAC,CACDC,OAAO,CAAE,CACPlJ,IAAI,CAAE,KAAK,CACXC,MAAM,CAAE,MACV,CACF,CAAC,CACDkJ,GAAG,CAAE,CACHC,IAAI,CAAE,UAAU,CAChBC,SAAS,CAAE,aAAa,CACxBC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,QAAQ,CAClBC,YAAY,CAAE,sBAAsB,CACpCC,UAAU,CAAE,WAAW,CACvBC,SAAS,CAAE,QAAQ,CACnBC,YAAY,CAAE,SAAS,CACvBC,cAAc,CAAE,kBAAkB,CAClC/T,oBAAoB,CAAE,mBAAmB,CACzCgU,WAAW,CAAE,cAAc,CAC3BC,UAAU,CAAE,SAAS,CACrBC,QAAQ,CAAE,YAAY,CACtBC,IAAI,CAAE,WAAW,CACjBC,KAAK,CAAE,cAAc,CACrBC,QAAQ,CAAE,YAAY,CACtBC,MAAM,CAAE,cAAc,CACtB3K,MAAM,CAAE,KAAK,CACb4K,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,YAAY,CACrBC,MAAM,CAAE,SAAS,CACjBC,cAAc,CAAE,kBAAkB,CAClCC,gBAAgB,CAAE,kBAAkB,CACpCC,gBAAgB,CAAE,kBACpB,CAAC,CACDjB,YAAY,CAAE,CACZxX,KAAK,CAAE,sBAAsB,CAC7B0Y,YAAY,CAAE,kBAAkB,CAChCC,eAAe,CAAE,kBAAkB,CACnCC,eAAe,CAAE,oBAAoB,CACrCC,WAAW,CAAE,oBAAoB,CACjCrL,MAAM,CAAE,OAAO,CACf7I,IAAI,CAAE,OAAO,CACbZ,GAAG,CAAE,MAAM,CACXwT,QAAQ,CAAE,MAAM,CAChBD,QAAQ,CAAE,QAAQ,CAClBwB,WAAW,CAAE,YAAY,CACzBC,OAAO,CAAE,OAAO,CAChBC,IAAI,CAAE,UAAU,CAChBC,IAAI,CAAE,OAAO,CACbC,YAAY,CAAE,aAAa,CAC3BC,cAAc,CAAE,WAAW,CAC3BC,SAAS,CAAE,aAAa,CACxB/W,OAAO,CAAE,WAAW,CACpBgX,WAAW,CAAE,cAAc,CAC3BC,WAAW,CAAE,kBAAkB,CAC/BC,eAAe,CAAE,WAAW,CAC5BnY,OAAO,CAAE,MAAM,CACfC,OAAO,CAAE,MAAM,CACfmY,UAAU,CAAE,WAAW,CACvBC,YAAY,CAAE,UAAU,CACxBC,WAAW,CAAE,UAAU,CACvBC,mBAAmB,CAAE,kBAAkB,CACvCC,qBAAqB,CAAE,4CAA4C,CACnEC,kBAAkB,CAAE,0DAA0D,CAC9EvX,MAAM,CAAE,OAAO,CACfwX,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,mBAAmB,CAC9BC,oBAAoB,CAAE,WAAW,CACjCC,sBAAsB,CAAE,yCAAyC,CACjEC,sBAAsB,CAAE,kDAAkD,CAC1EC,mBAAmB,CAAE,wDAAwD,CAC7EC,qBAAqB,CAAE,WAAW,CAClCC,QAAQ,CAAE,eAAe,CACzB/Y,WAAW,CAAE,aAAa,CAC1BgZ,aAAa,CAAE,wBAAwB,CACvCC,YAAY,CAAE,eAAe,CAC7BC,iBAAiB,CAAE,eACrB,CAAC,CACD9N,MAAM,CAAE,CACN+N,QAAQ,CAAE,kBAAkB,CAC5BlL,WAAW,CAAE,gBAAgB,CAC7BmL,WAAW,CAAE,gBAAgB,CAC7B3M,MAAM,CAAE,OAAO,CACfC,IAAI,CAAE,KAAK,CACXC,MAAM,CAAE,MAAM,CACd0M,QAAQ,CAAE,gBAAgB,CAC1B3H,YAAY,CAAE,eAAe,CAC7B4H,aAAa,CAAE,eAAe,CAC9B5S,MAAM,CAAE,SAAS,CACjB6S,QAAQ,CAAE,eAAe,CACzBC,aAAa,CAAE,gBAAgB,CAC/BvM,QAAQ,CAAE,MAAM,CAChBwM,aAAa,CAAE,iBAAiB,CAChC9H,gBAAgB,CAAE,iBAAiB,CACnC+H,UAAU,CAAE,gBAAgB,CAC5BxE,QAAQ,CAAE,iBAAiB,CAC3BtI,MAAM,CAAE,UAAU,CAClBT,KAAK,CAAE,OACT,CAAC,CACDa,MAAM,CAAE,CACN2M,YAAY,CAAE,iBAAiB,CAC/BC,UAAU,CAAE,yBAAyB,CACrCC,QAAQ,CAAE,QAAQ,CAClBC,QAAQ,CAAE,QAAQ,CAClBC,OAAO,CAAE,OAAO,CAChBC,SAAS,CAAE,UAAU,CACrBC,UAAU,CAAE,aAAa,CACzBhQ,KAAK,CAAE,QAAQ,CACfiQ,OAAO,CAAE,UAAU,CACnBC,OAAO,CAAE,gBAAgB,CACzBC,KAAK,CAAE,aAAa,CACpBC,GAAG,CAAE,iBAAiB,CACtBC,OAAO,CAAE,aAAa,CACtBrP,SAAS,CAAE,wBAAwB,CACnCC,OAAO,CAAE,yBACX,CAAC,CACDqP,IAAI,CAAE,CACJ7b,KAAK,CAAE,sBAAsB,CAC7B8b,QAAQ,CAAE,oDAAoD,CAC9DC,aAAa,CAAE,aAAa,CAC5BC,aAAa,CAAE,WAAW,CAC1BC,QAAQ,CAAE,eACZ,CAAC,CACD7E,IAAI,CAAE,CACJ8E,QAAQ,CAAE,iBAAiB,CAC3BC,gBAAgB,CAAE,gFAAgF,CAClG7E,QAAQ,CAAE,MAAM,CAChB8E,cAAc,CAAE,gBAAgB,CAChCC,kBAAkB,CAAE,2CAA2C,CAC/DC,cAAc,CAAE,YAAY,CAC5BC,OAAO,CAAE,OAAO,CAChBC,WAAW,CAAE,kDAAkD,CAC/DC,YAAY,CAAE,2BAA2B,CACzCC,WAAW,CAAE,eAAe,CAC5BC,mBAAmB,CAAE,gFAAgF,CACrGC,YAAY,CAAE,kBAAkB,CAChCC,oBAAoB,CAAE,sEAAsE,CAC5FC,QAAQ,CAAE,CACRC,OAAO,CAAE,mBAAmB,CAC5BC,WAAW,CAAE,mCAAmC,CAChDC,QAAQ,CAAE,iBAAiB,CAC3BC,YAAY,CAAE,4BAA4B,CAC1CC,WAAW,CAAE,aAAa,CAC1BC,eAAe,CAAE,8BAA8B,CAC/CC,SAAS,CAAE,eAAe,CAC1BC,aAAa,CAAE,uCACjB,CAAC,CACDC,YAAY,CAAE,kBAAkB,CAChCC,oBAAoB,CAAE,6DAA6D,CACnFC,SAAS,CAAE,UAAU,CACrBC,SAAS,CAAE,cACb,CAAC,CACDH,YAAY,CAAE,CACZnc,OAAO,CAAE,MAAM,CACfqc,SAAS,CAAE,UACb,CAAC,CACDjQ,MAAM,CAAE,CACNqK,WAAW,CAAE,cAAc,CAC3B8F,OAAO,CAAE,SAAS,CAClB1Y,OAAO,CAAE,QAAQ,CACjB2Y,WAAW,CAAE,aAAa,CAC1BxF,QAAQ,CAAE,OAAO,CACjByF,YAAY,CAAE,aAAa,CAC3BC,UAAU,CAAE,YAAY,CACxB9V,MAAM,CAAE,SAAS,CACjB+V,SAAS,CAAE,UAAU,CACrBC,QAAQ,CAAE,SAAS,CACnBC,eAAe,CAAE,0BAA0B,CAC3CC,iBAAiB,CAAE,2BAA2B,CAC9CC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,OAAO,CAChBlC,QAAQ,CAAE,QAAQ,CAClBxE,SAAS,CAAE,QAAQ,CACnB2G,YAAY,CAAE,KAAK,CACnBC,YAAY,CAAE,aAAa,CAC3BL,eAAe,CAAE,2CAA2C,CAC5DvJ,iBAAiB,CAAE,cAAc,CACjC6J,eAAe,CAAE,WAAW,CAC5BC,UAAU,CAAE,gBAAgB,CAC5BC,eAAe,CAAE,sBAAsB,CACvCC,WAAW,CAAE,kBAAkB,CAC/BC,cAAc,CAAE,aAClB,CAAC,CACD7Y,OAAO,CAAE,CACPC,cAAc,CAAE,eAAe,CAC/BC,cAAc,CAAE,WAAW,CAC3BC,YAAY,CAAE,eAAe,CAC7BC,gBAAgB,CAAE,uFAAuF,CACzGC,cAAc,CAAE,aAAa,CAC7ByY,iBAAiB,CAAE,iCAAiC,CACpDC,WAAW,CAAE,sBAAsB,CACnC2jB,QAAQ,CAAE,YAAY,CACtB1jB,iBAAiB,CAAE,kBAAkB,CACrCC,YAAY,CAAE,cAAc,CAC5BxX,UAAU,CAAE,qBAAqB,CACjCC,UAAU,CAAE,oBAAoB,CAChCu2B,gBAAgB,CAAE,eAAe,CACjC12B,cAAc,CAAE,gBAAgB,CAChC2X,mBAAmB,CAAE,gBAAgB,CACrCC,WAAW,CAAE,WAAW,CACxBC,iBAAiB,CAAE,yBAAyB,CAC5CC,iBAAiB,CAAE,0BAA0B,CAC7CC,cAAc,CAAE,iCAAiC,CACjDC,wBAAwB,CAAE,qCAAqC,CAC/DC,eAAe,CAAE,+BAA+B,CAChDC,aAAa,CAAE,UAAU,CACzBjY,QAAQ,CAAE,OAAO,CACjBlB,gBAAgB,CAAE,iCAAiC,CACnDI,cAAc,CAAE,aAAa,CAC7BnF,OAAO,CAAE,QAAQ,CACjBgF,GAAG,CAAE,OAAO,CACZ9F,IAAI,CAAE,SAAS,CACf+F,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,OAAO,CACdE,aAAa,CAAE,cAAc,CAC7BC,mBAAmB,CAAE,iBAAiB,CACtCC,qBAAqB,CAAE,+EAA+E,CACtGC,aAAa,CAAE,mBAAmB,CAClCM,cAAc,CAAE,aAAa,CAC7BC,SAAS,CAAE,eAAe,CAC1BC,aAAa,CAAE,oCAAoC,CACnDzE,mBAAmB,CAAE,uDAAuD,CAC5E4L,QAAQ,CAAE,OAAO,CACjB1H,WAAW,CAAE,gBAAgB,CAC7BC,MAAM,CAAE,OAAO,CACfC,YAAY,CAAE,gBAAgB,CAC9BC,QAAQ,CAAE,gBAAgB,CAC1BC,cAAc,CAAE,qBAAqB,CACrCwY,eAAe,CAAE,oBAAoB,CACrCC,SAAS,CAAE,aAAa,CACxBC,OAAO,CAAE,aAAa,CACtBC,cAAc,CAAE,WAAW,CAC3BC,IAAI,CAAE,IAAI,CACVC,EAAE,CAAE,KAAK,CACTC,aAAa,CAAE,YAAY,CAC3B1Y,cAAc,CAAE,YAClB,CAAC,CACD2Y,QAAQ,CAAE,CACRhgB,KAAK,CAAE,SAAS,CAChBigB,WAAW,CAAE,oBAAoB,CACjCzf,WAAW,CAAE,wCAAwC,CACrD0f,iBAAiB,CAAE,wCAAwC,CAC3DC,cAAc,CAAE,cAAc,CAC9BC,UAAU,CAAE,2BAA2B,CACvC1a,UAAU,CAAE,8CAA8C,CAC1DrE,OAAO,CAAE,QAAQ,CACjBd,IAAI,CAAE,SAAS,CACf+F,IAAI,CAAE,OAAO,CACb+Z,SAAS,CAAE,YAAY,CACvB/Y,QAAQ,CAAE,OAAO,CACjBkK,OAAO,CAAE,OAAO,CAChB9Q,MAAM,CAAE,QAAQ,CAChB4f,YAAY,CAAE,CACZC,SAAS,CAAE,OAAO,CAClBzf,SAAS,CAAE,OAAO,CAClBE,SAAS,CAAE,MAAM,CACjBwf,cAAc,CAAE,qBAAqB,CACrCC,OAAO,CAAE,MACX,CAAC,CACDla,KAAK,CAAE,OAAO,CACdjE,MAAM,CAAE,OAAO,CACfoe,aAAa,CAAE,eAAe,CAC9BC,aAAa,CAAE,yEAAyE,CACxFC,kBAAkB,CAAE,uBAAuB,CAC3CC,6BAA6B,CAAE,mCAAmC,CAClEC,mBAAmB,CAAE,kBAAkB,CACvCC,UAAU,CAAE,iBAAiB,CAC7BC,aAAa,CAAE,mDAAmD,CAClEC,WAAW,CAAE,6CAA6C,CAC1DlB,aAAa,CAAE,UAAU,CACzBmB,WAAW,CAAE,cAAc,CAC3BC,QAAQ,CAAE,UAAU,CACpBC,SAAS,CAAE,UAAU,CACrBC,cAAc,CAAE,oBAAoB,CACpCC,gBAAgB,CAAE,6BAA6B,CAC/CC,aAAa,CAAE,iDAAiD,CAChEC,iBAAiB,CAAE,qBAAqB,CACxCC,eAAe,CAAE,mBAAmB,CACpCC,KAAK,CAAE,MAAM,CACbC,gBAAgB,CAAE,gBAAgB,CAClCC,gBAAgB,CAAE,kBAAkB,CACpCC,kBAAkB,CAAE,+BAA+B,CACnDC,eAAe,CAAE,wCAAwC,CACzDC,qBAAqB,CAAE,uBAAuB,CAC9CC,mBAAmB,CAAE,qBAAqB,CAC1CC,UAAU,CAAE,aAAa,CACzBC,eAAe,CAAE,mBAAmB,CACpCC,qBAAqB,CAAE,kDAAkD,CACzEC,cAAc,CAAE,cAAc,CAC9BC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YAAY,CACvBC,UAAU,CAAE,YAAY,CACxBC,UAAU,CAAE,YAAY,CACxBC,WAAW,CAAE,8BAA8B,CAC3CC,YAAY,CAAE,+BAA+B,CAC7CC,eAAe,CAAE,+CAA+C,CAChEC,gBAAgB,CAAE,mCAAmC,CACrDC,cAAc,CAAE,aAAa,CAC7BC,gBAAgB,CAAE,6BAA6B,CAC/CC,2BAA2B,CAAE,yCAAyC,CACtEC,iBAAiB,CAAE,4BAA4B,CAC/CC,eAAe,CAAE,mDAAmD,CACpEC,cAAc,CAAE,oDAAoD,CACpEC,eAAe,CAAE,qDAAqD,CACtEf,cAAc,CAAE,cAAc,CAC9BgB,aAAa,CAAE,qBAAqB,CACpCC,aAAa,CAAE,mBAAmB,CAClCC,eAAe,CAAE,+BAA+B,CAChDC,YAAY,CAAE,eAAe,CAC7BC,iBAAiB,CAAE,qBAAqB,CACxCC,cAAc,CAAE,YAAY,CAC5BC,eAAe,CAAE,sBACnB,CAAC,CACDC,cAAc,CAAE,CACdC,YAAY,CAAE,kBAAkB,CAChCC,eAAe,CAAE,0BAA0B,CAC3Crf,aAAa,CAAE,0BAA0B,CACzCoD,OAAO,CAAE,SAAS,CAClB6W,eAAe,CAAE,yBAAyB,CAC1CqF,cAAc,CAAE,wBAAwB,CACxC3F,UAAU,CAAE,SAAS,CACrB4F,gBAAgB,CAAE,kBAAkB,CACpCrM,SAAS,CAAE,QAAQ,CACnBjD,cAAc,CAAE,YAAY,CAC5BC,iBAAiB,CAAE,cAAc,CACjC2B,cAAc,CAAE,UAAU,CAC1B6H,iBAAiB,CAAE,2BAA2B,CAC9C8F,cAAc,CAAE,gBAAgB,CAChCtb,SAAS,CAAE,0BAA0B,CACrCub,WAAW,CAAE,iBAAiB,CAC9B7X,KAAK,CAAE,mBAAmB,CAC1BkI,KAAK,CAAE,YAAY,CACnB4P,QAAQ,CAAE,QAAQ,CAClB3P,OAAO,CAAE,QAAQ,CACjBC,SAAS,CAAE,eAAe,CAC1BgC,QAAQ,CAAE,iBAAiB,CAC3BK,UAAU,CAAE,QAAQ,CACpBsN,WAAW,CAAE,SAAS,CACtB5N,cAAc,CAAE,iBAAiB,CACjC6N,WAAW,CAAE,eAAe,CAC5Bpe,cAAc,CAAE,WAAW,CAC3ByQ,aAAa,CAAE,aAAa,CAC5B4N,EAAE,CAAE,gBAAgB,CACpBC,UAAU,CAAE,kBAAkB,CAC9BlO,WAAW,CAAE,eACf,CAAC,CACD4B,IAAI,CAAE,CACJuM,aAAa,CAAE,WAAW,CAC1BC,QAAQ,CAAE,SAAS,CACnBC,WAAW,CAAE,eAAe,CAC5Btf,IAAI,CAAE,OAAO,CACbM,UAAU,CAAE,eAAe,CAC3Bif,OAAO,CAAE,iBAAiB,CAC1BC,SAAS,CAAE,mBAAmB,CAC9B/X,OAAO,CAAE,iBAAiB,CAC1BgY,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,KAAK,CAChBC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,UAAU,CACnBC,IAAI,CAAE,YAAY,CAClBC,SAAS,CAAE,YAAY,CACvBC,IAAI,CAAE,aAAa,CACnBnkB,MAAM,CAAE,aAAa,CACrBokB,KAAK,CAAE,gBAAgB,CACvBC,YAAY,CAAE,wCAAwC,CACtDC,gBAAgB,CAAE,iBAAiB,CACnCC,YAAY,CAAE,oBAAoB,CAClCC,mBAAmB,CAAE,mBAAmB,CACxCC,SAAS,CAAE,UAAU,CACrBC,MAAM,CAAE,SAAS,CACjBC,kBAAkB,CAAE,cAAc,CAClCC,oBAAoB,CAAE,oDAAoD,CAC1EC,mBAAmB,CAAE,uBAAuB,CAC5CC,0BAA0B,CAAE,0CAA0C,CACtEC,uBAAuB,CAAE,4BAA4B,CACrDC,cAAc,CAAE,sBAAsB,CACtCC,aAAa,CAAE,wBAAwB,CACvCC,SAAS,CAAE,6BAA6B,CACxCC,WAAW,CAAE,2BAA2B,CACxCC,eAAe,CAAE,iBAAiB,CACjCnO,IAAI,CAAE,OACT,CAAC,CACDoO,IAAI,CAAE,CACJnO,KAAK,CAAE,cAAc,CACrBE,MAAM,CAAE,cAAc,CACtBkO,kBAAkB,CAAE,oBAAoB,CACxCC,oBAAoB,CAAE,qCAAqC,CAC3DC,WAAW,CAAE,gBAAgB,CAC7Bna,KAAK,CAAE,mBAAmB,CAC1BsB,QAAQ,CAAE,aAAa,CACvB6a,MAAM,CAAE,cAAc,CACtBC,EAAE,CAAE,IAAI,CACRC,SAAS,CAAE,gBAAgB,CAC3BjC,cAAc,CAAE,mBAAmB,CACnCC,0BAA0B,CAAE,sEAAsE,CAClGC,aAAa,CAAE,kBAAkB,CACjCC,WAAW,CAAE,yBAAyB,CACtCC,aAAa,CAAE,0CAA0C,CACzDC,kBAAkB,CAAE,sBAAsB,CAC1CC,UAAU,CAAE,iBAAiB,CAC7BC,sBAAsB,CAAE,gEAAgE,CACxFK,WAAW,CAAE,2BAA2B,CACxCC,sBAAsB,CAAE,uBAAuB,CAC/C9jB,MAAM,CAAE,MAAM,CACd+jB,SAAS,CAAE,gBAAgB,CAC3BC,oBAAoB,CAAE,2BAA2B,CACjDC,kBAAkB,CAAE,iCAAiC,CACrDC,YAAY,CAAE,4BAA4B,CAC1CC,YAAY,CAAE,wBAAwB,CACtCpc,OAAO,CAAE,iBAAiB,CAC1B6D,WAAW,CAAE,qCAAqC,CAClD1M,aAAa,CAAE,sBAAsB,CACrCklB,WAAW,CAAE,2CAA2C,CACxDC,kBAAkB,CAAE,qEAAqE,CACzFC,aAAa,CAAE,sFAAsF,CACrGC,aAAa,CAAE,gDAAgD,CAC/DC,cAAc,CAAE,6CAA6C,CAC7Df,gBAAgB,CAAE,YAAY,CAC9BC,iBAAiB,CAAE,kBAAkB,CACrCC,UAAU,CAAE,mBAAmB,CAC/BC,WAAW,CAAE,yBAAyB,CACtChY,WAAW,CAAE,qBAAqB,CAClCqY,kBAAkB,CAAE,YAAY,CAChCQ,eAAe,CAAE,gDAAgD,CACjEC,aAAa,CAAE,yBAAyB,CACxCC,yBAAyB,CAAE,6BAA6B,CACxDzY,WAAW,CAAE,qBAAqB,CAClC7B,eAAe,CAAE,mBAAmB,CACpCua,oBAAoB,CAAE,mCAAmC,CACzDC,2BAA2B,CAAE,6CAA6C,CAC1EC,kBAAkB,CAAE,6CAA6C,CACjEC,mBAAmB,CAAE,6BAA6B,CAClDI,MAAM,CAAE,YAAY,CACpBC,kBAAkB,CAAE,wBAAwB,CAC5CC,iBAAiB,CAAE,iDAAiD,CACpEC,gBAAgB,CAAE,wBAAwB,CAC1CC,UAAU,CAAE,6DAA6D,CACzEC,eAAe,CAAE,kBAAkB,CACnCC,WAAW,CAAE,uBAAuB,CACpCC,iBAAiB,CAAE,kBAAkB,CACrCC,mBAAmB,CAAE,iGAAiG,CACtHC,iBAAiB,CAAE,WAAW,CAC9BC,kBAAkB,CAAE,qHAAqH,CACzIC,iBAAiB,CAAE,YAAY,CAC/BC,kBAAkB,CAAE,4FAA4F,CAChHC,kBAAkB,CAAE,mBAAmB,CACvCC,UAAU,CAAE,WAAW,CACvBC,mBAAmB,CAAE,iBAAiB,CACtCC,mBAAmB,CAAE,iBAAiB,CACtCC,gBAAgB,CAAE,0BAA0B,CAC5CC,eAAe,CAAE,wBAAwB,CACzCC,aAAa,CAAE,YAAY,CAC3BC,iBAAiB,CAAE,sCAAsC,CACzDC,YAAY,CAAE,qBAAqB,CACnChc,MAAM,CAAE,OAAO,CACfC,IAAI,CAAE,KAAK,CACXC,MAAM,CAAE,MACV,CAAC,CACDmI,WAAW,CAAE,CACX4T,WAAW,CAAE,mBAAmB,CAChCC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,eAAe,CACtBC,OAAO,CAAE,SAAS,CAClBC,eAAe,CAAE,kBAAkB,CACnCC,cAAc,CAAE,qBAAqB,CACrCC,eAAe,CAAE,kBAAkB,CACnCC,cAAc,CAAE,iBAAiB,CACjCC,cAAc,CAAE,mBAAmB,CACnCC,MAAM,CAAE,eAAe,CACvBC,OAAO,CAAE,SACX,CAAC,CACDhT,SAAS,CAAE,CACT/C,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,YAAY,CACrBC,MAAM,CAAE,UAAU,CAClBC,OAAO,CAAE,WACX,CAAC,CACD/G,MAAM,CAAE,CACNC,IAAI,CAAE,KAAK,CACXC,MAAM,CAAE,MACV,CAAC,CACDoJ,SAAS,CAAE,CACTsT,OAAO,CAAE,QAAQ,CACjBC,YAAY,CAAE,qCAAqC,CACnDllB,UAAU,CAAE,4BAA4B,CACxCkV,aAAa,CAAE,eAAe,CAC9BiQ,YAAY,CAAE,eAAe,CAC7B1hB,aAAa,CAAE,eAAe,CAC9B2R,aAAa,CAAE,gBAAgB,CAC/BrD,UAAU,CAAE,cAAc,CAC1BqT,cAAc,CAAE,cAAc,CAC9BC,aAAa,CAAE,eAAe,CAC9BC,gBAAgB,CAAE,aAAa,CAC/BC,YAAY,CAAE,eAAe,CAC7BC,mBAAmB,CAAE,iBAAiB,CACtCC,eAAe,CAAE,gBAAgB,CACjCC,iBAAiB,CAAE,oBAAoB,CACvCC,aAAa,CAAE,oBAAoB,CACnCxT,WAAW,CAAE,cAAc,CAC3ByT,aAAa,CAAE,cAAc,CAC7BthB,OAAO,CAAE,UAAU,CACnBuhB,iBAAiB,CAAE,uBAAuB,CAC1CC,sBAAsB,CAAE,6FAA6F,CACrHC,uBAAuB,CAAE,4DAA4D,CACrFC,eAAe,CAAE,oBAAoB,CACrCL,aAAa,CAAE,oBAAoB,CACnCM,kBAAkB,CAAE,uBACtB,CAAC,CACDC,OAAO,CAAE,CACPC,WAAW,CAAE,cAAc,CAC3BC,YAAY,CAAE,cAAc,CAC5BC,kBAAkB,CAAE,sBAAsB,CAC1CC,UAAU,CAAE,WAAW,CACvBC,cAAc,CAAE,eAAe,CAC/BC,YAAY,CAAE,aAAa,CAC3BC,MAAM,CAAE,QAAQ,CAChBC,aAAa,CAAE,iBAAiB,CAChCC,aAAa,CAAE,iBAAiB,CAChCC,SAAS,CAAE,UAAU,CACrBC,OAAO,CAAE,YAAY,CACrBC,MAAM,CAAE,MACV,CAAC,CACDnrB,OAAO,CAAE,CACPorB,aAAa,CAAE,SAAS,CACxBhY,cAAc,CAAE,YAAY,CAC5BC,iBAAiB,CAAE,CACjB1U,KAAK,CAAE,cAAc,CACrB2U,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,YAAY,CACrBC,MAAM,CAAE,UAAU,CAClBC,OAAO,CAAE,WAAW,CACpBC,IAAI,CAAE,UAAU,CAChBC,OAAO,CAAE,SAAS,CAClBC,UAAU,CAAE,aAAa,CACzBC,KAAK,CAAE,WAAW,CAClBC,OAAO,CAAE,WAAW,CACpBC,KAAK,CAAE,SAAS,CAChBC,OAAO,CAAE,UAAU,CACnBC,MAAM,CAAE,WAAW,CACnBC,OAAO,CAAE,WAAW,CACpBC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,SAAS,CACjBC,IAAI,CAAE,aAAa,CACnBC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,YAAY,CACrBC,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,WAAW,CACnBC,MAAM,CAAE,mBAAmB,CAC3BC,WAAW,CAAE,wBACf,CAAC,CACDE,cAAc,CAAE,UAAU,CAC1BQ,UAAU,CAAE,QAAQ,CACpBqF,QAAQ,CAAE,QAAQ,CAClBpP,OAAO,CAAE,CACP9M,KAAK,CAAE,YAAY,CACnBoU,YAAY,CAAE,mBAAmB,CACjCC,YAAY,CAAE,iBAAiB,CAC/BC,KAAK,CAAE,YAAY,CACnBC,OAAO,CAAE,QAAQ,CACjBC,SAAS,CAAE,cAAc,CACzBC,cAAc,CAAE,YAAY,CAC5BC,iBAAiB,CAAE,CACjB1U,KAAK,CAAE,cAAc,CACrB2U,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,YAAY,CACrBC,MAAM,CAAE,UAAU,CAClBC,OAAO,CAAE,WAAW,CACpBC,IAAI,CAAE,UAAU,CAChBC,OAAO,CAAE,SAAS,CAClBC,UAAU,CAAE,aAAa,CACzBC,KAAK,CAAE,WAAW,CAClBC,OAAO,CAAE,WAAW,CACpBC,KAAK,CAAE,SAAS,CAChBC,OAAO,CAAE,UAAU,CACnBC,MAAM,CAAE,WAAW,CACnBC,OAAO,CAAE,WAAW,CACpBC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,WAAW,CACrBC,MAAM,CAAE,SAAS,CACjBC,IAAI,CAAE,aAAa,CACnBC,UAAU,CAAE,YAAY,CACxBC,OAAO,CAAE,YAAY,CACrBC,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,WAAW,CACnBC,MAAM,CAAE,mBAAmB,CAC3BC,WAAW,CAAE,wBACf,CAAC,CACDC,WAAW,CAAE,eAAe,CAC5BC,cAAc,CAAE,UAAU,CAC1BC,kBAAkB,CAAE,mBAAmB,CACvCC,cAAc,CAAE,iBAAiB,CACjCvQ,cAAc,CAAE,eAAe,CAC/BwQ,QAAQ,CAAE,iBAAiB,CAC3BC,aAAa,CAAE,aAAa,CAC5BlI,QAAQ,CAAE,CACRmI,GAAG,CAAE,cAAc,CACnBC,GAAG,CAAE,MAAM,CACXC,GAAG,CAAE,eACP,CAAC,CACDC,UAAU,CAAE,CACVC,QAAQ,CAAE,mBAAmB,CAC7BC,YAAY,CAAE,mBAAmB,CACjCC,QAAQ,CAAE,oBAAoB,CAC9BC,MAAM,CAAE,yBACV,CACF,CAAC,CACDyV,WAAW,CAAE,CACX1sB,KAAK,CAAE,qBAAqB,CAC5BuC,MAAM,CAAE,aAAa,CACrB2K,OAAO,CAAE,sBAAsB,CAC/BC,KAAK,CAAE,2BAA2B,CAClCwf,iBAAiB,CAAE,4BAA4B,CAC/CC,uBAAuB,CAAE,6BAA6B,CACtDC,eAAe,CAAE,kBAAkB,CACnC1e,IAAI,CAAE,aAAa,CACnB2e,eAAe,CAAE,kEAAkE,CACnFC,YAAY,CAAE,oBAAoB,CAClCC,cAAc,CAAE,qDAAqD,CACrEC,mBAAmB,CAAE,mEAAmE,CACxFC,mBAAmB,CAAE,wEAAwE,CAC7FC,mBAAmB,CAAE,oCAAoC,CACzDC,WAAW,CAAE,kBAAkB,CAC/BC,eAAe,CAAE,CACfrtB,KAAK,CAAE,aAAa,CACpBQ,WAAW,CAAE,sEAAsE,CACnF8sB,OAAO,CAAE,6DACX,CAAC,CACDC,WAAW,CAAE,eAAe,CAC5B7sB,MAAM,CAAE,CACNG,OAAO,CAAE,cAAc,CACvB+P,QAAQ,CAAE,WAAW,CACrBC,QAAQ,CAAE,UACZ,CAAC,CACD2c,aAAa,CAAE,CACb3sB,OAAO,CAAE,mBAAmB,CAC5B+P,QAAQ,CAAE,6BAA6B,CACvCC,QAAQ,CAAE,oBAAoB,CAC9B4c,OAAO,CAAE,qBACX,CAAC,CACDC,oBAAoB,CAAE,CACpB9c,QAAQ,CAAE,CACR5Q,KAAK,CAAE,iBAAiB,CACxB2tB,KAAK,CAAE,CACL,8BAA8B,CAC9B,qDAAqD,CAEzD,CACF,CACF,CAAC,CACDpZ,OAAO,CAAE,QAAQ,CACjBC,SAAS,CAAE,eAAe,CAC1BC,cAAc,CAAE,YAAY,CAC5BC,iBAAiB,CAAE,cAAc,CACjC0B,WAAW,CAAE,eAAe,CAC5BC,cAAc,CAAE,UAAU,CAC1BuX,yBAAyB,CAAE,kCAAkC,CAC7DtX,kBAAkB,CAAE,yBAAyB,CAC7CgO,UAAU,CAAE,eAAe,CAC3BuJ,aAAa,CAAE,oBAAoB,CACnCC,wBAAwB,CAAE,aAAa,CACvCC,YAAY,CAAE,2BAA2B,CACzCC,YAAY,CAAE,cAAc,CAC5BC,YAAY,CAAE,WAAW,CACzBC,SAAS,CAAE,MAAM,CACjBhd,WAAW,CAAE,KAAK,CAClBid,aAAa,CAAE,wCAAwC,CACvDC,aAAa,CAAE,iDAAiD,CAChEC,eAAe,CAAE,kDAAkD,CACnEC,sBAAsB,CAAE,WAAW,CACnCC,aAAa,CAAE,gBAAgB,CAC/BC,gBAAgB,CAAE,oBAAoB,CACtCE,cAAc,CAAE,gBAAgB,CAChCC,WAAW,CAAE,oBAAoB,CACjCC,oBAAoB,CAAE,oBAAoB,CAC1CvK,EAAE,CAAE,gBAAgB,CACpBwK,aAAa,CAAE,oBAAoB,CACnCC,YAAY,CAAE,6EAA6E,CAC3FC,UAAU,CAAE,KAAK,CACjBC,cAAc,CAAE,gBAAgB,CAChCC,yBAAyB,CAAE,oBAAoB,CAC/CC,sBAAsB,CAAE,uBAAuB,CAC/C3Y,cAAc,CAAE,uBAAuB,CACvClE,yBAAyB,CAAE,uDAAuD,CAClFE,gCAAgC,CAAE,kEAAkE,CACpGC,kBAAkB,CAAE,qBAAqB,CACzCC,6BAA6B,CAAE,8CAA8C,CAC7EgB,gBAAgB,CAAE,8BAA8B,CAChDG,QAAQ,CAAE,gBAAgB,CAC1BJ,SAAS,CAAE,aAAa,CACxBK,SAAS,CAAE,MAAM,CACjBC,WAAW,CAAE,UAAU,CACvBpB,kBAAkB,CAAE,uBAAuB,CAC3Cyc,cAAc,CAAE,iBAAiB,CACjCC,mBAAmB,CAAE,mCAAmC,CACxD9oB,IAAI,CAAE,OAAO,CACb2N,MAAM,CAAE,cAAc,CACtBob,UAAU,CAAE,gBAAgB,CAC5BC,UAAU,CAAE,wBAAwB,CACpCC,YAAY,CAAE,kBAAkB,CAChCC,UAAU,CAAE,eAAe,CAC3BxpB,cAAc,CAAE,eAAe,CAC/BypB,yBAAyB,CAAE,8BAA8B,CACzDC,gBAAgB,CAAE,oBAAoB,CACtCC,2BAA2B,CAAE,2CAA2C,CACxEC,yBAAyB,CAAE,iEAAiE,CAC5FpZ,QAAQ,CAAE,iBAAiB,CAC3BC,aAAa,CAAE,aAAa,CAC5BnC,KAAK,CAAE,YAAY,CACnBub,SAAS,CAAE,0CAA0C,CACrDU,UAAU,CAAE,QAAQ,CACpBC,qBAAqB,CAAE,wCAAwC,CAC/DC,4BAA4B,CAAE,yCAAyC,CACvEC,kBAAkB,CAAE,kCAAkC,CACtDC,gBAAgB,CAAE,CAChBC,QAAQ,CAAE,gBAAgB,CAC1B/vB,OAAO,CAAE,+BAA+B,CACxCgQ,QAAQ,CAAE,eACZ,CAAC,CACD4d,aAAa,CAAE,4CAA4C,CAC3DqB,aAAa,CAAE,cAAc,CAC7Bpc,SAAS,CAAE,aAAa,CACxBqc,QAAQ,CAAE,iBAAiB,CAC3BtB,aAAa,CAAE,+BAA+B,CAC9C3Q,UAAU,CAAE,iCAAiC,CAC7CkS,YAAY,CAAE,gCAAgC,CAC9CC,UAAU,CAAE,wBAAwB,CACpCC,YAAY,CAAE,4BAA4B,CAC1CC,YAAY,CAAE,uBAAuB,CACrChe,cAAc,CAAE,kBAAkB,CAClCP,UAAU,CAAE,cAAc,CAC1BQ,WAAW,CAAE,aAAa,CAC1BQ,oBAAoB,CAAE,uBAAuB,CAC7Cqe,cAAc,CAAE,4BAA4B,CAC5CC,yBAAyB,CAAE,6CAA6C,CAExEL,eAAe,CAAE,eAAe,CAChCC,kBAAkB,CAAE,gBAAgB,CACpCC,cAAc,CAAE,6BAA6B,CAC7CC,MAAM,CAAE,OAAO,CACf9sB,MAAM,CAAE,MAAM,CACdwqB,cAAc,CAAE,gBAAgB,CAChCC,WAAW,CAAE,wBAAwB,CACrCyB,YAAY,CAAE,gBAAgB,CAC9BC,YAAY,CAAE,cAAc,CAC5BC,aAAa,CAAE,0CAA0C,CACzDa,SAAS,CAAE,CACTC,KAAK,CAAE,mDAAmD,CAC1DC,QAAQ,CAAE,0DACZ,CAAC,CACDC,eAAe,CAAE,YAAY,CAC7BC,eAAe,CAAE,YAAY,CAC7BC,cAAc,CAAE,iCAAiC,CACjDC,cAAc,CAAE,wBAAwB,CACxCC,gBAAgB,CAAE,yBAAyB,CAC3CC,2BAA2B,CAAE,wCAAwC,CACrEC,eAAe,CAAE,aAAa,CAC9BC,qBAAqB,CAAE,CACrBhxB,OAAO,CAAE,aAAa,CACtBixB,QAAQ,CAAE,SACZ,CACF,CAAC,CACD7Y,IAAI,CAAE,CACJrV,KAAK,CAAE,MAAM,CACbvC,OAAO,CAAE,MAAM,CACfD,OAAO,CAAE,MACX,CAAC,CACD2wB,UAAU,CAAE,CACVhC,QAAQ,CAAE,iBAAiB,CAC3B3jB,KAAK,CAAE,+BAA+B,CACtCsB,QAAQ,CAAE,CACRskB,GAAG,CAAE,gDAAgD,CACrDC,GAAG,CAAE,kDAAkD,CACvDC,KAAK,CAAE,0BACT,CACF,CAAC,CACDC,IAAI,CAAE,CACJC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,UAAU,CACnBC,SAAS,CAAE,UAAU,CACrBC,QAAQ,CAAE,QAAQ,CAClBC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,OAAO,CACjBC,MAAM,CAAE,OAAO,CACfC,WAAW,CAAE,OAAO,CACpBC,YAAY,CAAE,QAAQ,CACtBC,cAAc,CAAE,QAAQ,CACxBC,aAAa,CAAE,MAAM,CACrBC,WAAW,CAAE,MAAM,CACnBC,aAAa,CAAE,KAAK,CACpBC,WAAW,CAAE,KACf,CAAC,CACDC,KAAK,CAAE,CACLC,KAAK,CAAE,QAAQ,CACfC,KAAK,CAAE,QAAQ,CACfC,KAAK,CAAE,QAAQ,CACfC,KAAK,CAAE,QAAQ,CACfC,KAAK,CAAE,QAAQ,CACfC,KAAK,CAAE,QAAQ,CACfC,KAAK,CAAE,QAAQ,CACfC,KAAK,CAAE,QAAQ,CACfC,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SACV,CAAC,CACDtzB,OAAO,CAAE,CACP0L,OAAO,CAAE,CACP9M,KAAK,CAAE,qBAAqB,CAC5B20B,QAAQ,CAAE,kBAAkB,CAC5BC,mBAAmB,CAAE,eAAe,CACpCxgB,YAAY,CAAE,mBAAmB,CACjCygB,mBAAmB,CAAE,gBAAgB,CACrCpgB,cAAc,CAAE,YAAY,CAC5BqgB,wBAAwB,CAAE,6BAA6B,CACvDC,uBAAuB,CAAE,6BAA6B,CACtDC,qBAAqB,CAAE,kBAAkB,CACzCC,sBAAsB,CAAE,kBAAkB,CAC1CC,GAAG,CAAE,OAAO,CACZ3gB,OAAO,CAAE,OAAO,CAChBiC,QAAQ,CAAE,iBAAiB,CAC3B2e,WAAW,CAAE,qBAAqB,CAClCC,eAAe,CAAE,2BAA2B,CAC5CC,WAAW,CAAE,IAAI,CACjBC,aAAa,CAAE,KAAK,CACpB9mB,aAAa,CAAE,6BAA6B,CAC5CG,QAAQ,CAAE,4BAA4B,CACtC4mB,MAAM,CAAE,CACNze,QAAQ,CAAE,OAAO,CACjBC,YAAY,CAAE,OAAO,CACrBC,QAAQ,CAAE,OACZ,CAAC,CACD9J,OAAO,CAAE,8DACX,CAAC,CACD4K,UAAU,CAAE,SAAS,CACrB0d,aAAa,CAAE,yBAAyB,CACxCC,YAAY,CAAE,YAChB,CAAC,CACD7xB,KAAK,CAAE,CACLkJ,OAAO,CAAE,CACPsH,YAAY,CAAE,mBAAmB,CACjC5E,cAAc,CAAE,mBAAmB,CACnCR,eAAe,CAAE,qBAAqB,CACtCS,WAAW,CAAE,qBAAqB,CAClCqrB,kBAAkB,CAAE,2BAA2B,CAC/CtsB,aAAa,CAAE,6BAA6B,CAC5CusB,WAAW,CAAE,2BAA2B,CACxCC,eAAe,CAAE,4BAA4B,CAC7CC,aAAa,CAAE,0BAA0B,CACzCC,YAAY,CAAE,kCAAkC,CAChDC,UAAU,CAAE,gCACd,CAAC,CACDC,WAAW,CAAE,CACXC,gBAAgB,CAAE,YAAY,CAC9BC,WAAW,CAAE,WACf,CAAC,CACDhkB,QAAQ,CAAE,CACRtX,KAAK,CAAE,UAAU,CACjBi6B,iBAAiB,CAAE,oBAAoB,CACvC3tB,IAAI,CAAE,OAAO,CACbF,KAAK,CAAE,mBAAmB,CAC1B2B,MAAM,CAAE,OAAO,CACfkL,IAAI,CAAE,OAAO,CACb5W,OAAO,CAAE,WAAW,CACpBgX,WAAW,CAAE,cAAc,CAC3BsK,cAAc,CAAE,eAAe,CAC/BvP,YAAY,CAAE,mBAAmB,CACjCmnB,aAAa,CAAE,iCAAiC,CAChDvD,aAAa,CAAE,qBAAqB,CACpC9R,WAAW,CAAE,mBAAmB,CAChCxgB,UAAU,CAAE,qBACd,CAAC,CACD6R,QAAQ,CAAE,CACRvX,KAAK,CAAE,QAAQ,CACfi6B,iBAAiB,CAAE,kBAAkB,CACrC3tB,IAAI,CAAE,OAAO,CACbF,KAAK,CAAE,mBAAmB,CAC1B2B,MAAM,CAAE,OAAO,CACfwG,OAAO,CAAE,QAAQ,CACjB2gB,GAAG,CAAE,OAAO,CACZ1e,QAAQ,CAAE,iBAAiB,CAC3BnU,OAAO,CAAE,WAAW,CACpBgX,WAAW,CAAE,cAAc,CAC3BmiB,cAAc,CAAE,eAAe,CAC/BpnB,YAAY,CAAE,mBAAmB,CACjCygB,mBAAmB,CAAE,gBAAgB,CACrCpgB,cAAc,CAAE,YAAY,CAC5BugB,qBAAqB,CAAE,kBAAkB,CACzCC,sBAAsB,CAAE,kBAAkB,CAC1CwG,sBAAsB,CAAE,2BAA2B,CACnDrG,eAAe,CAAE,iBAAiB,CAClCsG,gBAAgB,CAAE,qBAAqB,CACvCH,aAAa,CAAE,iCAAiC,CAChDvD,aAAa,CAAE,qBAAqB,CACpC9R,WAAW,CAAE,mBAAmB,CAChCxgB,UAAU,CAAE,mBAAmB,CAC/B6wB,UAAU,CAAE,wBAAwB,CACpCpuB,MAAM,CAAE,KAAK,CACbwzB,iBAAiB,CAAE,CACjB7kB,QAAQ,CAAE,OAAO,CACjBC,YAAY,CAAE,OAAO,CACrBC,QAAQ,CAAE,OACZ,CACF,CAAC,CACDS,UAAU,CAAE,CACVzX,KAAK,CAAE,WAAW,CAClB47B,MAAM,CAAE,kBAAkB,CAC1BC,SAAS,CAAE,eAAe,CAC1BC,QAAQ,CAAE,aAAa,CACvBxvB,IAAI,CAAE,OAAO,CACb9L,WAAW,CAAE,OAAO,CACpBu7B,SAAS,CAAE,mBAAmB,CAC9BvtB,aAAa,CAAE,wBAAwB,CACvCwtB,aAAa,CAAE,wBAAwB,CACvChE,aAAa,CAAE,sBAAsB,CACrCuD,aAAa,CAAE,kCAAkC,CACjD71B,UAAU,CAAE,sBAAsB,CAClC2yB,SAAS,CAAE,oBAAoB,CAC/BnS,WAAW,CAAE,oBAAoB,CACjC0E,YAAY,CAAE,aAAa,CAC3BqR,SAAS,CAAE,uBACb,CAAC,CACDvkB,SAAS,CAAE,CACT1X,KAAK,CAAE,QAAQ,CACf47B,MAAM,CAAE,iBAAiB,CACzBC,SAAS,CAAE,aAAa,CACxBC,QAAQ,CAAE,WAAW,CACrBxvB,IAAI,CAAE,WAAW,CACjB4vB,YAAY,CAAE,iBAAiB,CAC/BtR,YAAY,CAAE,gCAAgC,CAC9CllB,UAAU,CAAE,mBAAmB,CAC/Bs2B,aAAa,CAAE,sBAAsB,CACrCG,WAAW,CAAE,oBAAoB,CACjC3tB,aAAa,CAAE,sBAAsB,CACrCusB,WAAW,CAAE,oBAAoB,CACjC/C,aAAa,CAAE,oBAAoB,CACnC9R,WAAW,CAAE,kBAAkB,CAC/BqV,aAAa,CAAE,sCAAsC,CACrDU,SAAS,CAAE,qBAAqB,CAChC5D,SAAS,CAAE,kBACb,CAAC,CACD1gB,YAAY,CAAE,CACZsiB,iBAAiB,CAAE,qBAAqB,CACxCmC,cAAc,CAAE,kBAAkB,CAClC9vB,IAAI,CAAE,OAAO,CACbF,KAAK,CAAE,mBAAmB,CAC1BkI,KAAK,CAAE,QAAQ,CACfC,OAAO,CAAE,QAAQ,CACjBmD,SAAS,CAAE,QAAQ,CACnBhX,MAAM,CAAE,QAAQ,CAChB2B,OAAO,CAAE,WAAW,CACpBg6B,QAAQ,CAAE,CACRx7B,OAAO,CAAE,cAAc,CACvB+P,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,OACZ,CAAC,CACD7Q,KAAK,CAAE,gBAAgB,CACvBs8B,WAAW,CAAE,cAAc,CAC3BjjB,WAAW,CAAE,cAAc,CAC3BpV,OAAO,CAAE,QAAQ,CACjBC,MAAM,CAAE,KAAK,CACbq4B,kBAAkB,CAAE,cAAc,CAClCnoB,YAAY,CAAE,mBAAmB,CACjCC,YAAY,CAAE,iBAAiB,CAC/BmoB,SAAS,CAAE,WAAW,CACtB/nB,cAAc,CAAE,YAAY,CAC5BC,iBAAiB,CAAE,cAAc,CACjC2B,cAAc,CAAE,UAAU,CAC1BQ,UAAU,CAAE,QAAQ,CACpB7Q,cAAc,CAAE,WAAW,CAC3By2B,SAAS,CAAE,sBAAsB,CACjCC,MAAM,CAAE,oBAAoB,CAC5BptB,SAAS,CAAE,oBAAoB,CAC/BqtB,eAAe,CAAE,gBAAgB,CACjCC,QAAQ,CAAE,eAAe,CACzB1Y,QAAQ,CAAE,QAAQ,CAClB2Y,eAAe,CAAE,eAAe,CAChCzmB,WAAW,CAAE,eAAe,CAC5B0mB,OAAO,CAAE,SAAS,CAClBrmB,aAAa,CAAE,aAAa,CAC5B6N,UAAU,CAAE,kBAAkB,CAC9BD,EAAE,CAAE,gBAAgB,CACpB9N,cAAc,CAAE,iBAAiB,CACjC2H,iBAAiB,CAAE,2BACrB,CACF,CAAC,CACD6e,IAAI,CAAE,CACJ1lB,SAAS,CAAE,aAAa,CACxBU,QAAQ,CAAE,YAAY,CACtBjL,OAAO,CAAE,cAAc,CACvBkL,IAAI,CAAE,WAAW,CACjBglB,cAAc,CAAE,cAClB,CAAC,CACDjlB,QAAQ,CAAE,CACR/X,KAAK,CAAE,YAAY,CACnBi9B,UAAU,CAAE,WAAW,CACvBz8B,WAAW,CAAE,2CAA2C,CACxD08B,UAAU,CAAE,yBAAyB,CACrCC,qBAAqB,CAAE,yDAAyD,CAChF97B,OAAO,CAAE,QAAQ,CACjBd,IAAI,CAAE,SAAS,CACf+F,IAAI,CAAE,OAAO,CACbZ,UAAU,CAAE,uBAAuB,CACnC03B,IAAI,CAAE,IAAI,CACVC,SAAS,CAAE,mBAAmB,CAC9BC,WAAW,CAAE,cAAc,CAC3BC,WAAW,CAAE,gBAAgB,CAC7Bj2B,QAAQ,CAAE,OAAO,CACjBkK,OAAO,CAAE,OAAO,CAChBgsB,MAAM,CAAE,gBAAgB,CACxBC,KAAK,CAAE,cAAc,CACrBC,IAAI,CAAE,iBAAiB,CACvBC,UAAU,CAAE,sBAAsB,CAClCnG,KAAK,CAAE,gBAAgB,CACvBoG,SAAS,CAAE,0BAA0B,CACrCt7B,MAAM,CAAE,gBAAgB,CACxBu7B,QAAQ,CAAE,gBAAgB,CAC1BC,UAAU,CAAE,mBAAmB,CAC/B9c,aAAa,CAAE,yBAAyB,CACxCC,WAAW,CAAE,8BAA8B,CAC3C+a,aAAa,CAAE,yBAAyB,CACxCG,WAAW,CAAE,8BAA8B,CAC3C30B,UAAU,CAAE,SAAS,CACrBD,UAAU,CAAE,WAAW,CACvBgH,QAAQ,CAAE,OAAO,CACjB7N,MAAM,CAAE,CACNG,OAAO,CAAE,cAAc,CACvB4f,OAAO,CAAE,MAAM,CACf3f,SAAS,CAAE,OAAO,CAClBE,SAAS,CAAE,MAAM,CACjBuf,SAAS,CAAE,OACb,CAAC,CACDyd,gBAAgB,CAAE,yBAAyB,CAC3CC,sBAAsB,CAAE,8BAA8B,CACtDC,WAAW,CAAE,OAAO,CACpBC,GAAG,CAAE,KAAK,CACVC,WAAW,CAAE,YAAY,CACzBC,aAAa,CAAE,eAAe,CAC9BC,MAAM,CAAE,aAAa,CACrBC,WAAW,CAAE,oBAAoB,CACjCC,aAAa,CAAE,iEAAiE,CAChFC,iBAAiB,CAAE,gCAAgC,CACnDC,cAAc,CAAE,iBAAiB,CACjCnH,MAAM,CAAE,KAAK,CACboH,OAAO,CAAE,OAAO,CAChB5M,UAAU,CAAE,CACVmK,YAAY,CAAE,oBAAoB,CAClC0C,YAAY,CAAE,sBAAsB,CACpCC,gBAAgB,CAAE,qBAAqB,CACvCC,iBAAiB,CAAE,oBACrB,CAAC,CACD19B,OAAO,CAAE,QAAQ,CACjB29B,QAAQ,CAAE,gBAAgB,CAC1Bt+B,MAAM,CAAE,QAAQ,CAChB4B,OAAO,CAAE,WACX,CAAC,CACDoZ,OAAO,CAAE,CACPzb,KAAK,CAAE,gBAAgB,CACvBwL,KAAK,CAAE,wNAAwN,CAC/NwzB,QAAQ,CAAE,CACRh/B,KAAK,CAAE,0BAA0B,CACjC8b,QAAQ,CAAE,iCAAiC,CAC3CmjB,KAAK,CAAE,8CAA8C,CACrDC,KAAK,CAAE,uBAAuB,CAC9BC,KAAK,CAAE,kDACT,CAAC,CACDC,QAAQ,CAAE,CACRp/B,KAAK,CAAE,4BAA4B,CACnC8b,QAAQ,CAAE,yBAAyB,CACnCmjB,KAAK,CAAE,kCAAkC,CACzCC,KAAK,CAAE,kCAAkC,CACzCC,KAAK,CAAE,gCAAgC,CACvCE,KAAK,CAAE,gCAAgC,CACvCC,KAAK,CAAE,0CACT,CAAC,CACDC,QAAQ,CAAE,CACRv/B,KAAK,CAAE,qBAAqB,CAC5B8b,QAAQ,CAAE,4CAA4C,CACtDtb,WAAW,CAAE,iBAAiB,CAC9By+B,KAAK,CAAE,gCAAgC,CACvCC,KAAK,CAAE,iDAAiD,CACxDC,KAAK,CAAE,kDACT,CAAC,CACDK,QAAQ,CAAE,CACRx/B,KAAK,CAAE,iBAAiB,CACxBy/B,OAAO,CAAE,gGACX,CAAC,CACDC,QAAQ,CAAE,CACR1/B,KAAK,CAAE,UAAU,CACjB8b,QAAQ,CAAE,SAAS,CACnBmjB,KAAK,CAAE,4BAA4B,CACnCC,KAAK,CAAE,sBAAsB,CAC7BC,KAAK,CAAE,0BAA0B,CACjCE,KAAK,CAAE,2CAA2C,CAClD7jB,OAAO,CAAE,gFACX,CAAC,CACDmkB,QAAQ,CAAE,CACR3/B,KAAK,CAAE,mCAAmC,CAC1Cy/B,OAAO,CAAE,wHACX,CAAC,CACDG,QAAQ,CAAE,CACR5/B,KAAK,CAAE,mBAAmB,CAC1By/B,OAAO,CAAE,gJACX,CAAC,CACDI,QAAQ,CAAE,CACR7/B,KAAK,CAAE,qBAAqB,CAC5By/B,OAAO,CAAE,mGACX,CAAC,CACDK,QAAQ,CAAE,CACR9/B,KAAK,CAAE,8BAA8B,CACrCy/B,OAAO,CAAE,+HACX,CAAC,CACDM,SAAS,CAAE,CACT//B,KAAK,CAAE,kBAAkB,CACzBy/B,OAAO,CAAE,2EAA2E,CACpFrzB,KAAK,CAAE,2BACT,CACF,CAAC,CACD4zB,QAAQ,CAAE,CACRC,MAAM,CAAE,CACNjgC,KAAK,CAAE,iBAAiB,CACxBg/B,QAAQ,CAAE,CACRh/B,KAAK,CAAE,8CAA8C,CACrDQ,WAAW,CAAE,mFAAmF,CAChG0/B,KAAK,CAAE,CACL,qDAAqD,CACrD,2DAA2D,CAE/D,CAAC,CACDd,QAAQ,CAAE,CACRp/B,KAAK,CAAE,4DAA4D,CACnEQ,WAAW,CAAE,oEAAoE,CACjF0/B,KAAK,CAAE,CACL,kDAAkD,CAClD,4CAA4C,CAC5C,wDAAwD,CACxD,oDAAoD,CACpD,sEAAsE,CACtE,iCAAiC,CACjC,2CAA2C,CAE/C,CAAC,CACDX,QAAQ,CAAE,CACRv/B,KAAK,CAAE,+BAA+B,CACtCQ,WAAW,CAAE,4HACf,CAAC,CACDgb,OAAO,CAAE,CACPxb,KAAK,CAAE,cAAc,CACrBoM,KAAK,CAAE,8BACT,CACF,CAAC,CACD+zB,cAAc,CAAE,CACdngC,KAAK,CAAE,oBAAoB,CAC3B8b,QAAQ,CAAE,oFAAoF,CAC9FkjB,QAAQ,CAAE,CACRh/B,KAAK,CAAE,eAAe,CACtBogC,MAAM,CAAE,CACN,iFAAiF,CACjF,mDAAmD,CACnD,6CAA6C,CAC7C,4EAA4E,CAEhF,CAAC,CACDhB,QAAQ,CAAE,CACRp/B,KAAK,CAAE,UAAU,CACjBogC,MAAM,CAAE,CACN,yCAAyC,CACzC,wFAAwF,CACxF,mEAAmE,CACnE,2CAA2C,CAE/C,CAAC,CACDb,QAAQ,CAAE,CACRv/B,KAAK,CAAE,wBAAwB,CAC/BogC,MAAM,CAAE,CACN,qDAAqD,CACrD,+CAA+C,CAC/C,sGAAsG,CACtG,kGAAkG,CAEtG,CAAC,CACDZ,QAAQ,CAAE,CACRx/B,KAAK,CAAE,oBAAoB,CAC3BogC,MAAM,CAAE,CACN,4FAA4F,CAC5F,wDAAwD,CAE5D,CAAC,CACDV,QAAQ,CAAE,CACR1/B,KAAK,CAAE,2BAA2B,CAClCogC,MAAM,CAAE,CACN,mFAAmF,CACnF,6CAA6C,CAEjD,CAAC,CACDT,QAAQ,CAAE,CACR3/B,KAAK,CAAE,uBAAuB,CAC9BogC,MAAM,CAAE,CACN,oEAAoE,CACpE,6DAA6D,CAEjE,CAAC,CACDR,QAAQ,CAAE,CACR5/B,KAAK,CAAE,2CAA2C,CAClDogC,MAAM,CAAE,CACN,yGAAyG,CACzG,iEAAiE,CAErE,CAAC,CACDP,QAAQ,CAAE,CACR7/B,KAAK,CAAE,yBAAyB,CAChCQ,WAAW,CAAE,0EAA0E,CACvF4L,KAAK,CAAE,2BACT,CAAC,CACD0Q,QAAQ,CAAE,CACRujB,cAAc,CAAE,aAAa,CAC7BC,kBAAkB,CAAE,2EAA2E,CAC/FC,kBAAkB,CAAE,cAAc,CAClCC,sBAAsB,CAAE,oEAAoE,CAC5FC,eAAe,CAAE,eAAe,CAChCC,mBAAmB,CAAE,sDAAsD,CAC3EC,mBAAmB,CAAE,YAAY,CACjCC,uBAAuB,CAAE,8CAC3B,CACF,CAAC,CACDC,mBAAmB,CAAE,CACnB7gC,KAAK,CAAE,qCAAqC,CAC5C8b,QAAQ,CAAE,qLAAqL,CAC/LglB,aAAa,CAAE,CACb9gC,KAAK,CAAE,qBAAqB,CAC5B8F,OAAO,CAAE,CACP9F,KAAK,CAAE,eAAe,CACtBogC,MAAM,CAAE,CACN,6CAA6C,CAC7C,6CAA6C,CAEjD,CAAC,CACDW,YAAY,CAAE,CACZ/gC,KAAK,CAAE,gBAAgB,CACvBogC,MAAM,CAAE,CACN,6FAA6F,CAC7F,+DAA+D,CAC/D,gGAAgG,CAEpG,CAAC,CACDY,YAAY,CAAE,CACZhhC,KAAK,CAAE,qBAAqB,CAC5BogC,MAAM,CAAE,CACN,gHAAgH,CAChH,kIAAkI,CAEtI,CAAC,CACDa,WAAW,CAAE,CACXjhC,KAAK,CAAE,qBAAqB,CAC5BogC,MAAM,CAAE,CACN,0DAA0D,CAC1D,wFAAwF,CAE5F,CACF,CAAC,CACDc,WAAW,CAAE,CACXlhC,KAAK,CAAE,sBAAsB,CAC7BmhC,YAAY,CAAE,CACZnhC,KAAK,CAAE,yBAAyB,CAChCogC,MAAM,CAAE,CACN,+EAA+E,CAEnF,CAAC,CACDW,YAAY,CAAE,CACZ/gC,KAAK,CAAE,gCAAgC,CACvCogC,MAAM,CAAE,CACN,0EAA0E,CAC1E,yIAAyI,CAE7I,CAAC,CACDY,YAAY,CAAE,CACZhhC,KAAK,CAAE,wBAAwB,CAC/BogC,MAAM,CAAE,CACN,wFAAwF,CACxF,4EAA4E,CAEhF,CAAC,CACDa,WAAW,CAAE,CACXjhC,KAAK,CAAE,qBAAqB,CAC5BogC,MAAM,CAAE,CACN,8CAA8C,CAC9C,sGAAsG,CAE1G,CACF,CAAC,CACDgB,YAAY,CAAE,CACZphC,KAAK,CAAE,cAAc,CACrBogC,MAAM,CAAE,CACN,wDAAwD,CACxD,qHAAqH,CAEzH,CAAC,CACDiB,OAAO,CAAE,CACPC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,QAAQ,CACnBC,gBAAgB,CAAE,yBAAyB,CAC3CC,WAAW,CAAE,oBAAoB,CACjCC,aAAa,CAAE,yBAAyB,CACxCC,kBAAkB,CAAE,oBAAoB,CACxCC,cAAc,CAAE,qBAAqB,CACrCC,qBAAqB,CAAE,mBAAmB,CAC1CC,aAAa,CAAE,6DACjB,CACF,CACF,CAAC,CACDQ,gBAAgB,CAAE,eAAe,CACjC/2B,KAAK,CAAE,CACLvL,KAAK,CAAE,QAAQ,CACfwL,KAAK,CAAE,+NAA+N,CACtOC,OAAO,CAAE,+JAA+J,CACxKC,WAAW,CAAE,YAAY,CACzBC,QAAQ,CAAE,CACRC,cAAc,CAAE,2EAA2E,CAC3FC,oBAAoB,CAAE,iEAAiE,CACvFC,gBAAgB,CAAE,wFAAwF,CAC1GC,eAAe,CAAE,8FAA8F,CAC/GC,cAAc,CAAE,8GAClB,CAAC,CACDC,UAAU,CAAE,SAAS,CACrBC,WAAW,CAAE,kLAAkL,CAC/LxH,SAAS,CAAE,cAAc,CACzByH,WAAW,CAAE,gEAAgE,CAC7EC,KAAK,CAAE,2BACT,CACF,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAq2B,eAAe,CAAG,CACtB9iC,EAAE,CAAE,CACFC,WAAW,CAAE,CACX,GAAGF,SAAS,CAACC,EAAE,CAACC,WAAW,CAC3B8iC,aAAa,CAAE,CACb1iC,KAAK,CAAE,mCAAmC,CAC1CwL,KAAK,CAAE,0MAA0M,CACjNm3B,aAAa,CAAE,CACb3iC,KAAK,CAAE,iCACT,CAAC,CACD8gC,aAAa,CAAE,CACb9gC,KAAK,CAAE,gBAAgB,CACvB8F,OAAO,CAAE,CACP9F,KAAK,CAAE,iBAAiB,CACxBQ,WAAW,CAAE,kJACf,CAAC,CACDugC,YAAY,CAAE,CACZ/gC,KAAK,CAAE,oBAAoB,CAC3BQ,WAAW,CAAE,sRACf,CAAC,CACDwgC,YAAY,CAAE,CACZhhC,KAAK,CAAE,sBAAsB,CAC7BQ,WAAW,CAAE,+HACf,CAAC,CACDoiC,UAAU,CAAE,CACV5iC,KAAK,CAAE,YAAY,CACnBQ,WAAW,CAAE,uIACf,CACF,CAAC,CACDqiC,aAAa,CAAE,CACb7iC,KAAK,CAAE,gBAAgB,CACvBmhC,YAAY,CAAE,CACZnhC,KAAK,CAAE,cAAc,CACrBQ,WAAW,CAAE,wFACf,CAAC,CACDugC,YAAY,CAAE,CACZ/gC,KAAK,CAAE,4BAA4B,CACnCQ,WAAW,CAAE,kOACf,CAAC,CACDwgC,YAAY,CAAE,CACZhhC,KAAK,CAAE,sBAAsB,CAC7BQ,WAAW,CAAE,+FACf,CAAC,CACDoiC,UAAU,CAAE,CACV5iC,KAAK,CAAE,YAAY,CACnBQ,WAAW,CAAE,iLACf,CACF,CAAC,CACD4gC,YAAY,CAAE,CACZphC,KAAK,CAAE,eAAe,CACtB8iC,QAAQ,CAAE,wEAAwE,CAClFC,aAAa,CAAE,yIACjB,CAAC,CACDC,gBAAgB,CAAE,CAChBhjC,KAAK,CAAE,mBAAmB,CAC1BQ,WAAW,CAAE,qEAAqE,CAClFyiC,KAAK,CAAE,CACL,CAAC,CAAE,kCAAkC,CACrC,CAAC,CAAE,gCAAgC,CACnC,CAAC,CAAE,gCAAgC,CACnC,CAAC,CAAE,kCAAkC,CACrC,CAAC,CAAE,0CACL,CAAC,CACDC,WAAW,CAAE,uOACf,CAAC,CAEDC,SAAS,CAAE,CACTC,QAAQ,CAAE,kEAAkE,CAC5ErT,QAAQ,CAAE,uEACZ,CACF,CAAC,CACDnsB,KAAK,CAAE,CACL,GAAGlE,SAAS,CAACC,EAAE,CAACC,WAAW,CAACgE,KAC9B,CAAC,CACDvC,OAAO,CAAE,CACP,GAAG3B,SAAS,CAACC,EAAE,CAACC,WAAW,CAACyB,OAAO,CACnCyR,SAAS,CAAE,YAAY,CACvBC,WAAW,CAAE,cAAc,CAC3BC,YAAY,CAAE,eAAe,CAC7BI,cAAc,CAAE,kBAAkB,CAClCke,eAAe,CAAE,kBAAkB,CACnCC,eAAe,CAAE,sBAAsB,CACvCC,cAAc,CAAE,iDAAiD,CACjEC,cAAc,CAAE,+BAA+B,CAC/CC,gBAAgB,CAAE,8BAA8B,CAChDC,2BAA2B,CAAE,2CAA2C,CACxEC,eAAe,CAAE,kBAAkB,CACnCC,qBAAqB,CAAE,CACrBhxB,OAAO,CAAE,SAAS,CAClBixB,QAAQ,CAAE,UACZ,CAAC,CACD5gB,WAAW,CAAE,CACXlR,KAAK,CAAE,2BAA2B,CAClCQ,WAAW,CAAE,4GAA4G,CACzH2Q,YAAY,CAAE,oBAAoB,CAClCC,iBAAiB,CAAE,iBAAiB,CACpCC,eAAe,CAAE,cAAc,CAC/BC,iBAAiB,CAAE,oBAAoB,CACvCE,OAAO,CAAE,SAAS,CAClBC,WAAW,CAAE,cAAc,CAC3BC,MAAM,CAAE,cAAc,CACtBC,SAAS,CAAE,cAAc,CACzBzE,OAAO,CAAE,8BAA8B,CACvC0E,UAAU,CAAE,gGAAgG,CAC5G5E,QAAQ,CAAE,yBAAyB,CACnC7E,MAAM,CAAE,cAAc,CACtB0J,UAAU,CAAE,cACd,CAAC,CACDwxB,eAAe,CAAE,CACfrjC,KAAK,CAAE,yBAAyB,CAChCQ,WAAW,CAAE,0FAA0F,CACvG2Q,YAAY,CAAE,oBAAoB,CAClCC,iBAAiB,CAAE,QAAQ,CAC3BC,eAAe,CAAE,MAAM,CACvBC,iBAAiB,CAAE,UAAU,CAC7BE,OAAO,CAAE,SAAS,CAClBC,WAAW,CAAE,kBAAkB,CAC/BC,MAAM,CAAE,cAAc,CACtBC,SAAS,CAAE,cAAc,CACzBzE,OAAO,CAAE,8BAA8B,CACvC0E,UAAU,CAAE,0BAA0B,CACtCvE,aAAa,CAAE,iBAAiB,CAChClF,MAAM,CAAE,cAAc,CACtB2J,kBAAkB,CAAE,6DAA6D,CACjFC,aAAa,CAAE,kCAAkC,CACjDC,aAAa,CAAE,mCAAmC,CAClDC,eAAe,CAAE,6BAA6B,CAC9CC,gBAAgB,CAAE,yCACpB,CAAC,CACDqe,UAAU,CAAE,oBAAoB,CAChCC,qBAAqB,CAAE,mDAAmD,CAC1EC,4BAA4B,CAAE,oDAAoD,CAClFC,kBAAkB,CAAE,8CAA8C,CAClEC,gBAAgB,CAAE,CAChBC,QAAQ,CAAE,qBAAqB,CAC/B/vB,OAAO,CAAE,6BAA6B,CACtCgQ,QAAQ,CAAE,qBACZ,CAAC,CACDggB,eAAe,CAAE,oBAAoB,CACrCC,kBAAkB,CAAE,qBAAqB,CACzCC,cAAc,CAAE,kCAAkC,CAClDC,MAAM,CAAE,SAAS,CACjB9sB,MAAM,CAAE,WAAW,CACnB8rB,YAAY,CAAE,iCAAiC,CAE/CsT,cAAc,CAAE,CACd93B,KAAK,CAAE,sIAAsI,CAC7I+3B,MAAM,CAAE,8KAA8K,CACtLC,MAAM,CAAE,6KAA6K,CACrLC,MAAM,CAAE,qRAAqR,CAC7RC,MAAM,CAAE,8KAA8K,CACtLC,MAAM,CAAE,4MAA4M,CACpNC,MAAM,CAAE,uKAAuK,CAC/KC,UAAU,CAAE,kNACd,CACF,CACF,CACF,CAAC,CACDtB,EAAE,CAAE,CACF3iC,WAAW,CAAE,CACX,GAAGF,SAAS,CAAC6iC,EAAE,CAAC3iC,WAAW,CAC3B8iC,aAAa,CAAE,CACb1iC,KAAK,CAAE,6BAA6B,CACpCwL,KAAK,CAAE,kKAAkK,CACzKm3B,aAAa,CAAE,CACb3iC,KAAK,CAAE,sBACT,CAAC,CACD8gC,aAAa,CAAE,CACb9gC,KAAK,CAAE,cAAc,CACrB8F,OAAO,CAAE,CACP9F,KAAK,CAAE,YAAY,CACnBQ,WAAW,CAAE,gGACf,CAAC,CACDugC,YAAY,CAAE,CACZ/gC,KAAK,CAAE,cAAc,CACrBQ,WAAW,CAAE,gNACf,CAAC,CACDwgC,YAAY,CAAE,CACZhhC,KAAK,CAAE,kBAAkB,CACzBQ,WAAW,CAAE,+FACf,CAAC,CACDoiC,UAAU,CAAE,CACV5iC,KAAK,CAAE,mBAAmB,CAC1BQ,WAAW,CAAE,0EACf,CACF,CAAC,CACDqiC,aAAa,CAAE,CACb7iC,KAAK,CAAE,cAAc,CACrBmhC,YAAY,CAAE,CACZnhC,KAAK,CAAE,mBAAmB,CAC1BQ,WAAW,CAAE,2EACf,CAAC,CACDugC,YAAY,CAAE,CACZ/gC,KAAK,CAAE,8BAA8B,CACrCQ,WAAW,CAAE,+LACf,CAAC,CACDwgC,YAAY,CAAE,CACZhhC,KAAK,CAAE,kBAAkB,CACzBQ,WAAW,CAAE,mEACf,CAAC,CACDoiC,UAAU,CAAE,CACV5iC,KAAK,CAAE,mBAAmB,CAC1BQ,WAAW,CAAE,oJACf,CACF,CAAC,CACD4gC,YAAY,CAAE,CACZphC,KAAK,CAAE,cAAc,CACrB8iC,QAAQ,CAAE,gDAAgD,CAC1DC,aAAa,CAAE,6GACjB,CAAC,CACDC,gBAAgB,CAAE,CAChBhjC,KAAK,CAAE,eAAe,CACtBQ,WAAW,CAAE,qDAAqD,CAClEyiC,KAAK,CAAE,CACL,CAAC,CAAE,mCAAmC,CACtC,CAAC,CAAE,iCAAiC,CACpC,CAAC,CAAE,iCAAiC,CACpC,CAAC,CAAE,mCAAmC,CACtC,CAAC,CAAE,yCACL,CAAC,CACDC,WAAW,CAAE,yKACf,CAAC,CAEDC,SAAS,CAAE,CACTC,QAAQ,CAAE,iDAAiD,CAC3DrT,QAAQ,CAAE,sDACZ,CACF,CAAC,CACD1uB,OAAO,CAAE,CACP,GAAG3B,SAAS,CAAC6iC,EAAE,CAAC3iC,WAAW,CAACyB,OAAO,CACnCyR,SAAS,CAAE,OAAO,CAClBC,WAAW,CAAE,YAAY,CACzBC,YAAY,CAAE,eAAe,CAC7BI,cAAc,CAAE,cAAc,CAC9Bke,eAAe,CAAE,YAAY,CAC7BC,eAAe,CAAE,YAAY,CAC7BC,cAAc,CAAE,iCAAiC,CACjDC,cAAc,CAAE,wBAAwB,CACxCC,gBAAgB,CAAE,yBAAyB,CAC3CC,2BAA2B,CAAE,wCAAwC,CACrEC,eAAe,CAAE,aAAa,CAC9BC,qBAAqB,CAAE,CACrBhxB,OAAO,CAAE,aAAa,CACtBixB,QAAQ,CAAE,SACZ,CAAC,CACD5gB,WAAW,CAAE,CACXlR,KAAK,CAAE,kBAAkB,CACzBQ,WAAW,CAAE,wFAAwF,CACrG2Q,YAAY,CAAE,iBAAiB,CAC/BC,iBAAiB,CAAE,mBAAmB,CACtCC,eAAe,CAAE,cAAc,CAC/BC,iBAAiB,CAAE,gBAAgB,CACnCE,OAAO,CAAE,OAAO,CAChBC,WAAW,CAAE,cAAc,CAC3BC,MAAM,CAAE,aAAa,CACrBC,SAAS,CAAE,eAAe,CAC1BzE,OAAO,CAAE,uBAAuB,CAChC0E,UAAU,CAAE,+EAA+E,CAC3F5E,QAAQ,CAAE,0BAA0B,CACpC7E,MAAM,CAAE,aAAa,CACrB0J,UAAU,CAAE,WACd,CAAC,CACDwxB,eAAe,CAAE,CACfrjC,KAAK,CAAE,wBAAwB,CAC/BQ,WAAW,CAAE,oFAAoF,CACjG2Q,YAAY,CAAE,iBAAiB,CAC/BC,iBAAiB,CAAE,QAAQ,CAC3BC,eAAe,CAAE,OAAO,CACxBC,iBAAiB,CAAE,OAAO,CAC1BE,OAAO,CAAE,OAAO,CAChBC,WAAW,CAAE,iBAAiB,CAC9BC,MAAM,CAAE,aAAa,CACrBC,SAAS,CAAE,eAAe,CAC1BzE,OAAO,CAAE,uBAAuB,CAChC0E,UAAU,CAAE,qBAAqB,CACjCvE,aAAa,CAAE,aAAa,CAC5BlF,MAAM,CAAE,aAAa,CACrB2J,kBAAkB,CAAE,6DAA6D,CACjFC,aAAa,CAAE,+CAA+C,CAC9DC,aAAa,CAAE,0CAA0C,CACzDC,eAAe,CAAE,wBAAwB,CACzCC,gBAAgB,CAAE,6CACpB,CAAC,CACDqe,UAAU,CAAE,QAAQ,CACpBC,qBAAqB,CAAE,wCAAwC,CAC/DC,4BAA4B,CAAE,yCAAyC,CACvEC,kBAAkB,CAAE,kCAAkC,CACtDC,gBAAgB,CAAE,CAChBC,QAAQ,CAAE,gBAAgB,CAC1B/vB,OAAO,CAAE,+BAA+B,CACxCgQ,QAAQ,CAAE,eACZ,CAAC,CACDggB,eAAe,CAAE,eAAe,CAChCC,kBAAkB,CAAE,gBAAgB,CACpCC,cAAc,CAAE,6BAA6B,CAC7CC,MAAM,CAAE,OAAO,CACf9sB,MAAM,CAAE,MAAM,CACd8rB,YAAY,CAAE,gCAAgC,CAE9CsT,cAAc,CAAE,CACd93B,KAAK,CAAE,mGAAmG,CAC1G+3B,MAAM,CAAE,+HAA+H,CACvIC,MAAM,CAAE,+GAA+G,CACvHC,MAAM,CAAE,6IAA6I,CACrJC,MAAM,CAAE,kHAAkH,CAC1HC,MAAM,CAAE,6IAA6I,CACrJC,MAAM,CAAE,4GAA4G,CACpHC,UAAU,CAAE,sIACd,CACF,CAAC,CACDjgC,KAAK,CAAE,CACL,GAAGlE,SAAS,CAAC6iC,EAAE,CAAC3iC,WAAW,CAACgE,KAAK,CACjCC,oBAAoB,CAAE,CACpB7D,KAAK,CAAE,mBAAmB,CAC1B8D,YAAY,CAAE,cAAc,CAC5BC,GAAG,CAAE,MAAM,CACXxD,IAAI,CAAE,SAAS,CACfc,OAAO,CAAE,QAAQ,CACjBZ,MAAM,CAAE,QAAQ,CAChB2B,WAAW,CAAE,wBAAwB,CACrC1B,MAAM,CAAE,QAAQ,CAChBsD,KAAK,CAAE,WAAW,CAClB3B,OAAO,CAAE,WAAW,CACpB4B,OAAO,CAAE,QAAQ,CACjBC,MAAM,CAAE,KAAK,CACbC,iBAAiB,CAAE,oBAAoB,CACvCC,gBAAgB,CAAE,WAAW,CAC7BC,aAAa,CAAE,oBAAoB,CACnCC,iBAAiB,CAAE,gBAAgB,CACnCC,cAAc,CAAE,2CAA2C,CAC3DC,aAAa,CAAE,wBAAwB,CACvCC,eAAe,CAAE,qBACnB,CAAC,CACD40B,QAAQ,CAAE,CACRr5B,KAAK,CAAE,cAAc,CACrBs5B,eAAe,CAAE,gBAAgB,CACjCtmB,YAAY,CAAE,eAAe,CAC7B8iB,YAAY,CAAE,kBAAkB,CAChCyD,iBAAiB,CAAE,oBAAoB,CACvCC,SAAS,CAAE,eAAe,CAC1BC,OAAO,CAAE,eAAe,CACxBC,MAAM,CAAE,OAAO,CACfC,WAAW,CAAE,aAAa,CAC1Bp5B,IAAI,CAAE,SAAS,CACf62B,OAAO,CAAE,UAAU,CACnB/1B,OAAO,CAAE,QAAQ,CACjBD,OAAO,CAAE,QAAQ,CACjBw4B,YAAY,CAAE,YAAY,CAC1BC,cAAc,CAAE,cAAc,CAC9BC,gBAAgB,CAAE,cAAc,CAChCC,eAAe,CAAE,cAAc,CAC/Bv1B,aAAa,CAAE,2BACjB,CAAC,CACD6S,SAAS,CAAE,CACTrX,KAAK,CAAE,aAAa,CACpB01B,cAAc,CAAE,+BAA+B,CAC/CC,QAAQ,CAAE,qDAAqD,CAC/DC,aAAa,CAAE,iBAAiB,CAChCC,mBAAmB,CAAE,iBAAiB,CACtCjb,aAAa,CAAE,eAAe,CAC9Bkb,YAAY,CAAE,kBAAkB,CAChCC,qBAAqB,CAAE,eAAe,CACtCC,cAAc,CAAE,eAAe,CAC/Bjb,aAAa,CAAE,iBAAiB,CAChCkb,YAAY,CAAE,gBAAgB,CAC9BC,kBAAkB,CAAE,oBAAoB,CACxCC,iBAAiB,CAAE,kBAAkB,CACrCC,kBAAkB,CAAE,iBAAiB,CACrCC,cAAc,CAAE,cAAc,CAC9BrsB,OAAO,CAAE,UAAU,CACnBqP,WAAW,CAAE,cAAc,CAC3Bid,cAAc,CAAE,qBAAqB,CACrCC,UAAU,CAAE,kBAAkB,CAC9BC,mBAAmB,CAAE,+BAA+B,CACpDC,oBAAoB,CAAE,iCAAiC,CACvDC,cAAc,CAAE,oBAAoB,CACpC51B,SAAS,CAAE,OAAO,CAClBE,SAAS,CAAE,MAAM,CACjBH,OAAO,CAAE,cAAc,CACvB6E,UAAU,CAAE,+BACd,CAAC,CACDixB,eAAe,CAAE,CACf32B,KAAK,CAAE,uBAAuB,CAC9B21B,QAAQ,CAAE,+DAA+D,CACzEiB,aAAa,CAAE,mBAAmB,CAClCC,SAAS,CAAE,cAAc,CACzBC,WAAW,CAAE,cAAc,CAC3BC,WAAW,CAAE,YAAY,CACzBC,aAAa,CAAE,eAAe,CAC9BC,QAAQ,CAAE,cAAc,CACxBlzB,GAAG,CAAE,MAAM,CACX1C,OAAO,CAAE,MAAM,CACfD,OAAO,CAAE,MAAM,CACf81B,KAAK,CAAE,aAAa,CACpBC,QAAQ,CAAE,kBAAkB,CAC5Bne,IAAI,CAAE,UAAU,CAChBrU,IAAI,CAAE,OAAO,CACbyyB,OAAO,CAAE,UAAU,CACnBC,QAAQ,CAAE,cAAc,CACxBC,SAAS,CAAE,YAAY,CACvBhwB,QAAQ,CAAE,OAAO,CACjB5G,MAAM,CAAE,QAAQ,CAChB62B,MAAM,CAAE,KAAK,CACbC,KAAK,CAAE,OACT,CAAC,CACDC,aAAa,CAAE,CACbz3B,KAAK,CAAE,kBACT,CAAC,CACDwkB,QAAQ,CAAE,CACRxkB,KAAK,CAAE,SAAS,CAChB6f,IAAI,CAAE,IAAI,CACVlb,IAAI,CAAE,OAAO,CACbM,OAAO,CAAE,SAAS,CAClB1E,IAAI,CAAE,SAAS,CACfG,MAAM,CAAE,QAAQ,CAChBG,OAAO,CAAE,cAAc,CACvB2E,QAAQ,CAAE,SAAS,CACnBzB,GAAG,CAAE,cAAc,CACnBqK,IAAI,CAAE,KAAK,CACXspB,KAAK,CAAE,IAAI,CACXvvB,MAAM,CAAE,KAAK,CACbqF,MAAM,CAAE,qBAAqB,CAC7B/H,UAAU,CAAE,eAAe,CAC3BC,UAAU,CAAE,oBAAoB,CAChCiyB,OAAO,CAAE,QAAQ,CACjBC,eAAe,CAAE,iBAAiB,CAClCC,SAAS,CAAE,KAAK,CAChB/sB,SAAS,CAAE,YAAY,CACvBgtB,SAAS,CAAE,qBAAqB,CAChC3sB,UAAU,CAAE,mBAAmB,CAC/B/C,aAAa,CAAE,aAAa,CAC5B2vB,aAAa,CAAE,2EAA2E,CAC1FC,aAAa,CAAE,sBAAsB,CACrC9R,WAAW,CAAE,oBACf,CAAC,CACD+R,cAAc,CAAE,CACdj4B,KAAK,CAAE,yBAAyB,CAChCQ,WAAW,CAAE,yDAAyD,CACtEyF,YAAY,CAAE,0HAA0H,CACxIiyB,gBAAgB,CAAE,yBAAyB,CAC3CC,iBAAiB,CAAE,gBAAgB,CACnC/qB,IAAI,CAAE,YAAY,CAClBgrB,WAAW,CAAE,qBAAqB,CAClCC,SAAS,CAAE,mBAAmB,CAC9B3yB,UAAU,CAAE,oBAAoB,CAChC4yB,cAAc,CAAE,mBAAmB,CACnCC,OAAO,CAAE,QAAQ,CACjBC,aAAa,CAAE,0BAA0B,CACzCC,kBAAkB,CAAE,mBAAmB,CACvCC,aAAa,CAAE,wEAAwE,CACvFC,SAAS,CAAE,sBAAsB,CACjCC,oBAAoB,CAAE,uCAAuC,CAC7DC,QAAQ,CAAE,mBAAmB,CAC7BC,gBAAgB,CAAE,gCAAgC,CAClDC,cAAc,CAAE,8BAA8B,CAC9CC,SAAS,CAAE,CACTC,WAAW,CAAE,qBAAqB,CAClCC,YAAY,CAAE,gCAAgC,CAC9CC,YAAY,CAAE,uBAAuB,CACrCC,eAAe,CAAE,eACnB,CACF,CAAC,CACDC,QAAQ,CAAE,CACRr5B,KAAK,CAAE,cAAc,CACrBs5B,eAAe,CAAE,gBAAgB,CACjCtmB,YAAY,CAAE,eAAe,CAC7B8iB,YAAY,CAAE,kBAAkB,CAChCyD,iBAAiB,CAAE,oBAAoB,CACvCC,SAAS,CAAE,eAAe,CAC1BC,OAAO,CAAE,eAAe,CACxBC,MAAM,CAAE,OAAO,CACfC,WAAW,CAAE,aAAa,CAC1Bp5B,IAAI,CAAE,SAAS,CACf62B,OAAO,CAAE,UAAU,CACnB/1B,OAAO,CAAE,QAAQ,CACjBD,OAAO,CAAE,QAAQ,CACjBw4B,YAAY,CAAE,YAAY,CAC1BC,cAAc,CAAE,cAAc,CAC9BC,gBAAgB,CAAE,cAAc,CAChCC,eAAe,CAAE,cAAc,CAC/Bv1B,aAAa,CAAE,2BACjB,CAAC,CACDw1B,kBAAkB,CAAE,CAClBh6B,KAAK,CAAE,oBAAoB,CAC3BQ,WAAW,CAAE,4DAA4D,CACzEy5B,iBAAiB,CAAE,mCAAmC,CACtDjhB,IAAI,CAAE,UAAU,CAChB5M,KAAK,CAAE,mBAAmB,CAC1B6M,IAAI,CAAE,OAAO,CACbnI,WAAW,CAAE,aAAa,CAC1BpQ,MAAM,CAAE,QAAQ,CAChB2B,OAAO,CAAE,WAAW,CACpB4lB,aAAa,CAAE,yBAAyB,CACxCiS,gBAAgB,CAAE,mCAAmC,CACrDzqB,WAAW,CAAE,qBAAqB,CAClC0qB,gBAAgB,CAAE,iBAAiB,CACnCC,YAAY,CAAE,6CAA6C,CAC3DC,UAAU,CAAE,gCAAgC,CAC5CC,UAAU,CAAE,uCAAuC,CACnDC,aAAa,CAAE,cAAc,CAC7BC,eAAe,CAAE,OAAO,CACxBC,aAAa,CAAE,4BAA4B,CAC3CC,gBAAgB,CAAE,kCAAkC,CACpDC,oBAAoB,CAAE,+FAA+F,CACrHC,SAAS,CAAE,qBAAqB,CAChCC,aAAa,CAAE,uDAAuD,CACtE9B,cAAc,CAAE,yCAClB,CACF,CACF,CACF,CACF,CAAC,CAEDz5B,IAAI,CACDwkC,GAAG,CAACvkC,gBAAgB,CAAC,CACrBwkC,IAAI,CAAC,CACJrkC,SAAS,CAAE+iC,eAAe,CAC1BuB,GAAG,CAAEC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,EAAI,IAAI,CAC7CC,WAAW,CAAE,IAAI,CACjBC,KAAK,CAAE,KAAK,CACZC,aAAa,CAAE,CACbC,WAAW,CAAE,KACf,CAAC,CACDC,KAAK,CAAE,CACLC,WAAW,CAAE,KACf,CAAC,CACD;AACAC,IAAI,CAAE,cAAc,CACpBC,SAAS,CAAE,IACb,CAAC,CAAC,CAEJ,cAAe,CAAAplC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}