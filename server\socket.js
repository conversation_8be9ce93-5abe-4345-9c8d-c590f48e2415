const jwt = require('jsonwebtoken');
const db = require('./db');
const config = require('./config/auth.config');

let io;

const init = (socketIo) => {
  io = socketIo;

  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;

      if (!token) {
        console.error('Socket auth error: No token provided');
        return next(new Error('Authentication error: No token provided'));
      }

      // Remove Bearer prefix if present
      const tokenValue = token.startsWith('Bearer ') ? token.slice(7) : token;

      try {
        const decoded = jwt.verify(tokenValue, config.secret);
        if (!decoded || !decoded.id) {
          console.error('Socket auth error: Invalid token payload', decoded);
          return next(new Error('Authentication error: Invalid token'));
        }

        // Get user from database
        const [users] = await db.pool.execute(
          'SELECT id, full_name, role FROM users WHERE id = ?',
          [decoded.id]
        ).catch(err => {
          console.error('Socket auth DB error:', err);
          throw new Error('Database error');
        });

        if (!users || users.length === 0) {
          console.error('Socket auth error: User not found', decoded.id);
          return next(new Error('Authentication error: User not found'));
        }

        const user = users[0];
        console.log('Socket auth success:', {
          userId: user.id,
          role: user.role,
          name: user.full_name
        });

        // Attach user data to socket
        socket.userId = user.id;
        socket.userRole = user.role;
        socket.user = user;

        next();
      } catch (jwtError) {
        console.error('Socket auth JWT error:', jwtError);
        return next(new Error('Authentication error: Invalid token'));
      }
    } catch (error) {
      console.error('Socket auth error:', error);
      next(new Error('Authentication error: ' + (error.message || 'Unknown error')));
    }
  });

  io.on('connection', async (socket) => {
    try {
      console.log('Socket connected:', {
        userId: socket.userId,
        role: socket.userRole,
        name: socket.user?.full_name
      });

      // Join user to their private room
      socket.join(`user:${socket.userId}`);

      // Join role-based room
      socket.join(`role:${socket.userRole}`);

      // Handle get_teacher_chats event
      socket.on('get_teacher_chats', async ({ teacherId }, callback) => {
        try {
          console.log('Fetching teacher chats for:', teacherId);

          if (socket.userId !== teacherId) {
            throw new Error('Unauthorized: Can only fetch own chats');
          }

          const [chats] = await db.pool.execute(`
            SELECT
              c.id,
              c.created_at,
              student.id as student_id,
              student.full_name as student_name,
              student.profile_picture_url as student_picture,
              (
                SELECT COUNT(*)
                FROM messages m
                WHERE m.conversation_id = c.id
                AND m.read_at IS NULL
                AND m.sender_id != ?
              ) as unread_count,
              (
                SELECT content
                FROM messages
                WHERE conversation_id = c.id
                ORDER BY created_at DESC
                LIMIT 1
              ) as last_message,
              (
                SELECT created_at
                FROM messages
                WHERE conversation_id = c.id
                ORDER BY created_at DESC
                LIMIT 1
              ) as last_message_time
            FROM conversations c
            JOIN users student ON c.student_id = student.id
            WHERE c.teacher_id = ?
            ORDER BY COALESCE(last_message_time, c.created_at) DESC
          `, [teacherId, teacherId]);

          console.log('Found chats:', chats);
          callback({ success: true, chats });
        } catch (error) {
          console.error('Error fetching teacher chats:', error);
          callback({ success: false, error: error.message });
        }
      });

      // Handle get_student_chats event
      socket.on('get_student_chats', async ({ studentId }, callback) => {
        try {
          console.log('Fetching student chats for:', studentId);

          if (socket.userId !== studentId) {
            throw new Error('Unauthorized: Can only fetch own chats');
          }

          const [chats] = await db.pool.execute(`
            SELECT
              c.id,
              c.created_at,
              teacher.id as teacher_id,
              CASE
                WHEN teacher.deleted_at IS NOT NULL THEN CONCAT(teacher.full_name, ' (محذوف)')
                ELSE teacher.full_name
              END as teacher_name,
              CASE
                WHEN teacher.deleted_at IS NOT NULL THEN NULL
                ELSE teacher.profile_picture_url
              END as teacher_picture,
              teacher.deleted_at as teacher_deleted_at,
              (
                SELECT COUNT(*)
                FROM messages m
                WHERE m.conversation_id = c.id
                AND m.read_at IS NULL
                AND m.sender_id != ?
              ) as unread_count,
              (
                SELECT content
                FROM messages
                WHERE conversation_id = c.id
                ORDER BY created_at DESC
                LIMIT 1
              ) as last_message,
              (
                SELECT created_at
                FROM messages
                WHERE conversation_id = c.id
                ORDER BY created_at DESC
                LIMIT 1
              ) as last_message_time
            FROM conversations c
            JOIN users teacher ON c.teacher_id = teacher.id
            WHERE c.student_id = ?
            ORDER BY COALESCE(last_message_time, c.created_at) DESC
          `, [studentId, studentId]);

          console.log('Found chats:', chats);
          callback({ success: true, chats });
        } catch (error) {
          console.error('Error fetching student chats:', error);
          callback({ success: false, error: error.message });
        }
      });

      // Handle get_chat_messages event
      socket.on('get_chat_messages', async ({ chatId }, callback) => {
        try {
          console.log('Fetching messages for chat:', chatId);

          // Verify user has access to this chat
          const [chat] = await db.pool.execute(
            'SELECT * FROM conversations WHERE id = ? AND (student_id = ? OR teacher_id = ?)',
            [chatId, socket.userId, socket.userId]
          );

          if (!chat.length) {
            throw new Error('Unauthorized: No access to this chat');
          }

          const [messages] = await db.pool.execute(`
            SELECT
              id,
              conversation_id,
              sender_id,
              content,
              read_at IS NOT NULL as is_read,
              UNIX_TIMESTAMP(created_at) as created_at
            FROM messages
            WHERE conversation_id = ?
            ORDER BY created_at ASC
          `, [chatId]);

          console.log('Found messages:', messages.length);
          callback({ success: true, messages });

          // Mark messages as read if recipient is requesting
          if (messages.length > 0) {
            const lastMessage = messages[messages.length - 1];
            if (lastMessage.sender_id !== socket.userId) {
              await db.pool.execute(
                'UPDATE messages SET read_at = CURRENT_TIMESTAMP WHERE conversation_id = ? AND sender_id != ? AND read_at IS NULL',
                [chatId, socket.userId]
              );
            }
          }
        } catch (error) {
          console.error('Error fetching chat messages:', error);
          callback({ success: false, error: error.message });
        }
      });

      // Handle send_message event
      socket.on('send_message', async ({ chatId, content, tempId, timestamp }) => {
        try {
          // Convert timestamp to MySQL format
          const mysqlTimestamp = new Date(timestamp * 1000).toISOString().slice(0, 19).replace('T', ' ');

          const [result] = await db.pool.execute(
            'INSERT INTO messages (conversation_id, sender_id, recipient_id, content, created_at) ' +
            'SELECT ?, ?, ' +
            'CASE ' +
            '  WHEN c.student_id = ? THEN c.teacher_id ' +
            '  ELSE c.student_id ' +
            'END, ?, ? ' +
            'FROM conversations c WHERE c.id = ?',
            [chatId, socket.user.id, socket.user.id, content, mysqlTimestamp, chatId]
          );

          const messageId = result.insertId;

          // Fetch the complete message details
          const [messages] = await db.pool.execute(
            'SELECT * FROM messages WHERE id = ?',
            [messageId]
          );

          const message = messages[0];

          // Get the recipient's socket
          const [conversation] = await db.pool.execute(
            'SELECT * FROM conversations WHERE id = ?',
            [chatId]
          );

          if (conversation[0]) {
            const recipientId = conversation[0].student_id === socket.user.id
              ? conversation[0].teacher_id
              : conversation[0].student_id;

            // Emit only to recipient
            io.to(`user:${recipientId}`).emit('new_message', message);
          }

          // Send confirmation to sender with tempId
          socket.emit('message_sent', { success: true, message, tempId });
        } catch (error) {
          console.error('Error sending message:', error);
          socket.emit('message_sent', { success: false, error: error.message, tempId });
        }
      });

      // Handle mark_messages_read event
      socket.on('mark_messages_read', async ({ chatId }) => {
        try {
          console.log('Marking messages as read:', chatId);

          // Update messages
          const [result] = await db.pool.execute(
            'UPDATE messages SET read_at = CURRENT_TIMESTAMP WHERE conversation_id = ? AND sender_id != ? AND read_at IS NULL',
            [chatId, socket.userId]
          );

          if (result.affectedRows > 0) {
            // Get chat details to notify both participants
            const [chat] = await db.pool.execute(
              'SELECT * FROM conversations WHERE id = ?',
              [chatId]
            );

            if (chat.length > 0) {
              const senderId = chat[0].student_id === socket.userId ? chat[0].teacher_id : chat[0].student_id;
              // Notify the sender that their messages were read
              socket.to(`user:${senderId}`).emit('messages_read', { chatId });
              // Also notify the reader (current user) to update their unread count
              socket.emit('messages_read', { chatId });
            }
          }
        } catch (error) {
          console.error('Error marking messages as read:', error);
        }
      });

      // Handle delete_message event
      socket.on('delete_message', async ({ messageId, userId }, callback) => {
        try {
          console.log('Deleting message:', messageId, 'by user:', userId);

          // Verify message ownership and get conversation details
          const [message] = await db.pool.execute(`
            SELECT m.*, c.student_id, c.teacher_id
            FROM messages m
            JOIN conversations c ON m.conversation_id = c.id
            WHERE m.id = ? AND m.sender_id = ?
          `, [messageId, userId]);

          if (!message.length) {
            console.error('Message not found or unauthorized');
            return callback({
              success: false,
              error: 'You are not authorized to delete this message'
            });
          }

          // Delete the message
          await db.pool.execute('DELETE FROM messages WHERE id = ?', [messageId]);

          const msg = message[0];
          console.log('Message deleted, notifying users:', {
            studentId: msg.student_id,
            teacherId: msg.teacher_id
          });

          // Emit to both participants
          io.to(`user:${msg.student_id}`).emit('message_deleted', { messageId });
          io.to(`user:${msg.teacher_id}`).emit('message_deleted', { messageId });

          callback({ success: true });
        } catch (error) {
          console.error('Error deleting message:', error);
          callback({ success: false, error: 'Failed to delete message' });
        }
      });

      // Handle edit_message event
      socket.on('edit_message', async ({ messageId, content, userId }, callback) => {
        try {
          console.log('Editing message:', messageId, 'by user:', userId);

          // Verify message ownership and get conversation details
          const [message] = await db.pool.execute(`
            SELECT m.*, c.student_id, c.teacher_id
            FROM messages m
            JOIN conversations c ON m.conversation_id = c.id
            WHERE m.id = ? AND m.sender_id = ?
          `, [messageId, userId]);

          if (!message.length) {
            console.error('Message not found or unauthorized');
            return callback({
              success: false,
              error: 'You are not authorized to edit this message'
            });
          }

          // Update the message
          await db.pool.execute(
            'UPDATE messages SET content = ? WHERE id = ?',
            [content, messageId]
          );

          const msg = message[0];
          console.log('Message updated, notifying users:', {
            studentId: msg.student_id,
            teacherId: msg.teacher_id
          });

          // Emit to both participants
          io.to(`user:${msg.student_id}`).emit('message_updated', { messageId, content });
          io.to(`user:${msg.teacher_id}`).emit('message_updated', { messageId, content });

          callback({ success: true });
        } catch (error) {
          console.error('Error editing message:', error);
          callback({ success: false, error: 'Failed to edit message' });
        }
      });

      // Handle delete_chat event
      socket.on('delete_chat', async ({ chatId, userId }, callback) => {
        try {
          console.log('Deleting chat:', chatId, 'by user:', userId);

          // Verify chat access
          const [chat] = await db.pool.execute(
            'SELECT * FROM conversations WHERE id = ? AND (student_id = ? OR teacher_id = ?)',
            [chatId, userId, userId]
          );

          if (!chat.length) {
            console.error('Chat not found or unauthorized');
            return callback({
              success: false,
              error: 'You are not authorized to delete this conversation'
            });
          }

          // Get the other participant's ID for notification
          const otherUserId = chat[0].student_id === userId ? chat[0].teacher_id : chat[0].student_id;

          // Delete all messages in the conversation
          await db.pool.execute('DELETE FROM messages WHERE conversation_id = ?', [chatId]);

          // Delete the conversation
          await db.pool.execute('DELETE FROM conversations WHERE id = ?', [chatId]);

          console.log('Chat deleted, notifying users');

          // Notify the other participant
          io.to(`user:${otherUserId}`).emit('chat_deleted', { chatId });

          callback({ success: true });
        } catch (error) {
          console.error('Error deleting chat:', error);
          callback({ success: false, error: 'Failed to delete conversation' });
        }
      });

      socket.on('disconnect', () => {
        console.log('Socket disconnected:', {
          userId: socket.userId,
          role: socket.userRole
        });
      });

    } catch (error) {
      console.error('Socket connection error:', error);
    }
  });
};

const getIO = () => {
  if (!io) {
    throw new Error('Socket.io not initialized');
  }
  return io;
};

// Helper function to emit to a specific user
const emitToUser = (userId, event, data) => {
  const io = getIO();
  io.sockets.sockets.forEach(socket => {
    if (socket.userId === userId) {
      socket.emit(event, data);
    }
  });
};

// Helper function to emit to a specific role
const emitToRole = (role, event, data) => {
  const io = getIO();
  io.sockets.sockets.forEach(socket => {
    if (socket.userRole === role) {
      socket.emit(event, data);
    }
  });
};

module.exports = {
  init,
  getIO,
  emitToUser,
  emitToRole
};
