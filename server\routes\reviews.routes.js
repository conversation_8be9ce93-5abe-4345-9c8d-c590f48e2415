const express = require('express');
const router = express.Router();
const db = require('../config/db');
const authMiddleware = require('../middleware/auth.middleware');
const verifyToken = authMiddleware.verifyToken;
const isStudent = authMiddleware.isStudent;
const isTeacher = authMiddleware.isTeacher;

// Get all reviews for a teacher
router.get('/teacher/:teacherId', verifyToken, async (req, res) => {
  const { teacherId } = req.params;

  console.log('Fetching reviews for teacher ID:', teacherId);

  try {
    const connection = await db.pool.getConnection();

    // First, check if the ID is a user ID and get the corresponding teacher profile ID
    const [teacherProfile] = await connection.execute(`
      SELECT id
      FROM teacher_profiles
      WHERE user_id = ?
    `, [teacherId]);

    let profileId;

    if (teacherProfile.length > 0) {
      // If we found a teacher profile with this user ID, use its ID
      profileId = teacherProfile[0].id;
      console.log('Found teacher profile ID:', profileId, 'for user ID:', teacherId);
    } else {
      // Otherwise, assume the ID is already a teacher profile ID
      profileId = teacherId;

      // Check if this profile ID exists
      const [teacherExists] = await connection.execute(`
        SELECT COUNT(*) as count
        FROM teacher_profiles
        WHERE id = ?
      `, [profileId]);

      if (teacherExists[0].count === 0) {
        connection.release();
        console.log('Teacher not found with ID:', teacherId);
        return res.status(404).json({
          success: false,
          message: 'Teacher not found'
        });
      }
    }

    const [reviews] = await connection.execute(`
      SELECT r.*, u.full_name as student_name, u.profile_picture_url as student_profile_picture,
             rr.reply_text, rr.created_at as reply_created_at
      FROM reviews r
      JOIN users u ON r.student_id = u.id
      LEFT JOIN review_replies rr ON r.id = rr.review_id
      WHERE r.teacher_profile_id = ?
      ORDER BY r.created_at DESC
    `, [profileId]);

    console.log('Found reviews:', reviews.length);

    // Calculate average rating
    const [avgRating] = await connection.execute(`
      SELECT AVG(rating) as average_rating, COUNT(*) as review_count
      FROM reviews
      WHERE teacher_profile_id = ?
    `, [profileId]);

    connection.release();

    console.log('Average rating:', avgRating[0].average_rating, 'Review count:', avgRating[0].review_count);

    res.json({
      success: true,
      reviews,
      average_rating: avgRating[0].average_rating || 0,
      review_count: avgRating[0].review_count || 0
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch reviews'
    });
  }
});

// Get reviews written by a student
router.get('/student', verifyToken, isStudent, async (req, res) => {
  try {
    const connection = await db.pool.getConnection();

    const [reviews] = await connection.execute(`
      SELECT r.*, tp.user_id,
        CASE
          WHEN u.deleted_at IS NOT NULL THEN CONCAT(u.full_name, ' (محذوف)')
          ELSE u.full_name
        END as teacher_name,
        CASE
          WHEN u.deleted_at IS NOT NULL THEN NULL
          ELSE u.profile_picture_url
        END as teacher_profile_picture,
        u.deleted_at as teacher_deleted_at
      FROM reviews r
      JOIN teacher_profiles tp ON r.teacher_profile_id = tp.id
      JOIN users u ON tp.user_id = u.id
      WHERE r.student_id = ?
      ORDER BY r.created_at DESC
    `, [req.user.id]);

    connection.release();

    res.json({
      success: true,
      reviews
    });
  } catch (error) {
    console.error('Error fetching student reviews:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch reviews'
    });
  }
});

// Create or update a review
router.post('/', verifyToken, isStudent, async (req, res) => {
  const { teacher_profile_id, rating, comment } = req.body;
  const student_id = req.user.id;

  console.log('Creating review with data:', {
    teacher_profile_id,
    rating,
    comment,
    student_id
  });

  // Validate required fields
  if (!teacher_profile_id || !rating) {
    console.log('Missing required fields:', { teacher_profile_id, rating });
    return res.status(400).json({
      success: false,
      message: 'Teacher ID and rating are required'
    });
  }

  // Validate rating (between 1 and 5)
  if (rating < 1 || rating > 5) {
    return res.status(400).json({
      success: false,
      message: 'Rating must be between 1 and 5'
    });
  }

  try {
    const connection = await db.pool.getConnection();
    await connection.beginTransaction();

    // Check if the student has had a lesson with this teacher
    const [bookings] = await connection.execute(`
      SELECT COUNT(*) as booking_count
      FROM bookings
      WHERE teacher_profile_id = ? AND student_id = ? AND status = 'completed'
    `, [teacher_profile_id, student_id]);

    if (bookings[0].booking_count === 0) {
      await connection.rollback();
      connection.release();
      return res.status(400).json({
        success: false,
        message: 'You can only review teachers you have had lessons with'
      });
    }

    // Check if review already exists
    const [existingReview] = await connection.execute(`
      SELECT id FROM reviews
      WHERE teacher_profile_id = ? AND student_id = ?
    `, [teacher_profile_id, student_id]);

    let result;

    if (existingReview.length > 0) {
      // Update existing review
      [result] = await connection.execute(`
        UPDATE reviews
        SET rating = ?, comment = ?, updated_at = CURRENT_TIMESTAMP
        WHERE teacher_profile_id = ? AND student_id = ?
      `, [rating, comment, teacher_profile_id, student_id]);

      await connection.commit();
      connection.release();

      res.json({
        success: true,
        message: 'Review updated successfully',
        review_id: existingReview[0].id
      });
    } else {
      // Create new review
      [result] = await connection.execute(`
        INSERT INTO reviews (teacher_profile_id, student_id, rating, comment)
        VALUES (?, ?, ?, ?)
      `, [teacher_profile_id, student_id, rating, comment]);

      await connection.commit();
      connection.release();

      res.status(201).json({
        success: true,
        message: 'Review created successfully',
        review_id: result.insertId
      });
    }
  } catch (error) {
    console.error('Error creating/updating review:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create/update review'
    });
  }
});

// Update a review
router.put('/:id', verifyToken, isStudent, async (req, res) => {
  const { id } = req.params;
  const { rating, comment } = req.body;

  // Validate required fields
  if (!rating) {
    return res.status(400).json({
      success: false,
      message: 'Rating is required'
    });
  }

  // Validate rating (between 1 and 5)
  if (rating < 1 || rating > 5) {
    return res.status(400).json({
      success: false,
      message: 'Rating must be between 1 and 5'
    });
  }

  try {
    const connection = await db.pool.getConnection();

    // Check if the review belongs to the student
    const [review] = await connection.execute(`
      SELECT * FROM reviews
      WHERE id = ? AND student_id = ?
    `, [id, req.user.id]);

    if (review.length === 0) {
      connection.release();
      return res.status(404).json({
        success: false,
        message: 'Review not found or does not belong to you'
      });
    }

    // Update the review
    await connection.execute(`
      UPDATE reviews
      SET rating = ?, comment = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [rating, comment, id]);

    connection.release();

    res.json({
      success: true,
      message: 'Review updated successfully'
    });
  } catch (error) {
    console.error('Error updating review:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update review'
    });
  }
});

// Delete a review
router.delete('/:id', verifyToken, isStudent, async (req, res) => {
  const { id } = req.params;

  try {
    const connection = await db.pool.getConnection();

    // Check if the review belongs to the student
    const [review] = await connection.execute(`
      SELECT * FROM reviews
      WHERE id = ? AND student_id = ?
    `, [id, req.user.id]);

    if (review.length === 0) {
      connection.release();
      return res.status(404).json({
        success: false,
        message: 'Review not found or does not belong to you'
      });
    }

    // Delete the review
    await connection.execute(`
      DELETE FROM reviews
      WHERE id = ?
    `, [id]);

    connection.release();

    res.json({
      success: true,
      message: 'Review deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting review:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete review'
    });
  }
});

// Create or update a reply to a review (Teachers only)
router.post('/:reviewId/reply', verifyToken, isTeacher, async (req, res) => {
  const { reviewId } = req.params;
  const { reply_text } = req.body;
  const teacher_id = req.user.id;

  console.log('Creating reply with data:', {
    reviewId,
    reply_text,
    teacher_id
  });

  // Validate required fields
  if (!reply_text || reply_text.trim() === '') {
    return res.status(400).json({
      success: false,
      message: 'Reply text is required'
    });
  }

  try {
    const connection = await db.pool.getConnection();
    await connection.beginTransaction();

    // Check if the review exists and belongs to this teacher
    const [review] = await connection.execute(`
      SELECT r.*, tp.user_id as teacher_user_id
      FROM reviews r
      JOIN teacher_profiles tp ON r.teacher_profile_id = tp.id
      WHERE r.id = ?
    `, [reviewId]);

    if (review.length === 0) {
      await connection.rollback();
      connection.release();
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Check if the teacher owns this review
    if (review[0].teacher_user_id !== teacher_id) {
      await connection.rollback();
      connection.release();
      return res.status(403).json({
        success: false,
        message: 'You can only reply to reviews for your profile'
      });
    }

    // Check if reply already exists
    const [existingReply] = await connection.execute(`
      SELECT id FROM review_replies
      WHERE review_id = ?
    `, [reviewId]);

    let result;

    if (existingReply.length > 0) {
      // Update existing reply
      [result] = await connection.execute(`
        UPDATE review_replies
        SET reply_text = ?, updated_at = CURRENT_TIMESTAMP
        WHERE review_id = ?
      `, [reply_text, reviewId]);

      await connection.commit();
      connection.release();

      res.json({
        success: true,
        message: 'Reply updated successfully',
        reply_id: existingReply[0].id
      });
    } else {
      // Create new reply
      [result] = await connection.execute(`
        INSERT INTO review_replies (review_id, teacher_id, reply_text)
        VALUES (?, ?, ?)
      `, [reviewId, teacher_id, reply_text]);

      await connection.commit();
      connection.release();

      res.status(201).json({
        success: true,
        message: 'Reply created successfully',
        reply_id: result.insertId
      });
    }
  } catch (error) {
    console.error('Error creating/updating reply:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create/update reply'
    });
  }
});

// Delete a reply (Teachers only)
router.delete('/:reviewId/reply', verifyToken, isTeacher, async (req, res) => {
  const { reviewId } = req.params;
  const teacher_id = req.user.id;

  try {
    const connection = await db.pool.getConnection();

    // Check if the reply exists and belongs to this teacher
    const [reply] = await connection.execute(`
      SELECT rr.*, r.teacher_profile_id, tp.user_id as teacher_user_id
      FROM review_replies rr
      JOIN reviews r ON rr.review_id = r.id
      JOIN teacher_profiles tp ON r.teacher_profile_id = tp.id
      WHERE rr.review_id = ? AND rr.teacher_id = ?
    `, [reviewId, teacher_id]);

    if (reply.length === 0) {
      connection.release();
      return res.status(404).json({
        success: false,
        message: 'Reply not found or does not belong to you'
      });
    }

    // Delete the reply
    await connection.execute(`
      DELETE FROM review_replies
      WHERE review_id = ? AND teacher_id = ?
    `, [reviewId, teacher_id]);

    connection.release();

    res.json({
      success: true,
      message: 'Reply deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting reply:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete reply'
    });
  }
});

// Create or update a reply to a review (Teachers only)
router.post('/:reviewId/reply', verifyToken, isTeacher, async (req, res) => {
  const { reviewId } = req.params;
  const { reply_text } = req.body;
  const teacher_id = req.user.id;

  console.log('Creating reply with data:', {
    reviewId,
    reply_text,
    teacher_id
  });

  // Validate required fields
  if (!reply_text || reply_text.trim() === '') {
    return res.status(400).json({
      success: false,
      message: 'Reply text is required'
    });
  }

  try {
    const connection = await db.pool.getConnection();
    await connection.beginTransaction();

    // Check if the review exists and belongs to this teacher
    const [review] = await connection.execute(`
      SELECT r.*, tp.user_id as teacher_user_id
      FROM reviews r
      JOIN teacher_profiles tp ON r.teacher_profile_id = tp.id
      WHERE r.id = ?
    `, [reviewId]);

    if (review.length === 0) {
      await connection.rollback();
      connection.release();
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Check if the teacher owns this review
    if (review[0].teacher_user_id !== teacher_id) {
      await connection.rollback();
      connection.release();
      return res.status(403).json({
        success: false,
        message: 'You can only reply to reviews for your profile'
      });
    }

    // Check if reply already exists
    const [existingReply] = await connection.execute(`
      SELECT id FROM review_replies
      WHERE review_id = ?
    `, [reviewId]);

    let result;

    if (existingReply.length > 0) {
      // Update existing reply
      [result] = await connection.execute(`
        UPDATE review_replies
        SET reply_text = ?, updated_at = CURRENT_TIMESTAMP
        WHERE review_id = ?
      `, [reply_text, reviewId]);

      await connection.commit();
      connection.release();

      res.json({
        success: true,
        message: 'Reply updated successfully',
        reply_id: existingReply[0].id
      });
    } else {
      // Create new reply
      [result] = await connection.execute(`
        INSERT INTO review_replies (review_id, teacher_id, reply_text)
        VALUES (?, ?, ?)
      `, [reviewId, teacher_id, reply_text]);

      await connection.commit();
      connection.release();

      res.status(201).json({
        success: true,
        message: 'Reply created successfully',
        reply_id: result.insertId
      });
    }
  } catch (error) {
    console.error('Error creating/updating reply:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create/update reply'
    });
  }
});

// Delete a reply (Teachers only)
router.delete('/:reviewId/reply', verifyToken, isTeacher, async (req, res) => {
  const { reviewId } = req.params;
  const teacher_id = req.user.id;

  try {
    const connection = await db.pool.getConnection();

    // Check if the reply exists and belongs to this teacher
    const [reply] = await connection.execute(`
      SELECT rr.*, r.teacher_profile_id, tp.user_id as teacher_user_id
      FROM review_replies rr
      JOIN reviews r ON rr.review_id = r.id
      JOIN teacher_profiles tp ON r.teacher_profile_id = tp.id
      WHERE rr.review_id = ? AND rr.teacher_id = ?
    `, [reviewId, teacher_id]);

    if (reply.length === 0) {
      connection.release();
      return res.status(404).json({
        success: false,
        message: 'Reply not found or does not belong to you'
      });
    }

    // Delete the reply
    await connection.execute(`
      DELETE FROM review_replies
      WHERE review_id = ? AND teacher_id = ?
    `, [reviewId, teacher_id]);

    connection.release();

    res.json({
      success: true,
      message: 'Reply deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting reply:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete reply'
    });
  }
});

module.exports = router;
