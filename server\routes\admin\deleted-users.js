const express = require('express');
const router = express.Router();
const db = require('../../config/db');
const { restoreUser, hardDeleteUser } = require('../../scripts/processScheduledDeletions');

// Get all soft deleted users with pagination and filters
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const role = req.query.role || '';

    let query = `
      SELECT 
        u.id,
        u.full_name,
        u.email,
        u.role,
        u.gender,
        u.profile_picture_url,
        u.created_at,
        u.deleted_at,
        u.deletion_reason,
        u.deleted_by,
        admin.full_name as deleted_by_name
      FROM users u
      LEFT JOIN users admin ON u.deleted_by = admin.id
      WHERE u.deleted_at IS NOT NULL
    `;

    let countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      WHERE u.deleted_at IS NOT NULL
    `;

    const queryParams = [];
    
    if (search) {
      query += ` AND (u.full_name LIKE ? OR u.email LIKE ?)`;
      countQuery += ` AND (u.full_name LIKE ? OR u.email LIKE ?)`;
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    if (role) {
      query += ` AND u.role = ?`;
      countQuery += ` AND u.role = ?`;
      queryParams.push(role);
    }

    query += ` ORDER BY u.deleted_at DESC LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    const [users] = await db.pool.query(query, queryParams);
    const [totalCount] = await db.pool.query(countQuery, queryParams.slice(0, -2));

    res.json({
      users,
      total: totalCount[0].total,
      page,
      limit
    });
  } catch (error) {
    console.error('Error fetching deleted users:', error);
    res.status(500).json({ 
      success: false,
      message: 'Internal server error' 
    });
  }
});

// Get deleted user details
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [user] = await db.pool.query(
      `SELECT 
        u.*,
        admin.full_name as deleted_by_name,
        scd.native_language,
        scd.islam_learning_language,
        scd.arabic_learning_language,
        scd.age,
        scd.country,
        scd.timezone,
        scd.arabic_proficiency_level,
        scd.private_tutoring_preference,
        scd.is_completed
      FROM users u
      LEFT JOIN users admin ON u.deleted_by = admin.id
      LEFT JOIN student_completion_data scd ON u.id = scd.user_id
      WHERE u.id = ? AND u.deleted_at IS NOT NULL`,
      [id]
    );

    if (!user.length) {
      return res.status(404).json({ 
        success: false,
        message: 'Deleted user not found' 
      });
    }

    res.json({
      success: true,
      user: user[0]
    });
  } catch (error) {
    console.error('Error fetching deleted user details:', error);
    res.status(500).json({ 
      success: false,
      message: 'Internal server error' 
    });
  }
});

// Restore soft deleted user
router.post('/:id/restore', async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من أن المستخدم محذوف ناعم
    const [userCheck] = await db.pool.query(
      'SELECT id, email, full_name, role, deleted_at FROM users WHERE id = ? AND deleted_at IS NOT NULL',
      [id]
    );

    if (userCheck.length === 0) {
      return res.status(404).json({ 
        success: false,
        message: 'Deleted user not found' 
      });
    }

    const user = userCheck[0];

    // Start transaction
    const connection = await db.pool.getConnection();
    await connection.beginTransaction();

    try {
      console.log(`Admin restoring user: ${user.email} (ID: ${user.id})`);
      
      // استخدام دالة الاسترداد
      await restoreUser(connection, user.id, user.email, req.user?.id);

      await connection.commit();
      
      console.log(`Successfully restored user: ${user.email}`);
      res.json({ 
        success: true,
        message: 'User restored successfully' 
      });
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error restoring user:', error);
    res.status(500).json({ 
      success: false,
      message: 'Internal server error' 
    });
  }
});

// Permanently delete user (hard delete)
router.delete('/:id/permanent', async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من أن المستخدم محذوف ناعم
    const [userCheck] = await db.pool.query(
      'SELECT id, email, full_name, role, deleted_at FROM users WHERE id = ? AND deleted_at IS NOT NULL',
      [id]
    );

    if (userCheck.length === 0) {
      return res.status(404).json({ 
        success: false,
        message: 'Deleted user not found' 
      });
    }

    const user = userCheck[0];

    // Start transaction
    const connection = await db.pool.getConnection();
    await connection.beginTransaction();

    try {
      console.log(`Admin permanently deleting user: ${user.email} (ID: ${user.id})`);
      
      // استخدام الحذف الصلب
      await hardDeleteUser(connection, user.id, user.email);

      await connection.commit();
      
      console.log(`Successfully permanently deleted user: ${user.email}`);
      res.json({ 
        success: true,
        message: 'User permanently deleted' 
      });
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error permanently deleting user:', error);
    res.status(500).json({ 
      success: false,
      message: 'Internal server error' 
    });
  }
});

// Get deletion statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const [stats] = await db.pool.query(`
      SELECT 
        COUNT(CASE WHEN deleted_at IS NOT NULL AND role = 'student' THEN 1 END) as deleted_students,
        COUNT(CASE WHEN deleted_at IS NOT NULL AND role IN ('platform_teacher', 'new_teacher') THEN 1 END) as deleted_teachers,
        COUNT(CASE WHEN deleted_at IS NOT NULL THEN 1 END) as total_deleted,
        COUNT(CASE WHEN deleted_at IS NULL THEN 1 END) as total_active,
        COUNT(*) as total_users
      FROM users
    `);

    res.json({
      success: true,
      stats: stats[0]
    });
  } catch (error) {
    console.error('Error fetching deletion stats:', error);
    res.status(500).json({ 
      success: false,
      message: 'Internal server error' 
    });
  }
});

module.exports = router;
