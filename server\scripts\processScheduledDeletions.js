const mysql = require('mysql2/promise');
const db = require('../config/db');

/**
 * دالة الحذف الناعم للمستخدم - تحتفظ بالبيانات مع تمييزها كمحذوفة
 * @param {Object} connection - اتصال قاعدة البيانات
 * @param {number} userId - معرف المستخدم
 * @param {string} userEmail - إيميل المستخدم للسجلات
 * @param {string} reason - سبب الحذف
 * @param {number|null} deletedBy - معرف من قام بالحذف (null للحذف الذاتي)
 */
async function softDeleteUser(connection, userId, userEmail = 'Unknown', reason = 'User requested deletion', deletedBy = null) {
  console.log(`Starting soft deletion for user: ${userEmail} (ID: ${userId})`);

  try {
    // التحقق من أن المستخدم موجود وغير محذوف
    const [userCheck] = await connection.execute(
      'SELECT id, full_name, email, role, deleted_at FROM users WHERE id = ?',
      [userId]
    );

    if (userCheck.length === 0) {
      throw new Error(`User with ID ${userId} not found`);
    }

    if (userCheck[0].deleted_at) {
      console.log(`User ${userId} is already soft deleted`);
      return;
    }

    // تنفيذ الحذف الناعم
    await connection.execute(`
      UPDATE users SET
        deleted_at = NOW(),
        deletion_reason = ?,
        deleted_by = ?,
        status = 'deleted'
      WHERE id = ?
    `, [reason, deletedBy, userId]);

    console.log(`Successfully soft deleted user: ${userEmail} (ID: ${userId})`);

  } catch (error) {
    console.error(`Error in softDeleteUser for user ${userId}:`, error);
    throw error;
  }
}

/**
 * دالة شاملة لحذف جميع بيانات المستخدم من قاعدة البيانات (الحذف الصلب)
 * هذه الدالة للحالات الاستثنائية فقط - يُفضل استخدام softDeleteUser
 * @param {Object} connection - اتصال قاعدة البيانات
 * @param {number} userId - معرف المستخدم
 * @param {string} userEmail - إيميل المستخدم للسجلات
 */
async function hardDeleteUser(connection, userId, userEmail = 'Unknown') {
  console.log(`Starting complete deletion for user: ${userEmail} (ID: ${userId})`);

  try {
    // حذف البيانات المرتبطة بالمستخدم بالترتيب الصحيح لتجنب مشاكل Foreign Keys

    // 1. حذف الأرباح الإدارية
    await connection.execute('DELETE FROM admin_earnings WHERE teacher_id = ? OR student_id = ?', [userId, userId]);
    console.log(`Deleted admin_earnings for user ${userId}`);

    // 2. حذف جلسات الاجتماعات
    await connection.execute('DELETE FROM meeting_sessions WHERE user_id = ?', [userId]);
    console.log(`Deleted meeting_sessions for user ${userId}`);

    // 3. حذف مشاكل الاجتماعات
    await connection.execute('DELETE FROM meeting_issues WHERE user_id = ?', [userId]);
    console.log(`Deleted meeting_issues for user ${userId}`);

    // 4. حذف الاجتماعات
    await connection.execute('DELETE FROM meetings WHERE teacher_id = ? OR student_id = ?', [userId, userId]);
    console.log(`Deleted meetings for user ${userId}`);

    // 5. حذف الحجوزات
    await connection.execute('DELETE FROM bookings WHERE student_id = ? OR teacher_id IN (SELECT id FROM teacher_profiles WHERE user_id = ?)', [userId, userId]);
    console.log(`Deleted bookings for user ${userId}`);

    // 6. حذف الرسائل
    await connection.execute('DELETE FROM messages WHERE sender_id = ? OR recipient_id = ?', [userId, userId]);
    console.log(`Deleted messages for user ${userId}`);

    // 7. حذف المحادثات
    await connection.execute('DELETE FROM conversations WHERE student_id = ? OR teacher_id = ?', [userId, userId]);
    console.log(`Deleted conversations for user ${userId}`);

    // 8. حذف الملاحظات
    await connection.execute('DELETE FROM notes WHERE teacher_id = ? OR student_id = ?', [userId, userId]);
    console.log(`Deleted notes for user ${userId}`);

    // 9. حذف ردود المراجعات
    await connection.execute('DELETE FROM review_replies WHERE teacher_id = ?', [userId]);
    console.log(`Deleted review_replies for user ${userId}`);

    // 10. حذف المراجعات
    await connection.execute('DELETE FROM reviews WHERE student_id = ? OR teacher_profile_id IN (SELECT id FROM teacher_profiles WHERE user_id = ?)', [userId, userId]);
    console.log(`Deleted reviews for user ${userId}`);

    // 11. حذف المدفوعات
    await connection.execute('DELETE FROM payments WHERE student_id = ? OR teacher_profile_id IN (SELECT id FROM teacher_profiles WHERE user_id = ?)', [userId, userId]);
    console.log(`Deleted payments for user ${userId}`);

    // 12. حذف المعاملات المالية
    await connection.execute('DELETE FROM transactions WHERE user_id = ?', [userId]);
    console.log(`Deleted transactions for user ${userId}`);

    // 13. حذف طلبات السحب
    await connection.execute('DELETE FROM withdrawal_requests WHERE teacher_id = ?', [userId]);
    console.log(`Deleted withdrawal_requests for user ${userId}`);

    // 14. حذف رسائل التواصل
    await connection.execute('DELETE FROM contact_messages WHERE user_id = ?', [userId]);
    console.log(`Deleted contact_messages for user ${userId}`);

    // 15. حذف بيانات إكمال الطلاب
    await connection.execute('DELETE FROM student_completion_data WHERE user_id = ?', [userId]);
    console.log(`Deleted student_completion_data for user ${userId}`);

    // 16. حذف الإجازات الأسبوعية للمعلمين
    await connection.execute('DELETE FROM teacher_weekly_breaks WHERE teacher_profile_id IN (SELECT id FROM teacher_profiles WHERE user_id = ?)', [userId]);
    console.log(`Deleted teacher_weekly_breaks for user ${userId}`);

    // 17. حذف طلبات تحديث ملف المعلم
    await connection.execute('DELETE FROM teacher_profile_updates WHERE user_id = ?', [userId]);
    console.log(`Deleted teacher_profile_updates for user ${userId}`);

    // 18. حذف لغات المعلم
    await connection.execute('DELETE FROM teacher_languages WHERE teacher_profile_id IN (SELECT id FROM teacher_profiles WHERE user_id = ?)', [userId]);
    console.log(`Deleted teacher_languages for user ${userId}`);

    // 19. حذف تصنيفات المعلم
    await connection.execute('DELETE FROM teacher_categories WHERE teacher_profile_id IN (SELECT id FROM teacher_profiles WHERE user_id = ?)', [userId]);
    console.log(`Deleted teacher_categories for user ${userId}`);

    // 20. حذف الملفات الشخصية للمعلمين
    await connection.execute('DELETE FROM teacher_profiles WHERE user_id = ?', [userId]);
    console.log(`Deleted teacher_profiles for user ${userId}`);

    // 21. حذف طلبات التسجيل كمعلم
    await connection.execute('DELETE FROM teacher_applications WHERE user_id = ?', [userId]);
    console.log(`Deleted teacher_applications for user ${userId}`);

    // 22. حذف الإشعارات
    await connection.execute('DELETE FROM notifications WHERE user_id = ?', [userId]);
    console.log(`Deleted notifications for user ${userId}`);

    // 23. حذف جلسات المستخدم
    await connection.execute('DELETE FROM user_sessions WHERE user_id = ?', [userId]);
    console.log(`Deleted user_sessions for user ${userId}`);

    // 24. حذف طلبات إعادة تعيين كلمة المرور
    await connection.execute('DELETE FROM password_reset_tokens WHERE user_id = ?', [userId]);
    console.log(`Deleted password_reset_tokens for user ${userId}`);

    // 25. حذف طلبات حذف الحساب
    await connection.execute('DELETE FROM user_delete_requests WHERE user_id = ?', [userId]);
    console.log(`Deleted user_delete_requests for user ${userId}`);

    // 26. أخيراً، حذف المستخدم نفسه
    await connection.execute('DELETE FROM users WHERE id = ?', [userId]);
    console.log(`Deleted user ${userId} from users table`);

    console.log(`Successfully completed deletion for user: ${userEmail} (ID: ${userId})`);

  } catch (error) {
    console.error(`Error in deleteUserCompletely for user ${userId}:`, error);
    throw error;
  }
}

/**
 * دالة استرداد المستخدم المحذوف ناعم
 * @param {Object} connection - اتصال قاعدة البيانات
 * @param {number} userId - معرف المستخدم
 * @param {string} userEmail - إيميل المستخدم للسجلات
 * @param {number|null} restoredBy - معرف من قام بالاسترداد
 */
async function restoreUser(connection, userId, userEmail = 'Unknown', restoredBy = null) {
  console.log(`Starting user restoration for: ${userEmail} (ID: ${userId})`);

  try {
    // التحقق من أن المستخدم محذوف ناعم
    const [userCheck] = await connection.execute(
      'SELECT id, full_name, email, role, deleted_at FROM users WHERE id = ? AND deleted_at IS NOT NULL',
      [userId]
    );

    if (userCheck.length === 0) {
      throw new Error(`User with ID ${userId} not found or not soft deleted`);
    }

    // استرداد المستخدم
    await connection.execute(`
      UPDATE users SET
        deleted_at = NULL,
        deletion_reason = NULL,
        deleted_by = NULL,
        status = 'active'
      WHERE id = ?
    `, [userId]);

    // تسجيل عملية الاسترداد
    console.log(`Successfully restored user: ${userEmail} (ID: ${userId}) by user: ${restoredBy || 'system'}`);

  } catch (error) {
    console.error(`Error in restoreUser for user ${userId}:`, error);
    throw error;
  }
}

async function processScheduledDeletions() {
  let connection;
  try {
    connection = await db.pool.getConnection();
    
    console.log('Starting scheduled account deletions process...');
    
    // البحث عن الحسابات المجدولة للحذف والتي انتهت مدتها (مقارنة بـ UTC)
    const [usersToDelete] = await connection.execute(`
      SELECT id, email, full_name, delete_scheduled_at
      FROM users
      WHERE status = 'pending_deletion'
      AND delete_scheduled_at <= UTC_TIMESTAMP()
    `);
    
    if (usersToDelete.length === 0) {
      console.log('No accounts scheduled for deletion at this time.');
      return;
    }
    
    console.log(`Found ${usersToDelete.length} accounts to delete.`);
    
    for (const user of usersToDelete) {
      try {
        // بدء transaction
        await connection.beginTransaction();

        // استخدام الحذف الناعم بدلاً من الحذف الكامل
        await softDeleteUser(connection, user.id, user.email, 'Scheduled deletion after 10 days', null);

        // تأكيد التغييرات
        await connection.commit();

        console.log(`Successfully deleted account for user: ${user.email}`);

      } catch (error) {
        // التراجع عن التغييرات في حالة الخطأ
        await connection.rollback();
        console.error(`Error deleting account for user ${user.email}:`, error);
      }
    }
    
    console.log('Scheduled account deletions process completed.');
    
  } catch (error) {
    console.error('Error in processScheduledDeletions:', error);
  } finally {
    if (connection) connection.release();
  }
}

// تشغيل الدالة إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  processScheduledDeletions()
    .then(() => {
      console.log('Process completed successfully.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Process failed:', error);
      process.exit(1);
    });
}

module.exports = {
  processScheduledDeletions,
  softDeleteUser,
  restoreUser,
  hardDeleteUser: hardDeleteUser // للحالات الاستثنائية فقط
};
