{"ast": null, "code": "import React,{useEffect}from'react';import{BrowserRouter as Router,Routes,Route,Navigate,useLocation}from'react-router-dom';import ResizeObserverFix from'./utils/ResizeObserverFix';import'./utils/axiosConfig';// إعداد axios للتعامل مع المستخدمين المحذوفين\nimport{ThemeProvider,createTheme}from'@mui/material/styles';import{CssBaseline,Box,CircularProgress}from'@mui/material';import{useTranslation}from'react-i18next';import{AuthProvider,useAuth}from'./contexts/AuthContext';import{SocketProvider}from'./contexts/SocketContext';import{UnreadMessagesProvider}from'./contexts/UnreadMessagesContext';import{Toaster}from'react-hot-toast';import{setupResizeObserverPolyfill}from'./utils/resizeObserver';import Header from'./components/layout/Header';import Footer from'./components/Footer';import AuthenticatedFooter from'./components/AuthenticatedFooter';import'./i18n/i18n';// Import pages here\nimport Home from'./pages/Home';import AboutUs from'./pages/AboutUs';import PlatformPolicy from'./pages/PlatformPolicy';import TermsAndConditions from'./pages/TermsAndConditions';import PrivacyPolicy from'./pages/PrivacyPolicy';import RefundPolicy from'./pages/RefundPolicy';import BookingPaymentPolicy from'./pages/BookingPaymentPolicy';import BookingCancellationPolicy from'./pages/BookingCancellationPolicy';import Login from'./pages/auth/Login';import PendingDeletion from'./pages/PendingDeletion';import RegisterChoice from'./pages/auth/RegisterChoice';import StudentRegister from'./pages/auth/StudentRegister';import TeacherRegister from'./pages/auth/TeacherRegister';import ForgotPassword from'./pages/auth/ForgotPassword';import VerifyResetCode from'./pages/auth/VerifyResetCode';import ResetPassword from'./pages/auth/ResetPassword';import VerifyEmail from'./pages/auth/VerifyEmail';import AdminDashboard from'./pages/admin/Dashboard';import AdminProfile from'./pages/admin/Profile';import TeacherApplications from'./pages/admin/TeacherApplications';import Teachers from'./pages/admin/Teachers';import Students from'./pages/admin/Students';import Categories from'./pages/admin/Categories';import Languages from'./pages/admin/Languages';import ProfileUpdates from'./pages/admin/ProfileUpdates';import MeetingSessions from'./pages/admin/MeetingSessions';import TeacherDashboard from'./pages/teacher/Dashboard';import TeacherApplication from'./pages/teacher/Application';import EditApplication from'./pages/teacher/EditApplication';import EditVideoUpload from'./pages/teacher/EditVideoUpload';import TeacherVideoUpload from'./pages/teacher/VideoUpload';import TeacherApplicationAvailableHours from'./pages/teacher/AvailableHours';import TeacherProfileAvailableHours from'./pages/teacher/TeacherAvailableHours';import TeacherViewAvailableHours from'./pages/teacher/ViewAvailableHours';import TeacherProfile from'./pages/teacher/Profile';import TeacherChat from'./pages/teacher/Chat';import TeacherMeetings from'./pages/teacher/Meetings';import TeacherBookings from'./pages/teacher/Bookings';import MyLessons from'./pages/teacher/MyLessons';import StudentDashboard from'./pages/student/Dashboard';import StudentProfile from'./pages/student/Profile';import CompleteProfile from'./pages/student/CompleteProfile';import StudentChat from'./pages/student/Chat';import StudentFindTeacher from'./pages/student/FindTeacher';import StudentTeacherProfile from'./pages/student/TeacherProfile';import StudentChatEmbed from'./pages/student/ChatEmbed';import FindTeacher from'./pages/FindTeacher';import TeacherDetails from'./pages/TeacherDetails';import JoinMeeting from'./pages/student/JoinMeeting';import StudentMeetings from'./pages/student/Meetings';import AdminWallet from'./pages/admin/Wallet';import TeacherWallet from'./pages/teacher/Wallet';import StudentWallet from'./pages/student/Wallet';import StudentContactUs from'./pages/student/ContactUs';import TeacherContactUs from'./pages/teacher/ContactUs';import AdminMessages from'./pages/admin/Messages';import StudentMyMessages from'./pages/student/MyMessages';import TeacherMyMessages from'./pages/teacher/MyMessages';import BookingPage from'./pages/student/BookingPage';import Bookings from'./pages/student/Bookings';import MyTeachers from'./pages/student/MyTeachers';import WriteReview from'./pages/student/WriteReview';import TeacherReviews from'./pages/teacher/Reviews';import TeacherWithdrawal from'./pages/teacher/Withdrawal';import AdminWithdrawalManagement from'./pages/admin/WithdrawalManagement';import AdminEarnings from'./pages/admin/AdminEarnings';import AdminMeetingIssues from'./pages/admin/MeetingIssues';import ContactUs from'./pages/ContactUs';import ProtectedRouteComponent from'./components/ProtectedRoute';// Define roles\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ROLES={ADMIN:'admin',PLATFORM_TEACHER:'platform_teacher',NEW_TEACHER:'new_teacher',STUDENT:'student'};// Protected Route Component with User Status Check\nconst ProtectedRoute=_ref=>{let{children,allowedRoles,allowPendingDeletion=false}=_ref;const{currentUser,isAuthenticated,loading}=useAuth();const location=useLocation();// If still loading, show loading spinner\nif(loading){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"100vh\",children:/*#__PURE__*/_jsx(CircularProgress,{})});}// Use the new ProtectedRoute component for user status checking\nreturn/*#__PURE__*/_jsx(ProtectedRouteComponent,{allowPendingDeletion:allowPendingDeletion,children:/*#__PURE__*/_jsx(RoleBasedRoute,{allowedRoles:allowedRoles,children:children})});};// Role-based Route Component (separated from user status checking)\nconst RoleBasedRoute=_ref2=>{let{children,allowedRoles}=_ref2;const{currentUser}=useAuth();const location=useLocation();// Check if user has required role\nif(!allowedRoles.includes(currentUser.role)){// Special case: if a new teacher tries to access platform teacher routes\nif(currentUser.role===ROLES.NEW_TEACHER&&location.pathname==='/teacher/dashboard'){return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/application\",replace:true});}// Special case: if a platform teacher tries to access new teacher routes\nif(currentUser.role===ROLES.PLATFORM_TEACHER&&location.pathname==='/teacher/application'){return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/dashboard\",replace:true});}// Default redirects based on role\nswitch(currentUser.role){case ROLES.ADMIN:return/*#__PURE__*/_jsx(Navigate,{to:\"/admin/dashboard\",replace:true});case ROLES.PLATFORM_TEACHER:return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/dashboard\",replace:true});case ROLES.NEW_TEACHER:return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/application\",replace:true});case ROLES.STUDENT:return/*#__PURE__*/_jsx(Navigate,{to:\"/student/dashboard\",replace:true});default:return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}}// All checks passed, render the protected component\nreturn children;};// Public Route Component\nconst PublicRoute=_ref3=>{let{children}=_ref3;const{isAuthenticated,currentUser}=useAuth();// If authenticated, redirect to appropriate dashboard\nif(isAuthenticated&&currentUser){switch(currentUser.role){case ROLES.ADMIN:return/*#__PURE__*/_jsx(Navigate,{to:\"/admin/dashboard\",replace:true});case ROLES.PLATFORM_TEACHER:return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/dashboard\",replace:true});case ROLES.NEW_TEACHER:return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/application\",replace:true});case ROLES.STUDENT:return/*#__PURE__*/_jsx(Navigate,{to:\"/student/dashboard\",replace:true});default:return/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true});}}return children;};// Component to determine which Footer should be shown\nconst ConditionalFooter=()=>{const location=useLocation();const{isAuthenticated,currentUser}=useAuth();// Check if user is on protected routes (when user is logged in)\nconst isProtectedRoute=isAuthenticated&&currentUser&&(location.pathname.startsWith('/admin/')||location.pathname.startsWith('/teacher/')||location.pathname.startsWith('/student/')||location.pathname.startsWith('/join-meeting/')||location.pathname==='/platform-policy');// Show authenticated footer on protected routes\nif(isProtectedRoute){return/*#__PURE__*/_jsx(AuthenticatedFooter,{});}// Show public footer on public routes\nreturn/*#__PURE__*/_jsx(Footer,{});};function App(){const{i18n}=useTranslation();const direction=i18n.language==='ar'?'rtl':'ltr';useEffect(()=>{setupResizeObserverPolyfill();},[]);const theme=createTheme({direction,palette:{primary:{main:'#0C4B33'// Deep Islamic green\n},secondary:{main:'#C3A343'// Islamic gold\n}},typography:{fontFamily:direction==='rtl'?'Tajawal, sans-serif':'Roboto, sans-serif'}});return/*#__PURE__*/_jsxs(Router,{children:[/*#__PURE__*/_jsxs(ThemeProvider,{theme:theme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(ResizeObserverFix,{}),/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(SocketProvider,{children:/*#__PURE__*/_jsx(UnreadMessagesProvider,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',minHeight:'100vh'},children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(Box,{component:\"main\",sx:{flexGrow:1,py:3},children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(Home,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(Login,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/pending-deletion\",element:/*#__PURE__*/_jsx(PendingDeletion,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(RegisterChoice,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/register/student\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(StudentRegister,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/register/teacher\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(TeacherRegister,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/verify-email\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(VerifyEmail,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/forgot-password\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(ForgotPassword,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/verify-reset-code\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(VerifyResetCode,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/reset-password\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(ResetPassword,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/about-us\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(AboutUs,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/terms-and-conditions\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(TermsAndConditions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/privacy-policy\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(PrivacyPolicy,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/booking-cancellation-policy\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(BookingCancellationPolicy,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/booking-payment-policy\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(BookingPaymentPolicy,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/refund-policy\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(RefundPolicy,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/find-teacher\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(FindTeacher,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/:id\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(TeacherDetails,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/teachers\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(Teachers,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/students\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(Students,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/applications\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(TeacherApplications,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/categories\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(Categories,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/languages\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(Languages,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/wallet\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminWallet,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/messages\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminMessages,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/profile-updates\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(ProfileUpdates,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/meeting-sessions\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(MeetingSessions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/withdrawals\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminWithdrawalManagement,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/meeting-issues\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminMeetingIssues,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/earnings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminEarnings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/meetings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherMeetings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/bookings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherBookings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/application\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherApplication,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/edit-application\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(EditApplication,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/edit-video\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(EditVideoUpload,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/upload-video\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.NEW_TEACHER,ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherVideoUpload,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/available-hours\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherApplicationAvailableHours,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/manage-hours\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.TEACHER,ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherProfileAvailableHours,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/view-hours\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.TEACHER,ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherViewAvailableHours,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER],allowPendingDeletion:true,children:/*#__PURE__*/_jsx(TeacherProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/chat\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherChat,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/wallet\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherWallet,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/withdrawal\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherWithdrawal,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/contact-us\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherContactUs,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/my-messages\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherMyMessages,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/reviews\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherReviews,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/my-lessons\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(MyLessons,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/meetings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentMeetings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],allowPendingDeletion:true,children:/*#__PURE__*/_jsx(StudentProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/complete-profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(CompleteProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/chat\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentChat,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/find-teacher\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentFindTeacher,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/teacher/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentTeacherProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/book/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(BookingPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/my-teachers\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(MyTeachers,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/bookings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(Bookings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/chat-embed\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentChatEmbed,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/wallet\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentWallet,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/contact-us\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentContactUs,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/my-messages\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentMyMessages,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/write-review\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(WriteReview,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/join-meeting/:roomName\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT,ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(JoinMeeting,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/find-teacher\",element:/*#__PURE__*/_jsx(FindTeacher,{})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/:id\",element:/*#__PURE__*/_jsx(TeacherDetails,{})}),/*#__PURE__*/_jsx(Route,{path:\"/about-us\",element:/*#__PURE__*/_jsx(AboutUs,{})}),/*#__PURE__*/_jsx(Route,{path:\"/contact-us\",element:/*#__PURE__*/_jsx(ContactUs,{})}),/*#__PURE__*/_jsx(Route,{path:\"/platform-policy\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN,ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER,ROLES.STUDENT],children:/*#__PURE__*/_jsx(PlatformPolicy,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/terms-and-conditions\",element:/*#__PURE__*/_jsx(TermsAndConditions,{})}),/*#__PURE__*/_jsx(Route,{path:\"/privacy-policy\",element:/*#__PURE__*/_jsx(PrivacyPolicy,{})}),/*#__PURE__*/_jsx(Route,{path:\"/booking-cancellation-policy\",element:/*#__PURE__*/_jsx(BookingCancellationPolicy,{})}),/*#__PURE__*/_jsx(Route,{path:\"/booking-payment-policy\",element:/*#__PURE__*/_jsx(BookingPaymentPolicy,{})}),/*#__PURE__*/_jsx(Route,{path:\"/refund-policy\",element:/*#__PURE__*/_jsx(RefundPolicy,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})]})}),/*#__PURE__*/_jsx(ConditionalFooter,{})]})})})})]}),/*#__PURE__*/_jsx(Toaster,{position:\"top-center\",toastOptions:{duration:4000,style:{background:'#333',color:'#fff'}}})]});}export default App;", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "useLocation", "ResizeObserverFix", "ThemeProvider", "createTheme", "CssBaseline", "Box", "CircularProgress", "useTranslation", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "SocketProvider", "UnreadMessagesProvider", "Toaster", "setupResizeObserverPolyfill", "Header", "Footer", "Authenticated<PERSON><PERSON>er", "Home", "AboutUs", "PlatformPolicy", "TermsAndConditions", "PrivacyPolicy", "RefundPolicy", "BookingPaymentPolicy", "BookingCancellationPolicy", "<PERSON><PERSON>", "PendingDeletion", "RegisterChoice", "StudentRegister", "TeacherRegister", "ForgotPassword", "VerifyResetCode", "ResetPassword", "VerifyEmail", "AdminDashboard", "AdminProfile", "TeacherApplications", "Teachers", "Students", "Categories", "Languages", "ProfileUpdates", "MeetingSessions", "TeacherDashboard", "TeacherApplication", "EditApplication", "EditVideoUpload", "TeacherVideoUpload", "TeacherApplicationAvailableHours", "TeacherProfileAvailableHours", "TeacherViewAvailableHours", "TeacherP<PERSON><PERSON>le", "TeacherChat", "TeacherMeetings", "TeacherBookings", "MyLessons", "StudentDashboard", "StudentProfile", "CompleteProfile", "StudentChat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StudentTeacherProfile", "StudentChatEmbed", "<PERSON><PERSON><PERSON><PERSON>", "TeacherDetails", "Join<PERSON>eeting", "StudentMeetings", "AdminWallet", "TeacherWallet", "StudentWallet", "StudentContactUs", "TeacherContactUs", "AdminMessages", "StudentMyMessages", "TeacherMyMessages", "BookingPage", "Bookings", "MyTeachers", "WriteReview", "TeacherReviews", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AdminWithdrawalManagement", "AdminEarnings", "AdminMeeting<PERSON>ssues", "ContactUs", "ProtectedRouteComponent", "jsx", "_jsx", "jsxs", "_jsxs", "ROLES", "ADMIN", "PLATFORM_TEACHER", "NEW_TEACHER", "STUDENT", "ProtectedRoute", "_ref", "children", "allowedRoles", "allowPendingDeletion", "currentUser", "isAuthenticated", "loading", "location", "display", "justifyContent", "alignItems", "minHeight", "RoleBasedRoute", "_ref2", "includes", "role", "pathname", "to", "replace", "PublicRoute", "_ref3", "ConditionalFooter", "isProtectedRoute", "startsWith", "App", "i18n", "direction", "language", "theme", "palette", "primary", "main", "secondary", "typography", "fontFamily", "sx", "flexDirection", "component", "flexGrow", "py", "path", "element", "TEACHER", "position", "toastOptions", "duration", "style", "background", "color"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/App.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport ResizeObserverFix from './utils/ResizeObserverFix';\nimport './utils/axiosConfig'; // إعداد axios للتعامل مع المستخدمين المحذوفين\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline, Box, CircularProgress } from '@mui/material';\nimport { useTranslation } from 'react-i18next';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { SocketProvider } from './contexts/SocketContext';\nimport { UnreadMessagesProvider } from './contexts/UnreadMessagesContext';\nimport { Toaster } from 'react-hot-toast';\nimport { setupResizeObserverPolyfill } from './utils/resizeObserver';\nimport Header from './components/layout/Header';\nimport Footer from './components/Footer';\nimport AuthenticatedFooter from './components/AuthenticatedFooter';\nimport './i18n/i18n';\n\n// Import pages here\nimport Home from './pages/Home';\nimport AboutUs from './pages/AboutUs';\nimport PlatformPolicy from './pages/PlatformPolicy';\nimport TermsAndConditions from './pages/TermsAndConditions';\nimport PrivacyPolicy from './pages/PrivacyPolicy';\nimport RefundPolicy from './pages/RefundPolicy';\nimport BookingPaymentPolicy from './pages/BookingPaymentPolicy';\nimport BookingCancellationPolicy from './pages/BookingCancellationPolicy';\nimport Login from './pages/auth/Login';\nimport PendingDeletion from './pages/PendingDeletion';\nimport RegisterChoice from './pages/auth/RegisterChoice';\nimport StudentRegister from './pages/auth/StudentRegister';\nimport TeacherRegister from './pages/auth/TeacherRegister';\nimport ForgotPassword from './pages/auth/ForgotPassword';\nimport VerifyResetCode from './pages/auth/VerifyResetCode';\nimport ResetPassword from './pages/auth/ResetPassword';\nimport VerifyEmail from './pages/auth/VerifyEmail';\nimport AdminDashboard from './pages/admin/Dashboard';\nimport AdminProfile from './pages/admin/Profile';\nimport TeacherApplications from './pages/admin/TeacherApplications';\nimport Teachers from './pages/admin/Teachers';\nimport Students from './pages/admin/Students';\nimport Categories from './pages/admin/Categories';\nimport Languages from './pages/admin/Languages';\nimport ProfileUpdates from './pages/admin/ProfileUpdates';\nimport MeetingSessions from './pages/admin/MeetingSessions';\nimport TeacherDashboard from './pages/teacher/Dashboard';\nimport TeacherApplication from './pages/teacher/Application';\nimport EditApplication from './pages/teacher/EditApplication';\nimport EditVideoUpload from './pages/teacher/EditVideoUpload';\nimport TeacherVideoUpload from './pages/teacher/VideoUpload';\nimport TeacherApplicationAvailableHours from './pages/teacher/AvailableHours';\nimport TeacherProfileAvailableHours from './pages/teacher/TeacherAvailableHours';\nimport TeacherViewAvailableHours from './pages/teacher/ViewAvailableHours';\nimport TeacherProfile from './pages/teacher/Profile';\nimport TeacherChat from './pages/teacher/Chat';\nimport TeacherMeetings from './pages/teacher/Meetings';\nimport TeacherBookings from './pages/teacher/Bookings';\nimport MyLessons from './pages/teacher/MyLessons';\nimport StudentDashboard from './pages/student/Dashboard';\nimport StudentProfile from './pages/student/Profile';\nimport CompleteProfile from './pages/student/CompleteProfile';\nimport StudentChat from './pages/student/Chat';\nimport StudentFindTeacher from './pages/student/FindTeacher';\nimport StudentTeacherProfile from './pages/student/TeacherProfile';\nimport StudentChatEmbed from './pages/student/ChatEmbed';\nimport FindTeacher from './pages/FindTeacher';\nimport TeacherDetails from './pages/TeacherDetails';\nimport JoinMeeting from './pages/student/JoinMeeting';\nimport StudentMeetings from './pages/student/Meetings';\nimport AdminWallet from './pages/admin/Wallet';\nimport TeacherWallet from './pages/teacher/Wallet';\nimport StudentWallet from './pages/student/Wallet';\nimport StudentContactUs from './pages/student/ContactUs';\nimport TeacherContactUs from './pages/teacher/ContactUs';\nimport AdminMessages from './pages/admin/Messages';\nimport StudentMyMessages from './pages/student/MyMessages';\nimport TeacherMyMessages from './pages/teacher/MyMessages';\nimport BookingPage from './pages/student/BookingPage';\nimport Bookings from './pages/student/Bookings';\nimport MyTeachers from './pages/student/MyTeachers';\nimport WriteReview from './pages/student/WriteReview';\nimport TeacherReviews from './pages/teacher/Reviews';\nimport TeacherWithdrawal from './pages/teacher/Withdrawal';\nimport AdminWithdrawalManagement from './pages/admin/WithdrawalManagement';\nimport AdminEarnings from './pages/admin/AdminEarnings';\nimport AdminMeetingIssues from './pages/admin/MeetingIssues';\nimport ContactUs from './pages/ContactUs';\nimport ProtectedRouteComponent from './components/ProtectedRoute';\n\n// Define roles\nconst ROLES = {\n  ADMIN: 'admin',\n  PLATFORM_TEACHER: 'platform_teacher',\n  NEW_TEACHER: 'new_teacher',\n  STUDENT: 'student'\n};\n\n// Protected Route Component with User Status Check\nconst ProtectedRoute = ({ children, allowedRoles, allowPendingDeletion = false }) => {\n  const { currentUser, isAuthenticated, loading } = useAuth();\n  const location = useLocation();\n\n  // If still loading, show loading spinner\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"100vh\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  // Use the new ProtectedRoute component for user status checking\n  return (\n    <ProtectedRouteComponent allowPendingDeletion={allowPendingDeletion}>\n      <RoleBasedRoute allowedRoles={allowedRoles}>\n        {children}\n      </RoleBasedRoute>\n    </ProtectedRouteComponent>\n  );\n};\n\n// Role-based Route Component (separated from user status checking)\nconst RoleBasedRoute = ({ children, allowedRoles }) => {\n  const { currentUser } = useAuth();\n  const location = useLocation();\n\n  // Check if user has required role\n  if (!allowedRoles.includes(currentUser.role)) {\n    // Special case: if a new teacher tries to access platform teacher routes\n    if (currentUser.role === ROLES.NEW_TEACHER && location.pathname === '/teacher/dashboard') {\n      return <Navigate to=\"/teacher/application\" replace />;\n    }\n\n    // Special case: if a platform teacher tries to access new teacher routes\n    if (currentUser.role === ROLES.PLATFORM_TEACHER && location.pathname === '/teacher/application') {\n      return <Navigate to=\"/teacher/dashboard\" replace />;\n    }\n\n    // Default redirects based on role\n    switch (currentUser.role) {\n      case ROLES.ADMIN:\n        return <Navigate to=\"/admin/dashboard\" replace />;\n      case ROLES.PLATFORM_TEACHER:\n        return <Navigate to=\"/teacher/dashboard\" replace />;\n      case ROLES.NEW_TEACHER:\n        return <Navigate to=\"/teacher/application\" replace />;\n      case ROLES.STUDENT:\n        return <Navigate to=\"/student/dashboard\" replace />;\n      default:\n        return <Navigate to=\"/login\" replace />;\n    }\n  }\n\n  // All checks passed, render the protected component\n  return children;\n};\n\n// Public Route Component\nconst PublicRoute = ({ children }) => {\n  const { isAuthenticated, currentUser } = useAuth();\n\n  // If authenticated, redirect to appropriate dashboard\n  if (isAuthenticated && currentUser) {\n    switch (currentUser.role) {\n      case ROLES.ADMIN:\n        return <Navigate to=\"/admin/dashboard\" replace />;\n      case ROLES.PLATFORM_TEACHER:\n        return <Navigate to=\"/teacher/dashboard\" replace />;\n      case ROLES.NEW_TEACHER:\n        return <Navigate to=\"/teacher/application\" replace />;\n      case ROLES.STUDENT:\n        return <Navigate to=\"/student/dashboard\" replace />;\n      default:\n        return <Navigate to=\"/\" replace />;\n    }\n  }\n\n  return children;\n};\n\n// Component to determine which Footer should be shown\nconst ConditionalFooter = () => {\n  const location = useLocation();\n  const { isAuthenticated, currentUser } = useAuth();\n\n  // Check if user is on protected routes (when user is logged in)\n  const isProtectedRoute = isAuthenticated && currentUser && (\n    location.pathname.startsWith('/admin/') ||\n    location.pathname.startsWith('/teacher/') ||\n    location.pathname.startsWith('/student/') ||\n    location.pathname.startsWith('/join-meeting/') ||\n    location.pathname === '/platform-policy'\n  );\n\n  // Show authenticated footer on protected routes\n  if (isProtectedRoute) {\n    return <AuthenticatedFooter />;\n  }\n\n  // Show public footer on public routes\n  return <Footer />;\n};\n\nfunction App() {\n  const { i18n } = useTranslation();\n  const direction = i18n.language === 'ar' ? 'rtl' : 'ltr';\n\n  useEffect(() => {\n    setupResizeObserverPolyfill();\n  }, []);\n\n  const theme = createTheme({\n    direction,\n    palette: {\n      primary: {\n        main: '#0C4B33', // Deep Islamic green\n      },\n      secondary: {\n        main: '#C3A343', // Islamic gold\n      },\n    },\n    typography: {\n      fontFamily: direction === 'rtl' ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n    },\n  });\n\n  return (\n    <Router>\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <ResizeObserverFix />\n        <AuthProvider>\n          <SocketProvider>\n            <UnreadMessagesProvider>\n              <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>\n              <Header />\n              <Box component=\"main\" sx={{ flexGrow: 1, py: 3 }}>\n                <Routes>\n                  {/* Public Routes */}\n                  <Route path=\"/\" element={<PublicRoute><Home /></PublicRoute>} />\n                  <Route path=\"/login\" element={<PublicRoute><Login /></PublicRoute>} />\n                  <Route path=\"/pending-deletion\" element={<PendingDeletion />} />\n                  <Route path=\"/register\" element={<PublicRoute><RegisterChoice /></PublicRoute>} />\n                  <Route path=\"/register/student\" element={<PublicRoute><StudentRegister /></PublicRoute>} />\n                  <Route path=\"/register/teacher\" element={<PublicRoute><TeacherRegister /></PublicRoute>} />\n                  <Route path=\"/verify-email\" element={<PublicRoute><VerifyEmail /></PublicRoute>} />\n                  <Route path=\"/forgot-password\" element={<PublicRoute><ForgotPassword /></PublicRoute>} />\n                  <Route path=\"/verify-reset-code\" element={<PublicRoute><VerifyResetCode /></PublicRoute>} />\n                  <Route path=\"/reset-password\" element={<PublicRoute><ResetPassword /></PublicRoute>} />\n                  <Route path=\"/about-us\" element={<PublicRoute><AboutUs /></PublicRoute>} />\n                  {/* Policy Pages */}\n                  <Route path=\"/terms-and-conditions\" element={<PublicRoute><TermsAndConditions /></PublicRoute>} />\n                  <Route path=\"/privacy-policy\" element={<PublicRoute><PrivacyPolicy /></PublicRoute>} />\n                  <Route path=\"/booking-cancellation-policy\" element={<PublicRoute><BookingCancellationPolicy /></PublicRoute>} />\n                  <Route path=\"/booking-payment-policy\" element={<PublicRoute><BookingPaymentPolicy /></PublicRoute>} />\n                  <Route path=\"/refund-policy\" element={<PublicRoute><RefundPolicy /></PublicRoute>} />\n                  <Route path=\"/find-teacher\" element={<PublicRoute><FindTeacher /></PublicRoute>} />\n                  <Route path=\"/teacher/:id\" element={<PublicRoute><TeacherDetails /></PublicRoute>} />\n\n                  {/* Admin Routes */}\n                  <Route path=\"/admin/dashboard\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminDashboard />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/profile\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminProfile />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/teachers\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <Teachers />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/students\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <Students />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/applications\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <TeacherApplications />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/categories\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <Categories />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/languages\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <Languages />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/wallet\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminWallet />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/messages\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminMessages />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/profile-updates\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <ProfileUpdates />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/meeting-sessions\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <MeetingSessions />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/withdrawals\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminWithdrawalManagement />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/meeting-issues\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminMeetingIssues />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/earnings\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminEarnings />\n                    </ProtectedRoute>\n                  } />\n\n                  {/* Teacher Routes */}\n                  <Route path=\"/teacher/dashboard\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherDashboard />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/meetings\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherMeetings />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/bookings\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherBookings />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/application\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.NEW_TEACHER]}>\n                      <TeacherApplication />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/edit-application\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <EditApplication />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/edit-video\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <EditVideoUpload />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/upload-video\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.NEW_TEACHER, ROLES.PLATFORM_TEACHER]}>\n                      <TeacherVideoUpload />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/available-hours\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.NEW_TEACHER]}>\n                      <TeacherApplicationAvailableHours />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/manage-hours\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.TEACHER, ROLES.PLATFORM_TEACHER]}>\n                      <TeacherProfileAvailableHours />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/view-hours\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.TEACHER, ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>\n                      <TeacherViewAvailableHours />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/profile\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]} allowPendingDeletion={true}>\n                      <TeacherProfile />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/chat\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherChat />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/wallet\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>\n                      <TeacherWallet />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/withdrawal\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherWithdrawal />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/contact-us\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>\n                      <TeacherContactUs />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/my-messages\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>\n                      <TeacherMyMessages />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/reviews\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherReviews />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/my-lessons\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <MyLessons />\n                    </ProtectedRoute>\n                  } />\n\n                  {/* Student Routes */}\n                  <Route\n                    path=\"/student/dashboard\"\n                    element={\n                      <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                        <StudentDashboard />\n                      </ProtectedRoute>\n                    }\n                  />\n                  <Route\n                    path=\"/student/meetings\"\n                    element={\n                      <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                        <StudentMeetings />\n                      </ProtectedRoute>\n                    }\n                  />\n                  <Route\n                    path=\"/student/profile\"\n                    element={\n                      <ProtectedRoute allowedRoles={[ROLES.STUDENT]} allowPendingDeletion={true}>\n                        <StudentProfile />\n                      </ProtectedRoute>\n                    }\n                  />\n                  <Route path=\"/student/complete-profile\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <CompleteProfile />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/chat\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentChat />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/find-teacher\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentFindTeacher />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/teacher/:id\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentTeacherProfile />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/book/:id\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <BookingPage />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/my-teachers\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <MyTeachers />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/bookings\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <Bookings />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/chat-embed\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentChatEmbed />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/wallet\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentWallet />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/contact-us\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentContactUs />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/my-messages\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentMyMessages />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/write-review\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <WriteReview />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/join-meeting/:roomName\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT, ROLES.PLATFORM_TEACHER]}>\n                      <JoinMeeting />\n                    </ProtectedRoute>\n                  } />\n\n                  {/* Common Routes */}\n                  <Route path=\"/find-teacher\" element={<FindTeacher />} />\n                  <Route path=\"/teacher/:id\" element={<TeacherDetails />} />\n                  <Route path=\"/about-us\" element={<AboutUs />} />\n                  <Route path=\"/contact-us\" element={<ContactUs />} />\n                  {/* Unified Platform Policy - accessible to all authenticated users */}\n                  <Route path=\"/platform-policy\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN, ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER, ROLES.STUDENT]}>\n                      <PlatformPolicy />\n                    </ProtectedRoute>\n                  } />\n\n                  {/* Policy Pages - accessible to authenticated users */}\n                  <Route path=\"/terms-and-conditions\" element={<TermsAndConditions />} />\n                  <Route path=\"/privacy-policy\" element={<PrivacyPolicy />} />\n                  <Route path=\"/booking-cancellation-policy\" element={<BookingCancellationPolicy />} />\n                  <Route path=\"/booking-payment-policy\" element={<BookingPaymentPolicy />} />\n                  <Route path=\"/refund-policy\" element={<RefundPolicy />} />\n\n                  {/* Catch all route */}\n                  <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n                </Routes>\n              </Box>\n              <ConditionalFooter />\n              </Box>\n            </UnreadMessagesProvider>\n          </SocketProvider>\n        </AuthProvider>\n      </ThemeProvider>\n      <Toaster\n        position=\"top-center\"\n        toastOptions={{\n          duration: 4000,\n          style: {\n            background: '#333',\n            color: '#fff',\n          },\n        }}\n      />\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,CAAEC,WAAW,KAAQ,kBAAkB,CAChG,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,MAAO,qBAAqB,CAAE;AAC9B,OAASC,aAAa,CAAEC,WAAW,KAAQ,sBAAsB,CACjE,OAASC,WAAW,CAAEC,GAAG,CAAEC,gBAAgB,KAAQ,eAAe,CAClE,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,YAAY,CAAEC,OAAO,KAAQ,wBAAwB,CAC9D,OAASC,cAAc,KAAQ,0BAA0B,CACzD,OAASC,sBAAsB,KAAQ,kCAAkC,CACzE,OAASC,OAAO,KAAQ,iBAAiB,CACzC,OAASC,2BAA2B,KAAQ,wBAAwB,CACpE,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,mBAAmB,KAAM,kCAAkC,CAClE,MAAO,aAAa,CAEpB;AACA,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,kBAAkB,KAAM,4BAA4B,CAC3D,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,oBAAoB,KAAM,8BAA8B,CAC/D,MAAO,CAAAC,yBAAyB,KAAM,mCAAmC,CACzE,MAAO,CAAAC,KAAK,KAAM,oBAAoB,CACtC,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAC1D,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAC1D,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAC1D,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,YAAY,KAAM,uBAAuB,CAChD,MAAO,CAAAC,mBAAmB,KAAM,mCAAmC,CACnE,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAC7C,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAC7C,MAAO,CAAAC,UAAU,KAAM,0BAA0B,CACjD,MAAO,CAAAC,SAAS,KAAM,yBAAyB,CAC/C,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CACxD,MAAO,CAAAC,kBAAkB,KAAM,6BAA6B,CAC5D,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,kBAAkB,KAAM,6BAA6B,CAC5D,MAAO,CAAAC,gCAAgC,KAAM,gCAAgC,CAC7E,MAAO,CAAAC,4BAA4B,KAAM,uCAAuC,CAChF,MAAO,CAAAC,yBAAyB,KAAM,oCAAoC,CAC1E,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,eAAe,KAAM,0BAA0B,CACtD,MAAO,CAAAC,eAAe,KAAM,0BAA0B,CACtD,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CACxD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,kBAAkB,KAAM,6BAA6B,CAC5D,MAAO,CAAAC,qBAAqB,KAAM,gCAAgC,CAClE,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CACxD,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,eAAe,KAAM,0BAA0B,CACtD,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,aAAa,KAAM,wBAAwB,CAClD,MAAO,CAAAC,aAAa,KAAM,wBAAwB,CAClD,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CACxD,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CACxD,MAAO,CAAAC,aAAa,KAAM,wBAAwB,CAClD,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAC1D,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAC1D,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAC1D,MAAO,CAAAC,yBAAyB,KAAM,oCAAoC,CAC1E,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,kBAAkB,KAAM,6BAA6B,CAC5D,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,uBAAuB,KAAM,6BAA6B,CAEjE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,KAAK,CAAG,CACZC,KAAK,CAAE,OAAO,CACdC,gBAAgB,CAAE,kBAAkB,CACpCC,WAAW,CAAE,aAAa,CAC1BC,OAAO,CAAE,SACX,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAA8D,IAA7D,CAAEC,QAAQ,CAAEC,YAAY,CAAEC,oBAAoB,CAAG,KAAM,CAAC,CAAAH,IAAA,CAC9E,KAAM,CAAEI,WAAW,CAAEC,eAAe,CAAEC,OAAQ,CAAC,CAAG7F,OAAO,CAAC,CAAC,CAC3D,KAAM,CAAA8F,QAAQ,CAAGvG,WAAW,CAAC,CAAC,CAE9B;AACA,GAAIsG,OAAO,CAAE,CACX,mBACEf,IAAA,CAAClF,GAAG,EAACmG,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAACC,SAAS,CAAC,OAAO,CAAAV,QAAA,cAC/EV,IAAA,CAACjF,gBAAgB,GAAE,CAAC,CACjB,CAAC,CAEV,CAEA;AACA,mBACEiF,IAAA,CAACF,uBAAuB,EAACc,oBAAoB,CAAEA,oBAAqB,CAAAF,QAAA,cAClEV,IAAA,CAACqB,cAAc,EAACV,YAAY,CAAEA,YAAa,CAAAD,QAAA,CACxCA,QAAQ,CACK,CAAC,CACM,CAAC,CAE9B,CAAC,CAED;AACA,KAAM,CAAAW,cAAc,CAAGC,KAAA,EAAgC,IAA/B,CAAEZ,QAAQ,CAAEC,YAAa,CAAC,CAAAW,KAAA,CAChD,KAAM,CAAET,WAAY,CAAC,CAAG3F,OAAO,CAAC,CAAC,CACjC,KAAM,CAAA8F,QAAQ,CAAGvG,WAAW,CAAC,CAAC,CAE9B;AACA,GAAI,CAACkG,YAAY,CAACY,QAAQ,CAACV,WAAW,CAACW,IAAI,CAAC,CAAE,CAC5C;AACA,GAAIX,WAAW,CAACW,IAAI,GAAKrB,KAAK,CAACG,WAAW,EAAIU,QAAQ,CAACS,QAAQ,GAAK,oBAAoB,CAAE,CACxF,mBAAOzB,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,sBAAsB,CAACC,OAAO,MAAE,CAAC,CACvD,CAEA;AACA,GAAId,WAAW,CAACW,IAAI,GAAKrB,KAAK,CAACE,gBAAgB,EAAIW,QAAQ,CAACS,QAAQ,GAAK,sBAAsB,CAAE,CAC/F,mBAAOzB,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,oBAAoB,CAACC,OAAO,MAAE,CAAC,CACrD,CAEA;AACA,OAAQd,WAAW,CAACW,IAAI,EACtB,IAAK,CAAArB,KAAK,CAACC,KAAK,CACd,mBAAOJ,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,kBAAkB,CAACC,OAAO,MAAE,CAAC,CACnD,IAAK,CAAAxB,KAAK,CAACE,gBAAgB,CACzB,mBAAOL,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,oBAAoB,CAACC,OAAO,MAAE,CAAC,CACrD,IAAK,CAAAxB,KAAK,CAACG,WAAW,CACpB,mBAAON,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,sBAAsB,CAACC,OAAO,MAAE,CAAC,CACvD,IAAK,CAAAxB,KAAK,CAACI,OAAO,CAChB,mBAAOP,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,oBAAoB,CAACC,OAAO,MAAE,CAAC,CACrD,QACE,mBAAO3B,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAC,CAC3C,CACF,CAEA;AACA,MAAO,CAAAjB,QAAQ,CACjB,CAAC,CAED;AACA,KAAM,CAAAkB,WAAW,CAAGC,KAAA,EAAkB,IAAjB,CAAEnB,QAAS,CAAC,CAAAmB,KAAA,CAC/B,KAAM,CAAEf,eAAe,CAAED,WAAY,CAAC,CAAG3F,OAAO,CAAC,CAAC,CAElD;AACA,GAAI4F,eAAe,EAAID,WAAW,CAAE,CAClC,OAAQA,WAAW,CAACW,IAAI,EACtB,IAAK,CAAArB,KAAK,CAACC,KAAK,CACd,mBAAOJ,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,kBAAkB,CAACC,OAAO,MAAE,CAAC,CACnD,IAAK,CAAAxB,KAAK,CAACE,gBAAgB,CACzB,mBAAOL,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,oBAAoB,CAACC,OAAO,MAAE,CAAC,CACrD,IAAK,CAAAxB,KAAK,CAACG,WAAW,CACpB,mBAAON,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,sBAAsB,CAACC,OAAO,MAAE,CAAC,CACvD,IAAK,CAAAxB,KAAK,CAACI,OAAO,CAChB,mBAAOP,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,oBAAoB,CAACC,OAAO,MAAE,CAAC,CACrD,QACE,mBAAO3B,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAC,CACtC,CACF,CAEA,MAAO,CAAAjB,QAAQ,CACjB,CAAC,CAED;AACA,KAAM,CAAAoB,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAd,QAAQ,CAAGvG,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEqG,eAAe,CAAED,WAAY,CAAC,CAAG3F,OAAO,CAAC,CAAC,CAElD;AACA,KAAM,CAAA6G,gBAAgB,CAAGjB,eAAe,EAAID,WAAW,GACrDG,QAAQ,CAACS,QAAQ,CAACO,UAAU,CAAC,SAAS,CAAC,EACvChB,QAAQ,CAACS,QAAQ,CAACO,UAAU,CAAC,WAAW,CAAC,EACzChB,QAAQ,CAACS,QAAQ,CAACO,UAAU,CAAC,WAAW,CAAC,EACzChB,QAAQ,CAACS,QAAQ,CAACO,UAAU,CAAC,gBAAgB,CAAC,EAC9ChB,QAAQ,CAACS,QAAQ,GAAK,kBAAkB,CACzC,CAED;AACA,GAAIM,gBAAgB,CAAE,CACpB,mBAAO/B,IAAA,CAACvE,mBAAmB,GAAE,CAAC,CAChC,CAEA;AACA,mBAAOuE,IAAA,CAACxE,MAAM,GAAE,CAAC,CACnB,CAAC,CAED,QAAS,CAAAyG,GAAGA,CAAA,CAAG,CACb,KAAM,CAAEC,IAAK,CAAC,CAAGlH,cAAc,CAAC,CAAC,CACjC,KAAM,CAAAmH,SAAS,CAAGD,IAAI,CAACE,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CAExDjI,SAAS,CAAC,IAAM,CACdmB,2BAA2B,CAAC,CAAC,CAC/B,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA+G,KAAK,CAAGzH,WAAW,CAAC,CACxBuH,SAAS,CACTG,OAAO,CAAE,CACPC,OAAO,CAAE,CACPC,IAAI,CAAE,SAAW;AACnB,CAAC,CACDC,SAAS,CAAE,CACTD,IAAI,CAAE,SAAW;AACnB,CACF,CAAC,CACDE,UAAU,CAAE,CACVC,UAAU,CAAER,SAAS,GAAK,KAAK,CAAG,qBAAqB,CAAG,oBAC5D,CACF,CAAC,CAAC,CAEF,mBACEjC,KAAA,CAAC7F,MAAM,EAAAqG,QAAA,eACLR,KAAA,CAACvF,aAAa,EAAC0H,KAAK,CAAEA,KAAM,CAAA3B,QAAA,eAC1BV,IAAA,CAACnF,WAAW,GAAE,CAAC,cACfmF,IAAA,CAACtF,iBAAiB,GAAE,CAAC,cACrBsF,IAAA,CAAC/E,YAAY,EAAAyF,QAAA,cACXV,IAAA,CAAC7E,cAAc,EAAAuF,QAAA,cACbV,IAAA,CAAC5E,sBAAsB,EAAAsF,QAAA,cACrBR,KAAA,CAACpF,GAAG,EAAC8H,EAAE,CAAE,CAAE3B,OAAO,CAAE,MAAM,CAAE4B,aAAa,CAAE,QAAQ,CAAEzB,SAAS,CAAE,OAAQ,CAAE,CAAAV,QAAA,eAC1EV,IAAA,CAACzE,MAAM,GAAE,CAAC,cACVyE,IAAA,CAAClF,GAAG,EAACgI,SAAS,CAAC,MAAM,CAACF,EAAE,CAAE,CAAEG,QAAQ,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cAC/CR,KAAA,CAAC5F,MAAM,EAAAoG,QAAA,eAELV,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,GAAG,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACtE,IAAI,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAChEsE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAC9D,KAAK,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACtE8D,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAElD,IAAA,CAAC7D,eAAe,GAAE,CAAE,CAAE,CAAC,cAChE6D,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,WAAW,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAC5D,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAClF4D,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAC3D,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3F2D,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAC1D,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3F0D,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,eAAe,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACtD,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACnFsD,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACzD,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFyD,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,oBAAoB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACxD,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC5FwD,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACvD,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvFuD,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,WAAW,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACrE,OAAO,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAE3EqE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,uBAAuB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACnE,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAClGmE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAClE,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvFkE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,8BAA8B,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAC/D,yBAAyB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAChH+D,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAChE,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACtGgE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACjE,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACrFiE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,eAAe,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACxB,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACnFwB,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,cAAc,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACvB,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGrFuB,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACrD,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJqD,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAClClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACpD,YAAY,GAAE,CAAC,CACF,CACjB,CAAE,CAAC,cACJoD,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAAClD,QAAQ,GAAE,CAAC,CACE,CACjB,CAAE,CAAC,cACJkD,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACjD,QAAQ,GAAE,CAAC,CACE,CACjB,CAAE,CAAC,cACJiD,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACnD,mBAAmB,GAAE,CAAC,CACT,CACjB,CAAE,CAAC,cACJmD,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAAChD,UAAU,GAAE,CAAC,CACA,CACjB,CAAE,CAAC,cACJgD,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAAC/C,SAAS,GAAE,CAAC,CACC,CACjB,CAAE,CAAC,cACJ+C,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,eAAe,CAACC,OAAO,cACjClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACpB,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJoB,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACf,aAAa,GAAE,CAAC,CACH,CACjB,CAAE,CAAC,cACJe,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAC1ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAAC9C,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJ8C,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAC3ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAAC7C,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJ6C,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,oBAAoB,CAACC,OAAO,cACtClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACN,yBAAyB,GAAE,CAAC,CACf,CACjB,CAAE,CAAC,cACJM,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,uBAAuB,CAACC,OAAO,cACzClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACJ,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJI,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACL,aAAa,GAAE,CAAC,CACH,CACjB,CAAE,CAAC,cAGJK,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,oBAAoB,CAACC,OAAO,cACtClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAAC5C,gBAAgB,GAAE,CAAC,CACN,CACjB,CAAE,CAAC,cACJ4C,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAAClC,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJkC,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAACjC,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJiC,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cAChDV,IAAA,CAAC3C,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJ2C,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,2BAA2B,CAACC,OAAO,cAC7ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAAC1C,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJ0C,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAACzC,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJyC,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,uBAAuB,CAACC,OAAO,cACzClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACG,WAAW,CAAEH,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACxEV,IAAA,CAACxC,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJwC,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,0BAA0B,CAACC,OAAO,cAC5ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cAChDV,IAAA,CAACvC,gCAAgC,GAAE,CAAC,CACtB,CACjB,CAAE,CAAC,cACJuC,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,uBAAuB,CAACC,OAAO,cACzClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACgD,OAAO,CAAEhD,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACpEV,IAAA,CAACtC,4BAA4B,GAAE,CAAC,CAClB,CACjB,CAAE,CAAC,cACJsC,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACgD,OAAO,CAAEhD,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cACvFV,IAAA,CAACrC,yBAAyB,GAAE,CAAC,CACf,CACjB,CAAE,CAAC,cACJqC,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAE,CAACM,oBAAoB,CAAE,IAAK,CAAAF,QAAA,cACpGV,IAAA,CAACpC,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJoC,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,eAAe,CAACC,OAAO,cACjClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAACnC,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJmC,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cACxEV,IAAA,CAACnB,aAAa,GAAE,CAAC,CACH,CACjB,CAAE,CAAC,cACJmB,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAACP,iBAAiB,GAAE,CAAC,CACP,CACjB,CAAE,CAAC,cACJO,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cACxEV,IAAA,CAAChB,gBAAgB,GAAE,CAAC,CACN,CACjB,CAAE,CAAC,cACJgB,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cACxEV,IAAA,CAACb,iBAAiB,GAAE,CAAC,CACP,CACjB,CAAE,CAAC,cACJa,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAACR,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJQ,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAAChC,SAAS,GAAE,CAAC,CACC,CACjB,CAAE,CAAC,cAGJgC,IAAA,CAACzF,KAAK,EACJ0I,IAAI,CAAC,oBAAoB,CACzBC,OAAO,cACLlD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAC/B,gBAAgB,GAAE,CAAC,CACN,CACjB,CACF,CAAC,cACF+B,IAAA,CAACzF,KAAK,EACJ0I,IAAI,CAAC,mBAAmB,CACxBC,OAAO,cACLlD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACrB,eAAe,GAAE,CAAC,CACL,CACjB,CACF,CAAC,cACFqB,IAAA,CAACzF,KAAK,EACJ0I,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACLlD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAACK,oBAAoB,CAAE,IAAK,CAAAF,QAAA,cACxEV,IAAA,CAAC9B,cAAc,GAAE,CAAC,CACJ,CACjB,CACF,CAAC,cACF8B,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,2BAA2B,CAACC,OAAO,cAC7ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAC7B,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJ6B,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,eAAe,CAACC,OAAO,cACjClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAC5B,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJ4B,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,uBAAuB,CAACC,OAAO,cACzClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAC3B,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJ2B,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAC1B,qBAAqB,GAAE,CAAC,CACX,CACjB,CAAE,CAAC,cACJ0B,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACZ,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJY,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACV,UAAU,GAAE,CAAC,CACA,CACjB,CAAE,CAAC,cACJU,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACX,QAAQ,GAAE,CAAC,CACE,CACjB,CAAE,CAAC,cACJW,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACzB,gBAAgB,GAAE,CAAC,CACN,CACjB,CAAE,CAAC,cACJyB,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAClB,aAAa,GAAE,CAAC,CACH,CACjB,CAAE,CAAC,cACJkB,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACjB,gBAAgB,GAAE,CAAC,CACN,CACjB,CAAE,CAAC,cACJiB,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACd,iBAAiB,GAAE,CAAC,CACP,CACjB,CAAE,CAAC,cACJc,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,uBAAuB,CAACC,OAAO,cACzClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACT,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJS,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAC3ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAEJ,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACpEV,IAAA,CAACtB,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cAGJsB,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,eAAe,CAACC,OAAO,cAAElD,IAAA,CAACxB,WAAW,GAAE,CAAE,CAAE,CAAC,cACxDwB,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,cAAc,CAACC,OAAO,cAAElD,IAAA,CAACvB,cAAc,GAAE,CAAE,CAAE,CAAC,cAC1DuB,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,WAAW,CAACC,OAAO,cAAElD,IAAA,CAACrE,OAAO,GAAE,CAAE,CAAE,CAAC,cAChDqE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,aAAa,CAACC,OAAO,cAAElD,IAAA,CAACH,SAAS,GAAE,CAAE,CAAE,CAAC,cAEpDG,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAED,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAEH,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cACpGV,IAAA,CAACpE,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cAGJoE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,uBAAuB,CAACC,OAAO,cAAElD,IAAA,CAACnE,kBAAkB,GAAE,CAAE,CAAE,CAAC,cACvEmE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAElD,IAAA,CAAClE,aAAa,GAAE,CAAE,CAAE,CAAC,cAC5DkE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,8BAA8B,CAACC,OAAO,cAAElD,IAAA,CAAC/D,yBAAyB,GAAE,CAAE,CAAE,CAAC,cACrF+D,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAAElD,IAAA,CAAChE,oBAAoB,GAAE,CAAE,CAAE,CAAC,cAC3EgE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAElD,IAAA,CAACjE,YAAY,GAAE,CAAE,CAAE,CAAC,cAG1DiE,IAAA,CAACzF,KAAK,EAAC0I,IAAI,CAAC,GAAG,CAACC,OAAO,cAAElD,IAAA,CAACxF,QAAQ,EAACkH,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EAClD,CAAC,CACN,CAAC,cACN3B,IAAA,CAAC8B,iBAAiB,GAAE,CAAC,EAChB,CAAC,CACgB,CAAC,CACX,CAAC,CACL,CAAC,EACF,CAAC,cAChB9B,IAAA,CAAC3E,OAAO,EACN+H,QAAQ,CAAC,YAAY,CACrBC,YAAY,CAAE,CACZC,QAAQ,CAAE,IAAI,CACdC,KAAK,CAAE,CACLC,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,MACT,CACF,CAAE,CACH,CAAC,EACI,CAAC,CAEb,CAEA,cAAe,CAAAxB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}