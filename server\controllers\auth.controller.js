const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require('../config/db');
const config = require('../config/auth.config');
const { OAuth2Client } = require('google-auth-library');
const nodemailer = require('nodemailer');
const crypto = require('crypto');
const emailService = require('../utils/emailService');

const googleClient = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

const generateToken = (user) => {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      role: user.role
    },
    config.secret,
    { expiresIn: '24h' }
  );
};

const register = async (req, res) => {
  const { full_name, email, password, role, gender } = req.body;

  if (!full_name || !email || !password || !role || !gender) {
    return res.status(400).json({
      success: false,
      message: 'All fields are required'
    });
  }

  try {
    // Check if email exists
    const [existingUsers] = await db.pool.execute(
      'SELECT id, is_email_verified FROM users WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      // If user exists and email is verified, return error
      if (existingUsers[0].is_email_verified) {
        return res.status(400).json({
          success: false,
          message: 'Email already exists'
        });
      }
      // If user exists but email not verified, delete the old record
      await db.pool.execute(
        'DELETE FROM users WHERE email = ? AND is_email_verified = 0',
        [email]
      );
    }

    // Generate verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    const verificationExpiry = new Date(Date.now() + 10 * 60 * 1000); // 3 minutes

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user with verification code (not verified yet)
    const [result] = await db.pool.execute(
      'INSERT INTO users (full_name, email, password, role, gender, email_verification_code, email_verification_expiry, is_email_verified, profile_picture_url) VALUES (?, ?, ?, ?, ?, ?, ?, 0, "")',
      [full_name, email, hashedPassword, role, gender, verificationCode, verificationExpiry]
    );

    // Send verification email
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #4a6da7; margin: 0;">Allemni online</h1>
        </div>
        <h2 style="color: #4a6da7; text-align: center;">Email Verification</h2>
        <p>Welcome ${full_name}!</p>
        <p>Thank you for registering with Allemni online. To complete your registration, please verify your email address using the code below:</p>
        <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0; border-radius: 5px; border: 2px solid #4a6da7;">
          ${verificationCode}
        </div>
        <p><strong>This code will expire in 10 minutes.</strong></p>
        <p>If you did not create an account, please ignore this email.</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
            Visit our platform: <a href="https://allemnionline.com" style="color: #4a6da7; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
            &copy; ${new Date().getFullYear()} Allemnionline. All rights reserved.
          </p>
        </div>
      </div>
    `;

    await emailService.sendEmail({
      to: email,
      subject: 'Email Verification - Allemni online',
      html: emailHtml
    });

    return res.status(201).json({
      success: true,
      message: 'Registration initiated. Please check your email for verification code.',
      email: email
    });
  } catch (error) {
    console.error('Register error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error registering user'
    });
  }
};

const login = async (req, res) => {
  console.log('Login attempt:', req.body);
  const { email, password } = req.body;

  if (!email || !password) {
    console.log('Missing email or password');
    return res.status(400).json({
      success: false,
      message: 'Email and password are required'
    });
  }

  try {
    // Get user with role and profile picture and deletion status
    console.log('Querying database for user:', email);
    const [users] = await db.pool.execute(
      'SELECT id, email, password, full_name, role, gender, profile_picture_url, is_email_verified, status, deleted_at, deletion_reason FROM users WHERE email = ?',
      [email]
    );

    if (users.length === 0) {
      console.log('User not found:', email);
      return res.status(401).json({
        success: false,
        message: 'Email not found',
        errorType: 'EMAIL_NOT_FOUND'
      });
    }

    const user = users[0];

    // Check if user is soft deleted
    if (user.deleted_at) {
      console.log('User is soft deleted:', email);
      return res.status(401).json({
        success: false,
        message: 'تم حذف هذا الحساب ولا يمكن تسجيل الدخول',
        message_en: 'This account has been deleted and cannot be used to login',
        errorType: 'ACCOUNT_DELETED',
        accountStatus: 'deleted',
        deletedAt: user.deleted_at,
        deletionReason: user.deletion_reason
      });
    }

    // Check if email is verified
    if (!user.is_email_verified) {
      // Generate new verification code
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
      const verificationExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      // Update code in DB
      await db.pool.execute(
        'UPDATE users SET email_verification_code = ?, email_verification_expiry = ? WHERE id = ?',
        [verificationCode, verificationExpiry, user.id]
      );

      // Send verification email
      try {
        await emailService.sendEmail({
          to: user.email,
          subject: 'Email Verification - Allemni online',
          html: `<p>Your new verification code is: <strong>${verificationCode}</strong></p>`
        });
      } catch (err) {
        console.error('Error sending verification email:', err);
      }

      return res.status(401).json({
        success: false,
        message: 'Please verify your email before logging in. A new code has been sent.',
        requiresVerification: true,
        email: email
      });
    }

    // Verify password
    const validPassword = await bcrypt.compare(password, user.password);
    if (!validPassword) {
      console.log('Invalid password for user:', email);
      return res.status(401).json({
        success: false,
        message: 'Incorrect password',
        errorType: 'WRONG_PASSWORD'
      });
    }

    // Generate token using the consistent generateToken function
    const token = generateToken(user);

    // Create response object without password
    const response = {
      success: true,
      token,
      user: {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        gender: user.gender,
        profile_picture_url: user.profile_picture_url
      }
    };

    console.log('Login successful:', {
      userId: user.id,
      role: user.role,
      email: user.email
    });

    return res.json(response);
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error logging in'
    });
  }
};

const verify = async (req, res) => {
  try {
    // User is already attached to req by auth middleware (with status)
    const user = req.user;

    console.log('🔍 User verification details:', {
      userId: user.id,
      email: user.email,
      role: user.role,
      fullName: user.full_name,
      status: user.status,
      deleteScheduledAt: user.delete_scheduled_at
    });

    // Return user data including status for client-side handling
    res.json({
      success: true,
      user: {
        id: user.id,
        full_name: user.full_name,
        email: user.email,
        role: user.role,
        gender: user.gender,
        profile_picture_url: user.profile_picture_url,
        status: user.status,
        delete_scheduled_at: user.delete_scheduled_at
      }
    });
  } catch (error) {
    console.error('Verify error:', error);
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
};

const updateProfile = async (req, res) => {
  const { full_name, email } = req.body;
  const userId = req.user.id;

  if (!full_name || !email) {
    return res.status(400).json({
      success: false,
      message: 'Name and email are required'
    });
  }

  try {
    // Check if email exists for another user
    const [existingUsers] = await db.pool.execute(
      'SELECT id FROM users WHERE email = ? AND id != ?',
      [email, userId]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Email already exists'
      });
    }

    // Update user
    await db.pool.execute(
      'UPDATE users SET full_name = ?, email = ? WHERE id = ?',
      [full_name, email, userId]
    );

    // Get updated user
    const [users] = await db.pool.execute(
      'SELECT id, full_name, email, role, gender FROM users WHERE id = ?',
      [userId]
    );

    return res.json({
      success: true,
      user: users[0]
    });
  } catch (error) {
    console.error('Update profile error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error updating profile'
    });
  }
};

const updatePassword = async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user.id;

  if (!currentPassword || !newPassword) {
    return res.status(400).json({
      success: false,
      message: 'Current password and new password are required'
    });
  }

  try {
    // Get user with password
    const [users] = await db.pool.execute(
      'SELECT password FROM users WHERE id = ?',
      [userId]
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = users[0];

    // Check current password
    const validPassword = await bcrypt.compare(currentPassword, user.password);
    if (!validPassword) {
      return res.status(401).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    await db.pool.execute(
      'UPDATE users SET password = ? WHERE id = ?',
      [hashedPassword, userId]
    );

    return res.json({
      success: true,
      message: 'Password updated successfully'
    });
  } catch (error) {
    console.error('Update password error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error updating password'
    });
  }
};

const googleLogin = async (req, res) => {
  try {
    const { credential } = req.body;

    if (!credential) {
      return res.status(400).json({
        success: false,
        message: 'Google credential is required'
      });
    }

    if (!process.env.GOOGLE_CLIENT_ID) {
      console.error('GOOGLE_CLIENT_ID not configured');
      return res.status(500).json({
        success: false,
        message: 'Google authentication not configured'
      });
    }

    // Verify Google token
    const ticket = await googleClient.verifyIdToken({
      idToken: credential,
      audience: process.env.GOOGLE_CLIENT_ID
    });

    const payload = ticket.getPayload();
    const { email, name, picture } = payload;

    // Check if user exists
    const [existingUser] = await db.pool.execute(
      'SELECT *, deleted_at, deletion_reason FROM users WHERE email = ?',
      [email]
    );

    let user;

    if (existingUser.length > 0) {
      // User exists, check if soft deleted
      user = existingUser[0];

      if (user.deleted_at) {
        return res.status(401).json({
          success: false,
          message: 'تم حذف هذا الحساب ولا يمكن تسجيل الدخول',
          message_en: 'This account has been deleted and cannot be used to login',
          errorType: 'ACCOUNT_DELETED',
          accountStatus: 'deleted',
          deletedAt: user.deleted_at,
          deletionReason: user.deletion_reason
        });
      }

      // Update their information
      await db.pool.execute(
        'UPDATE users SET full_name = ?, profile_picture_url = ? WHERE id = ?',
        [name, picture, user.id]
      );
    } else {
      // Create new user with student role
      const [result] = await db.pool.execute(
        'INSERT INTO users (email, full_name, profile_picture_url, role) VALUES (?, ?, ?, ?)',
        [email, name, picture, 'student']
      );

      const [newUser] = await db.pool.execute(
        'SELECT * FROM users WHERE id = ?',
        [result.insertId]
      );
      user = newUser[0];
    }

    // Check if email is verified
    if (!user.is_email_verified) {
      // Generate new verification code
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
      const verificationExpiry = new Date(Date.now() + 10 * 60 * 1000);
      await db.pool.execute(
        'UPDATE users SET email_verification_code = ?, email_verification_expiry = ? WHERE id = ?',
        [verificationCode, verificationExpiry, user.id]
      );

      try {
        await emailService.sendEmail({
          to: user.email,
          subject: 'Email Verification - Allemni online',
          html: `<p>Your new verification code is: <strong>${verificationCode}</strong></p>`
        });
      } catch (err) {
        console.error('Error sending verification email:', err);
      }

      return res.status(401).json({
        success: false,
        message: 'Please verify your email before logging in. A new code has been sent.',
        requiresVerification: true,
        email: user.email
      });
    }

    // Generate JWT token
    const token = generateToken(user);

    res.json({
      success: true,
      token,
      user: {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        profile_picture_url: user.profile_picture_url
      }
    });
  } catch (error) {
    console.error('Google login error:', error);
    res.status(401).json({
      success: false,
      message: 'Google authentication failed'
    });
  }
};

const googleRegister = async (req, res) => {
  try {
    console.log('🚀 Google register request received');
    console.log('Method:', req.method);
    console.log('Body:', req.body);
    console.log('Headers:', req.headers);

    const { credential, role, gender } = req.body;

    if (!credential) {
      console.log('❌ Missing credential in request');
      return res.status(400).json({
        success: false,
        message: 'Google credential is required'
      });
    }

    if (!role || !gender) {
      return res.status(400).json({
        success: false,
        message: 'Role and gender are required'
      });
    }

    if (!process.env.GOOGLE_CLIENT_ID) {
      console.error('GOOGLE_CLIENT_ID not configured');
      return res.status(500).json({
        success: false,
        message: 'Google authentication not configured'
      });
    }

    // Verify Google token
    const ticket = await googleClient.verifyIdToken({
      idToken: credential,
      audience: process.env.GOOGLE_CLIENT_ID
    });

    const payload = ticket.getPayload();
    const email = payload.email;
    const full_name = payload.name;

    // Check if email exists
    const [existingUsers] = await db.pool.execute(
      'SELECT id, is_email_verified FROM users WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      // If user exists and email is verified, return error
      if (existingUsers[0].is_email_verified) {
        return res.status(400).json({
          success: false,
          message: 'Email already exists'
        });
      }
      // If user exists but email not verified, delete the old record
      await db.pool.execute(
        'DELETE FROM users WHERE email = ? AND is_email_verified = 0',
        [email]
      );
    }

    // Generate verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    const verificationExpiry = new Date(Date.now() + 10 * 60 * 1000); // 3 minutes

    // Generate random password for Google users
    const randomPassword = Math.random().toString(36).slice(-8);
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(randomPassword, salt);

    // Create user with verification code (not verified yet)
    try {
      const [result] = await db.pool.execute(
        'INSERT INTO users (full_name, email, password, role, gender, profile_picture_url, email_verification_code, email_verification_expiry, is_email_verified) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0)',
        [full_name, email, hashedPassword, role, gender, payload.picture || '', verificationCode, verificationExpiry]
      );

      // Send verification email
      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #4a6da7; margin: 0;">Allemni online</h1>
          </div>
          <h2 style="color: #4a6da7; text-align: center;">Email Verification</h2>
          <p>Welcome ${full_name}!</p>
          <p>Thank you for registering with Allemni online using Google. To complete your registration, please verify your email address using the code below:</p>
          <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0; border-radius: 5px; border: 2px solid #4a6da7;">
            ${verificationCode}
          </div>
          <p><strong>This code will expire in 3 minutes.</strong></p>
          <p>If you did not create an account, please ignore this email.</p>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
            <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
              &copy; ${new Date().getFullYear()} Allemnionline. All rights reserved.
            </p>
          </div>
        </div>
      `;

      await emailService.sendEmail({
        to: email,
        subject: 'Email Verification - Allemni online',
        html: emailHtml
      });

      return res.status(201).json({
        success: true,
        message: 'Registration initiated. Please check your email for verification code.',
        email: email
      });
    } catch (dbError) {
      console.error('Database error during Google registration:', dbError);

      // Check if it's a duplicate entry error
      if (dbError.code === 'ER_DUP_ENTRY') {
        return res.status(400).json({
          success: false,
          message: 'Email already exists'
        });
      }

      // Check for missing required fields
      if (dbError.code === 'ER_BAD_NULL_ERROR') {
        return res.status(400).json({
          success: false,
          message: 'Missing required information'
        });
      }

      throw dbError; // Re-throw other database errors
    }
  } catch (error) {
    console.error('Google register error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error during Google registration'
    });
  }
};

const googleAuth = async (req, res) => {
  try {
    const { credential } = req.body;

    if (!credential) {
      return res.status(400).json({
        success: false,
        message: 'Google credential is required'
      });
    }

    // Verify the Google token
    const { OAuth2Client } = require('google-auth-library');
    const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

    const ticket = await client.verifyIdToken({
      idToken: credential,
      audience: process.env.GOOGLE_CLIENT_ID
    });

    const payload = ticket.getPayload();
    const { email, name, picture } = payload;

    try {
      // Check if user exists
      const [existingUser] = await db.pool.execute(
        'SELECT * FROM users WHERE email = ?',
        [email]
      );

      let user;
      let token;

      if (existingUser.length > 0) {
        // User exists, generate token
        user = existingUser[0];
        token = generateToken(user);
      } else {
        // Create new user
        const [result] = await db.pool.execute(
          'INSERT INTO users (email, full_name, role, profile_picture_url) VALUES (?, ?, ?, ?)',
          [email, name, 'student', picture || null]
        );

        const [newUser] = await db.pool.execute(
          'SELECT * FROM users WHERE id = ?',
          [result.insertId]
        );

        user = newUser[0];
        token = generateToken(user);
      }

      res.json({
        success: true,
        token,
        user: {
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          role: user.role,
          profile_picture_url: user.profile_picture_url
        }
      });
    } catch (dbError) {
      console.error('Database error:', dbError);
      res.status(500).json({
        success: false,
        message: 'Database operation failed'
      });
    }
  } catch (error) {
    console.error('Google auth error:', error);
    res.status(500).json({
      success: false,
      message: 'Authentication failed',
      error: error.message
    });
  }
};

const logout = async (req, res) => {
  try {
    // في الواقع، نحن لا نحتاج لفعل شيء في السيرفر لأن التوكن يتم تخزينه في الكلاينت
    // ولكن يمكننا إضافة أي منطق إضافي هنا مثل تسجيل وقت الخروج أو إبطال التوكن

    return res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error during logout'
    });
  }
};

// Function to generate a random 6-digit code
const generateResetCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Function to send password reset email using unified email service
const sendResetEmail = async (email, resetCode, fullName = 'User') => {
  try {
    console.log(`📧 Sending reset email to ${email} with code ${resetCode}`);

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #4a6da7; margin: 0;">Allemni online</h1>
        </div>
        <h2 style="color: #4a6da7; text-align: center;">Password Reset</h2>
        <p>Dear ${fullName},</p>
        <p>We received a request to reset your password. Please use the code below to reset your password:</p>
        <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0; border-radius: 5px; border: 2px solid #4a6da7;">
          ${resetCode}
        </div>
        <p><strong>This code will expire in 10 minutes.</strong></p>
        <p>If you did not request a password reset, please ignore this email.</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
            Visit our platform: <a href="https://allemnionline.com" style="color: #4a6da7; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
            &copy; ${new Date().getFullYear()} Allemnionline. All rights reserved.
          </p>
        </div>
      </div>
    `;

    const text = `Password Reset Code: ${resetCode}\n\nThis code will expire in 10 minutes.\n\nIf you did not request this, please ignore this email.`;

    // Use unified email service (SendGrid only)
    const result = await emailService.sendEmail({
      to: email,
      subject: 'Password Reset Code - Allemni online',
      html,
      text
    });

    console.log('✅ Reset email sent successfully:', result.messageId);
    return result;
  } catch (error) {
    console.error('❌ Error sending reset email:', error);
    throw error;
  }
};

// Forgot password handler
const forgotPassword = async (req, res) => {
  const { email } = req.body;
  console.log('Forgot password request for email:', email);

  if (!email) {
    return res.status(400).json({
      success: false,
      message: 'Email is required'
    });
  }

  try {
    // Check if user exists
    const [users] = await db.pool.execute(
      'SELECT id, full_name FROM users WHERE email = ?',
      [email]
    );

    console.log('Users found:', users.length);

    if (users.length === 0) {
      // For security reasons, don't reveal that the email doesn't exist
      return res.status(200).json({
        success: true,
        message: 'If your email is registered, you will receive a reset code'
      });
    }

    const userId = users[0].id;
    const resetCode = generateResetCode();
    const resetCodeExpiry = new Date(Date.now() + 10 * 60 * 1000); // 3 minutes from now

    console.log(`Generated reset code ${resetCode} for user ${userId}, expires at ${resetCodeExpiry}`);

    // Store reset code in database and capture result for logging
    const [updateResult] = await db.pool.execute(
      'UPDATE users SET reset_code = ?, reset_code_expiry = ? WHERE id = ?',
      [resetCode, resetCodeExpiry, userId]
    );
    console.log('DB update result:', updateResult);

    console.log('Reset code stored in database');

    try {
      // Send email with reset code
      await sendResetEmail(email, resetCode, users[0].full_name);
      console.log('Reset email sent successfully');
    } catch (emailError) {
      console.error('Error sending reset email:', emailError);
      // Continue execution even if email fails
    }

    return res.status(200).json({
      success: true,
      message: 'If your email is registered, you will receive a reset code'
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error processing your request'
    });
  }
};

// Verify reset code
const verifyResetCode = async (req, res) => {
  const { email, code } = req.body;

  if (!email || !code) {
    return res.status(400).json({
      success: false,
      message: 'Email and code are required'
    });
  }

  try {
    // Get user with reset code
    const [users] = await db.pool.execute(
      'SELECT id, reset_code, reset_code_expiry FROM users WHERE email = ?',
      [email]
    );

    if (users.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired code'
      });
    }

    const user = users[0];
    const now = new Date();
    const expiry = new Date(user.reset_code_expiry);

    // Check if code is valid and not expired
    if (user.reset_code !== code || now > expiry) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired code'
      });
    }

    // Generate a temporary token for password reset
    const resetToken = jwt.sign(
      { id: user.id, email, purpose: 'password_reset' },
      config.secret,
      { expiresIn: '15m' }
    );

    return res.status(200).json({
      success: true,
      message: 'Code verified successfully',
      resetToken
    });
  } catch (error) {
    console.error('Verify reset code error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error verifying code'
    });
  }
};

// Reset password
const resetPassword = async (req, res) => {
  const { resetToken, newPassword } = req.body;

  if (!resetToken || !newPassword) {
    return res.status(400).json({
      success: false,
      message: 'Reset token and new password are required'
    });
  }

  try {
    // Verify reset token
    const decoded = jwt.verify(resetToken, config.secret);

    // Check if token is for password reset
    if (decoded.purpose !== 'password_reset') {
      return res.status(400).json({
        success: false,
        message: 'Invalid reset token'
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password and clear reset code
    await db.pool.execute(
      'UPDATE users SET password = ?, reset_code = NULL, reset_code_expiry = NULL WHERE id = ?',
      [hashedPassword, decoded.id]
    );

    return res.status(200).json({
      success: true,
      message: 'Password reset successfully'
    });
  } catch (error) {
    console.error('Reset password error:', error);

    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Error resetting password'
    });
  }
};

// Send welcome email based on user role
const sendWelcomeEmail = async (user) => {
  try {
    let subject, emailHtml;

    if (user.role === 'new_teacher') {
      // Welcome email for new teacher
      subject = 'Welcome to "Allemni online" Platform – We\'re Happy to Have You!';
      emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #4a6da7; margin: 0;">Allemni online</h1>
          </div>
          <h2 style="color: #4a6da7; text-align: center;">Welcome to Our Teaching Community!</h2>

          <p><strong>Peace be upon you and Allah's mercy and blessings</strong></p>
          <p><strong>Dear Teacher ${user.full_name},</strong></p>

          <p>We warmly welcome you to the "Allemnionline in All Languages" platform. We are delighted to have you join our constellation of teachers who carry the mission of teaching Islam and Arabic to the world, according to the methodology of Ahl al-Sunnah wa al-Jama'ah and the understanding of the righteous predecessors.</p>

          <p>We believe that you will provide a qualitative addition to your students, God willing, and we are here to support you every step of the way on your teaching journey.</p>

          <div style="background-color: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px; border-left: 4px solid #4a6da7;">
            <h3 style="color: #4a6da7; margin-top: 0;">📚 You can now:</h3>
            <ul style="margin: 10px 0;">
              <li>Complete your teaching profile</li>
              <li>Set your schedule</li>
              <li>Receive requests from students</li>
            </ul>
          </div>

          <p>🔧 If you need any assistance, don't hesitate to contact us at any time.</p>

          <p><strong>May Allah benefit through you, record your reward, and bless your knowledge and efforts.</strong></p>

          <p style="margin-top: 30px;"><strong>The "Allemni online" Platform Team</strong></p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
            <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
              Visit our platform: <a href="https://allemnionline.com" style="color: #4a6da7; text-decoration: none;">allemnionline.com</a>
            </p>
            <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
              &copy; ${new Date().getFullYear()} Allemnionline. All rights reserved.
            </p>
          </div>
        </div>
      `;
    } else if (user.role === 'student') {
      // Welcome email for new student
      subject = 'Welcome to "Allemni online" Platform – Where Your Educational Journey Begins!';
      emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #4a6da7; margin: 0;">Allemni online</h1>
          </div>
          <h2 style="color: #4a6da7; text-align: center;">Welcome to Our Learning Community!</h2>

          <p><strong>Peace be upon you and Allah's mercy and blessings</strong></p>
          <p><strong>Dear Student ${user.full_name},</strong></p>

          <p>We welcome you to the "Allemnionline in All Languages" platform. We are pleased that you have chosen to join us to Arabic in All Languages from their authentic sources, under the guidance of specialized teachers, and according to a trusted methodology that combines knowledge and authenticity.</p>

          <div style="background-color: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px; border-left: 4px solid #4a6da7;">
            <h3 style="color: #4a6da7; margin-top: 0;">👨‍🏫 You can now:</h3>
            <ul style="margin: 10px 0;">
              <li>Browse teacher profiles</li>
              <li>Book live lessons at times convenient for you</li>
              <li>Choose the language you prefer to learn in</li>
            </ul>
          </div>

          <p>📌 Everything you need to start your journey in knowledge is at your fingertips!</p>

          <p>We wish you an educational journey filled with benefit, success, and blessings.</p>

          <p style="margin-top: 30px;"><strong>The "Allemni online" Platform Team</strong></p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
            <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
              Visit our platform: <a href="https://allemnionline.com" style="color: #4a6da7; text-decoration: none;">allemnionline.com</a>
            </p>
            <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
              &copy; ${new Date().getFullYear()} Allemnionline. All rights reserved.
            </p>
          </div>
        </div>
      `;
    } else {
      // Default welcome email for other roles
      return; // Don't send welcome email for other roles
    }

    await emailService.sendEmail({
      to: user.email,
      subject: subject,
      html: emailHtml
    });

    console.log(`✅ Welcome email sent to ${user.email} (${user.role})`);
  } catch (error) {
    console.error('❌ Error sending welcome email:', error);
    throw error;
  }
};

// Verify email with code
const verifyEmail = async (req, res) => {
  const { email, code } = req.body;

  if (!email || !code) {
    return res.status(400).json({
      success: false,
      message: 'Email and verification code are required'
    });
  }

  try {
    // Find user with verification code
    const [users] = await db.pool.execute(
      'SELECT * FROM users WHERE email = ? AND email_verification_code = ? AND is_email_verified = 0',
      [email, code]
    );

    if (users.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid verification code'
      });
    }

    const user = users[0];

    // Check if code is expired
    if (new Date() > new Date(user.email_verification_expiry)) {
      return res.status(400).json({
        success: false,
        message: 'Verification code has expired'
      });
    }

    // Verify email and clear verification code
    await db.pool.execute(
      'UPDATE users SET is_email_verified = 1, email_verification_code = NULL, email_verification_expiry = NULL WHERE id = ?',
      [user.id]
    );

    // Send welcome email based on user role
    try {
      await sendWelcomeEmail(user);
    } catch (emailError) {
      console.error('Error sending welcome email:', emailError);
      // Continue even if welcome email fails
    }

    // Generate token
    const token = generateToken(user);

    return res.status(200).json({
      success: true,
      message: 'Email verified successfully',
      token,
      user: {
        id: user.id,
        full_name: user.full_name,
        email: user.email,
        role: user.role,
        gender: user.gender,
        profile_picture_url: user.profile_picture_url
      }
    });
  } catch (error) {
    console.error('Email verification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error verifying email'
    });
  }
};

// Resend verification code
const resendVerificationCode = async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      message: 'Email is required'
    });
  }

  try {
    // Find unverified user
    const [users] = await db.pool.execute(
      'SELECT * FROM users WHERE email = ? AND is_email_verified = 0',
      [email]
    );

    if (users.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No pending verification found for this email'
      });
    }

    const user = users[0];

    // Generate new verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    const verificationExpiry = new Date(Date.now() + 10 * 60 * 1000); // 3 minutes

    // Update verification code
    await db.pool.execute(
      'UPDATE users SET email_verification_code = ?, email_verification_expiry = ? WHERE id = ?',
      [verificationCode, verificationExpiry, user.id]
    );

    // Send verification email
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #4a6da7; margin: 0;">Allemni online</h1>
        </div>
        <h2 style="color: #4a6da7; text-align: center;">Email Verification</h2>
        <p>Hello ${user.full_name}!</p>
        <p>Here is your new verification code:</p>
        <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0; border-radius: 5px; border: 2px solid #4a6da7;">
          ${verificationCode}
        </div>
        <p><strong>This code will expire in 10 minutes.</strong></p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
            Visit our platform: <a href="https://allemnionline.com" style="color: #4a6da7; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
            &copy; ${new Date().getFullYear()} Allemnionline. All rights reserved.
          </p>
        </div>
      </div>
    `;

    await emailService.sendEmail({
      to: email,
      subject: 'New Verification Code - Allemni online',
      html: emailHtml
    });

    return res.status(200).json({
      success: true,
      message: 'New verification code sent to your email'
    });
  } catch (error) {
    console.error('Resend verification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error sending verification code'
    });
  }
};

// Check user status by email (no token required)
const checkUserStatus = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني مطلوب',
        message_en: 'Email is required'
      });
    }

    // البحث عن المستخدم بالإيميل
    const [users] = await db.pool.execute(
      'SELECT id, email, status, delete_scheduled_at, deleted_at, deletion_reason FROM users WHERE email = ?',
      [email]
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود',
        message_en: 'User not found'
      });
    }

    const user = users[0];

    // التحقق من حالة المستخدم - الحذف الناعم أولاً
    if (user.deleted_at) {
      return res.status(200).json({
        success: false,
        message: 'تم حذف هذا الحساب',
        message_en: 'Account has been deleted',
        accountStatus: 'deleted',
        userExists: true,
        deletedAt: user.deleted_at,
        deletionReason: user.deletion_reason
      });
    }

    // التحقق من النظام القديم
    if (user.status === 'deleted') {
      return res.status(200).json({
        success: false,
        message: 'تم حذف هذا الحساب',
        message_en: 'Account has been deleted',
        accountStatus: 'deleted',
        userExists: true
      });
    }

    if (user.status === 'pending_deletion') {
      return res.status(200).json({
        success: false,
        message: 'هذا الحساب مجدول للحذف',
        message_en: 'Account is scheduled for deletion',
        accountStatus: 'pending_deletion',
        deleteScheduledAt: user.delete_scheduled_at,
        userExists: true
      });
    }

    // المستخدم نشط
    return res.status(200).json({
      success: true,
      message: 'الحساب نشط',
      message_en: 'Account is active',
      accountStatus: 'active',
      userExists: true
    });

  } catch (error) {
    console.error('Check user status error:', error);
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      message_en: 'Server error'
    });
  }
};

module.exports = {
  register,
  login,
  verify,
  updateProfile,
  updatePassword,
  googleLogin,
  googleRegister,
  googleAuth,
  logout,
  forgotPassword,
  verifyResetCode,
  resetPassword,
  verifyEmail,
  resendVerificationCode,
  checkUserStatus
};
