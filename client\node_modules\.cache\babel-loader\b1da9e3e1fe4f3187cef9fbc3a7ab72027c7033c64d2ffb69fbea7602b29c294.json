{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{useNavigate,Link,useLocation}from'react-router-dom';import{useTranslation}from'react-i18next';import{GoogleOAuthProvider,GoogleLogin}from'@react-oauth/google';import axios from'axios';import{Box,Container,Paper,TextField,Button,Typography,IconButton,InputAdornment,useTheme,alpha,Divider,Stack,Fade,Alert,CircularProgress}from'@mui/material';import{Visibility,VisibilityOff,Email,Lock,ArrowForward as ArrowForwardIcon,ArrowBack as ArrowBackIcon,Google as GoogleIcon}from'@mui/icons-material';import{useAuth}from'../../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Login=()=>{var _location$state,_location$state2;const{t,i18n}=useTranslation();const navigate=useNavigate();const location=useLocation();const theme=useTheme();const{login,googleLogin}=useAuth();const isRtl=i18n.language==='ar';const[formData,setFormData]=useState({email:'',password:''});const[showPassword,setShowPassword]=useState(false);const[error,setError]=useState('');const[loading,setLoading]=useState(false);const[accountStatusMessage,setAccountStatusMessage]=useState('');// التحقق من رسائل حالة الحساب من navigation state أو localStorage\nuseEffect(()=>{if(process.env.NODE_ENV==='development'){console.log('Login page: Checking for account status messages');console.log('Location state:',location.state);}// Test: إضافة رسالة تجريبية للاختبار (تم حذفها بعد التأكد من عمل النظام)\n// التحقق من navigation state أولاً\nif(location.state&&location.state.message){console.log('Found message in location state:',location.state.message);setAccountStatusMessage(location.state.message);setError(location.state.message);return;}// التحقق من localStorage للرسائل المحفوظة\nconst savedMessage=localStorage.getItem('accountStatusMessage');if(process.env.NODE_ENV==='development'){console.log('Saved message in localStorage:',savedMessage);}if(savedMessage){try{const messageData=JSON.parse(savedMessage);// استخدام الرسالة المترجمة حسب اللغة الحالية\nconst displayMessage=i18n.language==='ar'?messageData.message:messageData.message_en;setAccountStatusMessage(displayMessage);setError(displayMessage);// تحديث location.state للتوافق مع باقي الكود\nlocation.state={message:displayMessage,accountStatus:messageData.accountStatus,deleteScheduledAt:messageData.deleteScheduledAt};// حذف الرسالة من localStorage بعد عرضها\nlocalStorage.removeItem('accountStatusMessage');if(process.env.NODE_ENV==='development'){console.log('Displaying account status message:',{accountStatus:messageData.accountStatus,message:displayMessage,language:i18n.language});}}catch(e){if(process.env.NODE_ENV==='development'){console.error('Error parsing saved account status message:',e);}localStorage.removeItem('accountStatusMessage');}}else if(process.env.NODE_ENV==='development'){console.log('No account status message found');}},[location.state,i18n.language]);// إضافة i18n.language للتحديث عند تغيير اللغة\nconst handleChange=e=>{setFormData({...formData,[e.target.name]:e.target.value});setError('');};const handleGoogleSuccess=async credentialResponse=>{setError('');setLoading(true);try{const result=await googleLogin(credentialResponse);if(!result.success){throw new Error(result.message||t('auth.googleSignInError'));}if(result.success&&result.user){const{role}=result.user;if(!role){throw new Error(t('auth.invalidRole'));}switch(role){case'admin':navigate('/admin/dashboard');break;case'platform_teacher':navigate('/teacher/dashboard');break;case'new_teacher':navigate('/teacher/application');break;case'student':navigate('/student/dashboard');break;default:navigate('/');}}}catch(error){console.error('Google login error:',error);setError(error.message||t('auth.googleSignInError'));}finally{setLoading(false);}};const handleGoogleError=()=>{setError(t('auth.googleSignInError'));setLoading(false);};const handleSubmit=async e=>{e.preventDefault();setLoading(true);// Basic validation\nif(!formData.email||!formData.password){setError(t('auth.fillAllFields'));setLoading(false);return;}// Clear error only after validation passes\nsetError('');try{// أولاً، تحقق من حالة المستخدم قبل محاولة تسجيل الدخول\nconst statusResponse=await axios.post('/api/auth/check-user-status',{email:formData.email});// إذا كان المستخدم محذوف أو مجدول للحذف\nif(!statusResponse.data.success&&statusResponse.data.accountStatus){const displayMessage=i18n.language==='ar'?statusResponse.data.message:statusResponse.data.message_en;setAccountStatusMessage(displayMessage);setError(displayMessage);// حفظ الرسالة في localStorage للمرات القادمة\nlocalStorage.setItem('accountStatusMessage',JSON.stringify({message:statusResponse.data.message,message_en:statusResponse.data.message_en,accountStatus:statusResponse.data.accountStatus,deleteScheduledAt:statusResponse.data.deleteScheduledAt}));setLoading(false);return;}// إذا كان المستخدم نشط، تابع عملية تسجيل الدخول العادية\nawait login(formData.email,formData.password);navigate('/');}catch(error){var _error$response,_error$response2;console.error('Login error details:',{error:error,response:error.response,data:(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.data,status:(_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.status,message:error.message});// إذا كان الخطأ من التحقق من حالة المستخدم (المستخدم غير موجود)\nif(error.response&&error.response.status===404){// تابع عملية تسجيل الدخول العادية لعرض رسالة الخطأ المناسبة\ntry{await login(formData.email,formData.password);navigate('/');}catch(loginError){var _loginError$response;const errorData=(_loginError$response=loginError.response)===null||_loginError$response===void 0?void 0:_loginError$response.data;if(errorData!==null&&errorData!==void 0&&errorData.requiresVerification){navigate('/verify-email',{state:{email:formData.email}});}else{var _loginError$response2;let errorMessage;if((errorData===null||errorData===void 0?void 0:errorData.errorType)==='EMAIL_NOT_FOUND'){errorMessage=t('auth.emailNotFound');}else if((errorData===null||errorData===void 0?void 0:errorData.errorType)==='WRONG_PASSWORD'){errorMessage=t('auth.wrongPassword');}else if((errorData===null||errorData===void 0?void 0:errorData.errorType)==='ACCOUNT_DELETED'){errorMessage=t('auth.accountDeleted','تم حذف هذا الحساب ولا يمكن تسجيل الدخول');}else if(errorData!==null&&errorData!==void 0&&errorData.message){errorMessage=errorData.message;}else if(loginError.message){errorMessage=loginError.message;}else if(((_loginError$response2=loginError.response)===null||_loginError$response2===void 0?void 0:_loginError$response2.status)===401){errorMessage=t('auth.invalidCredentials');}else{errorMessage=t('auth.loginFailed');}setError(errorMessage);}}}else{var _error$response3;// خطأ آخر في تسجيل الدخول\nconst errorData=(_error$response3=error.response)===null||_error$response3===void 0?void 0:_error$response3.data;if(errorData!==null&&errorData!==void 0&&errorData.requiresVerification){navigate('/verify-email',{state:{email:formData.email}});}else{var _error$response4;let errorMessage;if((errorData===null||errorData===void 0?void 0:errorData.errorType)==='EMAIL_NOT_FOUND'){errorMessage=t('auth.emailNotFound');}else if((errorData===null||errorData===void 0?void 0:errorData.errorType)==='WRONG_PASSWORD'){errorMessage=t('auth.wrongPassword');}else if((errorData===null||errorData===void 0?void 0:errorData.errorType)==='ACCOUNT_DELETED'){errorMessage=t('auth.accountDeleted','تم حذف هذا الحساب ولا يمكن تسجيل الدخول');}else if(errorData!==null&&errorData!==void 0&&errorData.message){errorMessage=errorData.message;}else if(error.message){errorMessage=error.message;}else if(((_error$response4=error.response)===null||_error$response4===void 0?void 0:_error$response4.status)===401){errorMessage=t('auth.invalidCredentials');}else{errorMessage=t('auth.loginFailed');}setError(errorMessage);}}}finally{setLoading(false);}};return/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',display:'flex',alignItems:'center',background:`linear-gradient(to bottom, ${alpha(theme.palette.primary.dark,0.9)}, ${alpha('#000',0.7)})`,py:8,position:'relative',overflow:'hidden','&::before':{content:'\"\"',position:'absolute',top:0,left:0,right:0,bottom:0,backgroundImage:'url(\"https://png.pngtree.com/background/********/original/pngtree-islamic-background-picture-image_2027687.jpg\")',backgroundSize:'cover',backgroundPosition:'center',zIndex:-1},'&::after':{content:'\"\"',position:'absolute',top:0,left:0,right:0,bottom:0,background:`radial-gradient(circle at center, ${alpha(theme.palette.primary.main,0.1)}, ${alpha(theme.palette.primary.dark,0.4)})`,zIndex:-1}},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"sm\",sx:{position:'relative',zIndex:1},children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:1000,children:/*#__PURE__*/_jsx(Paper,{elevation:24,sx:{p:{xs:3,sm:6},borderRadius:4,backdropFilter:'blur(20px)',backgroundColor:alpha(theme.palette.background.paper,0.8),boxShadow:`0 8px 32px ${alpha(theme.palette.primary.dark,0.2)}`,position:'relative',overflow:'hidden',border:`1px solid ${alpha(theme.palette.primary.main,0.1)}`,'&::before':{content:'\"\"',position:'absolute',top:0,left:0,right:0,height:4,background:`linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`}},children:/*#__PURE__*/_jsxs(Box,{component:\"form\",onSubmit:handleSubmit,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",align:\"center\",gutterBottom:true,sx:{fontWeight:700,mb:4,background:`linear-gradient(120deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,WebkitBackgroundClip:'text',WebkitTextFillColor:'transparent',textShadow:'0 2px 4px rgba(0,0,0,0.1)',position:'relative','&::after':{content:'\"\"',position:'absolute',bottom:-8,left:'50%',transform:'translateX(-50%)',width:60,height:2,background:`linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`}},children:t('auth.welcomeBack')}),accountStatusMessage&&/*#__PURE__*/_jsxs(Alert,{severity:((_location$state=location.state)===null||_location$state===void 0?void 0:_location$state.accountStatus)==='deleted'?'error':'warning',sx:{mb:3,borderRadius:2},children:[accountStatusMessage,((_location$state2=location.state)===null||_location$state2===void 0?void 0:_location$state2.accountStatus)==='pending_deletion'&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mt:1},children:i18n.language==='ar'?'يمكنك تسجيل الدخول وإلغاء عملية الحذف من صفحة الملف الشخصي.':'You can log in and cancel the deletion from your profile page.'})]}),error&&!accountStatusMessage&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:3,borderRadius:2},children:error}),/*#__PURE__*/_jsxs(Stack,{spacing:3,children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,name:\"email\",label:t('common.email'),value:formData.email,onChange:handleChange,error:Boolean(error),InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(Email,{})})}}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,name:\"password\",label:t('common.password'),type:showPassword?'text':'password',value:formData.password,onChange:handleChange,error:Boolean(error),InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(Lock,{})}),endAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>setShowPassword(!showPassword),edge:\"end\",children:showPassword?/*#__PURE__*/_jsx(VisibilityOff,{}):/*#__PURE__*/_jsx(Visibility,{})})})}}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'flex-end',mb:2},children:/*#__PURE__*/_jsx(Link,{to:\"/forgot-password\",style:{color:theme.palette.primary.main,textDecoration:'none',fontSize:'0.875rem'},children:t('auth.forgotPassword')})}),/*#__PURE__*/_jsx(Button,{type:\"submit\",fullWidth:true,variant:\"contained\",size:\"large\",disabled:loading,endIcon:!isRtl?/*#__PURE__*/_jsx(ArrowForwardIcon,{}):/*#__PURE__*/_jsx(ArrowBackIcon,{}),sx:{mt:2,height:48,background:`linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,'&:hover':{background:`linear-gradient(90deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`}},children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:24,color:\"inherit\"}):t('auth.signIn')}),/*#__PURE__*/_jsx(Box,{sx:{position:'relative',my:2},children:/*#__PURE__*/_jsx(Divider,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:t('auth.or')})})}),/*#__PURE__*/_jsx(GoogleOAuthProvider,{clientId:\"52320482193-ig6u5a5r3hi0gu65g683c34t5efc2b6s.apps.googleusercontent.com\",children:/*#__PURE__*/_jsx(Box,{sx:{width:'100%',display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(GoogleLogin,{onSuccess:handleGoogleSuccess,onError:handleGoogleError,type:\"standard\",theme:\"outline\",size:\"large\",width:\"400\",text:\"continue_with\",shape:\"rectangular\",logo_alignment:\"left\",useOneTap:false,auto_select:false})})}),/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',mt:2},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",children:[t('auth.noAccount'),' ',/*#__PURE__*/_jsx(Link,{to:\"/register\",style:{color:theme.palette.primary.main,textDecoration:'none',fontWeight:600},children:t('auth.signUp')})]})})]})]})})})})});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useNavigate", "Link", "useLocation", "useTranslation", "GoogleOAuthProvider", "GoogleLogin", "axios", "Box", "Container", "Paper", "TextField", "<PERSON><PERSON>", "Typography", "IconButton", "InputAdornment", "useTheme", "alpha", "Divider", "<PERSON><PERSON>", "Fade", "<PERSON><PERSON>", "CircularProgress", "Visibility", "VisibilityOff", "Email", "Lock", "ArrowForward", "ArrowForwardIcon", "ArrowBack", "ArrowBackIcon", "Google", "GoogleIcon", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "_location$state", "_location$state2", "t", "i18n", "navigate", "location", "theme", "login", "googleLogin", "isRtl", "language", "formData", "setFormData", "email", "password", "showPassword", "setShowPassword", "error", "setError", "loading", "setLoading", "accountStatusMessage", "setAccountStatusMessage", "process", "env", "NODE_ENV", "console", "log", "state", "message", "savedMessage", "localStorage", "getItem", "messageData", "JSON", "parse", "displayMessage", "message_en", "accountStatus", "deleteScheduledAt", "removeItem", "e", "handleChange", "target", "name", "value", "handleGoogleSuccess", "credentialResponse", "result", "success", "Error", "user", "role", "handleGoogleError", "handleSubmit", "preventDefault", "statusResponse", "post", "data", "setItem", "stringify", "_error$response", "_error$response2", "response", "status", "loginError", "_loginError$response", "errorData", "requiresVerification", "_loginError$response2", "errorMessage", "errorType", "_error$response3", "_error$response4", "sx", "minHeight", "display", "alignItems", "background", "palette", "primary", "dark", "py", "position", "overflow", "content", "top", "left", "right", "bottom", "backgroundImage", "backgroundSize", "backgroundPosition", "zIndex", "main", "children", "max<PERSON><PERSON><PERSON>", "in", "timeout", "elevation", "p", "xs", "sm", "borderRadius", "<PERSON><PERSON>ilter", "backgroundColor", "paper", "boxShadow", "border", "height", "secondary", "component", "onSubmit", "variant", "align", "gutterBottom", "fontWeight", "mb", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "transform", "width", "severity", "mt", "spacing", "fullWidth", "label", "onChange", "Boolean", "InputProps", "startAdornment", "type", "endAdornment", "onClick", "edge", "justifyContent", "to", "style", "color", "textDecoration", "fontSize", "size", "disabled", "endIcon", "my", "clientId", "onSuccess", "onError", "text", "shape", "logo_alignment", "useOneTap", "auto_select", "textAlign"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/auth/Login.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useNavigate, Link, useLocation } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';\nimport axios from 'axios';\nimport {\n  Box,\n  Container,\n  Paper,\n  TextField,\n  Button,\n  Typography,\n  IconButton,\n  InputAdornment,\n  useTheme,\n  alpha,\n  Divider,\n  Stack,\n  Fade,\n  Alert,\n  CircularProgress\n} from '@mui/material';\nimport {\n  Visibility,\n  VisibilityOff,\n  Email,\n  Lock,\n  ArrowForward as ArrowForwardIcon,\n  ArrowBack as ArrowBackIcon,\n  Google as GoogleIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst Login = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const theme = useTheme();\n  const { login, googleLogin } = useAuth();\n  const isRtl = i18n.language === 'ar';\n\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [accountStatusMessage, setAccountStatusMessage] = useState('');\n\n  // التحقق من رسائل حالة الحساب من navigation state أو localStorage\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'development') {\n      console.log('Login page: Checking for account status messages');\n      console.log('Location state:', location.state);\n    }\n\n    // Test: إضافة رسالة تجريبية للاختبار (تم حذفها بعد التأكد من عمل النظام)\n\n    // التحقق من navigation state أولاً\n    if (location.state && location.state.message) {\n      console.log('Found message in location state:', location.state.message);\n      setAccountStatusMessage(location.state.message);\n      setError(location.state.message);\n      return;\n    }\n\n    // التحقق من localStorage للرسائل المحفوظة\n    const savedMessage = localStorage.getItem('accountStatusMessage');\n    if (process.env.NODE_ENV === 'development') {\n      console.log('Saved message in localStorage:', savedMessage);\n    }\n\n    if (savedMessage) {\n      try {\n        const messageData = JSON.parse(savedMessage);\n\n        // استخدام الرسالة المترجمة حسب اللغة الحالية\n        const displayMessage = i18n.language === 'ar' ? messageData.message : messageData.message_en;\n\n        setAccountStatusMessage(displayMessage);\n        setError(displayMessage);\n\n        // تحديث location.state للتوافق مع باقي الكود\n        location.state = {\n          message: displayMessage,\n          accountStatus: messageData.accountStatus,\n          deleteScheduledAt: messageData.deleteScheduledAt\n        };\n\n        // حذف الرسالة من localStorage بعد عرضها\n        localStorage.removeItem('accountStatusMessage');\n\n        if (process.env.NODE_ENV === 'development') {\n          console.log('Displaying account status message:', {\n            accountStatus: messageData.accountStatus,\n            message: displayMessage,\n            language: i18n.language\n          });\n        }\n      } catch (e) {\n        if (process.env.NODE_ENV === 'development') {\n          console.error('Error parsing saved account status message:', e);\n        }\n        localStorage.removeItem('accountStatusMessage');\n      }\n    } else if (process.env.NODE_ENV === 'development') {\n      console.log('No account status message found');\n    }\n  }, [location.state, i18n.language]); // إضافة i18n.language للتحديث عند تغيير اللغة\n\n\n\n  const handleChange = (e) => {\n    setFormData({ ...formData, [e.target.name]: e.target.value });\n    setError('');\n  };\n\n  const handleGoogleSuccess = async (credentialResponse) => {\n    setError('');\n    setLoading(true);\n    try {\n      const result = await googleLogin(credentialResponse);\n      if (!result.success) {\n        throw new Error(result.message || t('auth.googleSignInError'));\n      }\n      if (result.success && result.user) {\n        const { role } = result.user;\n\n        if (!role) {\n          throw new Error(t('auth.invalidRole'));\n        }\n\n        switch (role) {\n          case 'admin':\n            navigate('/admin/dashboard');\n            break;\n          case 'platform_teacher':\n            navigate('/teacher/dashboard');\n            break;\n          case 'new_teacher':\n            navigate('/teacher/application');\n            break;\n          case 'student':\n            navigate('/student/dashboard');\n            break;\n          default:\n            navigate('/');\n        }\n      }\n    } catch (error) {\n      console.error('Google login error:', error);\n      setError(error.message || t('auth.googleSignInError'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleGoogleError = () => {\n    setError(t('auth.googleSignInError'));\n    setLoading(false);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    // Basic validation\n    if (!formData.email || !formData.password) {\n      setError(t('auth.fillAllFields'));\n      setLoading(false);\n      return;\n    }\n\n    // Clear error only after validation passes\n    setError('');\n\n    try {\n      // أولاً، تحقق من حالة المستخدم قبل محاولة تسجيل الدخول\n      const statusResponse = await axios.post('/api/auth/check-user-status', {\n        email: formData.email\n      });\n\n      // إذا كان المستخدم محذوف أو مجدول للحذف\n      if (!statusResponse.data.success && statusResponse.data.accountStatus) {\n        const displayMessage = i18n.language === 'ar'\n          ? statusResponse.data.message\n          : statusResponse.data.message_en;\n\n        setAccountStatusMessage(displayMessage);\n        setError(displayMessage);\n\n        // حفظ الرسالة في localStorage للمرات القادمة\n        localStorage.setItem('accountStatusMessage', JSON.stringify({\n          message: statusResponse.data.message,\n          message_en: statusResponse.data.message_en,\n          accountStatus: statusResponse.data.accountStatus,\n          deleteScheduledAt: statusResponse.data.deleteScheduledAt\n        }));\n\n        setLoading(false);\n        return;\n      }\n\n      // إذا كان المستخدم نشط، تابع عملية تسجيل الدخول العادية\n      await login(formData.email, formData.password);\n      navigate('/');\n    } catch (error) {\n      console.error('Login error details:', {\n        error: error,\n        response: error.response,\n        data: error.response?.data,\n        status: error.response?.status,\n        message: error.message\n      });\n\n      // إذا كان الخطأ من التحقق من حالة المستخدم (المستخدم غير موجود)\n      if (error.response && error.response.status === 404) {\n        // تابع عملية تسجيل الدخول العادية لعرض رسالة الخطأ المناسبة\n        try {\n          await login(formData.email, formData.password);\n          navigate('/');\n        } catch (loginError) {\n          const errorData = loginError.response?.data;\n          if (errorData?.requiresVerification) {\n            navigate('/verify-email', { state: { email: formData.email } });\n          } else {\n            let errorMessage;\n            if (errorData?.errorType === 'EMAIL_NOT_FOUND') {\n              errorMessage = t('auth.emailNotFound');\n            } else if (errorData?.errorType === 'WRONG_PASSWORD') {\n              errorMessage = t('auth.wrongPassword');\n            } else if (errorData?.errorType === 'ACCOUNT_DELETED') {\n              errorMessage = t('auth.accountDeleted', 'تم حذف هذا الحساب ولا يمكن تسجيل الدخول');\n            } else if (errorData?.message) {\n              errorMessage = errorData.message;\n            } else if (loginError.message) {\n              errorMessage = loginError.message;\n            } else if (loginError.response?.status === 401) {\n              errorMessage = t('auth.invalidCredentials');\n            } else {\n              errorMessage = t('auth.loginFailed');\n            }\n            setError(errorMessage);\n          }\n        }\n      } else {\n        // خطأ آخر في تسجيل الدخول\n        const errorData = error.response?.data;\n        if (errorData?.requiresVerification) {\n          navigate('/verify-email', { state: { email: formData.email } });\n        } else {\n          let errorMessage;\n          if (errorData?.errorType === 'EMAIL_NOT_FOUND') {\n            errorMessage = t('auth.emailNotFound');\n          } else if (errorData?.errorType === 'WRONG_PASSWORD') {\n            errorMessage = t('auth.wrongPassword');\n          } else if (errorData?.errorType === 'ACCOUNT_DELETED') {\n            errorMessage = t('auth.accountDeleted', 'تم حذف هذا الحساب ولا يمكن تسجيل الدخول');\n          } else if (errorData?.message) {\n            errorMessage = errorData.message;\n          } else if (error.message) {\n            errorMessage = error.message;\n          } else if (error.response?.status === 401) {\n            errorMessage = t('auth.invalidCredentials');\n          } else {\n            errorMessage = t('auth.loginFailed');\n          }\n          setError(errorMessage);\n        }\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        display: 'flex',\n        alignItems: 'center',\n        background: `linear-gradient(to bottom, ${alpha(theme.palette.primary.dark, 0.9)}, ${alpha('#000', 0.7)})`,\n        py: 8,\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundImage: 'url(\"https://png.pngtree.com/background/********/original/pngtree-islamic-background-picture-image_2027687.jpg\")',\n          backgroundSize: 'cover',\n          backgroundPosition: 'center',\n          zIndex: -1\n        },\n        '&::after': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: `radial-gradient(circle at center, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.dark, 0.4)})`,\n          zIndex: -1\n        }\n      }}\n    >\n      <Container maxWidth=\"sm\" sx={{ position: 'relative', zIndex: 1 }}>\n        <Fade in timeout={1000}>\n          <Paper\n            elevation={24}\n            sx={{\n              p: { xs: 3, sm: 6 },\n              borderRadius: 4,\n              backdropFilter: 'blur(20px)',\n              backgroundColor: alpha(theme.palette.background.paper, 0.8),\n              boxShadow: `0 8px 32px ${alpha(theme.palette.primary.dark, 0.2)}`,\n              position: 'relative',\n              overflow: 'hidden',\n              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: 4,\n                background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`\n              }\n            }}\n          >\n            <Box component=\"form\" onSubmit={handleSubmit}>\n              <Typography\n                variant=\"h4\"\n                align=\"center\"\n                gutterBottom\n                sx={{\n                  fontWeight: 700,\n                  mb: 4,\n                  background: `linear-gradient(120deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                  position: 'relative',\n                  '&::after': {\n                    content: '\"\"',\n                    position: 'absolute',\n                    bottom: -8,\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    width: 60,\n                    height: 2,\n                    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`\n                  }\n                }}\n              >\n                {t('auth.welcomeBack')}\n              </Typography>\n\n              {/* رسائل حالة الحساب */}\n              {accountStatusMessage && (\n                <Alert\n                  severity={location.state?.accountStatus === 'deleted' ? 'error' : 'warning'}\n                  sx={{ mb: 3, borderRadius: 2 }}\n                >\n                  {accountStatusMessage}\n                  {location.state?.accountStatus === 'pending_deletion' && (\n                    <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                      {i18n.language === 'ar'\n                        ? 'يمكنك تسجيل الدخول وإلغاء عملية الحذف من صفحة الملف الشخصي.'\n                        : 'You can log in and cancel the deletion from your profile page.'\n                      }\n                    </Typography>\n                  )}\n                </Alert>\n              )}\n\n              {/* رسائل الخطأ العادية */}\n              {error && !accountStatusMessage && (\n                <Alert severity=\"error\" sx={{ mb: 3, borderRadius: 2 }}>\n                  {error}\n                </Alert>\n              )}\n\n\n\n              <Stack spacing={3}>\n                <TextField\n                  fullWidth\n                  name=\"email\"\n                  label={t('common.email')}\n                  value={formData.email}\n                  onChange={handleChange}\n                  error={Boolean(error)}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <Email />\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n                <TextField\n                  fullWidth\n                  name=\"password\"\n                  label={t('common.password')}\n                  type={showPassword ? 'text' : 'password'}\n                  value={formData.password}\n                  onChange={handleChange}\n                  error={Boolean(error)}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <Lock />\n                      </InputAdornment>\n                    ),\n                    endAdornment: (\n                      <InputAdornment position=\"end\">\n                        <IconButton\n                          onClick={() => setShowPassword(!showPassword)}\n                          edge=\"end\"\n                        >\n                          {showPassword ? <VisibilityOff /> : <Visibility />}\n                        </IconButton>\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Link\n                    to=\"/forgot-password\"\n                    style={{\n                      color: theme.palette.primary.main,\n                      textDecoration: 'none',\n                      fontSize: '0.875rem',\n                    }}\n                  >\n                    {t('auth.forgotPassword')}\n                  </Link>\n                </Box>\n\n                <Button\n                  type=\"submit\"\n                  fullWidth\n                  variant=\"contained\"\n                  size=\"large\"\n                  disabled={loading}\n                  endIcon={!isRtl ? <ArrowForwardIcon /> : <ArrowBackIcon />}\n                  sx={{\n                    mt: 2,\n                    height: 48,\n                    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                    '&:hover': {\n                      background: `linear-gradient(90deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,\n                    },\n                  }}\n                >\n                  {loading ? (\n                    <CircularProgress size={24} color=\"inherit\" />\n                  ) : (\n                    t('auth.signIn')\n                  )}\n                </Button>\n\n                <Box sx={{ position: 'relative', my: 2 }}>\n                  <Divider>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {t('auth.or')}\n                    </Typography>\n                  </Divider>\n                </Box>\n\n                <GoogleOAuthProvider clientId=\"52320482193-ig6u5a5r3hi0gu65g683c34t5efc2b6s.apps.googleusercontent.com\">\n                  <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>\n                    <GoogleLogin\n                      onSuccess={handleGoogleSuccess}\n                      onError={handleGoogleError}\n                      type=\"standard\"\n                      theme=\"outline\"\n                      size=\"large\"\n                      width=\"400\"\n                      text=\"continue_with\"\n                      shape=\"rectangular\"\n                      logo_alignment=\"left\"\n                      useOneTap={false}\n                      auto_select={false}\n                    />\n                  </Box>\n                </GoogleOAuthProvider>\n\n                <Box sx={{ textAlign: 'center', mt: 2 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {t('auth.noAccount')}{' '}\n                    <Link\n                      to=\"/register\"\n                      style={{\n                        color: theme.palette.primary.main,\n                        textDecoration: 'none',\n                        fontWeight: 600,\n                      }}\n                    >\n                      {t('auth.signUp')}\n                    </Link>\n                  </Typography>\n                </Box>\n              </Stack>\n            </Box>\n          </Paper>\n        </Fade>\n      </Container>\n    </Box>\n  );\n};\n\nexport default Login;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,WAAW,CAAEC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACjE,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,mBAAmB,CAAEC,WAAW,KAAQ,qBAAqB,CACtE,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OACEC,GAAG,CACHC,SAAS,CACTC,KAAK,CACLC,SAAS,CACTC,MAAM,CACNC,UAAU,CACVC,UAAU,CACVC,cAAc,CACdC,QAAQ,CACRC,KAAK,CACLC,OAAO,CACPC,KAAK,CACLC,IAAI,CACJC,KAAK,CACLC,gBAAgB,KACX,eAAe,CACtB,OACEC,UAAU,CACVC,aAAa,CACbC,KAAK,CACLC,IAAI,CACJC,YAAY,GAAI,CAAAC,gBAAgB,CAChCC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,MAAM,GAAI,CAAAC,UAAU,KACf,qBAAqB,CAC5B,OAASC,OAAO,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAAC,KAAK,CAAGA,CAAA,GAAM,KAAAC,eAAA,CAAAC,gBAAA,CAClB,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGtC,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAuC,QAAQ,CAAG1C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA2C,QAAQ,CAAGzC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA0C,KAAK,CAAG7B,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAE8B,KAAK,CAAEC,WAAY,CAAC,CAAGd,OAAO,CAAC,CAAC,CACxC,KAAM,CAAAe,KAAK,CAAGN,IAAI,CAACO,QAAQ,GAAK,IAAI,CAEpC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGrD,QAAQ,CAAC,CACvCsD,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGzD,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC0D,KAAK,CAAEC,QAAQ,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC4D,OAAO,CAAEC,UAAU,CAAC,CAAG7D,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC8D,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CAEpE;AACAE,SAAS,CAAC,IAAM,CACd,GAAI8D,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC1CC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC,CAC/DD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEtB,QAAQ,CAACuB,KAAK,CAAC,CAChD,CAEA;AAEA;AACA,GAAIvB,QAAQ,CAACuB,KAAK,EAAIvB,QAAQ,CAACuB,KAAK,CAACC,OAAO,CAAE,CAC5CH,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAEtB,QAAQ,CAACuB,KAAK,CAACC,OAAO,CAAC,CACvEP,uBAAuB,CAACjB,QAAQ,CAACuB,KAAK,CAACC,OAAO,CAAC,CAC/CX,QAAQ,CAACb,QAAQ,CAACuB,KAAK,CAACC,OAAO,CAAC,CAChC,OACF,CAEA;AACA,KAAM,CAAAC,YAAY,CAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CACjE,GAAIT,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC1CC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEG,YAAY,CAAC,CAC7D,CAEA,GAAIA,YAAY,CAAE,CAChB,GAAI,CACF,KAAM,CAAAG,WAAW,CAAGC,IAAI,CAACC,KAAK,CAACL,YAAY,CAAC,CAE5C;AACA,KAAM,CAAAM,cAAc,CAAGjC,IAAI,CAACO,QAAQ,GAAK,IAAI,CAAGuB,WAAW,CAACJ,OAAO,CAAGI,WAAW,CAACI,UAAU,CAE5Ff,uBAAuB,CAACc,cAAc,CAAC,CACvClB,QAAQ,CAACkB,cAAc,CAAC,CAExB;AACA/B,QAAQ,CAACuB,KAAK,CAAG,CACfC,OAAO,CAAEO,cAAc,CACvBE,aAAa,CAAEL,WAAW,CAACK,aAAa,CACxCC,iBAAiB,CAAEN,WAAW,CAACM,iBACjC,CAAC,CAED;AACAR,YAAY,CAACS,UAAU,CAAC,sBAAsB,CAAC,CAE/C,GAAIjB,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC1CC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAE,CAChDW,aAAa,CAAEL,WAAW,CAACK,aAAa,CACxCT,OAAO,CAAEO,cAAc,CACvB1B,QAAQ,CAAEP,IAAI,CAACO,QACjB,CAAC,CAAC,CACJ,CACF,CAAE,MAAO+B,CAAC,CAAE,CACV,GAAIlB,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC1CC,OAAO,CAACT,KAAK,CAAC,6CAA6C,CAAEwB,CAAC,CAAC,CACjE,CACAV,YAAY,CAACS,UAAU,CAAC,sBAAsB,CAAC,CACjD,CACF,CAAC,IAAM,IAAIjB,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CACjDC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC,CAChD,CACF,CAAC,CAAE,CAACtB,QAAQ,CAACuB,KAAK,CAAEzB,IAAI,CAACO,QAAQ,CAAC,CAAC,CAAE;AAIrC,KAAM,CAAAgC,YAAY,CAAID,CAAC,EAAK,CAC1B7B,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAE,CAAC8B,CAAC,CAACE,MAAM,CAACC,IAAI,EAAGH,CAAC,CAACE,MAAM,CAACE,KAAM,CAAC,CAAC,CAC7D3B,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED,KAAM,CAAA4B,mBAAmB,CAAG,KAAO,CAAAC,kBAAkB,EAAK,CACxD7B,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAA4B,MAAM,CAAG,KAAM,CAAAxC,WAAW,CAACuC,kBAAkB,CAAC,CACpD,GAAI,CAACC,MAAM,CAACC,OAAO,CAAE,CACnB,KAAM,IAAI,CAAAC,KAAK,CAACF,MAAM,CAACnB,OAAO,EAAI3B,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAChE,CACA,GAAI8C,MAAM,CAACC,OAAO,EAAID,MAAM,CAACG,IAAI,CAAE,CACjC,KAAM,CAAEC,IAAK,CAAC,CAAGJ,MAAM,CAACG,IAAI,CAE5B,GAAI,CAACC,IAAI,CAAE,CACT,KAAM,IAAI,CAAAF,KAAK,CAAChD,CAAC,CAAC,kBAAkB,CAAC,CAAC,CACxC,CAEA,OAAQkD,IAAI,EACV,IAAK,OAAO,CACVhD,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,IAAK,kBAAkB,CACrBA,QAAQ,CAAC,oBAAoB,CAAC,CAC9B,MACF,IAAK,aAAa,CAChBA,QAAQ,CAAC,sBAAsB,CAAC,CAChC,MACF,IAAK,SAAS,CACZA,QAAQ,CAAC,oBAAoB,CAAC,CAC9B,MACF,QACEA,QAAQ,CAAC,GAAG,CAAC,CACjB,CACF,CACF,CAAE,MAAOa,KAAK,CAAE,CACdS,OAAO,CAACT,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3CC,QAAQ,CAACD,KAAK,CAACY,OAAO,EAAI3B,CAAC,CAAC,wBAAwB,CAAC,CAAC,CACxD,CAAC,OAAS,CACRkB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAiC,iBAAiB,CAAGA,CAAA,GAAM,CAC9BnC,QAAQ,CAAChB,CAAC,CAAC,wBAAwB,CAAC,CAAC,CACrCkB,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,KAAM,CAAAkC,YAAY,CAAG,KAAO,CAAAb,CAAC,EAAK,CAChCA,CAAC,CAACc,cAAc,CAAC,CAAC,CAClBnC,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,GAAI,CAACT,QAAQ,CAACE,KAAK,EAAI,CAACF,QAAQ,CAACG,QAAQ,CAAE,CACzCI,QAAQ,CAAChB,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACjCkB,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACAF,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF;AACA,KAAM,CAAAsC,cAAc,CAAG,KAAM,CAAAxF,KAAK,CAACyF,IAAI,CAAC,6BAA6B,CAAE,CACrE5C,KAAK,CAAEF,QAAQ,CAACE,KAClB,CAAC,CAAC,CAEF;AACA,GAAI,CAAC2C,cAAc,CAACE,IAAI,CAACT,OAAO,EAAIO,cAAc,CAACE,IAAI,CAACpB,aAAa,CAAE,CACrE,KAAM,CAAAF,cAAc,CAAGjC,IAAI,CAACO,QAAQ,GAAK,IAAI,CACzC8C,cAAc,CAACE,IAAI,CAAC7B,OAAO,CAC3B2B,cAAc,CAACE,IAAI,CAACrB,UAAU,CAElCf,uBAAuB,CAACc,cAAc,CAAC,CACvClB,QAAQ,CAACkB,cAAc,CAAC,CAExB;AACAL,YAAY,CAAC4B,OAAO,CAAC,sBAAsB,CAAEzB,IAAI,CAAC0B,SAAS,CAAC,CAC1D/B,OAAO,CAAE2B,cAAc,CAACE,IAAI,CAAC7B,OAAO,CACpCQ,UAAU,CAAEmB,cAAc,CAACE,IAAI,CAACrB,UAAU,CAC1CC,aAAa,CAAEkB,cAAc,CAACE,IAAI,CAACpB,aAAa,CAChDC,iBAAiB,CAAEiB,cAAc,CAACE,IAAI,CAACnB,iBACzC,CAAC,CAAC,CAAC,CAEHnB,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACA,KAAM,CAAAb,KAAK,CAACI,QAAQ,CAACE,KAAK,CAAEF,QAAQ,CAACG,QAAQ,CAAC,CAC9CV,QAAQ,CAAC,GAAG,CAAC,CACf,CAAE,MAAOa,KAAK,CAAE,KAAA4C,eAAA,CAAAC,gBAAA,CACdpC,OAAO,CAACT,KAAK,CAAC,sBAAsB,CAAE,CACpCA,KAAK,CAAEA,KAAK,CACZ8C,QAAQ,CAAE9C,KAAK,CAAC8C,QAAQ,CACxBL,IAAI,EAAAG,eAAA,CAAE5C,KAAK,CAAC8C,QAAQ,UAAAF,eAAA,iBAAdA,eAAA,CAAgBH,IAAI,CAC1BM,MAAM,EAAAF,gBAAA,CAAE7C,KAAK,CAAC8C,QAAQ,UAAAD,gBAAA,iBAAdA,gBAAA,CAAgBE,MAAM,CAC9BnC,OAAO,CAAEZ,KAAK,CAACY,OACjB,CAAC,CAAC,CAEF;AACA,GAAIZ,KAAK,CAAC8C,QAAQ,EAAI9C,KAAK,CAAC8C,QAAQ,CAACC,MAAM,GAAK,GAAG,CAAE,CACnD;AACA,GAAI,CACF,KAAM,CAAAzD,KAAK,CAACI,QAAQ,CAACE,KAAK,CAAEF,QAAQ,CAACG,QAAQ,CAAC,CAC9CV,QAAQ,CAAC,GAAG,CAAC,CACf,CAAE,MAAO6D,UAAU,CAAE,KAAAC,oBAAA,CACnB,KAAM,CAAAC,SAAS,EAAAD,oBAAA,CAAGD,UAAU,CAACF,QAAQ,UAAAG,oBAAA,iBAAnBA,oBAAA,CAAqBR,IAAI,CAC3C,GAAIS,SAAS,SAATA,SAAS,WAATA,SAAS,CAAEC,oBAAoB,CAAE,CACnChE,QAAQ,CAAC,eAAe,CAAE,CAAEwB,KAAK,CAAE,CAAEf,KAAK,CAAEF,QAAQ,CAACE,KAAM,CAAE,CAAC,CAAC,CACjE,CAAC,IAAM,KAAAwD,qBAAA,CACL,GAAI,CAAAC,YAAY,CAChB,GAAI,CAAAH,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEI,SAAS,IAAK,iBAAiB,CAAE,CAC9CD,YAAY,CAAGpE,CAAC,CAAC,oBAAoB,CAAC,CACxC,CAAC,IAAM,IAAI,CAAAiE,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEI,SAAS,IAAK,gBAAgB,CAAE,CACpDD,YAAY,CAAGpE,CAAC,CAAC,oBAAoB,CAAC,CACxC,CAAC,IAAM,IAAI,CAAAiE,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEI,SAAS,IAAK,iBAAiB,CAAE,CACrDD,YAAY,CAAGpE,CAAC,CAAC,qBAAqB,CAAE,yCAAyC,CAAC,CACpF,CAAC,IAAM,IAAIiE,SAAS,SAATA,SAAS,WAATA,SAAS,CAAEtC,OAAO,CAAE,CAC7ByC,YAAY,CAAGH,SAAS,CAACtC,OAAO,CAClC,CAAC,IAAM,IAAIoC,UAAU,CAACpC,OAAO,CAAE,CAC7ByC,YAAY,CAAGL,UAAU,CAACpC,OAAO,CACnC,CAAC,IAAM,IAAI,EAAAwC,qBAAA,CAAAJ,UAAU,CAACF,QAAQ,UAAAM,qBAAA,iBAAnBA,qBAAA,CAAqBL,MAAM,IAAK,GAAG,CAAE,CAC9CM,YAAY,CAAGpE,CAAC,CAAC,yBAAyB,CAAC,CAC7C,CAAC,IAAM,CACLoE,YAAY,CAAGpE,CAAC,CAAC,kBAAkB,CAAC,CACtC,CACAgB,QAAQ,CAACoD,YAAY,CAAC,CACxB,CACF,CACF,CAAC,IAAM,KAAAE,gBAAA,CACL;AACA,KAAM,CAAAL,SAAS,EAAAK,gBAAA,CAAGvD,KAAK,CAAC8C,QAAQ,UAAAS,gBAAA,iBAAdA,gBAAA,CAAgBd,IAAI,CACtC,GAAIS,SAAS,SAATA,SAAS,WAATA,SAAS,CAAEC,oBAAoB,CAAE,CACnChE,QAAQ,CAAC,eAAe,CAAE,CAAEwB,KAAK,CAAE,CAAEf,KAAK,CAAEF,QAAQ,CAACE,KAAM,CAAE,CAAC,CAAC,CACjE,CAAC,IAAM,KAAA4D,gBAAA,CACL,GAAI,CAAAH,YAAY,CAChB,GAAI,CAAAH,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEI,SAAS,IAAK,iBAAiB,CAAE,CAC9CD,YAAY,CAAGpE,CAAC,CAAC,oBAAoB,CAAC,CACxC,CAAC,IAAM,IAAI,CAAAiE,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEI,SAAS,IAAK,gBAAgB,CAAE,CACpDD,YAAY,CAAGpE,CAAC,CAAC,oBAAoB,CAAC,CACxC,CAAC,IAAM,IAAI,CAAAiE,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEI,SAAS,IAAK,iBAAiB,CAAE,CACrDD,YAAY,CAAGpE,CAAC,CAAC,qBAAqB,CAAE,yCAAyC,CAAC,CACpF,CAAC,IAAM,IAAIiE,SAAS,SAATA,SAAS,WAATA,SAAS,CAAEtC,OAAO,CAAE,CAC7ByC,YAAY,CAAGH,SAAS,CAACtC,OAAO,CAClC,CAAC,IAAM,IAAIZ,KAAK,CAACY,OAAO,CAAE,CACxByC,YAAY,CAAGrD,KAAK,CAACY,OAAO,CAC9B,CAAC,IAAM,IAAI,EAAA4C,gBAAA,CAAAxD,KAAK,CAAC8C,QAAQ,UAAAU,gBAAA,iBAAdA,gBAAA,CAAgBT,MAAM,IAAK,GAAG,CAAE,CACzCM,YAAY,CAAGpE,CAAC,CAAC,yBAAyB,CAAC,CAC7C,CAAC,IAAM,CACLoE,YAAY,CAAGpE,CAAC,CAAC,kBAAkB,CAAC,CACtC,CACAgB,QAAQ,CAACoD,YAAY,CAAC,CACxB,CACF,CACF,CAAC,OAAS,CACRlD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACExB,IAAA,CAAC3B,GAAG,EACFyG,EAAE,CAAE,CACFC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,UAAU,CAAE,8BAA8BpG,KAAK,CAAC4B,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,KAAKvG,KAAK,CAAC,MAAM,CAAE,GAAG,CAAC,GAAG,CAC1GwG,EAAE,CAAE,CAAC,CACLC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,QAAQ,CAClB,WAAW,CAAE,CACXC,OAAO,CAAE,IAAI,CACbF,QAAQ,CAAE,UAAU,CACpBG,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTC,eAAe,CAAE,kHAAkH,CACnIC,cAAc,CAAE,OAAO,CACvBC,kBAAkB,CAAE,QAAQ,CAC5BC,MAAM,CAAE,CAAC,CACX,CAAC,CACD,UAAU,CAAE,CACVR,OAAO,CAAE,IAAI,CACbF,QAAQ,CAAE,UAAU,CACpBG,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTX,UAAU,CAAE,qCAAqCpG,KAAK,CAAC4B,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACc,IAAI,CAAE,GAAG,CAAC,KAAKpH,KAAK,CAAC4B,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GAAG,CACrIY,MAAM,CAAE,CAAC,CACX,CACF,CAAE,CAAAE,QAAA,cAEFnG,IAAA,CAAC1B,SAAS,EAAC8H,QAAQ,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAES,QAAQ,CAAE,UAAU,CAAEU,MAAM,CAAE,CAAE,CAAE,CAAAE,QAAA,cAC/DnG,IAAA,CAACf,IAAI,EAACoH,EAAE,MAACC,OAAO,CAAE,IAAK,CAAAH,QAAA,cACrBnG,IAAA,CAACzB,KAAK,EACJgI,SAAS,CAAE,EAAG,CACdzB,EAAE,CAAE,CACF0B,CAAC,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBC,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,YAAY,CAC5BC,eAAe,CAAE/H,KAAK,CAAC4B,KAAK,CAACyE,OAAO,CAACD,UAAU,CAAC4B,KAAK,CAAE,GAAG,CAAC,CAC3DC,SAAS,CAAE,cAAcjI,KAAK,CAAC4B,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,EAAE,CACjEE,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,QAAQ,CAClBwB,MAAM,CAAE,aAAalI,KAAK,CAAC4B,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACc,IAAI,CAAE,GAAG,CAAC,EAAE,CAC7D,WAAW,CAAE,CACXT,OAAO,CAAE,IAAI,CACbF,QAAQ,CAAE,UAAU,CACpBG,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRqB,MAAM,CAAE,CAAC,CACT/B,UAAU,CAAE,0BAA0BxE,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACc,IAAI,KAAKxF,KAAK,CAACyE,OAAO,CAAC+B,SAAS,CAAChB,IAAI,GACnG,CACF,CAAE,CAAAC,QAAA,cAEFjG,KAAA,CAAC7B,GAAG,EAAC8I,SAAS,CAAC,MAAM,CAACC,QAAQ,CAAE1D,YAAa,CAAAyC,QAAA,eAC3CnG,IAAA,CAACtB,UAAU,EACT2I,OAAO,CAAC,IAAI,CACZC,KAAK,CAAC,QAAQ,CACdC,YAAY,MACZzC,EAAE,CAAE,CACF0C,UAAU,CAAE,GAAG,CACfC,EAAE,CAAE,CAAC,CACLvC,UAAU,CAAE,2BAA2BxE,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACc,IAAI,KAAKxF,KAAK,CAACyE,OAAO,CAAC+B,SAAS,CAAChB,IAAI,GAAG,CACrGwB,oBAAoB,CAAE,MAAM,CAC5BC,mBAAmB,CAAE,aAAa,CAClCC,UAAU,CAAE,2BAA2B,CACvCrC,QAAQ,CAAE,UAAU,CACpB,UAAU,CAAE,CACVE,OAAO,CAAE,IAAI,CACbF,QAAQ,CAAE,UAAU,CACpBM,MAAM,CAAE,CAAC,CAAC,CACVF,IAAI,CAAE,KAAK,CACXkC,SAAS,CAAE,kBAAkB,CAC7BC,KAAK,CAAE,EAAE,CACTb,MAAM,CAAE,CAAC,CACT/B,UAAU,CAAE,0BAA0BxE,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACc,IAAI,KAAKxF,KAAK,CAACyE,OAAO,CAAC+B,SAAS,CAAChB,IAAI,GACnG,CACF,CAAE,CAAAC,QAAA,CAED7F,CAAC,CAAC,kBAAkB,CAAC,CACZ,CAAC,CAGZmB,oBAAoB,eACnBvB,KAAA,CAAChB,KAAK,EACJ6I,QAAQ,CAAE,EAAA3H,eAAA,CAAAK,QAAQ,CAACuB,KAAK,UAAA5B,eAAA,iBAAdA,eAAA,CAAgBsC,aAAa,IAAK,SAAS,CAAG,OAAO,CAAG,SAAU,CAC5EoC,EAAE,CAAE,CAAE2C,EAAE,CAAE,CAAC,CAAEd,YAAY,CAAE,CAAE,CAAE,CAAAR,QAAA,EAE9B1E,oBAAoB,CACpB,EAAApB,gBAAA,CAAAI,QAAQ,CAACuB,KAAK,UAAA3B,gBAAA,iBAAdA,gBAAA,CAAgBqC,aAAa,IAAK,kBAAkB,eACnD1C,IAAA,CAACtB,UAAU,EAAC2I,OAAO,CAAC,OAAO,CAACvC,EAAE,CAAE,CAAEkD,EAAE,CAAE,CAAE,CAAE,CAAA7B,QAAA,CACvC5F,IAAI,CAACO,QAAQ,GAAK,IAAI,CACnB,6DAA6D,CAC7D,gEAAgE,CAE1D,CACb,EACI,CACR,CAGAO,KAAK,EAAI,CAACI,oBAAoB,eAC7BzB,IAAA,CAACd,KAAK,EAAC6I,QAAQ,CAAC,OAAO,CAACjD,EAAE,CAAE,CAAE2C,EAAE,CAAE,CAAC,CAAEd,YAAY,CAAE,CAAE,CAAE,CAAAR,QAAA,CACpD9E,KAAK,CACD,CACR,cAIDnB,KAAA,CAAClB,KAAK,EAACiJ,OAAO,CAAE,CAAE,CAAA9B,QAAA,eAChBnG,IAAA,CAACxB,SAAS,EACR0J,SAAS,MACTlF,IAAI,CAAC,OAAO,CACZmF,KAAK,CAAE7H,CAAC,CAAC,cAAc,CAAE,CACzB2C,KAAK,CAAElC,QAAQ,CAACE,KAAM,CACtBmH,QAAQ,CAAEtF,YAAa,CACvBzB,KAAK,CAAEgH,OAAO,CAAChH,KAAK,CAAE,CACtBiH,UAAU,CAAE,CACVC,cAAc,cACZvI,IAAA,CAACpB,cAAc,EAAC2G,QAAQ,CAAC,OAAO,CAAAY,QAAA,cAC9BnG,IAAA,CAACV,KAAK,GAAE,CAAC,CACK,CAEpB,CAAE,CACH,CAAC,cACFU,IAAA,CAACxB,SAAS,EACR0J,SAAS,MACTlF,IAAI,CAAC,UAAU,CACfmF,KAAK,CAAE7H,CAAC,CAAC,iBAAiB,CAAE,CAC5BkI,IAAI,CAAErH,YAAY,CAAG,MAAM,CAAG,UAAW,CACzC8B,KAAK,CAAElC,QAAQ,CAACG,QAAS,CACzBkH,QAAQ,CAAEtF,YAAa,CACvBzB,KAAK,CAAEgH,OAAO,CAAChH,KAAK,CAAE,CACtBiH,UAAU,CAAE,CACVC,cAAc,cACZvI,IAAA,CAACpB,cAAc,EAAC2G,QAAQ,CAAC,OAAO,CAAAY,QAAA,cAC9BnG,IAAA,CAACT,IAAI,GAAE,CAAC,CACM,CACjB,CACDkJ,YAAY,cACVzI,IAAA,CAACpB,cAAc,EAAC2G,QAAQ,CAAC,KAAK,CAAAY,QAAA,cAC5BnG,IAAA,CAACrB,UAAU,EACT+J,OAAO,CAAEA,CAAA,GAAMtH,eAAe,CAAC,CAACD,YAAY,CAAE,CAC9CwH,IAAI,CAAC,KAAK,CAAAxC,QAAA,CAEThF,YAAY,cAAGnB,IAAA,CAACX,aAAa,GAAE,CAAC,cAAGW,IAAA,CAACZ,UAAU,GAAE,CAAC,CACxC,CAAC,CACC,CAEpB,CAAE,CACH,CAAC,cAEFY,IAAA,CAAC3B,GAAG,EAACyG,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAE4D,cAAc,CAAE,UAAU,CAAEnB,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,cAC9DnG,IAAA,CAACjC,IAAI,EACH8K,EAAE,CAAC,kBAAkB,CACrBC,KAAK,CAAE,CACLC,KAAK,CAAErI,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACc,IAAI,CACjC8C,cAAc,CAAE,MAAM,CACtBC,QAAQ,CAAE,UACZ,CAAE,CAAA9C,QAAA,CAED7F,CAAC,CAAC,qBAAqB,CAAC,CACrB,CAAC,CACJ,CAAC,cAENN,IAAA,CAACvB,MAAM,EACL+J,IAAI,CAAC,QAAQ,CACbN,SAAS,MACTb,OAAO,CAAC,WAAW,CACnB6B,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAE5H,OAAQ,CAClB6H,OAAO,CAAE,CAACvI,KAAK,cAAGb,IAAA,CAACP,gBAAgB,GAAE,CAAC,cAAGO,IAAA,CAACL,aAAa,GAAE,CAAE,CAC3DmF,EAAE,CAAE,CACFkD,EAAE,CAAE,CAAC,CACLf,MAAM,CAAE,EAAE,CACV/B,UAAU,CAAE,0BAA0BxE,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACc,IAAI,KAAKxF,KAAK,CAACyE,OAAO,CAAC+B,SAAS,CAAChB,IAAI,GAAG,CACpG,SAAS,CAAE,CACThB,UAAU,CAAE,0BAA0BxE,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACC,IAAI,KAAK3E,KAAK,CAACyE,OAAO,CAAC+B,SAAS,CAAC7B,IAAI,GACnG,CACF,CAAE,CAAAc,QAAA,CAED5E,OAAO,cACNvB,IAAA,CAACb,gBAAgB,EAAC+J,IAAI,CAAE,EAAG,CAACH,KAAK,CAAC,SAAS,CAAE,CAAC,CAE9CzI,CAAC,CAAC,aAAa,CAChB,CACK,CAAC,cAETN,IAAA,CAAC3B,GAAG,EAACyG,EAAE,CAAE,CAAES,QAAQ,CAAE,UAAU,CAAE8D,EAAE,CAAE,CAAE,CAAE,CAAAlD,QAAA,cACvCnG,IAAA,CAACjB,OAAO,EAAAoH,QAAA,cACNnG,IAAA,CAACtB,UAAU,EAAC2I,OAAO,CAAC,OAAO,CAAC0B,KAAK,CAAC,gBAAgB,CAAA5C,QAAA,CAC/C7F,CAAC,CAAC,SAAS,CAAC,CACH,CAAC,CACN,CAAC,CACP,CAAC,cAENN,IAAA,CAAC9B,mBAAmB,EAACoL,QAAQ,CAAC,yEAAyE,CAAAnD,QAAA,cACrGnG,IAAA,CAAC3B,GAAG,EAACyG,EAAE,CAAE,CAAEgD,KAAK,CAAE,MAAM,CAAE9C,OAAO,CAAE,MAAM,CAAE4D,cAAc,CAAE,QAAS,CAAE,CAAAzC,QAAA,cACpEnG,IAAA,CAAC7B,WAAW,EACVoL,SAAS,CAAErG,mBAAoB,CAC/BsG,OAAO,CAAE/F,iBAAkB,CAC3B+E,IAAI,CAAC,UAAU,CACf9H,KAAK,CAAC,SAAS,CACfwI,IAAI,CAAC,OAAO,CACZpB,KAAK,CAAC,KAAK,CACX2B,IAAI,CAAC,eAAe,CACpBC,KAAK,CAAC,aAAa,CACnBC,cAAc,CAAC,MAAM,CACrBC,SAAS,CAAE,KAAM,CACjBC,WAAW,CAAE,KAAM,CACpB,CAAC,CACC,CAAC,CACa,CAAC,cAEtB7J,IAAA,CAAC3B,GAAG,EAACyG,EAAE,CAAE,CAAEgF,SAAS,CAAE,QAAQ,CAAE9B,EAAE,CAAE,CAAE,CAAE,CAAA7B,QAAA,cACtCjG,KAAA,CAACxB,UAAU,EAAC2I,OAAO,CAAC,OAAO,CAAC0B,KAAK,CAAC,gBAAgB,CAAA5C,QAAA,EAC/C7F,CAAC,CAAC,gBAAgB,CAAC,CAAE,GAAG,cACzBN,IAAA,CAACjC,IAAI,EACH8K,EAAE,CAAC,WAAW,CACdC,KAAK,CAAE,CACLC,KAAK,CAAErI,KAAK,CAACyE,OAAO,CAACC,OAAO,CAACc,IAAI,CACjC8C,cAAc,CAAE,MAAM,CACtBxB,UAAU,CAAE,GACd,CAAE,CAAArB,QAAA,CAED7F,CAAC,CAAC,aAAa,CAAC,CACb,CAAC,EACG,CAAC,CACV,CAAC,EACD,CAAC,EACL,CAAC,CACD,CAAC,CACJ,CAAC,CACE,CAAC,CACT,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}