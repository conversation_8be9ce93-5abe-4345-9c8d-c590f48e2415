const express = require('express');
const router = express.Router();
const db = require('../../config/db');
const { deleteUserCompletely } = require('../../scripts/processScheduledDeletions');
const socket = require('../../socket');

// Get all teachers with pagination and filters
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';

    let query = `
      SELECT
        u.id,
        u.full_name,
        u.email,
        u.gender,
        u.profile_picture_url,
        u.role,
        u.created_at
      FROM users u
      WHERE u.role IN ('platform_teacher', 'new_teacher')
    `;

    let countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      WHERE u.role IN ('platform_teacher', 'new_teacher')
    `;

    const queryParams = [];
    if (search) {
      query += ` AND (u.full_name LIKE ? OR u.email LIKE ?)`;
      countQuery += ` AND (u.full_name LIKE ? OR u.email LIKE ?)`;
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    query += ` ORDER BY u.created_at DESC LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    const [teachers] = await db.pool.query(query, queryParams);
    const [totalCount] = await db.pool.query(countQuery, queryParams.slice(0, -2));

    res.json({
      teachers,
      total: totalCount[0].total
    });
  } catch (error) {
    console.error('Error fetching teachers:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Update teacher status
router.put('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // Start transaction
    const connection = await db.pool.getConnection();
    await connection.beginTransaction();

    try {
      // Update teacher profile status
      const [updateResult] = await connection.query(
        'UPDATE teacher_profiles SET status = ? WHERE user_id = ?',
        [status, id]
      );

      // Update user role based on status
      const newRole = status === 'approved' ? 'platform_teacher' : 'new_teacher';
      const [roleResult] = await connection.query(
        'UPDATE users SET role = ? WHERE id = ?',
        [newRole, id]
      );

      // Get updated teacher data
      const [updatedTeacher] = await connection.query(
        `SELECT
          u.id,
          u.full_name,
          u.email,
          u.role,
          tp.*
        FROM users u
        INNER JOIN teacher_profiles tp ON u.id = tp.user_id
        WHERE u.id = ?`,
        [id]
      );

      await connection.commit();

      // Emit socket event with updated data
      const io = socket.getIO();
      console.log('Emitting application_status_changed:', {
        applicationId: id,
        status,
        newRole,
        teacher: updatedTeacher[0]
      });
      io.emit('application_status_changed', {
        applicationId: id,
        status,
        newRole,
        teacher: updatedTeacher[0]
      });

      res.json({
        message: 'Teacher status updated successfully',
        status,
        newRole,
        teacher: updatedTeacher[0]
      });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error updating teacher status:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Delete teacher
router.delete('/:id', async (req, res) => {
  try {
    const teacherId = req.params.id;
    console.log(`Attempting to delete teacher with ID: ${teacherId}`);

    // Check if teacher exists before attempting to delete
    const [teacherExists] = await db.pool.query(
      'SELECT id, email, full_name, role FROM users WHERE id = ? AND role IN ("platform_teacher", "new_teacher")',
      [teacherId]
    );

    if (teacherExists.length === 0) {
      console.log(`Teacher with ID ${teacherId} not found`);
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const teacher = teacherExists[0];

    // Start transaction
    const connection = await db.pool.getConnection();
    await connection.beginTransaction();

    try {
      console.log(`Admin deleting teacher: ${teacher.email} (ID: ${teacher.id})`);

      // استخدام الدالة الشاملة للحذف
      await deleteUserCompletely(connection, teacher.id, teacher.email);

      await connection.commit();

      console.log(`Successfully deleted teacher: ${teacher.email}`);
      res.json({
        success: true,
        message: 'Teacher deleted successfully'

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error deleting teacher:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
