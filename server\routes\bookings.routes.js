const express = require('express');
const axios = require('axios');
const jwt = require('jsonwebtoken');
const router = express.Router();
const { verifyToken, isStudent } = require('../middleware/auth.middleware');
const { checkUserStatus } = require('../middleware/userStatus.middleware');
const db = require('../config/db');
const bookingCancellationEmailService = require('../services/bookingCancellationEmailService');
const bookingRescheduleEmailService = require('../services/bookingRescheduleEmailService');

// Helper to obtain or generate a VideoSDK auth token
async function getVideoSdkToken() {
  if (process.env.VIDEOSDK_TOKEN) {
    return process.env.VIDEOSDK_TOKEN;
  }

  const { VIDEOSDK_API_KEY, VIDEOSDK_SECRET_KEY } = process.env;
  if (!VIDEOSDK_API_KEY || !VIDEOSDK_SECRET_KEY) {
    throw new Error('VideoSDK credentials are missing');
  }
  return jwt.sign(
    {
      apikey: VIDEOSDK_API_KEY,
      permissions: ["allow_join", "allow_mod"],
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60
    },
    VIDEOSDK_SECRET_KEY,
    { algorithm: 'HS256' }
  );
}

// Helper to create a VideoSDK room and return its ID
async function createVideoSdkRoom() {
  const token = await getVideoSdkToken();
  const res = await axios.post('https://api.videosdk.live/v2/rooms', {}, {
    headers: {
      Authorization: token,
      'Content-Type': 'application/json'
    }
  });
  if (res.data && (res.data.roomId || res.data.meetingId)) {
    return res.data.roomId || res.data.meetingId;
  }
  throw new Error('Unexpected response from VideoSDK: ' + JSON.stringify(res.data));
}

// Create a new booking - check user status
router.post('/', verifyToken, isStudent, checkUserStatus, async (req, res) => {
  const { teacher_id, datetime, booking_type = 'regular' } = req.body;
  let { duration = '50' } = req.body;
  const student_id = req.user.id;

  // Convert duration to string if it's a number
  duration = String(duration);

  // Ensure duration is trimmed and has no extra spaces
  duration = duration.trim();

  console.log('🔍 Received booking request:', {
    teacher_id,
    datetime, // This datetime has student's timezone offset removed for base time storage
    duration,
    booking_type,
    durationType: typeof duration,
    student_id,
    slotsToCheck: [] // Will be populated later
  });

  // Validate required fields
  if (!teacher_id || !datetime) {
    return res.status(400).json({
      success: false,
      message: 'Teacher ID and datetime are required'
    });
  }

  // Validate duration (should be either 25 or 50)
  if (duration !== '25' && duration !== '50') {
    console.log('Invalid duration:', {
      duration,
      type: typeof duration,
      length: duration.length,
      equals25: duration === '25',
      equals50: duration === '50',
      charCodes: Array.from(duration).map(c => c.charCodeAt(0))
    });

    // Try to normalize the duration
    if (duration === '25' || duration === 25 || duration === '25.0' || duration === '25.00') {
      duration = '25';
    } else if (duration === '50' || duration === 50 || duration === '50.0' || duration === '50.00') {
      duration = '50';
    } else {
      return res.status(400).json({
        success: false,
        message: 'Duration must be either 25 or 50 minutes'
      });
    }

    console.log('Normalized duration to:', duration);
  }

  // Validate booking type
  const validBookingTypes = ['regular', 'second_half_only', 'cross_hour'];
  if (!validBookingTypes.includes(booking_type)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid booking type'
    });
  }

  // Start a transaction to ensure all database operations succeed or fail together
  const connection = await db.pool.getConnection();
  await connection.beginTransaction();

  try {
    // Get teacher profile ID from teacher user ID
    const [teacherProfiles] = await connection.execute(
      'SELECT id FROM teacher_profiles WHERE user_id = ?',
      [teacher_id]
    );

    if (teacherProfiles.length === 0) {
      await connection.rollback();
      connection.release();
      return res.status(404).json({
        success: false,
        message: 'Teacher profile not found'
      });
    }

    const teacher_profile_id = teacherProfiles[0].id;

    // Enhanced availability checking based on booking type
    const bookingDateTime = new Date(datetime);
    const slotsToCheck = [];

    if (booking_type === 'regular') {
      // Regular booking - check the requested slot
      slotsToCheck.push(datetime);

      // If it's a full lesson (50 min), also check the next 30-minute slot
      if (duration === '50') {
        const nextSlotTime = new Date(bookingDateTime.getTime() + 30 * 60 * 1000);
        slotsToCheck.push(nextSlotTime.toISOString().replace('T', ' ').substring(0, 19));
      }
    } else if (booking_type === 'second_half_only') {
      // Booking only the second half of an hour - check if it's available
      const minutes = bookingDateTime.getMinutes();
      if (minutes !== 0) {
        await connection.rollback();
        connection.release();
        return res.status(400).json({
          success: false,
          message: 'Second half booking must start at the beginning of an hour (XX:00)'
        });
      }

      // Check the second half slot (30 minutes later)
      const secondHalfTime = new Date(bookingDateTime.getTime() + 30 * 60 * 1000);
      slotsToCheck.push(secondHalfTime.toISOString().replace('T', ' ').substring(0, 19));

      // Force duration to 25 minutes for second half only
      duration = '25';
    } else if (booking_type === 'second_half_full') {
      // Full lesson starting from second half - check current slot (second half) and next hour's first half
      const minutes = bookingDateTime.getMinutes();
      if (minutes !== 30) {
        await connection.rollback();
        connection.release();
        return res.status(400).json({
          success: false,
          message: 'Second half full booking must start at XX:30'
        });
      }

      // Check current slot (second half)
      slotsToCheck.push(datetime);

      // Check next hour's first half
      const nextHourTime = new Date(bookingDateTime.getTime() + 30 * 60 * 1000);
      slotsToCheck.push(nextHourTime.toISOString().replace('T', ' ').substring(0, 19));

      // Force duration to 50 minutes for second half full
      duration = '50';
    } else if (booking_type === 'cross_hour') {
      // Cross-hour booking - check current slot (second half) and next hour's first half
      const minutes = bookingDateTime.getMinutes();
      if (minutes !== 30) {
        await connection.rollback();
        connection.release();
        return res.status(400).json({
          success: false,
          message: 'Cross-hour booking must start at XX:30'
        });
      }

      // Check current slot (second half)
      slotsToCheck.push(datetime);

      // Check next hour's first half
      const nextHourTime = new Date(bookingDateTime.getTime() + 30 * 60 * 1000);
      slotsToCheck.push(nextHourTime.toISOString().replace('T', ' ').substring(0, 19));

      // Force duration to 50 minutes for cross-hour
      duration = '50';
    }

    console.log('🔍 Slots to check for availability:', {
      slotsToCheck,
      booking_type,
      duration,
      originalDatetime: datetime
    });

    // Check if any of the required slots are already booked
    for (const slotTime of slotsToCheck) {
      const [existingBookings] = await connection.execute(
        'SELECT * FROM bookings WHERE teacher_profile_id = ? AND datetime = ? AND status != "cancelled"',
        [teacher_profile_id, slotTime]
      );

      console.log('🔍 Checking slot availability:', {
        slotTime,
        teacher_profile_id,
        existingBookings: existingBookings.map(b => ({
          id: b.id,
          datetime: b.datetime,
          status: b.status,
          student_id: b.student_id
        }))
      });

      if (existingBookings.length > 0) {
        console.log('❌ Slot already booked:', {
          slotTime,
          existingBookings: existingBookings.map(b => ({
            id: b.id,
            status: b.status,
            student_id: b.student_id
          }))
        });
        await connection.rollback();
        connection.release();
        return res.status(400).json({
          success: false,
          message: 'One or more required time slots are already booked'
        });
      }
    }

    // Check if any of the required slots have teacher breaks
    for (const slotTime of slotsToCheck) {
      const [teacherBreaks] = await connection.execute(
        'SELECT datetime FROM teacher_weekly_breaks WHERE teacher_profile_id = ? AND datetime = ?',
        [teacher_profile_id, slotTime]
      );

      if (teacherBreaks.length > 0) {
        await connection.rollback();
        connection.release();
        return res.status(400).json({
          success: false,
          message: 'This time slot is not available - teacher is on break'
        });
      }
    }

    // Get the teacher's price per lesson
    const [teacherPriceResult] = await connection.execute(
      'SELECT price_per_lesson, trial_lesson_price FROM teacher_profiles WHERE id = ?',
      [teacher_profile_id]
    );

    const price_per_lesson = teacherPriceResult[0].price_per_lesson;
    const trial_lesson_price = teacherPriceResult[0].trial_lesson_price;

    // Determine if this is the student's first booking with this teacher
    const [prevCntRows] = await connection.execute(
      'SELECT COUNT(*) AS cnt FROM bookings WHERE student_id = ? AND teacher_profile_id = ? AND status != "cancelled"',
      [student_id, teacher_profile_id]
    );
    const isFirstBooking = prevCntRows[0].cnt === 0;

    // Calculate the actual price based on duration and trial eligibility
    const durationNum = parseInt(duration, 10);
    let actualPrice;
    if (durationNum === 50) {
      actualPrice = price_per_lesson;
    } else {
      // 25-minute lesson
      if (isFirstBooking && trial_lesson_price != null) {
        actualPrice = trial_lesson_price;
      } else {
        actualPrice = price_per_lesson / 2;
      }
    }

    console.log('Price calculation:', {
      duration,
      durationNum,
      price_per_lesson,
      actualPrice,
      isFullLesson: durationNum === 50
    });

    // Check if student has enough balance
    const [studentBalance] = await connection.execute(
      'SELECT balance FROM users WHERE id = ?',
      [student_id]
    );

    console.log('Booking check:', {
      studentId: student_id,
      balance: studentBalance[0].balance,
      price: actualPrice,
      duration: duration,
      fullPrice: price_per_lesson
    });

    console.log('Checking student balance:', {
      studentBalance: studentBalance[0].balance,
      requiredAmount: actualPrice,
      hasEnoughBalance: studentBalance[0].balance >= actualPrice
    });

    if (parseFloat(studentBalance[0].balance) < actualPrice) {
      await connection.rollback();
      connection.release();
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance. Please add funds to your wallet.'
      });
    }

    // Deduct the amount from student's balance
    await connection.execute(
      'UPDATE users SET balance = balance - ? WHERE id = ?',
      [actualPrice, student_id]
    );

    // Create the booking(s) based on booking type
    const durationInt = parseInt(duration, 10);
    const bookingIds = [];

    console.log('Inserting booking with duration:', {
      originalDuration: duration,
      parsedDuration: durationInt,
      bookingType: booking_type,
      isValidNumber: !isNaN(durationInt)
    });

    if (booking_type === 'regular') {
      // Regular booking - single entry
      const [result] = await connection.execute(
        'INSERT INTO bookings (teacher_profile_id, student_id, datetime, status, duration) VALUES (?, ?, ?, "scheduled", ?)',
        [teacher_profile_id, student_id, datetime, durationInt]
      );
      bookingIds.push(result.insertId);

    } else if (booking_type === 'second_half_only') {
      // Book only the second half of the hour
      const secondHalfTime = new Date(bookingDateTime.getTime() + 30 * 60 * 1000);
      const secondHalfDatetime = secondHalfTime.toISOString().replace('T', ' ').substring(0, 19);

      const [result] = await connection.execute(
        'INSERT INTO bookings (teacher_profile_id, student_id, datetime, status, duration) VALUES (?, ?, ?, "scheduled", ?)',
        [teacher_profile_id, student_id, secondHalfDatetime, 25]
      );
      bookingIds.push(result.insertId);

    } else if (booking_type === 'second_half_full') {
      // Full lesson starting from second half - create two linked bookings
      // First booking: current slot (second half of current hour)
      const [result1] = await connection.execute(
        'INSERT INTO bookings (teacher_profile_id, student_id, datetime, status, duration) VALUES (?, ?, ?, "scheduled", ?)',
        [teacher_profile_id, student_id, datetime, 25]
      );
      bookingIds.push(result1.insertId);

      // Second booking: next hour's first half
      const nextHourTime = new Date(bookingDateTime.getTime() + 30 * 60 * 1000);
      const nextHourDatetime = nextHourTime.toISOString().replace('T', ' ').substring(0, 19);

      const [result2] = await connection.execute(
        'INSERT INTO bookings (teacher_profile_id, student_id, datetime, status, duration) VALUES (?, ?, ?, "scheduled", ?)',
        [teacher_profile_id, student_id, nextHourDatetime, 25]
      );
      bookingIds.push(result2.insertId);
    } else if (booking_type === 'cross_hour') {
      // Cross-hour booking - create two linked bookings
      // First booking: current slot (second half of current hour)
      const [result1] = await connection.execute(
        'INSERT INTO bookings (teacher_profile_id, student_id, datetime, status, duration) VALUES (?, ?, ?, "scheduled", ?)',
        [teacher_profile_id, student_id, datetime, 25]
      );
      bookingIds.push(result1.insertId);

      // Second booking: next hour's first half
      const nextHourTime = new Date(bookingDateTime.getTime() + 30 * 60 * 1000);
      const nextHourDatetime = nextHourTime.toISOString().replace('T', ' ').substring(0, 19);

      const [result2] = await connection.execute(
        'INSERT INTO bookings (teacher_profile_id, student_id, datetime, status, duration) VALUES (?, ?, ?, "scheduled", ?)',
        [teacher_profile_id, student_id, nextHourDatetime, 25]
      );
      bookingIds.push(result2.insertId);
    }

    const primaryBookingId = bookingIds[0];

    // Create a payment record with completed status
    await connection.execute(
      'INSERT INTO payments (teacher_profile_id, student_id, amount, status) VALUES (?, ?, ?, "completed")',
      [teacher_profile_id, student_id, actualPrice]
    );

    // Get student and teacher information for the meeting
    const [studentInfo] = await connection.execute(
      'SELECT full_name, email FROM users WHERE id = ?',
      [student_id]
    );

    const [teacherInfo] = await connection.execute(
      'SELECT u.id, u.full_name, u.email FROM users u JOIN teacher_profiles tp ON u.id = tp.user_id WHERE tp.id = ?',
      [teacher_profile_id]
    );

    if (studentInfo.length === 0 || teacherInfo.length === 0) {
      await connection.rollback();
      connection.release();
      return res.status(404).json({
        success: false,
        message: 'Student or teacher information not found'
      });
    }

    // Generate a unique room name for the meeting
    const { v4: uuidv4 } = require('uuid');
    const roomName = uuidv4();

    // Format the meeting name
    const meetingDate = new Date(datetime);
    const formattedDate = meetingDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
    const formattedTime = meetingDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
    const meetingName = `${studentInfo[0].full_name} & ${teacherInfo[0].full_name} - ${formattedDate} ${formattedTime}`;

    // Obtain VideoSDK meeting ID
let videosdkMeetingId;
try {
  videosdkMeetingId = await createVideoSdkRoom();
  console.log('Generated VideoSDK meeting ID:', videosdkMeetingId);
} catch (err) {
  await connection.rollback();
  connection.release();
  console.error('Failed to create VideoSDK meeting:', err);
  return res.status(500).json({ success: false, message: 'Failed to create VideoSDK meeting' });
}

// Create the meeting(s) based on booking type
    const meetingIds = [];

    if (booking_type === 'regular' || booking_type === 'second_half_only') {
      // Single meeting for regular or second-half-only bookings
      const meetingId = uuidv4();
      const meetingDatetime = booking_type === 'second_half_only'
        ? new Date(bookingDateTime.getTime() + 30 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19)
        : datetime;

      console.log('Creating meeting:', {
        meetingId,
        teacherId: teacherInfo[0].id,
        studentId: student_id,
        meetingName,
        roomName,
        meetingDatetime,
        duration,
        actualPrice,
        bookingType: booking_type
      });

      try {
        const durationInt = parseInt(duration, 10);

        await connection.execute(
          `INSERT INTO meetings
            (id, teacher_id, student_id, meeting_name, room_name, videosdk_meeting_id, meeting_date, duration, amount, status)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'scheduled')`,
          [meetingId, teacherInfo[0].id, student_id, meetingName, roomName, videosdkMeetingId, meetingDatetime, durationInt, actualPrice]
        );
        meetingIds.push(meetingId);
        console.log('Meeting created successfully with ID:', meetingId);
      } catch (meetingError) {
        console.error('Error creating meeting:', meetingError);
        throw meetingError;
      }

    } else if (booking_type === 'second_half_full') {
      // Single meeting for second-half-full booking (spans two slots but is one continuous meeting)
      const meetingId = uuidv4();

      console.log('Creating second-half-full meeting:', {
        meetingId,
        teacherId: teacherInfo[0].id,
        studentId: student_id,
        meetingName,
        roomName,
        datetime,
        duration: 50, // Second-half-full is always 50 minutes
        actualPrice,
        bookingType: booking_type
      });

      try {
        await connection.execute(
          `INSERT INTO meetings
            (id, teacher_id, student_id, meeting_name, room_name, videosdk_meeting_id, meeting_date, duration, amount, status)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'scheduled')`,
          [meetingId, teacherInfo[0].id, student_id, meetingName, roomName, videosdkMeetingId, datetime, 50, actualPrice]
        );
        meetingIds.push(meetingId);
        console.log('Second-half-full meeting created successfully with ID:', meetingId);
      } catch (meetingError) {
        console.error('Error creating second-half-full meeting:', meetingError);
        throw meetingError;
      }
    } else if (booking_type === 'cross_hour') {
      // Single meeting for cross-hour booking (spans two slots but is one continuous meeting)
      const meetingId = uuidv4();

      console.log('Creating cross-hour meeting:', {
        meetingId,
        teacherId: teacherInfo[0].id,
        studentId: student_id,
        meetingName,
        roomName,
        datetime,
        duration: 50, // Cross-hour is always 50 minutes
        actualPrice,
        bookingType: booking_type
      });

      try {
        await connection.execute(
          `INSERT INTO meetings
            (id, teacher_id, student_id, meeting_name, room_name, videosdk_meeting_id, meeting_date, duration, amount, status)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'scheduled')`,
          [meetingId, teacherInfo[0].id, student_id, meetingName, roomName, videosdkMeetingId, datetime, 50, actualPrice]
        );
        meetingIds.push(meetingId);
        console.log('Cross-hour meeting created successfully with ID:', meetingId);
      } catch (meetingError) {
        console.error('Error creating cross-hour meeting:', meetingError);
        throw meetingError;
      }
    }

    // Get the created booking
    const [bookings] = await connection.execute(
      'SELECT * FROM bookings WHERE id = ?',
      [primaryBookingId]
    );

    // Commit the transaction
    await connection.commit();
    connection.release();

    res.status(201).json({
      success: true,
      message: 'Booking and meeting created successfully',
      data: {
        booking: bookings[0],
        bookingIds: bookingIds,
        meetingIds: meetingIds,
        bookingType: booking_type,
        teacher: teacherInfo[0],
        student: studentInfo[0]
      }
    });
  } catch (error) {
    // Rollback the transaction in case of error
    await connection.rollback();
    connection.release();
    console.error('Error creating booking:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating booking'
    });
  }
});

// Get all bookings for a student - check user status
router.get('/student', verifyToken, isStudent, checkUserStatus, async (req, res) => {
  try {
    const meetingStatusService = require('../services/meetingStatusService');

    const [bookings] = await db.pool.execute(
      `SELECT b.*,
              CASE
                WHEN u.deleted_at IS NOT NULL THEN CONCAT(u.full_name, ' (محذوف)')
                ELSE u.full_name
              END as teacher_name,
              CASE
                WHEN u.deleted_at IS NOT NULL THEN '<EMAIL>'
                ELSE u.email
              END as teacher_email,
              CASE
                WHEN u.deleted_at IS NOT NULL THEN NULL
                ELSE COALESCE(tp.profile_picture_url, u.profile_picture_url)
              END as teacher_picture,
              u.deleted_at as teacher_deleted_at,
              tp.price_per_lesson,
              m.amount as price_paid,
              m.room_name,
               m.videosdk_meeting_id,
              m.id as meeting_id
       FROM bookings b
       JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
       JOIN users u ON tp.user_id = u.id
       LEFT JOIN meetings m ON m.teacher_id = u.id AND m.student_id = b.student_id AND m.meeting_date = b.datetime
       WHERE b.student_id = ?
       ORDER BY b.datetime DESC`,
      [req.user.id]
    );

    // Update booking statuses based on time and issues
    const updatedBookings = await Promise.all(bookings.map(async (booking) => {
      try {
        const calculatedStatus = await meetingStatusService.MeetingStatusService.determineBookingStatus(booking);

        console.log('🔍 Booking status check:', {
          bookingId: booking.id,
          currentStatus: booking.status,
          calculatedStatus: calculatedStatus,
          willUpdate: calculatedStatus !== booking.status
        });

        // Update database if status has changed
        if (calculatedStatus !== booking.status) {
          console.log('⚠️ Updating booking status:', {
            bookingId: booking.id,
            from: booking.status,
            to: calculatedStatus
          });

          await db.pool.execute(
            'UPDATE bookings SET status = ? WHERE id = ?',
            [calculatedStatus, booking.id]
          );
          return { ...booking, status: calculatedStatus };
        }

        return booking;
      } catch (error) {
        console.error(`Error updating booking ${booking.id} status:`, error);
        return booking;
      }
    }));

    res.json({
      success: true,
      data: updatedBookings
    });
  } catch (error) {
    console.error('Error fetching bookings:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching bookings'
    });
  }
});

// Get all bookings for a teacher - check user status
router.get('/teacher', verifyToken, checkUserStatus, async (req, res) => {
  try {
    // Check if user is a teacher and get teacher profile with timezone
    const [teacherCheck] = await db.pool.execute(
      'SELECT id, timezone FROM teacher_profiles WHERE user_id = ?',
      [req.user.id]
    );

    if (teacherCheck.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Teacher profile not found.'
      });
    }

    const teacher_profile_id = teacherCheck[0].id;
    const teacher_timezone = teacherCheck[0].timezone;
    // const meetingStatusService = require('../services/meetingStatusService');

    const [bookings] = await db.pool.execute(
      `SELECT b.*,
              CASE
                WHEN u.deleted_at IS NOT NULL THEN CONCAT(u.full_name, ' (محذوف)')
                ELSE u.full_name
              END as student_name,
              CASE
                WHEN u.deleted_at IS NOT NULL THEN '<EMAIL>'
                ELSE u.email
              END as student_email,
              CASE
                WHEN u.deleted_at IS NOT NULL THEN NULL
                ELSE u.profile_picture_url
              END as student_picture,
              u.deleted_at as student_deleted_at,
              tp.price_per_lesson,
              m.amount as price_paid,
              tp.timezone as teacher_timezone,
              m.room_name,
               m.videosdk_meeting_id,
              m.id as meeting_id
       FROM bookings b
       JOIN users u ON b.student_id = u.id
       JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
       LEFT JOIN meetings m ON m.teacher_id = ? AND m.student_id = b.student_id AND m.meeting_date = b.datetime
       WHERE b.teacher_profile_id = ?
       ORDER BY b.datetime DESC`,
      [req.user.id, teacher_profile_id]
    );

    // لا تحدث الحالة تلقائياً هنا، فقط أعد البيانات كما هي
    res.json({
      success: true,
      data: bookings,
      teacherTimezone: teacher_timezone // Include teacher timezone in response
    });
  } catch (error) {
    console.error('Error fetching teacher bookings:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching bookings'
    });
  }
});

// Cancel a booking (for students and teachers)
router.put('/:id/cancel', verifyToken, async (req, res) => {
  const { id } = req.params;
  const { role } = req.query; // Get role from query parameter
  const { cancellation_reason } = req.body; // Get cancellation reason from request body

  console.log('🔍 Cancel booking request received:', {
    bookingId: id,
    userId: req.user?.id,
    userRole: req.user?.role,
    queryRole: role,
    cancellationReason: cancellation_reason,
    method: req.method,
    url: req.originalUrl
  });

  // Start a transaction
  const connection = await db.pool.getConnection();
  await connection.beginTransaction();

  try {
    let bookings = [];
    let isTeacherCancellation = false;

    // Check if user is a student or teacher and get booking accordingly
    // Use query parameter to determine cancellation type for teachers
    if (req.user.role === 'student' && role !== 'teacher') {
      // Student cancellation - check if booking belongs to student
      const [studentBookings] = await connection.execute(
        'SELECT b.*, tp.price_per_lesson FROM bookings b JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id WHERE b.id = ? AND b.student_id = ?',
        [id, req.user.id]
      );
      bookings = studentBookings;
    } else if (req.user.role === 'teacher' || role === 'teacher') {
      // Teacher cancellation - check if booking belongs to teacher
      const [teacherBookings] = await connection.execute(
        `SELECT b.*, tp.price_per_lesson
         FROM bookings b
         JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
         WHERE b.id = ? AND tp.user_id = ?`,
        [id, req.user.id]
      );
      bookings = teacherBookings;
      isTeacherCancellation = true;
    } else {
      await connection.rollback();
      connection.release();
      return res.status(403).json({
        success: false,
        message: 'Access denied. Only students and teachers can cancel bookings.'
      });
    }

    if (bookings.length === 0) {
      await connection.rollback();
      connection.release();
      return res.status(404).json({
        success: false,
        message: 'Booking not found or does not belong to you'
      });
    }

    // Check if the booking can be cancelled (not already completed or cancelled)
    if (bookings[0].status !== 'scheduled') {
      await connection.rollback();
      connection.release();
      return res.status(400).json({
        success: false,
        message: `Cannot cancel a booking with status "${bookings[0].status}"`
      });
    }

    // Calculate refund based on who is cancelling
    const price_per_lesson = parseFloat(bookings[0].price_per_lesson);
    let refundAmount;
    let refundUserId;

    if (isTeacherCancellation) {
      // Teacher cancellation - full refund to student
      refundAmount = price_per_lesson;
      refundUserId = bookings[0].student_id;

      console.log('💰 Teacher cancellation - full refund:', {
        bookingId: id,
        fullPrice: price_per_lesson,
        refundAmount: refundAmount,
        refundToStudent: refundUserId
      });
    } else {
      // Student cancellation - calculate refund based on timing
      const bookingTime = new Date(bookings[0].datetime);
      const currentTime = new Date();
      const timeDifference = bookingTime.getTime() - currentTime.getTime();
      const hoursDifference = timeDifference / (1000 * 3600);

      // Full refund if cancelled more than 24 hours before the booking
      refundAmount = hoursDifference > 24 ? price_per_lesson : price_per_lesson * 0.5;
      refundUserId = req.user.id;

      console.log('💰 Student cancellation refund calculation:', {
        bookingId: id,
        bookingTime: bookingTime.toISOString(),
        currentTime: currentTime.toISOString(),
        hoursDifference: hoursDifference,
        fullPrice: price_per_lesson,
        refundAmount: refundAmount
      });
    }

    // Refund the amount to the appropriate user's balance
    await connection.execute(
      'UPDATE users SET balance = balance + ? WHERE id = ?',
      [refundAmount, refundUserId]
    );

    // Determine who cancelled the booking
    const cancelledBy = (req.user.role === 'teacher' || role === 'teacher') ? 'teacher' : 'student';

    // Cancel the booking with reason and metadata
    const [cancelResult] = await connection.execute(
      'UPDATE bookings SET status = "cancelled", cancellation_reason = ?, cancelled_by = ?, cancelled_at = NOW() WHERE id = ?',
      [cancellation_reason || null, cancelledBy, id]
    );

    console.log('✅ Booking cancelled successfully:', {
      bookingId: id,
      datetime: bookings[0].datetime,
      teacher_profile_id: bookings[0].teacher_profile_id,
      student_id: bookings[0].student_id,
      affectedRows: cancelResult.affectedRows
    });

    // Update the payment status
    await connection.execute(
      'UPDATE payments SET status = "failed" WHERE teacher_profile_id = ? AND student_id = ? AND created_at = (SELECT created_at FROM bookings WHERE id = ?)',
      [bookings[0].teacher_profile_id, bookings[0].student_id, id]
    );

    // Get the teacher ID for this booking
    const [teacherResult] = await connection.execute(
      'SELECT u.id as teacher_id FROM teacher_profiles tp JOIN users u ON tp.user_id = u.id WHERE tp.id = ?',
      [bookings[0].teacher_profile_id]
    );

    if (teacherResult.length > 0) {
      const teacherId = teacherResult[0].teacher_id;

      // Cancel any associated meetings
      await connection.execute(
        'UPDATE meetings SET status = "cancelled" WHERE teacher_id = ? AND student_id = ? AND meeting_date = ?',
        [teacherId, bookings[0].student_id, bookings[0].datetime]
      );

      console.log('Cancelled associated meetings for booking:', {
        bookingId: id,
        teacherId,
        studentId: bookings[0].student_id,
        datetime: bookings[0].datetime,
        cancelledBy: req.user.role
      });
    }

    // Commit the transaction
    await connection.commit();
    connection.release();

    // Send cancellation emails (don't wait for completion to avoid delaying response)
    try {
      // Get teacher and student information for emails (including timezone)
      const [teacherInfo] = await db.pool.execute(
        `SELECT u.full_name, u.email, tp.timezone
         FROM users u
         JOIN teacher_profiles tp ON u.id = tp.user_id
         WHERE tp.id = ?`,
        [bookings[0].teacher_profile_id]
      );

      const [studentInfo] = await db.pool.execute(
        `SELECT u.full_name, u.email, scd.timezone
         FROM users u
         LEFT JOIN student_completion_data scd ON u.id = scd.user_id
         WHERE u.id = ?`,
        [bookings[0].student_id]
      );

      if (teacherInfo.length > 0 && studentInfo.length > 0) {
        // Prepare booking data for email
        const bookingData = {
          id: id,
          datetime: bookings[0].datetime,
          duration: bookings[0].duration || '50',
          amount: bookings[0].amount || '0'
        };

        // Send emails asynchronously (don't block the response)
        bookingCancellationEmailService.sendCancellationEmails(
          bookingData,
          teacherInfo[0],
          studentInfo[0],
          cancelledBy,
          cancellation_reason
        ).catch(emailError => {
          console.error('❌ Error sending cancellation emails:', emailError);
        });

        console.log('📧 Cancellation emails queued for sending');
      }
    } catch (emailError) {
      console.error('❌ Error preparing cancellation emails:', emailError);
    }

    res.json({
      success: true,
      message: 'Booking cancelled successfully and amount refunded to your wallet'
    });
  } catch (error) {
    // Rollback the transaction in case of error
    await connection.rollback();
    connection.release();
    console.error('Error cancelling booking:', error);
    res.status(500).json({
      success: false,
      message: 'Error cancelling booking'
    });
  }
});

// -----------------------------------------------------------------------------
// Check if the logged-in student has any previous (non-cancelled) bookings with
// the specified teacher. This is used by the front-end to decide whether to
// offer a discounted trial lesson or not.
// -----------------------------------------------------------------------------
router.get('/student/hasBooked/:teacherId', verifyToken, isStudent, async (req, res) => {
  const { teacherId } = req.params;

  try {
    // Count completed/scheduled bookings between this student and the teacher
    // We join through teacher_profiles so the API accepts the teacher's user ID
    const [[{ cnt }]] = await db.pool.execute(
      `SELECT COUNT(*) AS cnt
       FROM bookings b
       JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
       WHERE b.student_id = ?
         AND tp.user_id = ?
         AND b.status != 'cancelled'`,
      [req.user.id, teacherId]
    );

    return res.json({ success: true, hasPrevious: cnt > 0, count: cnt });
  } catch (error) {
    console.error('Error checking previous bookings:', error);
    return res.status(500).json({ success: false, message: 'Error checking previous bookings' });
  }
});

// Reschedule a booking (for teachers only)
router.put('/:id/reschedule', verifyToken, async (req, res) => {
  const { id } = req.params;
  const { newDateTime, newDuration, reschedule_reason } = req.body;

  console.log('🔍 Reschedule booking request received:', {
    bookingId: id,
    userId: req.user?.id,
    userRole: req.user?.role,
    userEmail: req.user?.email,
    newDateTime,
    newDuration,
    rescheduleReason: reschedule_reason,
    fullUser: req.user
  });

  // Both teachers and students can reschedule bookings
  const isTeacher = req.user.role === 'platform_teacher' || req.user.role === 'new_teacher';
  const isStudent = req.user.role === 'student';

  if (!isTeacher && !isStudent) {
    console.log('❌ Reschedule denied - user is neither teacher nor student:', {
      userId: req.user?.id,
      userRole: req.user?.role,
      expectedRoles: ['platform_teacher', 'new_teacher', 'student']
    });
    return res.status(403).json({
      success: false,
      message: `Only teachers and students can reschedule bookings. Current role: ${req.user?.role}`
    });
  }

  if (!newDateTime) {
    return res.status(400).json({
      success: false,
      message: 'New date and time are required'
    });
  }

  // Start a transaction
  const connection = await db.pool.getConnection();
  await connection.beginTransaction();

  try {
    // Get the booking and verify it belongs to this user (teacher or student)
    let bookingQuery, bookingParams;

    if (isTeacher) {
      // For teachers: check if they own this booking
      bookingQuery = `SELECT b.*, tp.price_per_lesson, tp.user_id as teacher_user_id
                      FROM bookings b
                      JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
                      WHERE b.id = ? AND tp.user_id = ?`;
      bookingParams = [id, req.user.id];
    } else {
      // For students: check if this is their booking
      bookingQuery = `SELECT b.*, tp.price_per_lesson, tp.user_id as teacher_user_id
                      FROM bookings b
                      JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
                      WHERE b.id = ? AND b.student_id = ?`;
      bookingParams = [id, req.user.id];
    }

    const [bookings] = await connection.execute(bookingQuery, bookingParams);

    console.log('🔍 Booking lookup result:', {
      bookingId: id,
      userId: req.user.id,
      userRole: req.user.role,
      foundBookings: bookings.length,
      bookingDetails: bookings.length > 0 ? {
        id: bookings[0].id,
        teacherUserId: bookings[0].teacher_user_id,
        studentId: bookings[0].student_id,
        status: bookings[0].status
      } : null
    });

    if (bookings.length === 0) {
      await connection.rollback();
      return res.status(404).json({
        success: false,
        message: 'Booking not found or you do not have permission to reschedule it'
      });
    }

    const booking = bookings[0];

    // Check if booking can be rescheduled (only scheduled bookings)
    if (booking.status !== 'scheduled') {
      await connection.rollback();
      return res.status(400).json({
        success: false,
        message: 'Only scheduled bookings can be rescheduled'
      });
    }

    // Validate new date/time is in the future
    const newBookingTime = new Date(newDateTime);
    const currentTime = new Date();

    if (newBookingTime <= currentTime) {
      await connection.rollback();
      connection.release();
      return res.status(400).json({
        success: false,
        message: 'New booking time must be in the future'
      });
    }

    // Convert newDateTime to MySQL format
    const mysqlDateTime = new Date(newDateTime).toISOString().slice(0, 19).replace('T', ' ');

    // Check for conflicts with other bookings
    if (isStudent) {
      // For students: check if they have another booking at the same time
      const [studentConflicts] = await connection.execute(
        `SELECT id FROM bookings
         WHERE student_id = ? AND datetime = ? AND status = 'scheduled' AND id != ?`,
        [req.user.id, mysqlDateTime, id]
      );

      if (studentConflicts.length > 0) {
        await connection.rollback();
        connection.release();
        return res.status(400).json({
          success: false,
          message: 'You already have another booking at this time'
        });
      }
    }

    // Check if teacher is available at the new time
    const [teacherConflicts] = await connection.execute(
      `SELECT id FROM bookings
       WHERE teacher_profile_id = ? AND datetime = ? AND status = 'scheduled' AND id != ?`,
      [booking.teacher_profile_id, mysqlDateTime, id]
    );

    if (teacherConflicts.length > 0) {
      await connection.rollback();
      connection.release();
      return res.status(400).json({
        success: false,
        message: 'Teacher is not available at this time'
      });
    }

    console.log('🔍 Converting datetime for MySQL:', {
      original: newDateTime,
      converted: mysqlDateTime
    });

    // Check if the new time slot is available
    const [conflictingBookings] = await connection.execute(
      `SELECT id FROM bookings
       WHERE teacher_profile_id = ?
       AND datetime = ?
       AND status != 'cancelled'
       AND id != ?`,
      [booking.teacher_profile_id, mysqlDateTime, id]
    );

    if (conflictingBookings.length > 0) {
      await connection.rollback();
      return res.status(400).json({
        success: false,
        message: 'The selected time slot is not available'
      });
    }
    // Store original datetime before updating
    const originalDatetime = booking.datetime;

    // Determine who rescheduled
    const rescheduledBy = isTeacher ? 'teacher' : 'student';

    // Update the booking with new date/time and reschedule information
    await connection.execute(
      `UPDATE bookings
       SET datetime = ?, duration = ?, reschedule_reason = ?, rescheduled_by = ?,
           rescheduled_at = NOW(), original_datetime = ?, updated_at = NOW()
       WHERE id = ?`,
      [mysqlDateTime, newDuration || booking.duration, reschedule_reason || null, rescheduledBy, originalDatetime, id]
    );

    // Log the reschedule action
    console.log('✅ Booking rescheduled successfully:', {
      bookingId: id,
      teacherId: req.user.id,
      oldDateTime: booking.datetime,
      newDateTime: newDateTime,
      duration: newDuration || booking.duration
    });

    await connection.commit();
    connection.release();

    // Send reschedule emails (don't wait for completion to avoid delaying response)
    try {
      // Get teacher and student information for emails (including timezone)
      const [teacherInfo] = await db.pool.execute(
        `SELECT u.full_name, u.email, tp.timezone
         FROM users u
         JOIN teacher_profiles tp ON u.id = tp.user_id
         WHERE tp.id = ?`,
        [booking.teacher_profile_id]
      );

      const [studentInfo] = await db.pool.execute(
        `SELECT u.full_name, u.email, scd.timezone
         FROM users u
         LEFT JOIN student_completion_data scd ON u.id = scd.user_id
         WHERE u.id = ?`,
        [booking.student_id]
      );

      if (teacherInfo.length > 0 && studentInfo.length > 0) {
        // Prepare booking data for email
        const bookingData = {
          id: id,
          originalDatetime: originalDatetime,
          newDatetime: newDateTime,
          duration: newDuration || booking.duration
        };

        // Send emails asynchronously (don't block the response)
        bookingRescheduleEmailService.sendRescheduleEmails(
          bookingData,
          teacherInfo[0],
          studentInfo[0],
          rescheduledBy,
          reschedule_reason
        ).catch(emailError => {
          console.error('❌ Error sending reschedule emails:', emailError);
        });

        console.log('📧 Reschedule emails queued for sending');
      }
    } catch (emailError) {
      console.error('❌ Error preparing reschedule emails:', emailError);
    }

    res.json({
      success: true,
      message: 'Booking rescheduled successfully',
      booking: {
        id: booking.id,
        datetime: newDateTime,
        duration: newDuration || booking.duration
      }
    });

  } catch (error) {
    await connection.rollback();
    console.error('Error rescheduling booking:', error);
    res.status(500).json({
      success: false,
      message: 'Error rescheduling booking. Please try again.'
    });
  } finally {
    connection.release();
  }
});

// Get available slots for rescheduling (for students and teachers)
router.get('/:id/available-slots', verifyToken, async (req, res) => {
  console.log('🚀🚀🚀 RESCHEDULE API CALLED:', {
    bookingId: req.params.id,
    userRole: req.user?.role,
    userId: req.user?.id,
    url: req.originalUrl
  });

  const { id } = req.params;
  const { student_timezone } = req.query;

  // Check if user is teacher or student
  const isTeacher = req.user.role === 'platform_teacher' || req.user.role === 'new_teacher';
  const isStudent = req.user.role === 'student';

  console.log('🚀🚀🚀 USER TYPE:', { isTeacher, isStudent, role: req.user?.role });

  try {
    let booking, teacherId;

    if (isStudent) {
      // For students: get booking details and check ownership
      const [bookings] = await db.pool.execute(
        `SELECT b.*, tp.user_id as teacher_user_id
         FROM bookings b
         JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
         WHERE b.id = ? AND b.student_id = ?`,
        [id, req.user.id]
      );

      if (bookings.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Booking not found or you do not have permission to reschedule it'
        });
      }

      booking = bookings[0];
      teacherId = booking.teacher_user_id;
    } else if (isTeacher) {
      // For teachers: get booking details and check if they own it
      const [bookings] = await db.pool.execute(
        `SELECT b.*, tp.user_id as teacher_user_id
         FROM bookings b
         JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
         WHERE b.id = ? AND tp.user_id = ?`,
        [id, req.user.id]
      );

      if (bookings.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Booking not found or you do not have permission to reschedule it'
        });
      }

      booking = bookings[0];
      teacherId = booking.teacher_user_id;
    } else {
      return res.status(403).json({
        success: false,
        message: 'Only teachers and students can reschedule bookings'
      });
    }

    if (isStudent) {
      // For students: use existing logic (only available hours)
      const teacherPublicController = require('../controllers/teacher.public.controller');

      const mockReq = {
        params: { id: teacherId },
        query: {
          student_timezone: student_timezone,
          excludeBookingId: id
        }
      };

      let result = null;
      const mockRes = {
        json: (data) => { result = data; },
        status: (code) => ({ json: (data) => { result = { status: code, ...data }; } })
      };

      await teacherPublicController.getTeacherAvailableSlots(mockReq, mockRes);

      if (result && result.success) {
        res.json(result);
      } else {
        res.status(result?.status || 500).json(result || {
          success: false,
          message: 'Error getting available slots'
        });
      }
    } else if (isTeacher) {
      // For teachers: generate all possible time slots (not just available hours)
      await getTeacherRescheduleSlots(req, res, teacherId, id);
    }

  } catch (error) {
    console.error('Error getting available slots for reschedule:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting available slots'
    });
  }
});

// Helper function to get all possible time slots for teacher reschedule
async function getTeacherRescheduleSlots(req, res, teacherId, excludeBookingId) {
  console.log('🚀 TEACHER RESCHEDULE: Starting generation for teacher:', teacherId);

  let connection;
  try {
    connection = await db.pool.getConnection();

    // Get teacher profile
    const [teachers] = await connection.execute(
      `SELECT tp.id as profile_id, tp.timezone
       FROM teacher_profiles tp
       WHERE tp.user_id = ?`,
      [teacherId]
    );

    if (teachers.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const teacher = teachers[0];
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);

    // Get existing bookings (excluding current booking)
    const [bookings] = await connection.execute(
      `SELECT datetime, duration FROM bookings
       WHERE teacher_profile_id = ?
       AND datetime >= ?
       AND datetime <= ?
       AND status != 'cancelled'
       AND id != ?`,
      [teacher.profile_id, today.toISOString(), thirtyDaysFromNow.toISOString(), excludeBookingId]
    );

    // Get teacher's breaks
    const [weeklyBreaks] = await connection.execute(
      `SELECT datetime FROM teacher_weekly_breaks
       WHERE teacher_profile_id = ?
       AND datetime >= ?
       AND datetime <= ?`,
      [teacher.profile_id, today.toISOString(), thirtyDaysFromNow.toISOString()]
    );

    // Create set of booked/break slots
    const bookedSlots = new Set();

    // Add existing bookings
    bookings.forEach(booking => {
      const bookingDate = new Date(booking.datetime);
      const duration = parseInt(booking.duration) || 50;

      for (let i = 0; i < duration; i += 30) {
        const slotTime = new Date(bookingDate.getTime() + i * 60 * 1000);
        const slotDate = slotTime.toISOString().split('T')[0];
        const slotHour = slotTime.getUTCHours().toString().padStart(2, '0');
        const slotMinute = slotTime.getUTCMinutes().toString().padStart(2, '0');
        const slotKey = `${slotDate}_${slotHour}:${slotMinute}`;
        bookedSlots.add(slotKey);
      }
    });

    // Add breaks
    weeklyBreaks.forEach(breakSlot => {
      const utcDateTime = new Date(breakSlot.datetime);
      const breakDate = utcDateTime.toISOString().split('T')[0];
      const breakHour = utcDateTime.getUTCHours().toString().padStart(2, '0');
      const breakMinute = utcDateTime.getUTCMinutes().toString().padStart(2, '0');
      const breakKey = `${breakDate}_${breakHour}:${breakMinute}`;
      bookedSlots.add(breakKey);
    });

    // Generate all possible time slots (every 30 minutes, 24/7)
    const availableSlots = [];
    const fiveMinutesFromNowUTC = new Date(today.getTime() + 5 * 60 * 1000);

    for (let dayOffset = 0; dayOffset < 30; dayOffset++) {
      const currentDate = new Date(today);
      currentDate.setDate(today.getDate() + dayOffset);
      const dateString = currentDate.toISOString().split('T')[0];

      // Generate slots every 30 minutes for 24 hours
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
          const slotUTC = new Date(`${dateString}T${timeString}:00.000Z`);

          // Skip past slots
          if (slotUTC <= fiveMinutesFromNowUTC) {
            continue;
          }

          const slotKey = `${dateString}_${timeString}`;

          // Only exclude if booked or break
          if (!bookedSlots.has(slotKey)) {
            availableSlots.push({
              date: dateString,
              time: timeString,
              endTime: timeString, // For compatibility
              datetime: slotUTC.toISOString(),
              dayName: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][currentDate.getDay()],
              isAvailable: true
            });
          }
        }
      }
    }

    console.log('🔍 Teacher reschedule slots generated:', {
      totalSlots: availableSlots.length,
      firstSlot: availableSlots[0],
      lastSlot: availableSlots[availableSlots.length - 1],
      sampleSlots: availableSlots.slice(0, 5).map(s => `${s.date} ${s.time}`)
    });

    res.json({
      success: true,
      data: availableSlots,
      teacher: {
        id: teacherId,
        profile_id: teacher.profile_id,
        timezone: teacher.timezone
      }
    });

  } catch (error) {
    console.error('Error getting teacher reschedule slots:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting available slots'
    });
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

module.exports = router;
