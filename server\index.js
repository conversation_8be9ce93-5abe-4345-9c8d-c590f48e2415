const dotenv = require('dotenv');
dotenv.config();
const express = require('express');
const cors = require('cors');
const http = require('http');
const socketIo = require('socket.io');
const jwt = require('jsonwebtoken');
const config = require('./config/auth.config');
const path = require('path');
const fs = require('fs');
const db = require('./config/db');
const multer = require('multer');
const socketModule = require('./socket');
const { processScheduledDeletions } = require('./scripts/processScheduledDeletions');

const app = express();
const server = http.createServer(app);

// Middleware
app.use(cors({
  origin: [
    process.env.CLIENT_URL || 'https://allemnionline.com',
    'https://accounts.google.com',
    'https://www.google.com'
  ],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  optionsSuccessStatus: 200
}));
app.use(express.json({ limit: '100mb' }));
app.use(express.urlencoded({ extended: true, limit: '100mb' }));

// Security headers
app.use((req, res, next) => {
  res.header('Cross-Origin-Opener-Policy', 'same-origin-allow-popups');
  res.header('Cross-Origin-Embedder-Policy', 'unsafe-none');
  res.header('X-Content-Type-Options', 'nosniff');
  res.header('X-Frame-Options', 'SAMEORIGIN');
  res.header('X-XSS-Protection', '1; mode=block');
  next();
});

// Socket.IO setup
const io = socketIo(server, {
  cors: {
    origin: process.env.CLIENT_URL || "https://allemnionline.com",
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
    credentials: true
  }
});

// Initialize socket module
socketModule.init(io);

// Create uploads directories if they don't exist
const uploadDirs = [
  'uploads', 'uploads/profile-pictures', 'uploads/documents', 'uploads/videos'
];
uploadDirs.forEach(dir => {
  const fullPath = path.join(__dirname, dir);
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
  }
});

// Serve static files from uploads directory with proper MIME types
app.use('/uploads', express.static(path.join(__dirname, 'uploads'), {
  setHeaders: (res, filePath) => {
    // Set proper content type for images
    if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
      res.setHeader('Content-Type', 'image/jpeg');
    } else if (filePath.endsWith('.png')) {
      res.setHeader('Content-Type', 'image/png');
    }
    // Set proper content type for videos
    else if (filePath.endsWith('.mp4')) {
      res.setHeader('Content-Type', 'video/mp4');
    } else if (filePath.endsWith('.webm')) {
      res.setHeader('Content-Type', 'video/webm');
    } else if (filePath.endsWith('.ogg')) {
      res.setHeader('Content-Type', 'video/ogg');
    }
    // Enable caching for media files
    res.setHeader('Cache-Control', 'public, max-age=31536000');
  }
}));

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  if (req.url.includes('/auth/google')) {
    console.log('Google auth request details:', {
      method: req.method,
      url: req.url,
      body: req.body,
      headers: {
        'content-type': req.headers['content-type'],
        'user-agent': req.headers['user-agent']
      }
    });
  }
  next();
});

// API Routes
const authRoutes = require('./routes/auth.routes');
const teacherRoutes = require('./routes/teacher.routes');
const teacherPublicRoutes = require('./routes/teacher.public.routes');
const homeRoutes = require('./routes/home.routes');
const searchRoutes = require('./routes/search.routes');
const chatRoutes = require('./routes/chat.routes');
const studentRoutes = require('./routes/student.routes');
const meetingsRoutes = require('./routes/meetings.routes');
const walletRoutes = require('./routes/wallet.routes');
const withdrawalRoutes = require('./routes/withdrawal.routes');
const contactRoutes = require('./routes/contact');
const bookingsRoutes = require('./routes/bookings.routes');
const meetingIssuesRoutes = require('./routes/meetingIssues.routes');
const notesRoutes = require('./routes/notes.routes');
const reviewsRoutes = require('./routes/reviews.routes');

app.use('/api/auth', authRoutes);
app.use('/api/teacher', teacherRoutes);
app.use('/api/teacher/dashboard', require('./routes/teacher/dashboard'));
app.use('/api/teachers', teacherPublicRoutes);
app.use('/api/home', homeRoutes);
app.use('/api/users', require('./routes/users.routes'));
app.use('/api/students', studentRoutes);
app.use('/api/admin', require('./routes/admin.routes'));
app.use('/api/search', searchRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/meetings', meetingsRoutes);
app.use('/api/wallet', walletRoutes);
app.use('/api/withdrawal', withdrawalRoutes);
app.use('/api/contact-us', contactRoutes);
app.use('/api/bookings', bookingsRoutes);
app.use('/api/meeting-issues', meetingIssuesRoutes);
app.use('/api/notes', notesRoutes);
app.use('/api/reviews', reviewsRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'API is working correctly',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Add direct routes for teacher controller
const teacherController = require('./controllers/teacher.controller');
const { verifyToken } = require('./middleware/auth.middleware');
app.get('/api/teacher/languages', verifyToken, teacherController.getLanguages);
app.get('/api/teacher/application/status', verifyToken, teacherController.getApplicationStatus);

// Add direct routes for video upload and delete
const { uploadVideo } = require('./middleware/upload');
app.post('/api/teacher/upload-video', verifyToken, uploadVideo, teacherController.uploadVideo);
app.delete('/api/teacher/delete-video', verifyToken, teacherController.deleteVideo);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);

  if (err instanceof multer.MulterError) {
    console.error('Multer error details:', err);

    // Handle specific Multer errors
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(413).json({
        success: false,
        message: 'File too large. Maximum file size is 100MB.',
        error: err.message
      });
    }

    return res.status(400).json({
      success: false,
      message: 'File upload error. Please make sure your file meets the requirements.',
      error: err.message
    });
  }

  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      success: false,
      message: 'Invalid token or no token provided'
    });
  }

  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal server error'
  });
});

// Database connection
db.getConnection()
  .then(() => {
    console.log('Database connected successfully');

    // Start meeting status update service after database connection
    const meetingStatusService = require('./services/meetingStatusService');
    meetingStatusService.start();

    // Start scheduled account deletions process (runs every hour)
    setInterval(async () => {
      try {
        console.log('Running scheduled account deletions check...');
        await processScheduledDeletions();
      } catch (error) {
        console.error('Error in scheduled deletions process:', error);
      }
    }, 60 * 60 * 1000); // كل ساعة

    // Run once on startup
    setTimeout(async () => {
      try {
        await processScheduledDeletions();
      } catch (error) {
        console.error('Error in initial scheduled deletions check:', error);
      }
    }, 5000); // بعد 5 ثوان من بدء التشغيل
  })
  .catch(err => {
    console.error('Database connection error:', err);
    process.exit(1);
  });

const PORT = process.env.PORT || 5000;
const HOST = process.env.HOST || '0.0.0.0';

server.listen(PORT, HOST, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`Socket.IO is ready for connections`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  const meetingStatusService = require('./services/meetingStatusService');
  meetingStatusService.stop();
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  const meetingStatusService = require('./services/meetingStatusService');
  meetingStatusService.stop();
  server.close(() => {
    console.log('Process terminated');
  });
});
