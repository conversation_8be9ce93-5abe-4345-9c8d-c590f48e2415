/**
 * ملف اختبار للتأكد من أن دالة الحذف الشاملة تعمل بشكل صحيح
 * هذا الملف للاختبار فقط ولا يجب تشغيله في الإنتاج
 */

const mysql = require('mysql2/promise');
const config = require('./config/database');
const { deleteUserCompletely } = require('./scripts/processScheduledDeletions');

async function testDeletion() {
  let connection;
  try {
    connection = await mysql.createConnection(config);
    
    console.log('🔍 Testing deletion function...');
    
    // البحث عن مستخدم تجريبي للحذف (يجب أن يكون مستخدم تجريبي فقط!)
    const [testUsers] = await connection.execute(`
      SELECT id, email, full_name, role, status
      FROM users
      WHERE email LIKE '%test%' OR email LIKE '%demo%'
      LIMIT 1
    `);
    
    if (testUsers.length === 0) {
      console.log('❌ No test users found. Please create a test user first.');
      return;
    }
    
    const testUser = testUsers[0];
    console.log(`📋 Found test user: ${testUser.email} (ID: ${testUser.id}, Role: ${testUser.role})`);
    
    // عرض البيانات المرتبطة قبل الحذف
    console.log('\n📊 Data before deletion:');
    
    const tables = [
      'bookings',
      'reviews', 
      'meetings',
      'conversations',
      'messages',
      'notes',
      'payments',
      'transactions',
      'withdrawal_requests',
      'contact_messages',
      'student_completion_data',
      'teacher_profiles',
      'teacher_categories',
      'teacher_languages',
      'notifications',
      'user_sessions',
      'password_reset_tokens',
      'user_delete_requests'
    ];
    
    for (const table of tables) {
      try {
        let query;
        if (table === 'bookings') {
          query = `SELECT COUNT(*) as count FROM ${table} WHERE student_id = ? OR teacher_id IN (SELECT id FROM teacher_profiles WHERE user_id = ?)`;
        } else if (table === 'reviews') {
          query = `SELECT COUNT(*) as count FROM ${table} WHERE student_id = ? OR teacher_profile_id IN (SELECT id FROM teacher_profiles WHERE user_id = ?)`;
        } else if (table === 'meetings') {
          query = `SELECT COUNT(*) as count FROM ${table} WHERE teacher_id = ? OR student_id = ?`;
        } else if (table === 'conversations') {
          query = `SELECT COUNT(*) as count FROM ${table} WHERE student_id = ? OR teacher_id = ?`;
        } else if (table === 'messages') {
          query = `SELECT COUNT(*) as count FROM ${table} WHERE sender_id = ? OR recipient_id = ?`;
        } else if (table === 'notes') {
          query = `SELECT COUNT(*) as count FROM ${table} WHERE teacher_id = ? OR student_id = ?`;
        } else if (table === 'payments') {
          query = `SELECT COUNT(*) as count FROM ${table} WHERE student_id = ? OR teacher_profile_id IN (SELECT id FROM teacher_profiles WHERE user_id = ?)`;
        } else if (table === 'withdrawal_requests') {
          query = `SELECT COUNT(*) as count FROM ${table} WHERE teacher_id = ?`;
        } else if (table === 'teacher_categories') {
          query = `SELECT COUNT(*) as count FROM ${table} WHERE teacher_profile_id IN (SELECT id FROM teacher_profiles WHERE user_id = ?)`;
        } else if (table === 'teacher_languages') {
          query = `SELECT COUNT(*) as count FROM ${table} WHERE teacher_profile_id IN (SELECT id FROM teacher_profiles WHERE user_id = ?)`;
        } else {
          query = `SELECT COUNT(*) as count FROM ${table} WHERE user_id = ?`;
        }
        
        const [result] = await connection.execute(query, [testUser.id, testUser.id]);
        if (result[0].count > 0) {
          console.log(`   ${table}: ${result[0].count} records`);
        }
      } catch (error) {
        // تجاهل الأخطاء للجداول غير الموجودة
        if (!error.message.includes("doesn't exist")) {
          console.log(`   ${table}: Error - ${error.message}`);
        }
      }
    }
    
    console.log('\n⚠️  WARNING: This will permanently delete the test user and all related data!');
    console.log('Press Ctrl+C to cancel, or wait 5 seconds to proceed...');
    
    // انتظار 5 ثوان
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('\n🗑️  Starting deletion process...');
    
    // بدء transaction
    await connection.beginTransaction();
    
    try {
      // استخدام دالة الحذف الشاملة
      await deleteUserCompletely(connection, testUser.id, testUser.email);
      
      // تأكيد التغييرات
      await connection.commit();
      
      console.log('\n✅ Deletion completed successfully!');
      
      // التحقق من أن البيانات تم حذفها
      const [userCheck] = await connection.execute(
        'SELECT COUNT(*) as count FROM users WHERE id = ?',
        [testUser.id]
      );
      
      if (userCheck[0].count === 0) {
        console.log('✅ User successfully deleted from database');
      } else {
        console.log('❌ User still exists in database');
      }
      
    } catch (error) {
      await connection.rollback();
      console.error('❌ Error during deletion:', error);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    if (connection) await connection.end();
  }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  testDeletion()
    .then(() => {
      console.log('\n🏁 Test completed.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = testDeletion;
