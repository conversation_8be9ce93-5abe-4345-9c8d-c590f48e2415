import React, { createContext, useState, useContext, useEffect } from 'react';
import axios from '../utils/axios';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation();

  useEffect(() => {
    // Check if user is logged in on mount
    const storedToken = localStorage.getItem('token');
    if (storedToken) {
      // Set token in axios headers
      axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;
      
      // Verify token and get user data
      console.log('Verifying token...');
      axios.get('/auth/verify')
        .then(response => {
          console.log('Token verification response:', response.data);
          if (response.data && response.data.success && response.data.user) {
            const userData = {
              ...response.data.user,
              full_name: response.data.user.full_name || null,
              email: response.data.user.email || null,
              gender: response.data.user.gender || null,
              status: response.data.user.status || 'active',
              delete_scheduled_at: response.data.user.delete_scheduled_at || null
            };

            // التحقق من حالة المستخدم
            if (userData.status === 'deleted') {
              console.log('User account is deleted - logging out');
              handleLogout();
              return;
            }

            if (userData.status === 'pending_deletion') {
              console.log('User account is pending deletion - allowing limited access');
              // السماح بالوصول ولكن مع تحذير
            }

            setCurrentUser(userData);
            setToken(storedToken);
            setIsAuthenticated(true);
          } else {
            console.log('Invalid response from verify endpoint');
            handleLogout();
          }
        })
        .catch(error => {
          console.error('Token verification error:', error);

          // التحقق من نوع الخطأ
          if (error.response && error.response.status === 401) {
            const errorData = error.response.data;

            // إذا كان هناك معلومات عن حالة الحساب، حفظها للعرض في صفحة تسجيل الدخول
            if (errorData && errorData.accountStatus) {
              if (process.env.NODE_ENV === 'development') {
                console.log('Account status detected in AuthContext:', errorData.accountStatus);
              }

              // حفظ الرسالة المترجمة
              const messageToSave = {
                message: errorData.message, // الرسالة بالعربية
                message_en: errorData.message_en, // الرسالة بالإنجليزية
                accountStatus: errorData.accountStatus,
                deleteScheduledAt: errorData.deleteScheduledAt
              };

              localStorage.setItem('accountStatusMessage', JSON.stringify(messageToSave));

              // منع الدخول للمنصة - تسجيل خروج فوري
              handleLogout();

              // منع أي طلبات إضافية
              setLoading(false);
              setInitialized(true);
              return;
            }
          }

          handleLogout();
        })
        .finally(() => {
          setLoading(false);
          setInitialized(true);
        });
    } else {
      console.log('No token found in localStorage');
      setLoading(false);
      setInitialized(true);
    }
  }, []);

  const handleLogout = async () => {
    try {
      if (token) {
        await axios.post('/auth/logout');
      }
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      localStorage.removeItem('token');
      setCurrentUser(null);
      setToken(null);
      setIsAuthenticated(false);
      delete axios.defaults.headers.common['Authorization'];
      navigate('/login');
    }
  };

  const redirectToDashboard = (user) => {
    switch (user.role) {
      case 'admin':
        navigate('/admin/dashboard');
        break;
      case 'platform_teacher':
        navigate('/teacher/dashboard');
        break;
      case 'new_teacher':
        navigate('/teacher/application');
        break;
      case 'student':
        navigate('/student/dashboard');
        break;
      default:
        navigate('/');
    }
  };

  const login = async (emailOrToken, passwordOrUser) => {
    try {
      // If called with token and user (from email verification)
      if (typeof emailOrToken === 'string' && emailOrToken.length > 50 && typeof passwordOrUser === 'object') {
        const token = emailOrToken;
        const user = passwordOrUser;

        const userData = {
          ...user,
          full_name: user.full_name || null,
          email: user.email || null,
          gender: user.gender || null
        };
        localStorage.setItem('token', token);
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        setCurrentUser(userData);
        setToken(token);
        setIsAuthenticated(true);
        redirectToDashboard(userData);
        return { success: true };
      }

      // Normal login with email and password
      const response = await axios.post('/auth/login', {
        email: emailOrToken || null,
        password: passwordOrUser || null
      });

      if (response.data && response.data.success && response.data.token && response.data.user) {
        const { token, user } = response.data;
        const userData = {
          ...user,
          full_name: user.full_name || null,
          email: user.email || null,
          gender: user.gender || null
        };
        localStorage.setItem('token', token);
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        setCurrentUser(userData);
        setToken(token);
        setIsAuthenticated(true);
        redirectToDashboard(userData);
        return { success: true };
      }
      // If login failed, throw error to be caught by the calling component
      const errorMessage = response.data.message || 'Login failed';
      throw new Error(errorMessage);
    } catch (error) {
      console.error('Login error in AuthContext:', {
        error: error,
        response: error.response,
        data: error.response?.data,
        status: error.response?.status,
        message: error.message
      });

      // Re-throw the error as-is since axios interceptor already enhanced it
      throw error;
    }
  };

  const register = async (userData) => {
    try {
      const response = await axios.post('/auth/register', userData);

      // Check if registration was successful (email verification required)
      if (response.data && response.data.success) {
        return {
          success: true,
          email: response.data.email,
          message: response.data.message
        };
      }

      return {
        success: false,
        error: response.data.message || 'Registration failed'
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to register'
      };
    }
  };

  const updateUser = (userData) => {
    setCurrentUser(prevUser => ({
      ...prevUser,
      ...userData
    }));
  };

  const googleLogin = async (credentialResponse) => {
    try {
      const response = await axios.post('/auth/google', {
        credential: credentialResponse.credential
      });

      if (response.data && response.data.success && response.data.token && response.data.user) {
        const { token, user } = response.data;
        const userData = {
          ...user,
          full_name: user.full_name || null,
          email: user.email || null,
          gender: user.gender || null
        };
        localStorage.setItem('token', token);
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        setCurrentUser(userData);
        setToken(token);
        setIsAuthenticated(true);
        return { success: true, user: userData };
      }
      return { success: false, message: response.data.message };
    } catch (error) {
      console.error('Google login error:', error);

      // Handle specific error cases
      if (error.response && error.response.data) {
        const errorData = error.response.data;

        // Handle deleted account
        if (errorData.errorType === 'ACCOUNT_DELETED') {
          return {
            success: false,
            message: t('auth.accountDeleted'),
            errorType: 'ACCOUNT_DELETED',
            accountStatus: 'deleted'
          };
        }

        // Handle other server errors
        return {
          success: false,
          message: errorData.message || t('auth.loginFailed')
        };
      }

      // Handle network or other errors
      throw error;
    }
  };

  const googleRegister = async ({ credential, role, gender }) => {
    try {
      const response = await axios.post('/auth/google/register', {
        credential,
        role,
        gender
      });

      // Check if registration was successful (email verification required)
      if (response.data && response.data.success) {
        return {
          success: true,
          email: response.data.email,
          message: response.data.message
        };
      }

      return { success: false, error: response.data.message || 'Registration failed' };
    } catch (error) {
      console.error('Google registration error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to register with Google'
      };
    }
  };

  const value = {
    currentUser,
    token,
    loading,
    initialized,
    isAuthenticated,
    login,
    register,
    handleLogout,
    updateUser,
    googleLogin,
    googleRegister
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
